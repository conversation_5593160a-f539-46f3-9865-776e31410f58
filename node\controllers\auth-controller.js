const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const AuthServices = require("../services/auth-services");
const SubscriptionMapServices = require("../services/subscription-map-services");
const authServices = new AuthServices();
class AuthController {

    // Handles user registration and pushes the request to SQS queue
    async userSignupQueue(req, res, next) {
        try {
            let result;
            // if the environment is local, it will call the signup service directly and if the environment is not local, it will push the request to SQS queue
            if (process.env.NODE_ENV === "local") {
                result = await authServices.signup(req.body, req.elasticLogObj);
            } else {
                result = await authServices.userSignupQueue(req.body, req.elasticLogObj);
            }
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController userSignupQueue ->`, error);
            next(error);
        }
    }

    // this function is used to handle user signup process in worker route and its work seprately from other apis
    async userSignupProcess(req, res, next) {
        try {
            const { requestBody, elasticLogObj } = req.body;
            if (!requestBody || !elasticLogObj) {
                const message = "Missing Parameters";
                return res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            }

            console.log("Starting userSignupProcess with request body:", requestBody);

            const result = await authServices.signup(requestBody, elasticLogObj, true); // Call signup service with request body and elastic log object
            console.log("Ending userSignupProcess with result:", result);

            const { status, message } = result;

            if (status) {
                return res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            } else {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
            }
        } catch (error) {
            console.error("AuthController userSignupProcess ->", error);

            //return success response
            const message = error?.message || RESPONSE_MESSAGES.SOMETHING_WENT_WRONG;
            return res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
        }
    }
    // Handles user login
    async userLogin(req, res, next) {
        let elasticLog = {}
        elasticLog.url = `${req?.originalUrl}`;
        elasticLog = { userinfo: { ...elasticLog } };
        try {
            const result = await authServices.login(req.body, elasticLog); // Call login service with request body
            const { status, message, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController userLogin -> `, error);
            next(error);
        }
    }

    // Handles user password reset
    async userResetPassword(req, res, next) {
        let elasticLog = {}
        elasticLog.url = `${req?.originalUrl}`;
        elasticLog = { userinfo: { ...elasticLog } };
        try {
            const result = await authServices.resetPassword(req.body, elasticLog);
            const { status, message, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController userResetPassword ->`, error);
            next(error);
        }
    }

    // Handles user forgot password
    async userForgotPassword(req, res, next) {
        let elasticLog = {}
        elasticLog.url = `${req?.originalUrl}`;
        elasticLog = { userinfo: { ...elasticLog } };
        try {
            const result = await authServices.forgotPassword(req.body, elasticLog);
            const { status, message, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController userForgotPassword -> `, error);
            next(error);
        }
    }

    // Handles user cognito account creation
    async userCreateCognitoAccount(req, res, next) {
        try {
            const result = await authServices.createCognitoAccount(req.body, req.elasticLogObj); // Call createCognitoAccount service with request body
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController createCognitoAccount ->`, error);
            next(error);
        }
    }

    async deleteContact(req, res, next) {
        try {
            const result = await authServices.deleteContact(req.body);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`AuthController userSignup ->`, error);
            next(error);
        }
    }

    async mapSubscription(req, res, next) {
        try {
            const { id: contactId } = req.query;
            const subscriptionMapServices = new SubscriptionMapServices();
            subscriptionMapServices.mapSubscription(contactId);
            res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message: "Subscription mapping is in progress." });
        } catch (error) {
            console.error(`AuthController mapSubscription ->`, error);
            next(error);
        }
    }
}

// Export an instance of the AuthController class
module.exports = new AuthController();