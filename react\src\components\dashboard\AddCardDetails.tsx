import { AddCardDetailsProps, CardDetailState } from "../../typings/typing";
import React, { ChangeEvent, useState } from "react";

import Button from "../common/Button";
import InputFields from "../forms/InputFields";
import { addNotification } from "../../store/reducers/toasterReducer";
import { logout } from "../../store/reducers/authenticationReducer";
import { regEx } from "../../utils/helper";
import { useAddCardMutation } from "../../services/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

// Initial form state
const initialState: CardDetailState = {
  userName: "",
  cardNumber: "",
  cvv: "",
  expiryDate: "",
  pinCode: "",
  postalCode: "",
  primaryCard: false,
};

// AddCardDetails Component
const AddCardDetails: React.FC<AddCardDetailsProps> = ({
  refetch,
  closeHandler,
  subscription_id,
}) => {
  const [formData, setFormData] = useState<CardDetailState>(initialState);
  const [formError, setFormError] = useState<Partial<CardDetailState>>({});
  const [addCard, { isLoading: addCardLoading }] = useAddCardMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Handle form cancellation
  const handleCancel = () => {
    closeHandler();
  };

  // Validate form data
  const validateFormData = (formData: CardDetailState) => {
    const error: Partial<CardDetailState> = {};
    let isValid = true;

    if (formData.userName.trim().length < 1) {
      isValid = false;
      error.userName = "Enter cardholder’s name";
    } else if (!regEx.Name.test(formData.userName)) {
      isValid = false;
      error.userName = "Enter valid cardholder’s name";
    }

    if (formData.cardNumber.length < 12 || formData.cardNumber.length > 20) {
      isValid = false;
      error.cardNumber = "Enter valid card number";
    }

    if (formData.cvv.length < 3) {
      isValid = false;
      error.cvv = "Enter valid CVV number";
    }

    // if (!formData.postalCode.trim().length) {
    //   isValid = false;
    //   error.postalCode = "Enter valid postal code";
    // }
    // if (!/^[A-Z]\d[A-Z] \d[A-Z]\d$/.test(formData.postalCode)) {
    //   isValid = false;
    //   error.postalCode = "Enter valid postal code in A1A 1A1 format";
    // }

    if (formData.expiryDate && formData.expiryDate.length === 5) {
      const [month, year] = formData.expiryDate.split("/");

      const today = new Date();
      const expiryMonth = parseInt(month, 10);
      const expiryYear = parseInt(year, 10); // Assuming the year is in YY format (e.g., 24 for 2024)

      if (
        expiryYear < Number(String(today.getFullYear()).slice(-2)) ||
        (expiryYear === Number(String(today.getFullYear()).slice(-2)) &&
          expiryMonth < today.getMonth() + 1)
      ) {
        isValid = false;
        error.expiryDate = "Please enter valid year";
      } else if (expiryMonth < 1 || expiryMonth > 12) {
        isValid = false;
        error.expiryDate = "Please enter a valid month";
      }
    } else {
      isValid = false;
      error.expiryDate = "Please enter a valid expiry date in MM/YY format";
    }

    return { isValid, error };
  };

  // Handle checkbox change for making primary card
  const handleMakePrimaryCard = (
    e: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.checked,
    });
  };

  // Handle input change
  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    let updatedValue = value;

    // Auto-format and validate expiry date as MM/YY
    if (name === "expiryDate") {
      const inputType = (e as any).nativeEvent.inputType;

      // Remove any non-numeric characters
      updatedValue = value.replace(/\D/g, "");

      if (updatedValue.length > 4) {
        return; // Prevent entering more than 4 digits
      }
      // Add a slash after 2 digits
      if (updatedValue.length > 2) {
        updatedValue = updatedValue.slice(0, 2) + "/" + updatedValue.slice(2);
      }

      if (inputType === "deleteContentBackward" && value.length === 3) {
        updatedValue = updatedValue.slice(0, 2); // Remove the slash if the user deletes it
      }
    }

    if (name === "cardNumber") {
      updatedValue = updatedValue.replace(/\D/g, "");
      updatedValue = updatedValue.slice(0, 16);
      let formattedInput = "";
      for (let i = 0; i < updatedValue.length; i++) {
        if (i % 4 === 0 && i !== 0) {
          formattedInput += " "; // Add a space every 4 digits
        }
        formattedInput += updatedValue[i];
      }
      updatedValue = formattedInput;
    }

    if (name === "cvv" && value && (!/^\d*$/.test(value) || value.length > 3))
      return;
    if (name === "postalCode") {
      updatedValue = value.toUpperCase().replace(/[^A-Z0-9]/g, "");
      if (updatedValue.length > 6) {
        return; // Prevent entering more than 6 characters
      }
      if (updatedValue.length > 3) {
        updatedValue = updatedValue.slice(0, 3) + " " + updatedValue.slice(3);
      }
    }

    if (
      name === "userName" &&
      value &&
      (!/^[a-zA-Z0-9 ]*$/.test(value) || value.length > 50)
    )
      return;

    setFormData((state) => ({ ...state, [name]: updatedValue }));
    setFormError((state) => ({ ...state, [name]: "" }));
  };

  // Save card details
  const handleSaveCardDetails = async () => {
    try {
      const [month, year] = formData.expiryDate.split("/");
      const data = {
        card_name: formData.userName.trim(),
        card_number: formData.cardNumber.replace(/ /g, "").trim(),
        expiry_month: month,
        expiry_year: "20" + year,
        postal_code: formData.postalCode.replace(/ /g, "").trim(),
        is_primary: formData.primaryCard ? "1" : "0",
        cvv: formData.cvv,
        subscription_id,
      };
      const response = await addCard(data).unwrap();

      if (response.status === 200) {
        dispatch(
          addNotification({ type: "success", message: response.message })
        );
        closeHandler();
        refetch();
      }
    } catch (error: any) {
      if (
        error?.data?.error?.length > 0 &&
        error?.data?.error?.[0]?.path === "card_number"
      ) {
        setFormError((formError) => ({
          ...formError,
          cardNumber: error?.data?.error?.[0]?.message,
        }));
      } else {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e: ChangeEvent<HTMLFormElement>): void => {
    e.preventDefault();
    const { isValid, error } = validateFormData(formData);
    setFormError(error);

    if (isValid && !addCardLoading) {
      setFormError({});
      handleSaveCardDetails();
    }
  };

  return (
    <div className="card-details-container">
      <form onSubmit={handleSubmit} className="flex gap-5 2xl:gap-10 flex-col">
        <div className="description">
          <p className="sub-text">
            Please fill out the fields below to update the card on file. Only
            one card can be on file at a time.
          </p>
        </div>
        <div className="flex flex-col 2xl:gap-5 gap-2.5">
          <InputFields
            changeEvent={handleChange}
            placeHolder="Cardholder’s Name"
            attributes={{ name: "userName", value: formData.userName }}
            isErrorMsg={formError.userName}
            className="!bg-[#F5EFFF]"
          />
          <InputFields
            changeEvent={handleChange}
            placeHolder="Credit card number"
            attributes={{ name: "cardNumber", value: formData.cardNumber }}
            isErrorMsg={formError.cardNumber}
            className="!bg-[#F5EFFF]"
          />
          <div className="flex max-lg:flex-col 2xl:gap-5 gap-2.5">
            <div className="basis-full md:basis-[calc(50%-5px)]">
              <InputFields
                placeHolder="MM/YY"
                changeEvent={handleChange}
                attributes={{ name: "expiryDate", value: formData.expiryDate }}
                isErrorMsg={formError.expiryDate}
                className="!bg-[#F5EFFF]"
              />
            </div>
            <div className="basis-full md:basis-[calc(50%-5px)]">
              <InputFields
                placeHolder="CVV"
                changeEvent={handleChange}
                attributes={{ name: "cvv", value: formData.cvv }}
                isErrorMsg={formError.cvv}
                className="!bg-[#F5EFFF]"
              />
            </div>
            {/* <div className="basis-full md:basis-[calc(50%-5px)]">
              <InputFields
                placeHolder="Postal code"
                changeEvent={handleChange}
                attributes={{ name: "postalCode", value: formData.postalCode }}
                isErrorMsg={formError.postalCode}
              />
            </div> */}
          </div>
        </div>
        <div className="flex gap-2.5 relative">
          <input
            id="primaryCard"
            type="checkbox"
            name="primaryCard"
            className="accent-[#fbc400] h-[17px] w-[17px]"
            checked={formData.primaryCard}
            onChange={handleMakePrimaryCard}
          />
          <label htmlFor="primaryCard">Make primary card</label>
        </div>
        <div className="flex flex-col gap-5 md:flex-row lg:relative">
          <Button
            title="Cancel"
            clickEvent={handleCancel}
            btnType="transparent"
            type="button"
            
          />
          <Button title="Add" type="submit" isLoading={addCardLoading} />
        </div>
      </form>
    </div>
  );
};

export default AddCardDetails;
