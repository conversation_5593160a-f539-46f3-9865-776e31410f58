const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const PromotionsServices = require("../services/promotion-services");
const promotionsServices = new PromotionsServices();

class PromotionController {

    /**
     * Method to fetch a list of promotions for a specific user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next middleware function
     */
    async fetchPromotionsList(req, res, next) {
        try {
            const { id } = req.userDetails; // Get user ID from request details
            const result = await promotionsServices.fetchPromotionsList(id, req.elasticLogObj); // Fetch promotions list from service
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data }); // Send success response
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG); // Handle error
        } catch (error) {
            console.error(`fetchPromotionsList fetch ReferralList ->`, error); // Log error
            next(error); // Pass error to next middleware
        }
    }

    /**
     * Method to fetch a list of eligible locations based on promotions for a specific user
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next middleware function
     */
    async fetchEligibleLocation(req, res, next) {
        try {
            const { id } = req.userDetails; // Get user ID from request details
            const result = await promotionsServices.fetchEligibleLocation(id, req.body, req.elasticLogObj); // Fetch eligible locations from service
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data }); // Send success response
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG); // Handle error
        } catch (error) {
            console.error(`fetchEligibleLocation ->`, error); // Log error
            next(error); // Pass error to next middleware
        }
    }

    async claimOffer(req, res, next) {
        try {
            const result = await promotionsServices.claimOffer(req.body, req.elasticLogObj); // claimOffer
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data }); // Send success response
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG); // Handle error
        } catch (error) {
            console.error(`claimOffer -> `, error); // Log error
            next(error); // Pass error to next middleware
        }
    }
}

// Export an instance of the PromotionController class
module.exports = new PromotionController();