const { userDetails, locationDetails } = require("../controllers/dashboard-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// Dashboard Route for user details
	router.get("/user-details", userDetails);
	// Dashboard Route for location details
	router.get("/location-details", locationDetails);
	
	// Use the base path passed from index.js
	app.use(`${basePath}/dashboard`, router); // Mount the router at the base path
};
