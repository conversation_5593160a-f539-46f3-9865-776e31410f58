import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { YourBalanceCard } from "../../components/dashboard/YourBalanceCard";
import YourPlaces from "../../components/dashboard/YourPlaces";
import YourPlacesSkeleton from "../../components/dashboard/skeletons/YourPlacesSkeleton";
import { useDashboardLocationDataMutation } from "../../services/api";
import { logout } from "../../store/reducers/authenticationReducer";
import { addNotification } from "../../store/reducers/toasterReducer";
// import { BestDeal } from "../../components/dashboard/BestDeal";

interface DashboardProps {}

export const Dashboard: React.FC<DashboardProps> = () => {
  const [locationData, setLocationData] = useState();
  const [getLocationData, locationLoading] = useDashboardLocationDataMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch user data and location data on component mount
  useEffect(() => {
    handleGetLocationData();
  }, []);

  // Function to handle fetching location data
  const handleGetLocationData = async () => {
    try {
      const response = await getLocationData({}).unwrap();
      if (response.status === 200) {
        const filteredLocations =
          response?.data?.locations?.filter(
            (location: any) => location?.stage !== "Hidden"
          ) || [];
        setLocationData(filteredLocations);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };
  return (
    <div className="dashboard">
      <div className="flex gap-5">
        <div className="2xl:max-w-[calc(100%)] lg:max-w-[calc(100%)] w-full">
          <YourBalanceCard refreshLocation={handleGetLocationData} />
          <div className="py-6 flex-1 h-full">
            {locationLoading?.isLoading ? (
              [1, 2, 3].map((index) => <YourPlacesSkeleton key={index} />)
            ) : (
              <YourPlaces places={locationData} />
            )}
          </div>
        </div>
        {/* <div className="2xl:w-[320px] w-[280px] max-lg:hidden relative z-[100]"> */}
        {/* <div className="sticky top-5"> */}
        {/* <BestDeal /> */}
        {/* </div> */}
        {/* </div> */}
      </div>
    </div>
  );
};
