import { ReactNode } from "react";

type StatusCardProps = {
  status?: string;
  Icon?: ReactNode | null;
  label?: string;
  isMoveOrder?: any | null;
};

const StatusCard = ({
  status = "",
  Icon = null,
  label = "",
  isMoveOrder = null,
}: StatusCardProps) => {
  if (status === "Hidden") {
    return false;
  }
  return (
    <div
      className={`flex 2xl:gap-4 lg:gap-4 gap-2 bg-white rounded-20 ${
        status === "Pending" && "opacity-40"
      } items-center`}
    >
      <div className="uppercase whitespace-nowrap flex items-center justify-center p-2.5 bg-light-purple-gradient 2xl:rounded-20 lg:rounded-10 rounded-2xl 2xl:w-[76px] 2xl:h-[76px] w-[50px] h-[50px]">
        {Icon}
      </div>
      <div className="flex flex-col 2xl:gap-2.5 gap-2">
        <div>
          <p
            className={`max-lg:text-sm max-lg:whitespace-nowrap text-base capitalize ${
              status === "Complete" ? "line-through" : ""
            }`}
          >
            {label}
          </p>
        </div>

        {/* if move order is not present then show original status */}
        {!isMoveOrder && status !== "Pending" ? (
          <div className="w-fit relative">
            <p
              className={`status-${status
                .replace(" ", "")
                .toLowerCase()} text-sm py-1 px-3 rounded-[20px] whitespace-nowrap leading-[1.3]`}
            >
              {status}
            </p>
          </div>
        ) : (
          <>
            {/* for move order */}
            {isMoveOrder &&
            (isMoveOrder?.stage === "Eligible For Submission" ||
              isMoveOrder?.stage === "Pre Response") ? (
              <div className="status-processingmoverequest p-2 px-4 font-normal text-[12px] w-max rounded-20 ">
                Processing move request
              </div>
            ) : (
              <div className="w-fit relative">
                <p
                  className={`status-${status
                    .replace(" ", "")
                    .toLowerCase()} text-sm p-2 px-4 rounded-20px whitespace-nowrap leading-[1.3]`}
                >
                  {status}
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default StatusCard;
