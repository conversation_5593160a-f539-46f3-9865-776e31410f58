import React, { useState } from "react";
import { useDispatch } from "react-redux";
import Button from "../../components/common/Button";
import InputFields from "../../components/forms/InputFields";
import { useUpdatePasswordMutation } from "../../services/api";
import { addNotification } from "../../store/reducers/toasterReducer";
import { CloseHandlerProps } from "../../typings/typing";
import { regEx } from "../../utils/helper";

interface FormData {
  oldPassword: string;
  password: string;
  cnfPassword: string;
}

interface ValidationResponse {
  isValid: boolean;
  errors: Partial<FormData>;
}

// Function to validate the password update form
const validatePassword = (data: FormData): ValidationResponse => {
  const errors: Partial<FormData> = {};
  let isValid = true;

  if (!data.oldPassword) {
    errors.oldPassword = "Old password is required";
    isValid = false;
  } else if (!regEx.PASSWORD.test(data.oldPassword)) {
    errors.oldPassword =
      "Password must be between 8 to 24 characters, should contain at least one lowercase letter, one uppercase letter, one digit, and one special character";
    isValid = false;
  }
  if (!data.password) {
    errors.password = "Password is required";
    isValid = false;
  } else if (!regEx.PASSWORD.test(data.password)) {
    errors.password =
      "Password must be between 8 to 24 characters, should contain at least one lowercase letter, one uppercase letter, one digit, and one special character";
    isValid = false;
  } else if (data.oldPassword === data.password) {
    errors.password = "New Password must be different from old Password";
    isValid = false;
  }
  if (!data.cnfPassword) {
    errors.cnfPassword = "Confirm password is required";
    isValid = false;
  } else if (data.password !== data.cnfPassword) {
    errors.cnfPassword = "Password doesn't match";
    isValid = false;
  } else if (data.oldPassword === data.cnfPassword) {
    errors.cnfPassword = "New Password must be different from old Password";
    isValid = false;
  }

  return { isValid, errors };
};
const UpdatePassword: React.FC<CloseHandlerProps> = ({
  handleOPenForgotPasswordPopup,
}) => {
  const [formData, setFormData] = useState<FormData>({
    oldPassword: "",
    password: "",
    cnfPassword: "",
  });
  const [formError, setFormError] = useState<Partial<FormData>>({});
  const [focusedField, setFocusedField] = useState<string | null>(null); // Track focused input
  const [updatePassword, updatePasswordLoading] = useUpdatePasswordMutation();
  const dispatch = useDispatch();

  // Define criteria for password validation
  const validationCriteria = {
    length: (password: string) => password.length >= 8 && password.length <= 24,
    hasUppercase: (password: string) => /[A-Z]/.test(password),
    hasLowercase: (password: string) => /[a-z]/.test(password),
    hasSpecialCharacter: (password: string) =>
      /[!@#$%^&*(),.?":{}|<>]/.test(password),
    hasNumber: (password: string) => /\d/.test(password),
  };

  // Validate password field based on defined criteria
  const validatePasswordField = (password: string) =>
    Object.entries(validationCriteria).map(([key, check]) => ({
      key,
      valid: check(password),
    }));

  // Validation results for each password field
  const oldPasswordValidation = validatePasswordField(formData.oldPassword);
  const newPasswordValidation = validatePasswordField(formData.password);
  const confirmPasswordValidation = validatePasswordField(formData.cnfPassword);

  // Check if the password meets all criteria
  const isOldPasswordValid = oldPasswordValidation.every(
    (criteria) => criteria.valid
  );
  const isPasswordValid = newPasswordValidation.every(
    (criteria) => criteria.valid
  );
  const isConfirmPasswordValid = confirmPasswordValidation.every(
    (criteria) => criteria.valid
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((state) => ({ ...state, [name]: value }));
    setFormError((state) => ({ ...state, [name]: "" }));
  };

  // Handle focus on input fields
  const handleFocus = (name: string) => {
    setFocusedField(name);
  };

  // Handle blur event to reset focused field
  const handleBlur = () => {
    setFocusedField(null);
  };

  // Handle the update password action
  const handleUpdatePassword = async () => {
    try {
      const data = {
        old_password: formData?.oldPassword,
        password: formData?.password,
        confirm_password: formData?.cnfPassword,
      };
      const response = await updatePassword(data).unwrap();
      if (response.status === 200) {
        handleOPenForgotPasswordPopup();
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
      }
    } catch (error: any) {
      if (error?.data?.error?.length > 0) {
        dispatch(
          addNotification({
            type: "error",
            message: error?.data?.error?.[0]?.message,
          })
        );
      } else {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { isValid, errors } = validatePassword(formData);
    setFormError(errors);

    if (isValid && !updatePasswordLoading?.isLoading) {
      setFormError({});
      handleUpdatePassword();
    }
  };

  // Render validation criteria list based on the focused field
  const renderValidationList = (
    validation: { key: string; valid: boolean }[]
  ) => (
    <ul className="password-regex mt-3">
      {validation.map(({ key, valid }) => (
        <li key={key} className={valid ? "valid" : "invalid"}>
          {key === "length" && "Password must be 8 to 24 characters long."}
          {key === "hasUppercase" &&
            "Must include at least one uppercase letter."}
          {key === "hasLowercase" &&
            "Must include at least one lowercase letter."}
          {key === "hasSpecialCharacter" &&
            "Must include at least one special character."}
          {key === "hasNumber" && "Must include at least one number."}
        </li>
      ))}
    </ul>
  );

  return (
    <div>
      <form className="" onSubmit={handleSubmit}>
        <div>
          <p className="sub-text">
            Your new password must be different from previous used passwords.
          </p>
        </div>
        <div className="flex xl:gap-5 gap-4 flex-col lg:pt-10 pt-30">
          <InputFields
            placeHolder="Old Password"
            changeEvent={handleChange}
            isErrorMsg={formError.oldPassword}
            type="password"
            valid={!isOldPasswordValid && formData.oldPassword ? true : false}
            attributes={{
              name: "oldPassword",
              onFocus: () => handleFocus("oldPassword"),
              onBlur: handleBlur,
            }}
            className="!bg-[#F5EFFF]"
          />
          <InputFields
            placeHolder="New Password"
            changeEvent={handleChange}
            isErrorMsg={formError.password}
            type="password"
            valid={!isPasswordValid && formData.password ? true : false}
            attributes={{
              name: "password",
              onFocus: () => handleFocus("password"),
              onBlur: handleBlur,
            }}
            className="!bg-[#F5EFFF]"
          />
          <InputFields
            placeHolder="Confirm New Password"
            changeEvent={handleChange}
            isErrorMsg={formError.cnfPassword}
            type="password"
            valid={
              !isConfirmPasswordValid && formData.cnfPassword ? true : false
            }
            attributes={{
              name: "cnfPassword",
              onFocus: () => handleFocus("cnfPassword"),
              onBlur: handleBlur,
            }}
            className="!bg-[#F5EFFF]"
          />
          <div className="mt-3">
            <p className="font-medium">Password must be:</p>
            {focusedField === "oldPassword" ? (
              renderValidationList(oldPasswordValidation)
            ) : focusedField === "password" ? (
              renderValidationList(newPasswordValidation)
            ) : focusedField === "cnfPassword" ? (
              renderValidationList(confirmPasswordValidation)
            ) : (
              <ul className="password-regex mt-3">
                <li>Password must be 8 to 24 characters long.</li>
                <li>Must include at least one uppercase letter.</li>
                <li>Must include at least one lowercase letter.</li>
                <li>Must include at least one special character.</li>
                <li>Must include at least one number.</li>
              </ul>
            )}
          </div>

          <div className="flex items-center lg:gap-5 gap-2.5 max-md:flex-col">
            <Button
              title="Go back"
              type="button"
              clickEvent={handleOPenForgotPasswordPopup}
              btnType="transparent"
            />
            <Button
              title="Update password"
              isLoading={updatePasswordLoading?.isLoading}
              className="shadow-[0px_4px_7px_0px_rgba(0,0,0,0.12)]"
            />
          </div>
        </div>
      </form>
    </div>
  );
};

export default UpdatePassword;
