import { FC, useState } from "react";
import Calendar from "react-calendar";
import { NextLabel, PrevLabel } from "../../assets/Icons";
import "../../assets/scss/CustomCalendar.scss";
import { CustomCalendarProps, PrevDayTileState } from "../../typings/typing";
import { holidayList } from "../../Data/HolidayList";
import { useDispatch } from "react-redux";
import { addNotification } from "../../store/reducers/toasterReducer";

type ValuePiece = Date | null;

type Value = ValuePiece | [ValuePiece, ValuePiece];

const CustomCalendar: FC<CustomCalendarProps> = ({
  timeRangeInDays = Number.MAX_VALUE,
  coolingTime = 0,
  selectedDate,
  setDate,
  maxDate,
  minDate,
  isLoading,
  disableWeekEnds,
  disableHolidays,
  disableNext3WorkingDays,
}) => {
  const dispatch = useDispatch();

  // Initialize the selected date if provided
  const initialSelectedDate = selectedDate
    ? `value-${selectedDate?.getDate()}-${
        selectedDate.getMonth() + 1
      }-${selectedDate.getFullYear()}`
    : "";
  const [value, setValue] = useState<Value>(new Date());
  const [selectedDay, setSelectedDay] = useState<string>(initialSelectedDate);
  const initialPrevDayTileState = {
    className: "",
    suffix: "",
  };
  const [prevDayTileState, setPrevDayTileState] = useState<PrevDayTileState>(
    initialPrevDayTileState
  );
  const [selectedElement, setSelectedElement] = useState<HTMLElement>();

  // funnction for checking is selected date is in holiday array or not
  const isHoliday = (inputDate: Date, holidaysArray: any): any => {
    const formattedInputDate = inputDate?.toLocaleDateString("en-GB"); // Format date in DD/MM/YYYY
    const foundHoliday = holidaysArray?.find(
      (holiday: any) => holiday?.Date === formattedInputDate
    );

    return foundHoliday || false; // Return the found holiday object or false
  };

  // Handler for clicking on a day
  const handleOnClickDay = (value: Date): void => {
    // restrict click for weekends
    if (disableWeekEnds) {
      if (value?.getDay() === 0 || value?.getDay() === 6) {
        return;
      }
    }

    // restrict click for holiday
    if (disableHolidays && isHoliday(value, holidayList)) {
      dispatch(
        addNotification({
          type: "error",
          message: isHoliday(value, holidayList)?.Type
            ? `We'll be closed on ${
                isHoliday(value, holidayList)?.Type
              }, Happy Holidays!`
            : "Can't select this date",
        })
      );
      return;
    }

    if (disableNext3WorkingDays) {
      let today = new Date(); // Change this date for different test cases

      //  ---- first working day ----
      let firstWorkingDay = new Date(today);
      firstWorkingDay.setDate(today.getDate() + 1);
      firstWorkingDay.setHours(0, 0, 0, 0);

      for (let i = 0; i < 7; i++) {
        // add one day if first working day is saturday or sunday or holiday
        if (
          firstWorkingDay.getDay() === 6 ||
          firstWorkingDay.getDay() === 0 ||
          holidayList
            ?.map((holiday) => holiday?.Date)
            .includes(firstWorkingDay?.toLocaleDateString("en-GB"))
        ) {
          firstWorkingDay = new Date(
            new Date(firstWorkingDay).setDate(firstWorkingDay.getDate() + 1)
          );
        } else {
          break;
        }
      }

      //  ---- second working day ----
      let secondWorkingDay = new Date(firstWorkingDay);
      secondWorkingDay.setDate(firstWorkingDay.getDate() + 1);
      secondWorkingDay.setHours(0, 0, 0, 0);

      for (let i = 0; i < 7; i++) {
        // add one day if second working day is saturday or sunday or holiday
        if (
          secondWorkingDay.getDay() === 6 ||
          secondWorkingDay.getDay() === 0 ||
          holidayList
            ?.map((holiday) => holiday?.Date)
            .includes(secondWorkingDay?.toLocaleDateString("en-GB"))
        ) {
          secondWorkingDay = new Date(
            new Date(secondWorkingDay).setDate(secondWorkingDay.getDate() + 1)
          );
        } else {
          break;
        }
      }

      //  ---- third working day ----
      let thirdWorkingDay = new Date(secondWorkingDay);
      thirdWorkingDay.setDate(secondWorkingDay.getDate() + 1);
      thirdWorkingDay.setHours(0, 0, 0, 0);
      // add one day if third working day is saturday or sunday or holiday
      for (let i = 0; i < 7; i++) {
        if (
          thirdWorkingDay.getDay() === 6 ||
          thirdWorkingDay.getDay() === 0 ||
          holidayList
            ?.map((holiday) => holiday?.Date)
            .includes(thirdWorkingDay?.toLocaleDateString("en-GB"))
        ) {
          thirdWorkingDay = new Date(
            new Date(thirdWorkingDay).setDate(thirdWorkingDay.getDate() + 1)
          );
        } else {
          break;
        }
      }

      const selectedDate = value?.toLocaleDateString("en-GB");

      if (
        selectedDate === firstWorkingDay?.toLocaleDateString("en-GB") ||
        selectedDate === secondWorkingDay?.toLocaleDateString("en-GB") ||
        selectedDate === thirdWorkingDay?.toLocaleDateString("en-GB")
      ) {
        dispatch(
          addNotification({
            type: "error",
            message:
              "You can't select date within the next three business days.",
          })
        );
        return;
      }
    }

    const currentClassName = `value-${value?.getDate()}-${
      value.getMonth() + 1
    }-${value.getFullYear()}`;
    const currentElement = document.getElementsByClassName(
      currentClassName
    ) as HTMLCollectionOf<HTMLElement>;
    const today = new Date();
    const timeRange = 1000 * 60 * 60 * 24 * timeRangeInDays;
    const coolingTimeRange = 1000 * 60 * 60 * 24 * coolingTime;

    // Check if the selected date falls within the allowed range
    if (
      value.getTime() <= today.getTime() + timeRange &&
      value.getTime() >= today.getTime() + coolingTimeRange &&
      (value.getFullYear() > today.getFullYear() ||
        (value.getFullYear() === today.getFullYear() &&
          (value.getMonth() > today.getMonth() ||
            (value.getMonth() === today.getMonth() &&
              value.getDate() >= today.getDate()))))
    ) {
      if (selectedDay.length) {
        // Handle the previously selected day
        const prevObj = selectedElement || { className: "" };
        const prevClassName = prevObj.className.split(" ");
        prevClassName?.splice(-1, 1, prevDayTileState.suffix);
        prevObj.className = prevClassName?.join(" ") || "";
        setPrevDayTileState({
          className: currentElement[0].className,
          suffix: currentElement[0].className.split(" ").slice(-1)[0],
        });
        const classNameArr = currentElement[0].className.split(" ");
        classNameArr.splice(-1, 1, "selected");
        currentElement[0].className = classNameArr.join(" ");
      } else {
        setPrevDayTileState({
          className: currentClassName,
          suffix: currentElement[0].className.split(" ")[-1],
        });
        const classNameArr = currentElement[0].className.split(" ");
        classNameArr.splice(-1, 1, "selected");
        currentElement[0].className = classNameArr.join(" ");
      }

      // Update state with the new selected day
      setSelectedDay(currentClassName);
      setSelectedElement(currentElement[0]);
      setDate(value);
    }
  };

  // Function to determine the class name for each tile (day) on the calendar
  const tileClassName = ({
    date,
    view,
  }: {
    date: Date;
    view: string;
  }): string => {
    const today = new Date();
    const timeRange = timeRangeInDays
      ? 1000 * 60 * 60 * 24 * timeRangeInDays
      : Number.MAX_VALUE;
    const coolingRange = 1000 * 60 * 60 * 24 * coolingTime;
    const day = date.getDate(),
      month = date.getMonth() + 1,
      year = date.getFullYear();

    // Check if minDate and maxDate are available and if the date is within range
    let isWithinRange = false;
    if (minDate && maxDate) {
      isWithinRange = date >= new Date(minDate) && date <= new Date(maxDate);
    }

    // if flag is true then restrict weekends (css)
    if (disableWeekEnds) {
      if (date.getDay() === 0 || date.getDay() === 6) {
        return `day value-${day}-${month}-${year} other`;
      }
    }

    if (selectedDay === `value-${day}-${month}-${year}`) {
      return `day value-${day}-${month}-${year} selected`;
    }
    if (maxDate && minDate) {
      if (
        date.getMonth() === today.getMonth() &&
        today.getDate() === date.getDate() &&
        date.getFullYear() === today.getFullYear()
      ) {
        return `day value-${day}-${month}-${year} today`;
      } else if (
        date.getTime() >= today.getTime() + coolingRange &&
        date.getTime() <= today.getTime() + timeRange &&
        isWithinRange
      ) {
        return `day value-${day}-${month}-${year} range`;
      } else if (date.getMonth() === today.getMonth()) {
        return `day value-${day}-${month}-${year} current`;
      } else {
        return `day value-${day}-${month}-${year} other`;
      }
    } else {
      if (view === "month") {
        if (
          date.getMonth() === today.getMonth() &&
          today.getDate() === date.getDate() &&
          date.getFullYear() === today.getFullYear()
        ) {
          return `day value-${day}-${month}-${year} today`;
        } else if (
          date.getTime() >= today.getTime() + coolingRange &&
          date.getTime() <= today.getTime() + timeRange
        ) {
          return `day value-${day}-${month}-${year} range`;
        } else if (date.getMonth() === today.getMonth()) {
          return `day value-${day}-${month}-${year} current`;
        } else {
          return `day value-${day}-${month}-${year} other`;
        }
      }
    }
    return "";
  };

  return (
    <Calendar
      value={value}
      onChange={(value: Value) => {
        !isLoading && setValue(value);
      }}
      tileClassName={tileClassName}
      nextLabel={<NextLabel />}
      prevLabel={<PrevLabel />}
      onClickDay={(value: Date) => handleOnClickDay(value)}
      prev2Label={null}
      next2Label={null}
      minDetail={"month"}
      maxDetail={"month"}
      calendarType="gregory"
      minDate={new Date(minDate)}
      maxDate={new Date(maxDate)}
    />
  );
};

export default CustomCalendar;
