import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface Notification {
  message: string;
  type: string;
}

interface ToasterState {
  notifications: Notification[];
}

const initialState: ToasterState = {
  notifications: [],
};

const toasterReducer = createSlice({
  name: "toaster",
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.push(action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    // clearNotificationById: (state, action: PayloadAction<string>) => {
    //   state.notifications = state.notifications.filter(
    //     (notification) => notification.id !== action.payload
    //   );
    // },
  },
});

export const { addNotification, clearNotifications } = toasterReducer.actions;

export default toasterReducer.reducer;
