// /** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,js,jsx,tsx,ts}"],
  theme: {
    extend: {
      animation: {
        "peace-wiggle": "peace-wiggle 0.6s ease-in-out",
      },
      keyframes: {
        "peace-wiggle": {
          "0%, 100%": { transform: "rotate(0deg)" },
          "25%": { transform: "rotate(8deg)" },
          "50%": { transform: "rotate(-8deg)" },
          "75%": { transform: "rotate(6deg)" },
        },
      },
      screens: {
        xs: "375px",
        sm: "480px",
        md: "768px",
        lg: "1025px",
        xl: "1300px",
        "2xl": "1400px",
      },
      fontFamily: {
        anton: ["Anton", "sans-serif"],
        roboto: ['Roboto', 'sans-serif'],
    },
      spacing: {
        30: "30px",
        15: "15px",
      },
      colors: {
        black: "#000000",
        white: "#ffffff",
        "light-grey": "#f3f3f3",
        primary: "#2c212c",
        secondary: "#7f1b7f",
        warning: "#FF8C22",
        "warning-light": "#FFECDB",
        success: "#24cc20",
        "success-light": "#dcf9dc",
        error: "#cc2b20",
        error_1: "#FF2226",
        "error-light": "#ffdfdd",
        popupBg: "rgba(0, 0, 0, 0.6)",
        color_A9A9A9: "#A9A9A9",
        color_F3F3F3: "#F3F3F3",
        color_FFCE22: "#FFCE22",
        color_F7F7F7: "#F7F7F7",
        color_purple: "#D958DA",
        medium_purple: "#AA7DE6",
        dark_purple: "#7421B5",
      },
      backgroundImage: {
        "primary-gradient": "linear-gradient(90deg, #7f1b7f 0%, #f54bf7 100%)",
        "light-gradient": "linear-gradient(90deg, #FBEBFB 0%, #FEF4FE 100%)",
        "white-gradient":
          "linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%)",
        "light-purple-gradient":
          "linear-gradient(90deg, rgba(217, 88, 218, 0.2) 0%, rgba(147, 145, 255, 0.2) 100%)",
      },
      boxShadow: {
        custom:
          "0 4px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        cardShadow05: "0px 0px 100px 0px rgba(0, 0, 0, 0.05)",
        purpleShadow: "0px 30px 50px 0px rgba(217, 88, 218, 0.3)",
        purpleShadow03: "0px 20px 30px 0px rgba(217, 88, 218, 0.3)",
        purpleShadow2: "0px 10px 40px 0px rgba(217, 88, 218, 0.3)",
        purpleShadow4: "0px 10px 20px 0px rgba(217, 88, 218, 0.2)",
        cardShadow07: "0px 0px 100px 0px rgba(0, 0, 0, 0.07)",
        sidebarShadow: "30px 4px 100px -30px rgba(0, 0, 0, 0.05)",
        cardShadow09: "0px 0px 100px 0px rgba(0, 0, 0, 0.09)",
        cardShadow25: "0px 4px 4px 0px rgba(0, 0, 0, 0.25)",
        cardShadow01: "0px 0px 100px 0px rgba(0, 0, 0, 0.1)",
        cardShadow25_01: "0px 4px 16px 0px rgba(0, 0, 0, 0.25)",
        cardShadow25_02: "0px 10px 16px 0px rgba(0, 0, 0, 0.25)",
        balanceCard: "0px 0px 16px 0px rgba(0, 0, 0, 0.25)",
        purpleGlow: "0px 4px 23.4px 6px rgba(170, 125, 230, 0.41)",
        buttonShadow: "0px 4px 7px 0px rgba(0, 0, 0, 0.12)",
      },
      borderRadius: {
        10: "10px",
        20: "20px",
        30: "30px",
      },
      fontSize: {
        26: "1.625rem",
      },
      lineHeight: {
        "1_35": "1.35rem",
      },
    },
  },
  plugins: [],
};
