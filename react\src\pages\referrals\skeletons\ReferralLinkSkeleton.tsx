import React from "react";
import ContentLoader from "react-content-loader";

const ReferralLinkSkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      width={"100%"}
      height={"312px"}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {/* //left bar */}
      <rect x="8" y="8" rx="5" ry="5" width="12" height="259" />
      {/* bottom bar*/}
      <rect
        x="8"
        y="259px"
        rx="5"
        ry="5"
        width="99%"
        height="12"
      />
      {/* top bar*/}
      <rect x="8" y="8" rx="5" ry="5" width="99%" height="12" />
      {/* right bar*/}
      <rect x="98.80%" y="9" rx="5" ry="5" width="12" height="259" />

      {/* Referral Link text */}
      {/* left text */}
      <rect x="46" y="46" rx="5" ry="5" width="40%" height="36" />
      {/* left text */}
      <rect x="46" y="102" rx="5" ry="5" width="40%" height="80" />
      {/* left text */}
      <rect x="46" y="201" rx="5" ry="5" width="40%" height="36" />

      {/* right text */}
      <rect x="52%" y="56" rx="5" ry="5" width="45%" height="56" />
      {/* right text */}
      <rect x="52%" y="138" rx="5" ry="5" width="45%" height="79" />
    </ContentLoader>
  );
};

export default ReferralLinkSkeleton;
