import { rootState } from "../reducers/rootReducer";

export const currentAddress = (store: rootState) =>
  store.serviceAddressReducer.currentAddress;
export const newAddress = (store: rootState) =>
  store.serviceAddressReducer.newAddress;
export const moveDate = (store: rootState) =>
  store.serviceAddressReducer.moveDate;
export const serviceStatus = (store: rootState) =>
  store.serviceAddressReducer.serviceStatus;
export const showServiceAddressPopup = (store: rootState) =>
  store.serviceAddressReducer.showServiceAddressPopup;
export const showServiceAddressDateSelectorPopup = (store: rootState) =>
  store.serviceAddressReducer.showServiceAddressDateSelectorPopup;
export const showServiceAddressConfirmationPopup = (store: rootState) =>
  store.serviceAddressReducer.showServiceAddressConfirmationPopup;
export const showDeleteOrderPopup = (store:rootState) => store.serviceAddressReducer.showDeleteOrderPopup;
export const showFibreAddressPopup = (store: rootState) =>
  store.serviceAddressReducer.showFibreAddressPopup;
