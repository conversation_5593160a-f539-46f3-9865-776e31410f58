import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Navigate, Route, Routes, useLocation } from "react-router-dom";
import { Dashboard } from "../../pages/dashboard/Dashboard";
import ManagePlan from "../../pages/manage-plan/ManagePlan";
import Referrals from "../../pages/referrals/Referrals";
import { resetTVPopups } from "../../store/reducers/televisionReducer";
import { AppHeader } from "./AppHeader";
import SideBarMenu from "./SideBarMenu";
import { resetInternetPopups } from "../../store/reducers/internetReducer";
import { resetPhonePopups } from "../../store/reducers/homePhoneReducer";
import { resetMailPopups } from "../../store/reducers/mailingAddressReducer";
import { resetRenewalPopups } from "../../store/reducers/nextPaymentDateReducer";
import { resetServicePopups } from "../../store/reducers/serviceAddressReducer";

// Array of header names corresponding to different paths
const HeaderName = [
  { path: "", name: "Dashboard" },
  { path: "referrals", name: "Referrals " },
  { path: "manage-plan", name: "Manage" },
];

const Layout = () => {
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [headerTitle, setHeaderTitle] = useState<string>("");
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const extractedPath = pathname.split("/")[1];

  //  useEffect to update the header title and close the menu when the path changes
  useEffect(() => {
    const result = HeaderName.find((ele) => ele.path === extractedPath);
    setHeaderTitle(result?.name || "");
    setIsMenuOpen(false);
    dispatch(resetTVPopups());
    dispatch(resetInternetPopups());
    dispatch(resetPhonePopups());
    dispatch(resetMailPopups());
    dispatch(resetRenewalPopups());
    dispatch(resetServicePopups());
  }, [pathname]);

  // Function to handle the opening and closing of the sidebar menu
  const handleMenuOpen = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // close the sidebar drawer if disaply's width gets greater then 1024px
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 1024) {
        setIsMenuOpen(false);
      }
    };
    window.addEventListener("resize", handleResize);
    handleResize();
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return (
    <div className="relative">
      <div className="flex max-h-screen">
        <div className="sidebarMenu-wrapper bg-white">
          <SideBarMenu isOpen={isMenuOpen} menuHandler={handleMenuOpen} />
        </div>
        <div
          id="pageBody"
          className="h-full overflow-auto w-full min-h-screen max-h-screen bg-[#f5efff]"
        >
          <AppHeader title={headerTitle} menuHandler={handleMenuOpen} />

          <div className={`px-5 max-lg:px-4 2xl:px-6`}>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/home" element={<Dashboard />} />
              <Route path="/referrals" element={<Referrals />} />
              <Route path="/manage-plan/:id" element={<ManagePlan />} />
              {/* Redirect user to dashboard for none of above url visit */}
              <Route path="/*" element={<Navigate to={"/"} />} />
            </Routes>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout;
