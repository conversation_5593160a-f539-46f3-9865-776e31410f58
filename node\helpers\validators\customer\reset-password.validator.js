const Joi = require('joi');

const customerResetPasswordValidation = Joi.object().keys({
    old_password: Joi.string()
        .min(4)
        .max(30)
        .required()
        .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]).{3,30}$'))
        .messages({
            'any.required': 'Old password field is required.',
            'string.min': 'Old password must be at least {#limit} characters long.',
            'string.max': 'Old password cannot exceed {#limit} characters.',
            'string.pattern.base': 'Old password must contain at least one lowercase letter, one uppercase letter, one number, and one special character.'
        }),
    password: Joi.string()
        .min(4)
        .max(30)
        .required()
        .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]).{3,30}$'))
        .messages({
            'any.required': 'Password field is required.',
            'string.min': 'Password must be at least {#limit} characters long.',
            'string.max': 'Password cannot exceed {#limit} characters.',
            'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character.'
        }),
    confirm_password: Joi.string()
        .valid(Joi.ref('password')) // Ensure confirm_password matches password
        .required()
        .messages({
            'any.required': 'Confirm password field is required.',
            'any.only': 'Confirm password must match the password.'
        })
});

module.exports = { customerResetPasswordValidation };
