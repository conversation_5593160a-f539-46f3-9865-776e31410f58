const Validator = require('../helpers/validators')
const { validator } = require("../middleware/validationMid");
const { resetPassword, getCustomerDetails, logout, updateCustomerDetails } = require("../controllers/customer-controller");
const router = require("express").Router();
const { upload } = require("./../helpers/privacyAlgorithms")

module.exports = (app, basePath) => {
	// Register Route for getting customer details
	router.get("/details", getCustomerDetails);
	// Register Route for resetting customer password
	router.post("/reset-password", validator(Validator.customerResetPasswordValidation), resetPassword);
	// Register Route for updating customer details with image upload
	router.put("/", upload.single('image_url'), validator(Validator.customerUpdateValidation), updateCustomerDetails);
	// Register Route for customer logout
	router.get("/logout", logout);

	// Use the base path passed from index.js
	app.use(`${basePath}/customer`, router); // Mount the router at the base path
};
