import { Provider } from "react-redux";
import Routing from "./components/common/Routing.tsx";
import { store } from "./store/store.tsx";
import Toast from "./components/common/Toast.tsx";
import MaintenanceMode from "./pages/account/MaintenanceMode.tsx";

function App() {
  const maintenanceMode = import.meta.env.VITE_MAINTENANCE_MODE === "true";

  return (
    <div className="App">
      {maintenanceMode ? (
        <MaintenanceMode />
      ) : (
        <Provider store={store}>
          <span className="popup-backdrop fixed w-full h-full left-0 top-0 bg-popupBg z-[100]"></span>
          <Toast />
          <Routing />
        </Provider>
      )}
    </div>
  );
}

export default App;
