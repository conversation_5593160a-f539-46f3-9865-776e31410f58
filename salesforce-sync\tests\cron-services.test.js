const moment = require('moment');
const CronService = require('../services/cron-services');
const pool = require('../db');

// Mock the database pool
jest.mock('../db');

describe('CronService', () => {
  let cronService;

  beforeEach(() => {
    // Clear all mock implementations before each test
    jest.clearAllMocks();
    cronService = new CronService();
  });

  // Helper function to setup basic mock implementation
  const setupBasicMock = (results) => {
    pool.query.mockImplementation((query, values, callback) => {
      if (query.startsWith('SELECT')) {
        callback(null, results);
      } else {
        callback(null, { affectedRows: results.length });
      }
    });
  };

  describe('updateSpeedchange', () => {
    it('should update and delete speed change records when they exist', async () => {
      const currentDate = moment().format('YYYY-MM-DD');
      const mockResults = [
        { id: 1 },
        { id: 2 }
      ];

      // Mock the database query responses
      pool.query.mockImplementation((query, values, callback) => {
        if (query.startsWith('SELECT')) {
          callback(null, mockResults);
        } else {
          callback(null, { affectedRows: mockResults.length });
        }
      });

      const result = await cronService.updateSpeedchange();

      // Verify the function returns success
      expect(result).toEqual({ status: true });

      // Verify all queries were executed with correct parameters
      expect(pool.query).toHaveBeenCalledTimes(3);
      
      // Check SELECT query
      expect(pool.query.mock.calls[0][0]).toContain('SELECT id FROM internets_eastlink_speed_change_order');
      expect(pool.query.mock.calls[0][1]).toEqual([currentDate]);

      // Check UPDATE query
      expect(pool.query.mock.calls[1][0]).toContain('UPDATE customer_details_internets_eastlink');
      expect(pool.query.mock.calls[1][1]).toEqual([[1, 2]]);

      // Check DELETE query
      expect(pool.query.mock.calls[2][0]).toContain('DELETE FROM internets_eastlink_speed_change_order');
      expect(pool.query.mock.calls[2][1]).toEqual([[1, 2]]);
    });

    it('should return success message when no records to update', async () => {
      // Mock empty results
      pool.query.mockImplementation((query, values, callback) => {
        callback(null, []);
      });

      const result = await cronService.updateSpeedchange();

      // Verify the function returns success with no records message
      expect(result).toEqual({
        status: true,
        message: 'No records to update or delete.'
      });

      // Verify only SELECT query was executed
      expect(pool.query).toHaveBeenCalledTimes(1);
    });

    it('should handle database errors', async () => {
      const mockError = new Error('Database error');

      // Mock database error
      pool.query.mockImplementation((query, values, callback) => {
        callback(mockError);
      });

      // Verify the function throws error
      await expect(cronService.updateSpeedchange()).rejects.toThrow('Database error');
      
      // Verify query was attempted
      expect(pool.query).toHaveBeenCalledTimes(1);
    });
  });
});
