const Joi = require('joi');

const mobileNumberRegex = /^[0-9]{10,15}$/;

const phoneUpdateValidation = Joi.object({
    customer_details_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer details ID is required.',
            'number.base': 'Customer details ID must be a number.',
            'number.integer': 'Customer details ID must be an integer.'
        }),
    customer_subscription_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer subscription ID is required.',
            'number.base': 'Customer subscription ID must be a number.',
            'number.integer': 'Customer subscription ID must be an integer.'
        }),
    phone_number_type: Joi.string()
        .valid('new', 'existing')
        .required()
        .messages({
            'any.required': 'Phone number type is required.',
            'any.only': 'Phone number type must be either "new" or "existing".'
        }),
    phone_number: Joi.alternatives().conditional('phone_number_type', {
        is: 'existing',
        then: Joi.string()
            .trim()
            .required()
            .pattern(mobileNumberRegex)
            .messages({
                'any.required': 'Phone number is required when phone number type is `existing`.',
                'string.pattern.base': 'Phone number must be a valid mobile number consisting of 10 to 15 digits.'
            }),
        otherwise: Joi.string()
            .allow(null)
            .messages({
                'string.empty': 'Phone number can be null when phone number type is "new".'
            })
    }),
    phone_plan_id: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Phone plan ID is required.'
        })
});

module.exports = { phoneUpdateValidation };
