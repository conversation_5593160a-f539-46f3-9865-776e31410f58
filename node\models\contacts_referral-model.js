const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("contacts_referral", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    contact_id: {
      type: Sequelize.INTEGER(11),
      allowNull: false,
      references: {
        model: 'contacts',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'contacts' table, with cascading delete functionality.",
      onDelete: 'CASCADE'
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    referral_name: {
      type: Sequelize.STRING(150),
      comment: "This maps to 'Referrals_Name__c' field in Salesforce."
    },
    referred_amount: {
      type: Sequelize.FLOAT(16, 2),
      comment: "This maps to 'Credit_Amount__c' field in Salesforce."
    },
    installed_on: {
      type: Sequelize.DATEONLY,
      comment: "This maps to 'Referrals_Live_Date__c' field in Salesforce."
    },
    credit_added_on: {
      type: Sequelize.DATEONLY,
      comment: "This maps to 'Credit_Added_to_Account__c' field in Salesforce."
    },
    type: {
      type: Sequelize.ENUM('credit', 'debit'),
      defaultValue: 'credit',
      comment: "By defult the value is credit."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the 'LastModifiedDate' in Salesforce."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      comment: "Salesforce record ID"
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      indexes: [
        {
          name: "CustomerReferredUniqueIndex",
          unique: true,
          fields: ['sf_record_id', 'contact_id']
        }
      ]
    });
}