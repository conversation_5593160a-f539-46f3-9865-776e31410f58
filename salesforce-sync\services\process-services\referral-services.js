const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../customer-sync-services");
const ReferralsServices = require("../referral-services");
const referralsServices = new ReferralsServices();
const customerServices = new CustomerServices();
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);
const INTERVAL = CONFIG.interval;

class ReferralServices {
    async getReferralDetails(contactIds) {
        try {

            let query = `SELECT Id, Name, Contact__c, Referral__c, Referrals_Name__c, Referrals_Live_Date__c,Credit_Added_to_Account__c, Credit_Amount__c, CreatedDate, LastModifiedDate FROM Referral__c WHERE Contact__c IN (${contactIds})`;

            if (INTERVAL === "regular") query += " AND LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const referralData = await salesforceConnection.fetchAllRecords(query);
            if (referralData?.length) await referralsServices.checkAndManageDetails(referralData);

            const { deleteStatus, deletedData } = await salesforceConnection.getDeletedData("Referral__c");

            if (deleteStatus && Array.isArray(deletedData) && deletedData.length) {
                await this.checkAndDeleteDetails(deletedData);
            }

            return { execute: true, status: true, referralDataCount: referralData?.length, referralDeletedCount: deletedData?.length };
        } catch (error) {
            console.error("Sync service get bulk Referral Details -> ", error);
            return { execute: true, status: false, error: error?.message || null };
        }
    }

    async checkAndDeleteDetails(deletedData) {
        try {
            const ids = deletedData.map(record => record.id);
            const placeholders = ids.map(() => '?').join(', ');
            const deleteQuery = `DELETE FROM contacts_referrals WHERE sf_record_id IN (${placeholders})`;
            await customerServices.executeQuery(deleteQuery, ids);
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
        }
    }
}

module.exports = ReferralServices;