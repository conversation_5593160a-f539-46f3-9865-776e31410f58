import React, { ClipboardEvent, useRef } from "react";
import { InvalidIcon } from "../../assets/Icons";

interface Props {
  setVerificationCode: React.Dispatch<React.SetStateAction<string[]>>;
  verificationCode: string[];
  isErrorMsg: string;
}

const VerificationCodeInput: React.FC<Props> = ({
  setVerificationCode,
  verificationCode,
  isErrorMsg = null,
}) => {
  const inputsRef = useRef<HTMLInputElement[]>([]);

  // Function to handle input change
  const handleInputChange = (index: number, value: string) => {
    if (value === "") {
      // If input is cleared, clear the corresponding value in the code array
      setVerificationCode((prevCode) => {
        const newCode = [...prevCode];
        newCode[index] = "";
        return newCode;
      });

      // Move focus to the previous input
      if (index > 0 && inputsRef.current[index - 1]) {
        inputsRef.current[index - 1]?.focus();
      }
    } else if (!isNaN(Number(value))) {
      //  If input is a number, update the corresponding value in the code array
      setVerificationCode((prevCode) => {
        const newCode = [...prevCode];
        newCode[index] = value;
        return newCode;
      });

      // Move focus to the next input
      if (index < 5 && inputsRef.current[index + 1]) {
        inputsRef.current[index + 1]?.focus();
      }
    }
  };

  // Function to handle paste event
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const clipboardData = e.clipboardData?.getData("text") || "";
    const codeArray = clipboardData
      .split("")
      .filter((char) => !isNaN(Number(char)) && !isNaN(parseInt(char, 10)));
    if (codeArray.length > 0) {
      setVerificationCode((prevCode) => {
        const newCode = [...prevCode];
        codeArray.forEach((char, index) => {
          if (index < 6) {
            newCode[index] = char;
          }
        });
        return newCode;
      });
    }
  };

  return (
    <div className={`verification-code `}>
      <label className="control-label">Confirmation Code</label>
      <div className={`verification-code--inputs ${isErrorMsg ? "error" : ""}`}>
        {verificationCode.map((code, index) => (
          <input
            key={index}
            type="text"
            maxLength={1}
            value={code}
            ref={(el) => {
              if (el) inputsRef.current[index] = el;
            }}
            onChange={(e) => handleInputChange(index, e.target.value)}
            onPaste={handlePaste}
          />
        ))}
      </div>
      {isErrorMsg != null && (
        <p className="error-text flex gap-2 items-center pt-1.5 text-sm">
          <span className="icon">
            <InvalidIcon />
          </span>
          {isErrorMsg}
        </p>
      )}
    </div>
  );
};

export default VerificationCodeInput;
