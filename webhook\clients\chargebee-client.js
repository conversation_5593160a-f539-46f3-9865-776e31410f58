// Import required modules and configurations
const CONFIG = require("../config");
const Chargebee = require("chargebee"); 
const moment = require('moment');

// Define the interval for fetching invoices (in days)
const INTERVAL = 1; // Fetch invoices updated in the last 1 day

// ChargebeeClient class to interact with Chargebee API
class ChargebeeClient {
  constructor() {
    // Initialize Chargebee configuration using values from CONFIG
    this.site = CONFIG.chargebee.site; // Chargebee site name
    this.api_key = CONFIG.chargebee.api_key; // Chargebee API key

    // Initialize Chargebee SDK with site and API key
    this.chargebee = new Chargebee({
      site: this.site,
      apiKey: this.api_key,
    });
  }

  async getInvoicesList(offset) {
    try {
      const now = moment(); // Current date and time
      const dateBefore = now.subtract(INTERVAL, 'days'); // Date INTERVAL days ago
      const unixTimestamp = dateBefore.unix(); // Convert to Unix timestamp
      const operator = 'after'; // Operator for filtering updated_at field

      // Payload for the Chargebee API request
      const payload = {
        [`updated_at[${operator}]`]: unixTimestamp,
        limit: 100, // Maximum number of results to fetch
        offset: offset, // Pagination offset
      };

      // Fetch the list of invoices
      const result = await this.chargebee.invoice.list(payload);

      return result;
    } catch (error) {
      console.error("Error fetching invoice list from Chargebee:", error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

  async retrieveInvoice(invoiceId) {
    try {
      // Fetch the invoice details
      const result = await this.chargebee.invoice.retrieve(invoiceId);

      return result;
    } catch (error) {
      console.error("Error retrieving invoice from Chargebee:", error);
      throw error;
    }
  }

  async getSubscriptions(subscriptionId) {
    try {
      const operator = 'is'; // Operator for filtering subscription ID
      const payload = {
        [`id[${operator}]`]: subscriptionId,
        limit: 1, // Fetch only one subscription
      };

      // Fetch the subscription details
      const result = await this.chargebee.subscription.list(payload);

      // Validate the response to ensure subscriptions are returned
      // if (!result?.list || result.list.length === 0) {
      //   throw new Error(`No subscriptions found for subscription ID: ${subscriptionId}`);
      // }

      return result?.list;
    } catch (error) {
      console.error("Error fetching subscriptions from Chargebee:", error);
      throw error;
    }
  }

  async getSubInvoicesList(subscriptionId, offset) {
    try {
      const operator = 'is'; // Operator for filtering subscription ID
      const payload = {
        [`subscription_id[${operator}]`]: subscriptionId,
        limit: 100, // Maximum number of results to fetch
        offset: offset, // Pagination offset
      };

      // Fetch the list of invoices for the subscription
      const result = await this.chargebee.invoice.list(payload);

      return result;
    } catch (error) {
      console.error("Error fetching subscription invoices from Chargebee:", error);
      throw error;
    }
  }
}

// Export the ChargebeeClient class for use in other modules
module.exports = ChargebeeClient;