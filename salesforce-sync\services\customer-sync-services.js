const pool = require("../db");
const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const { generateSfAdrsObj, getInternetTvPhoneDetailsObj, getOrderDetailsObj, hasV<PERSON>dChanges, billindTypeCbSubs, getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const PlanServices = require("../services/plan-services");
const InvoicesSyncService = require("../services/chargebee/invoice-sync-services");

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const moment = require('moment');
const ChargebeeClient = require('../clients/chargebee-client');

const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

class CustomerSyncServices {


    // Get customer status whether exist in database or not  
    async checkAndManageCustomers(sfCustomerDetails) {
        try {
            for (const sfCustomerDetail of sfCustomerDetails) {
                const { Contact__c } = sfCustomerDetail;

                if (!Contact__c) continue;

                const { contactExist, contactId } = await this.checkContactExist(Contact__c);
                if (!contactExist) continue;

                const { customerExist, customerId } = await this.checkCustomerExist(sfCustomerDetail);

                if (customerExist) {
                    await this.updateCustomerDetails(sfCustomerDetail, customerId);
                } else {
                    await this.createNewCustomerDetails(sfCustomerDetail, contactId);
                }
            }
        } catch (error) {
            console.error("SalesforceService getAllContactDetails -> ", error);
        }
    }

    // Check customer Exist
    async checkCustomerExist(sfCustomerDetail) {
        try {
            const { Id: sf_record_id } = sfCustomerDetail;

            const query = `SELECT 
                            ccd.*, 
                            cs.sf_record_id as subsSfId,
                            cs.subscription_type as subscription_type,
                            sa.sf_record_id as serviceSfId, 
                            ma.sf_record_id as mailingSfId, 
                            cdi.sf_record_id as internetSfId, 
                            cdt.sf_record_id as tvSfId,
                            cdp.sf_record_id as phoneSfId
                            FROM contacts_customer_details ccd
                            left join customer_details_addresses sa on sa.id = ccd.service_address_id
                            left join customer_details_addresses ma on ma.id = ccd.mailing_address_id
                            left join customer_details_internets_eastlink cdi on cdi.id = ccd.internet_id
                            left join customer_details_phones cdp on cdp.id = ccd.phone_id
                            left join customer_details_tvs cdt on cdt.id = ccd.tv_id
                            left join customer_details_subscriptions cs on cs.customer_details_id = ccd.id
                            where ccd.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                customerExist: res.length > 0,
                customerId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Customer Exist -> ", error);
            return {
                customerExist: 0,
                customerId: null
            };
        }
    }

    // Check contact Exist
    async checkContactExist(sf_record_id) {
        try {
            const query = `SELECT id FROM contacts WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                contactExist: res.length > 0,
                contactId: res.length ? res[0].id : null
            };
        } catch (error) {
            console.error("Check User Exist -> ", error);
            return {
                contactExist: 0,
                contactId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async createNewCustomerDetails(sfCustomerDetail, contact_id) {
        try {
            const {
                Id: sf_record_id,
                Service_Address__c,
                Mailing_Address__c,
                Latest_TV__c,
                Latest_Internet__c,
                Latest_Phone_VOIP__c,
                Latest_CB_Subscription__c,
                CB_Subscription_Id__c,
                CP_Stage__c,
                Name,
                LastModifiedDate: sf_updatedAt,
                CreatedDate: createdAt,
                Mailing_Addresses__r,
                Internet__r,
                TV__r,
                Phones__r,
                CB_Subscriptions__r
            } = sfCustomerDetail;

            const checkStage = CP_Stage__c ? CP_Stage__c.toLowerCase() : null;

            if (checkStage && checkStage != "hidden") {

                const addressPromises = [];

                if (Service_Address__c) addressPromises.push(await this.handleAddress(Service_Address__c, "Service_Address__c"));
                else addressPromises.push(Promise.resolve(null));

                if (Mailing_Addresses__r && Mailing_Addresses__r?.records?.length && Mailing_Address__c) {
                    const mailingAddress = Mailing_Addresses__r?.records;
                    const findmailingAdrs = mailingAddress.find(mA => mA.Id === Mailing_Address__c);
                    if (findmailingAdrs) addressPromises.push(await this.handleAddress(Mailing_Address__c, "Mailing_Address__c", findmailingAdrs));
                    else addressPromises.push(Promise.resolve(null));
                } else addressPromises.push(Promise.resolve(null));

                const servicePromises = [];

                if (Internet__r && Internet__r?.records?.length && Latest_Internet__c) {
                    const internetDetail = Internet__r?.records;
                    const findInternet = internetDetail.find(iDe => iDe.Id === Latest_Internet__c);
                    if (findInternet) servicePromises.push(this.getServiceDetailId(findInternet, "Internet__c", "customer_details_internets_eastlink"));
                    else servicePromises.push(Promise.resolve(null));
                } else servicePromises.push(Promise.resolve(null));

                if (TV__r && TV__r?.records?.length && Latest_TV__c) {
                    const tvDetail = TV__r?.records;
                    const findTv = tvDetail.find(tDe => tDe.Id === Latest_TV__c);
                    if (findTv) servicePromises.push(this.getServiceDetailId(findTv, "TV__c", "customer_details_tvs"));
                    else servicePromises.push(Promise.resolve(null));
                } else servicePromises.push(Promise.resolve(null));

                if (Phones__r && Phones__r?.records?.length && Latest_Phone_VOIP__c) {
                    const phoneDetail = Phones__r?.records;
                    const findPhone = phoneDetail.find(pDe => pDe.Id === Latest_Phone_VOIP__c);
                    if (phoneDetail) servicePromises.push(this.getServiceDetailId(findPhone, "phoneDetail", "customer_details_phones"));
                    else servicePromises.push(Promise.resolve(null));
                } else servicePromises.push(Promise.resolve(null));

                const [serviceAddress, mailingAddress, internet, tv, phone] = await Promise.allSettled([...addressPromises, ...servicePromises]);

                const formatedDate = getCurrentAtlanticTime(sf_updatedAt);

                const customerDetailData = {
                    contact_id,
                    sf_record_id,
                    service_address_id: serviceAddress.status === 'fulfilled' ? serviceAddress.value : null,
                    mailing_address_id: mailingAddress.status === 'fulfilled' ? mailingAddress.value : null,
                    internet_id: internet.status === 'fulfilled' ? internet.value : null,
                    tv_id: tv.status === 'fulfilled' ? tv.value : null,
                    phone_id: phone.status === 'fulfilled' ? phone.value : null,
                    stage: CP_Stage__c || "Hidden",
                    sf_name: Name || null,
                    sf_updatedAt: formatedDate
                };

                let insertCustomerDetails;

                // Check if a record with the same sf_record_id and address_type already exists
                const checkQuery = `SELECT id 
                                FROM contacts_customer_details 
                                WHERE sf_record_id = ${sanitizeValue(sf_record_id)}`;

                const existingRecord = await this.executeQuery(checkQuery);

                if (existingRecord.length > 0) insertCustomerDetails = existingRecord[0].id;
                else insertCustomerDetails = await this.insertRecord("contacts_customer_details", customerDetailData, createdAt);
                if (insertCustomerDetails) {
                    if (Latest_CB_Subscription__c) await this.getSubscriptionDetailsfromSf(sfCustomerDetail, insertCustomerDetails, "salesforce", customerDetailData);
                    else if (CB_Subscription_Id__c) await this.getSubscriptionDetailsfromSf(sfCustomerDetail, insertCustomerDetails, "chargebee", customerDetailData);
                }
            }
        } catch (error) {
            console.error("SalesforceService createNewCustomerDetails -> ", error);
        }
    }

    async handleAddress(addressId, apiName, mailingAddressDetails) {
        try {
            if (!addressId) return null;

            // Check if a record with the same sf_record_id and address_type already exists
            const checkQuery = `SELECT id 
            FROM customer_details_addresses 
            WHERE sf_record_id = ${sanitizeValue(addressId)}`;

            const existingRecord = await this.executeQuery(checkQuery);

            if (existingRecord.length > 0) return existingRecord[0].id;
            else {
                let addressData;
                if (apiName == "Service_Address__c") {
                    const selectedObj = generateSfAdrsObj(addressId, apiName);
                    const { returnStatus, addresstData: serviceAdrs } = await salesforceConnection.getAddressDetailsById(addressId, apiName, selectedObj);
                    if (returnStatus) addressData = serviceAdrs;
                } else addressData = mailingAddressDetails;

                if (!addressData) return null;

                const {
                    Id: sf_record_id,
                    Name: name,
                    LastModifiedDate: sf_updatedAt,
                    Status__c: status,
                    CreatedDate,
                    ...addressDetails
                } = addressData;

                const addressFields = {
                    suit_unit: apiName === 'Service_Address__c' ? addressDetails.Service_Suite_Unit__c : addressDetails.Mailing_Suite_Unit__c,
                    street_number: apiName === 'Service_Address__c' ? addressDetails.Service_Street_Number__c : addressDetails.Mailing_Street_Number__c,
                    street_name: apiName === 'Service_Address__c' ? addressDetails.Service_Street_Name__c : addressDetails.Mailing_Street_Name__c,
                    town: apiName === 'Service_Address__c' ? addressDetails.Service_City_Town__c : addressDetails.Mailing_City_Town__c,
                    province: apiName === 'Service_Address__c' ? addressDetails.Service_Province__c : addressDetails.Mailing_Province__c,
                    postal_code: apiName === 'Service_Address__c' ? addressDetails.Service_Postal_Code__c : addressDetails.Mailing_Postal_Code__c,
                    full_address: apiName === 'Service_Address__c' ? addressDetails.Full_Service_Address__c : addressDetails.Full_Mailing_Address__c,
                    country: apiName === 'Service_Address__c' ? addressDetails.Service_Country__c : addressDetails.Mailing_Country__c,
                };

                const { suit_unit, street_number, street_name, town, province, postal_code, full_address, country } = addressFields;
                const addressType = apiName === 'Service_Address__c' ? "service" : "mailing";
                const formatedDate = getCurrentAtlanticTime(CreatedDate);
                const updatedTime = getCurrentAtlanticTime();
                const sfUpdatedAt = getCurrentAtlanticTime(sf_updatedAt);

                const query = `INSERT IGNORE INTO customer_details_addresses 
                            (address_type, sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt, createdAt, updatedAt)
                            VALUES (
                                ${sanitizeValue(addressType)}, 
                                ${sanitizeValue(sf_record_id)}, 
                                ${sanitizeValue(name)}, 
                                ${sanitizeValue(suit_unit)}, 
                                ${sanitizeValue(street_number)}, 
                                ${sanitizeValue(street_name)}, 
                                ${sanitizeValue(province)}, 
                                ${sanitizeValue(town)}, 
                                ${sanitizeValue(country)}, 
                                ${sanitizeValue(postal_code)}, 
                                ${sanitizeValue(full_address)}, 
                                ${sanitizeValue(status)},
                                '${sfUpdatedAt}', 
                                '${formatedDate}', 
                                '${updatedTime}'
                            )`;

                const insertStatus = await this.executeQuery(query);
                return insertStatus?.insertId;
            }
        } catch (error) {
            console.error("SalesforceService handleAddress -> ", error);
        }
    }

    async getServiceDetailId(serviceDetails, serviceType, tableName, subscription_type) {

        try {
            const serviceId = serviceDetails?.Id;
            if (!serviceId) return null;

            const checkExQuery = `SELECT id
                        FROM ${tableName} 
                        WHERE sf_record_id = ${sanitizeValue(serviceId)}`;

            const existRecord = await this.executeQuery(checkExQuery);

            if (existRecord.length > 0) {
                const serviceId = existRecord[0].id;
                if (subscription_type) {
                    if (tableName == "customer_details_internets_eastlink" && serviceId) this.updateInternetDetails(serviceId, subscription_type);
                    if (tableName == "customer_details_tvs" && serviceId) this.updateTvDetails(serviceId, subscription_type);
                    if (tableName == "customer_details_phones" && serviceId) this.updatePhoneDetails(serviceId, subscription_type);
                }
                return serviceId;
            }

            const planServices = new PlanServices();
            const sfUpdatedAt = getCurrentAtlanticTime(serviceDetails.LastModifiedDate);

            const insertData = { sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, sf_name: serviceDetails.Name };
            if (serviceType === "Internet__c") {
                if (!serviceDetails.CP_Speed__c) return null;
                insertData.plan_speed = serviceDetails.CP_Speed__c;
                if (serviceDetails?.Live_Date__c) insertData.live_date = serviceDetails.Live_Date__c;
                if (serviceDetails?.Disconnected_Date__c) insertData.disconnected_date = serviceDetails.Disconnected_Date__c;

                // Status
                insertData.internal_processing_status = serviceDetails.CP_Status_Internal_Processing__c;
                insertData.ship_package_status = serviceDetails.CP_Status_Ship_Package__c;
                insertData.modem_installation_status = serviceDetails.CP_Status_Modem_Installation__c;
                insertData.modem_activation_status = serviceDetails.CP_Status_Modem_Activation__c;

                const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order } = await this.getAllInternetOrderDetails(serviceDetails);

                if (creation_order) insertData.creation_order = creation_order;
                if (tech_appointment) insertData.tech_appointment = tech_appointment;
                if (disconnect_order) insertData.disconnect_order = disconnect_order;
                if (speed_change_order) insertData.speed_change_order = speed_change_order;
                if (move_order) insertData.move_order = move_order;

                const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                if (internetPlanDetails?.length) {
                    const getPrice = internetPlanDetails.find(internet => internet.speed === serviceDetails.CP_Speed__c);
                    if (getPrice?.api_name) insertData.plan_name = getPrice.api_name;
                    if (subscription_type) {
                        if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                            insertData.plan_price = getPrice.billing[0][subscription_type].price;
                        }
                    }
                }
            }
            if (serviceType === "TV__c") {
                const plan_name = serviceDetails.Current_Base_Package__c;
                if (!plan_name) return null;
                if (serviceDetails.current_account_status__c == "REMOVED" && serviceDetails?.State_Text__c == "Complete") return null;
                insertData.plan_name = plan_name;
                insertData.state_text = serviceDetails?.State_Text__c;
                insertData.extra_packages = serviceDetails?.Current_Extra_Packages__c ? JSON.stringify(serviceDetails.Current_Extra_Packages__c.split(";")) : "[]";
                insertData.single_channels = serviceDetails?.Current_Single_Channels__c ? JSON.stringify(serviceDetails.Current_Single_Channels__c.split(";")) : "[]";
                insertData.iptv_products = serviceDetails?.current_iptv_products__c ? JSON.stringify(serviceDetails.current_iptv_products__c.split(";")) : "[]";

                // Get the total service cost
                if (subscription_type) {
                    const getCostObj = {
                        plan_name,
                        extra_packages: serviceDetails?.Current_Extra_Packages__c ? serviceDetails.Current_Extra_Packages__c.split(";") : [],
                        single_channels: serviceDetails?.Current_Single_Channels__c ? serviceDetails.Current_Single_Channels__c.split(";") : [],
                        iptv_products: serviceDetails?.current_iptv_products__c ? serviceDetails.current_iptv_products__c.split(";") : [],
                    };

                    const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(getCostObj, subscription_type);
                    insertData.total_service_cost = totalAmount;
                }

                if (serviceDetails?.current_account_status__c) insertData.account_status = serviceDetails.current_account_status__c;
                if (serviceDetails?.Login_Details_Last_Sent__c) insertData.login_details_last_sent = getCurrentAtlanticTime(serviceDetails.Login_Details_Last_Sent__c);
                if (serviceDetails?.Requested_Cancellation_Date__c) insertData.requested_cancellation_date = serviceDetails.Requested_Cancellation_Date__c;
            }
            if (serviceType === "Phone__c") {
                if (serviceDetails.Account_Status__c == "Deleted") return null;
                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
                if (phonePlanDetails?.length) {
                    const getPrice = phonePlanDetails.find(phone => phone.api_name === serviceDetails.Calling_Plan__c);
                    if (getPrice) {
                        if (getPrice?.api_name) {
                            const phoneRes = await salesforceConnection.getPhoneApiValue(serviceDetails.Id);
                            const { status: phStatus, data: pfData } = phoneRes;
                            if (phStatus) {
                                insertData.plan_name = pfData?.Calling_Plan__c;
                            }
                        }
                        if (subscription_type) {
                            if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) insertData.plan_price = getPrice.billing_period[0][subscription_type].price;
                        }
                    }
                }
                insertData.api_name = serviceDetails.Calling_Plan__c;
                insertData.account_status = serviceDetails.Account_Status__c;
                if (serviceDetails?.Service_Start_Date__c) {
                    insertData.service_start_date = getCurrentAtlanticTime(serviceDetails.Service_Start_Date__c);
                }
                if (serviceDetails?.Requested_Cancellation_Date__c) {
                    insertData.requested_cancellation_date = getCurrentAtlanticTime(serviceDetails.Requested_Cancellation_Date__c);
                }

                // Check the port type of home phone
                let sf_phone_type = { sf_phone_type: "new", phone_number: "" };
                if (serviceDetails?.Phone_Number_To_Port__c) {
                    sf_phone_type.phone_number = serviceDetails.Phone_Number_To_Port__c;
                    sf_phone_type.sf_phone_type = "existing";
                }
                insertData.sf_phone_type = JSON.stringify(sf_phone_type);
            }
            return await this.insertRecord(tableName, insertData, serviceDetails.CreatedDate);
        } catch (error) {
            console.error("SalesforceService getServiceDetailId -> ", error);
        }
    }

    async getSubscriptionDetailsfromSf(sfCustomerDetail, customer_details_id, type, customerDetailData) {
        try {
            let cbSubId;
            let sfSubId;

            const { Latest_CB_Subscription__c, CB_Subscription_Id__c, CB_Subscriptions__r } = sfCustomerDetail;
            const subscriptionId = type == "salesforce" ? Latest_CB_Subscription__c : CB_Subscription_Id__c;

            const updatedTime = getCurrentAtlanticTime();

            if (type == "salesforce") {

                if (CB_Subscriptions__r && CB_Subscriptions__r?.records?.length && Latest_CB_Subscription__c) {
                    const subRec = CB_Subscriptions__r?.records;
                    const findSubDetails = subRec.find(sR => sR.Id === Latest_CB_Subscription__c);
                    if (!findSubDetails) return null;
                    const { chargebeeapps__CB_Subscription_Id__c } = findSubDetails;
                    cbSubId = chargebeeapps__CB_Subscription_Id__c;
                }
            } else if (type == "chargebee") {
                cbSubId = subscriptionId;
            }

            if (!cbSubId) return;

            const chargebeeClient = new ChargebeeClient();
            let subscriptionDetail = await chargebeeClient.getSubscriptions(cbSubId);

            if (!subscriptionDetail.length) return;

            subscriptionDetail = subscriptionDetail[0];

            const insertCustomerSubsDetails = await this.insertCustomerSubscriptionFromChargebee(subscriptionDetail, customer_details_id, sfSubId);

            // const insertCustomerSubsDetails = await this.insertCustomerSubscription(subscriptionData, customer_details_id);

            if (insertCustomerSubsDetails) {
                const query = `SELECT subscription_type FROM customer_details_subscriptions WHERE id = '${insertCustomerSubsDetails}'`;
                let res = await this.executeQuery(query);
                res = res?.[0];
                const { subscription_type } = res;
                const { internet_id, tv_id, phone_id } = customerDetailData;
                // Plan 
                const planServices = new PlanServices();

                if (internet_id) {

                    const query = `SELECT id, plan_speed FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
                    let res = await this.executeQuery(query);
                    let internetDetails = res?.[0];

                    if (internetDetails?.plan_speed) {
                        const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                        if (internetPlanDetails?.length) {
                            let updateObj = {};
                            const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
                            if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
                            if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                                updateObj.plan_price = getPrice.billing[0][subscription_type].price;
                            }

                            // Build the update query dynamically based on the available fields
                            let updateFields = [];
                            let queryValues = [];

                            if (updateObj.plan_name) {
                                updateFields.push('plan_name = ?');
                                queryValues.push(updateObj.plan_name);
                            }

                            if (updateObj.plan_price) {
                                updateFields.push('plan_price = ?');
                                queryValues.push(updateObj.plan_price);
                            }

                            updateFields.push('updatedAt = ?');
                            queryValues.push(updatedTime);

                            // Add the id at the end of queryValues for the WHERE clause
                            queryValues.push(internetDetails.id);

                            const updateQuery = `
                                    UPDATE customer_details_internets_eastlink 
                                    SET ${updateFields.join(', ')} 
                                    WHERE id = ?`;

                            await this.executeQuery(updateQuery, queryValues);

                        }
                    }
                }

                if (tv_id) {

                    const query = `SELECT id, plan_name, single_channels, iptv_products, extra_packages FROM customer_details_tvs WHERE id = '${tv_id}'`;
                    let res = await this.executeQuery(query);
                    let tvDetails = res?.[0];

                    if (tvDetails) {
                        const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

                        if (plan_name) {

                            const payload = {
                                plan_name: plan_name,
                                extra_packages: JSON.parse(extra_packages),
                                single_channels: JSON.parse(single_channels),
                                iptv_products: JSON.parse(iptv_products)
                            }

                            const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(payload, subscription_type);

                            const updateQuery = `
                                UPDATE customer_details_tvs 
                                SET total_service_cost = ${totalAmount}, updatedAt = '${updatedTime}'
                                WHERE id = ${id}`;

                            await this.executeQuery(updateQuery);
                        }
                    }
                }

                if (phone_id) {

                    const query = `SELECT id, sf_record_id, api_name FROM customer_details_phones WHERE id = '${phone_id}'`;
                    let res = await this.executeQuery(query);
                    let phoneDetails = res?.[0];

                    if (phoneDetails) {
                        const { id, api_name, sf_record_id } = phoneDetails;
                        if (api_name) {
                            const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
                            if (phonePlanDetails?.length) {
                                let updateObj = {};
                                const getPrice = phonePlanDetails.find(phone => phone.api_name === api_name);
                                if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                                    updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                                }
                                // Build the update query dynamically based on the available fields
                                let updateFields = [];
                                let queryValues = [];

                                if (updateObj.plan_price) {
                                    updateFields.push('plan_price = ?');
                                    queryValues.push(updateObj.plan_price);
                                }

                                updateFields.push('updatedAt = ?');
                                queryValues.push(updatedTime);

                                // Add the id at the end of queryValues for the WHERE clause
                                queryValues.push(id);

                                const updateQuery = `
                                        UPDATE customer_details_phones 
                                        SET ${updateFields.join(', ')} 
                                        WHERE id = ?`;

                                await this.executeQuery(updateQuery, queryValues);
                            }
                        }
                    }
                }

                const invoicesSyncService = new InvoicesSyncService();
                await invoicesSyncService.getInvoicesList(cbSubId);
            }
        } catch (error) {
            console.error("Sync Service getSubscriptionDetailsfromSf -> ", error);
        }
    }

    async insertCustomerSubscription(subscriptionData, customer_details_id) {
        try {

            const { Id: sf_record_id, chargebeeapps__CB_Subscription_Id__c: cb_subscription_id, chargebeeapps__Plan_Amount__c: amount, chargebeeapps__Subcription_Activated_At__c: activated_on, chargebeeapps__Next_billing__c: next_billing_at, chargebeeapps__Subscription_status__c: status, LastModifiedDate: sf_updatedAt } = subscriptionData;

            const sfUpdatedAt = getCurrentAtlanticTime(sf_updatedAt);

            const customerSubsDetails = { customer_details_id, cb_subscription_id, sf_record_id, amount, activated_on, next_billing_at, status, sf_updatedAt: sfUpdatedAt };

            return await this.insertRecord("customer_details_subscriptions", customerSubsDetails);
        } catch (error) {
            console.error("Sync Service insertCustomerSubscription -> ", error);
        }
    }

    async insertCustomerInvoice(invoiceData, customer_details_id, customer_subscription_id) {
        try {

            const { Id: sf_record_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id, chargebeeapps__Due_Amount__c: total_outstanding, chargebeeapps__Amount__c: amount, Expected_Payment_Date_Time__c: expected_payment_date, chargebeeapps__Status__c: status, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, LastModifiedDate: sf_updatedAt, chargebeeapps__Invoice_Date__c: createdAt } = invoiceData;
            const sfUpdatedAt = getCurrentAtlanticTime(sf_updatedAt);
            const customerSubsInvoiceDetails = { customer_details_id, customer_subscription_id, sf_record_id, cb_subscription_id, cb_invoice_id, amount, status, total_outstanding, expected_payment_date, sf_updatedAt: sfUpdatedAt };

            return await this.insertRecord("subscriptions_invoices", customerSubsInvoiceDetails, createdAt);
        } catch (error) {
            console.error("Sync Service insertCustomerInvoice -> ", error);
        }
    }
    async getAllInternetOrderDetails(internetDetails) {
        try {
            const { Id, Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c } = internetDetails;

            let query = `SELECT Id, (SELECT Id, Name, Record_Type_Name__c, Related_Shipping_Order__c, Requested_Disconnect_Date__c, Expected_Completion_Date__c, Stage__c, Response_Date__c, LastModifiedDate, Speed__c, Requested_Install_Date__c, Requested_Move_Date__c, Install_Date__c, Install_Time__c, Order_Reject_Reason__c, Order_Reject_Reason_Solved__c, Submit_Date__c, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c FROM Orders__r), (SELECT Id, Name, Install_Date__c, Install_Time__c, LastModifiedDate FROM Tech_Appointments__r) FROM Internet__c WHERE Id = '${Id}' LIMIT 1`;

            let fetchedData = await salesforceConnection.fetchAllRecords(query);
            if (!fetchedData?.length) return;
            fetchedData = fetchedData[0];
            const { Orders__r, Tech_Appointments__r } = fetchedData;

            const orderPromises = [
                Creation_Order__c ? this.getOrderDetailsId(Creation_Order__c, "internets_eastlink_creation_order", Orders__r) : Promise.resolve(null),
                Latest_Tech_Appointment__c ? this.getTechDetailsId(Latest_Tech_Appointment__c, "internets_eastlink_tech_appointment", Tech_Appointments__r) : Promise.resolve(null),
                Latest_Disconnect_Order__c ? this.getOrderDetailsId(Latest_Disconnect_Order__c, "internets_eastlink_disconnect_order", Orders__r) : Promise.resolve(null),
                Latest_Speed_Change_Order__c ? this.getOrderDetailsId(Latest_Speed_Change_Order__c, "internets_eastlink_speed_change_order", Orders__r) : Promise.resolve(null),
                Latest_Move_Order__c ? this.getOrderDetailsId(Latest_Move_Order__c, "internets_eastlink_move_order", Orders__r) : Promise.resolve(null),
                Latest_Modem_Swap_Order__c ? this.getOrderDetailsId(Latest_Modem_Swap_Order__c, "internets_eastlink_swap_order", Orders__r) : Promise.resolve(null)
            ];

            const [creationOrderId, techAppointmentId, disconnectOrderId, speedChangeOrderId, moveOrderId, swapOrderId] = await Promise.allSettled(orderPromises);

            const customerDetailData = {
                creation_order: creationOrderId.status === 'fulfilled' ? creationOrderId.value : null,
                tech_appointment: techAppointmentId.status === 'fulfilled' ? techAppointmentId.value : null,
                disconnect_order: disconnectOrderId.status === 'fulfilled' ? disconnectOrderId.value : null,
                speed_change_order: speedChangeOrderId.status === 'fulfilled' ? speedChangeOrderId.value : null,
                move_order: moveOrderId.status === 'fulfilled' ? moveOrderId.value : null,
                swap_order: swapOrderId.status === 'fulfilled' ? swapOrderId.value : null
            };

            return customerDetailData;
        } catch (error) {
            console.error("Sync Service getAllInternetOrderDetails -> ", error);
            return {}; // Return an empty object to avoid errors
        }
    }

    async getOrderDetailsId(orderId, tableName, Orders__r) {

        try {
            if (!orderId) return null;

            // Check the database for the existing record
            const checkExQuery = `
                    SELECT id 
                    FROM ${tableName} 
                    WHERE sf_record_id = ${sanitizeValue(orderId)}
                `;

            const existRecord = await this.executeQuery(checkExQuery);
            if (existRecord.length > 0) return existRecord[0].id;

            if (Orders__r && Orders__r?.records?.length && orderId) {
                const orders = Orders__r?.records;
                const orderDetails = orders.find(order => order.Id === orderId);
                if (!orderDetails) return null;

                const sfUpdatedAt = getCurrentAtlanticTime(orderDetails?.LastModifiedDate);

                let insertOrderDetails = { sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, sf_name: orderDetails?.Name };

                if (tableName === "internets_eastlink_creation_order") {
                    if (orderDetails?.Related_Shipping_Order__c) {

                        let shipping_id = null;
                        // Check if a record with the same sf_record_id and address_type already exists
                        const checkQuery = `SELECT id 
                              FROM creation_order_shipping 
                              WHERE sf_record_id = ${sanitizeValue(orderDetails.Related_Shipping_Order__c)}`;

                        const existingRecord = await this.executeQuery(checkQuery);

                        if (existingRecord.length > 0) shipping_id = existingRecord[0].id;
                        else {
                            const { shippingData } = await salesforceConnection.getShippingDetails(orderDetails.Related_Shipping_Order__c);

                            if (shippingData) {
                                const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updated_At, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingData;

                                const modified_date = getCurrentAtlanticTime(sf_updated_At);
                                const formatedDate = getCurrentAtlanticTime(CreatedDate);
                                const updatedTime = getCurrentAtlanticTime();

                                const query = `INSERT IGNORE INTO creation_order_shipping 
                                    (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                                    VALUES (
                                    ${sanitizeValue(sf_record_id)}, 
                                    ${sanitizeValue(sf_name)}, 
                                    ${sanitizeValue(full_mailing_address)}, 
                                    ${sanitizeValue(ship_date)}, 
                                    ${sanitizeValue(ship_drop_off_date)}, 
                                    ${sanitizeValue(tracking_url)}, 
                                    ${sanitizeValue(courier)}, 
                                    ${sanitizeValue(modified_date)}, 
                                    ${sanitizeValue(package_deliverted_at)}, 
                                    '${formatedDate}', 
                                    '${updatedTime}'
                                    )`;

                                const insertStatus = await this.executeQuery(query);
                                if (insertStatus) shipping_id = insertStatus?.insertId || null;
                            }
                        }
                        if (shipping_id) insertOrderDetails.shipping_id = shipping_id;
                    }
                    if (orderDetails?.Record_Type_Name__c) insertOrderDetails.record_type = orderDetails.Record_Type_Name__c;
                    if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
                }

                if (tableName === "internets_eastlink_disconnect_order" && orderDetails?.Requested_Disconnect_Date__c) insertOrderDetails.requested_disconnect_date = orderDetails?.Requested_Disconnect_Date__c;

                if (tableName === "internets_eastlink_swap_order" && orderDetails?.Stage__c) {
                    let value = orderDetails?.Stage__c;
                    if (value) {
                        insertOrderDetails.stage = value.replace(/['"`]/g, '');
                    }
                }

                if (tableName === "internets_eastlink_speed_change_order" && orderDetails?.Expected_Completion_Date__c) {
                    insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;
                    if (orderDetails?.Speed__c) insertOrderDetails.speed = orderDetails?.Speed__c;
                    let value = orderDetails?.Stage__c;
                    if (value) {
                        insertOrderDetails.stage = value.replace(/['"`]/g, '');
                    }
                    if (orderDetails?.Response_Date__c) {
                        insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);
                    }
                }

                if (tableName === "internets_eastlink_move_order") {
                    if (orderDetails?.Requested_Move_Date__c) insertOrderDetails.requested_move_date = orderDetails?.Requested_Move_Date__c;
                    if (orderDetails?.Requested_Install_Date__c) insertOrderDetails.requested_install_date = orderDetails?.Requested_Install_Date__c;
                    if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
                    if (orderDetails?.Submit_Date__c) insertOrderDetails.submit_date = getCurrentAtlanticTime(orderDetails?.Submit_Date__c);
                    if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
                    if (orderDetails?.Install_Time__c) insertOrderDetails.install_time = orderDetails?.Install_Time__c;
                    if (orderDetails?.Order_Reject_Reason__c) insertOrderDetails.reject_reason = orderDetails?.Order_Reject_Reason__c;
                    if (orderDetails?.Order_Reject_Reason_Solved__c) insertOrderDetails.reject_reason_solved = orderDetails?.Order_Reject_Reason_Solved__c;
                    if (orderDetails?.Service_Suite_Unit__c) insertOrderDetails.unit = orderDetails?.Service_Suite_Unit__c;
                    if (orderDetails?.Service_Street_Number__c) insertOrderDetails.street_number = orderDetails?.Service_Street_Number__c;
                    if (orderDetails?.Service_Street_Name__c) insertOrderDetails.street_name = orderDetails?.Service_Street_Name__c;
                    if (orderDetails?.Service_City_Town__c) insertOrderDetails.city = orderDetails?.Service_City_Town__c;
                    if (orderDetails?.Service_Province__c) insertOrderDetails.province = orderDetails?.Service_Province__c;
                    if (orderDetails?.Service_Postal_Code__c) insertOrderDetails.postal_code = orderDetails?.Service_Postal_Code__c;
                }
                return await this.insertRecord(tableName, insertOrderDetails, orderDetails.CreatedDate);
            }
            return null;
        } catch (error) {
            console.error("Sync Service getOrderDetailsId -> ", error);
        }
    }

    async getTechDetailsId(techId, tableName, Tech_Appointments__r) {
        try {
            if (!techId) return null;

            // Check if a record with the same sf_record_id and address_type already exists
            const checkQuery = `SELECT id 
                        FROM ${tableName} 
                        WHERE sf_record_id = ${sanitizeValue(techId)}`;

            const existingRecord = await this.executeQuery(checkQuery);

            if (existingRecord.length > 0) return existingRecord[0].id;

            if (Tech_Appointments__r && Tech_Appointments__r?.records?.length && techId) {
                const techApp = Tech_Appointments__r?.records;
                const techData = techApp.find(teAp => teAp.Id === techId);
                if (!techData) return null;

                const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = techData;

                const formatedDate = getCurrentAtlanticTime(CreatedDate);
                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const updatedTime = getCurrentAtlanticTime();

                const query = `INSERT IGNORE INTO ${tableName} 
                    (sf_record_id, sf_name, install_date, install_time, sf_updatedAt, createdAt, updatedAt)
                    VALUES ('${sf_record_id}', '${sf_name}', ${sanitizeValue(install_date)}, ${sanitizeValue(install_time)}, '${modifiedDate}', '${formatedDate}', '${updatedTime}')`;

                const insertStatus = await this.executeQuery(query);
                return insertStatus?.insertId;
            }
            return null;
        } catch (error) {
            console.error("Sync Service getTechDetailsId -> ", error);
        }
    }

    async insertRecord(tableName, data, created_date) {
        try {
            if (!tableName || !data || typeof data !== 'object') return;

            const createdAt = created_date ? getCurrentAtlanticTime(created_date) : getCurrentAtlanticTime();
            const updatedTime = getCurrentAtlanticTime();

            // Construct the SQL query
            const insertColumns = Object.keys(data).join(', ');
            const insertValues = Object.values(data).map(v => sanitizeValue(v)).join(', ');

            const query = `INSERT IGNORE INTO ${tableName} (${insertColumns}, createdAt, updatedAt) 
                            VALUES (${insertValues}, '${createdAt}', '${updatedTime}')`;

            // Execute the query and return the inserted ID
            const insertStatus = await this.executeQuery(query);
            return insertStatus?.insertId;
        } catch (error) {
            console.error("Error inserting record -> ", error);
        }
    }

    async updateCustomerDetails(sfCustomerDetail, customerDetails) {
        try {
            const {
                Id: sf_record_id,
                Service_Address__c,
                Mailing_Address__c,
                Latest_TV__c,
                Latest_Internet__c,
                Latest_Phone_VOIP__c,
                Latest_CB_Subscription__c,
                CP_Stage__c,
                Name,
                LastModifiedDate: sf_updatedAt
            } = sfCustomerDetail;

            const checkStage = CP_Stage__c ? CP_Stage__c.toLowerCase() : null;

            if (checkStage && checkStage != "hidden") {
                const { serviceSfId, mailingSfId, internetSfId, tvSfId, phoneSfId, subsSfId } = customerDetails;

                if (hasValidChanges(Service_Address__c, serviceSfId)) this.checkAndUpdateAddress(sfCustomerDetail, customerDetails, "service");
                if (hasValidChanges(Mailing_Address__c, mailingSfId)) this.checkAndUpdateAddress(sfCustomerDetail, customerDetails, "mailing");
                if (hasValidChanges(Latest_TV__c, tvSfId)) this.checkAndUpdateTvService(sfCustomerDetail, customerDetails);
                if (hasValidChanges(Latest_Internet__c, internetSfId)) this.checkAndUpdateInternetService(sfCustomerDetail, customerDetails);
                if (hasValidChanges(Latest_Phone_VOIP__c, phoneSfId)) this.updatePhoneService(sfCustomerDetail, customerDetails);
                if (hasValidChanges(Latest_CB_Subscription__c, subsSfId)) this.updateSubscriptions(sfCustomerDetail, customerDetails);

                const stage = CP_Stage__c || "Hidden";
                const sf_name = Name || null;

                const updatedTime = getCurrentAtlanticTime();
                const datetime = getCurrentAtlanticTime(sf_updatedAt);
                const updateQuery = `
                UPDATE contacts_customer_details 
                SET stage = '${stage}', sf_name = '${sf_name}', updatedAt = '${updatedTime}', sf_updatedAt = '${datetime}'
                WHERE sf_record_id = '${sf_record_id}'`;

                await this.executeQuery(updateQuery);
            } else {
                await this.deleteCustomerDetailsFromDb(customerDetails);
            }
            return { execute: true, status: true, sf_record_id: sfCustomerDetail?.Id || null }
        } catch (error) {
            console.error("Error updateCustomerDetails -> ", error);
            return { execute: true, status: false, sf_record_id: sfCustomerDetail?.Id || null }
        }
    }

    async checkAndUpdateTvService(sfCustomerDetail, customerDetails) {
        try {
            const { Latest_TV__c, LastModifiedDate, TV__r } = sfCustomerDetail;
            const { id, tv_id, subscription_type } = customerDetails;
            let tvId = null;

            if (TV__r && TV__r?.records?.length && Latest_TV__c) {
                const tvRec = TV__r?.records;
                const findTvDetails = tvRec.find(tR => tR.Id === Latest_TV__c);
                if (findTvDetails) tvId = await this.getServiceDetailId(findTvDetails, "TV__c", "customer_details_tvs", subscription_type);
            }

            const datetime = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
            UPDATE contacts_customer_details 
            SET tv_id = ?, updatedAt = ?, sf_updatedAt = ? 
            WHERE id = ?`;

            await this.executeQuery(updateQuery, [tvId, updatedTime, datetime, id]);

            if (tv_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "tv_id", tv_id);
                if (!dataCount) await this.deleteQuery("customer_details_tvs", tv_id);
            }
        } catch (error) {
            console.error("Error checkAndUpdateTvService -> ", error);
        }
    }

    async updatePhoneService(sfCustomerDetail, customerDetails) {
        try {
            const { Latest_Phone_VOIP__c, LastModifiedDate, Phones__r } = sfCustomerDetail;
            const { id, phone_id, subscription_type } = customerDetails;

            let phoneId = null;

            if (Phones__r && Phones__r?.records?.length && Latest_Phone_VOIP__c) {
                const phoneRec = Phones__r?.records;
                const findPhoneDetails = phoneRec.find(pR => pR.Id === Latest_Phone_VOIP__c);
                if (findPhoneDetails) phoneId = await this.getServiceDetailId(findPhoneDetails, "Latest_Phone_VOIP__c", "customer_details_phones", subscription_type);
            }

            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
            UPDATE contacts_customer_details 
            SET phone_id = ?, updatedAt = ?, sf_updatedAt = ? 
            WHERE id = ?`;
            await this.executeQuery(updateQuery, [phoneId, updatedTime, formatedDate, id]);
            if (phone_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "phone_id", phone_id);
                if (!dataCount) await this.deleteQuery("customer_details_phones", phone_id);
            }
        } catch (error) {
            console.error("Error updatePhoneService -> ", error);
        }
    }


    // @Deprecated Currently this function is not working, data updated from sync function in subscription service
    async updateSubscriptions(sfCustomerDetail, customerDetails) {
        try {
            const { id } = customerDetails;
            const { Latest_CB_Subscription__c, CB_Subscription_Id__c } = sfCustomerDetail;
            const deleteQuery = `DELETE FROM customer_details_subscriptions WHERE customer_details_id = ?`;

            await this.executeQuery(deleteQuery, [id]);
            if (Latest_CB_Subscription__c) await this.getSubscriptionDetailsfromSf(sfCustomerDetail, id, "salesforce", customerDetails);
            else if (CB_Subscription_Id__c) await this.getSubscriptionDetailsfromSf(sfCustomerDetail, id, "chargebee", customerDetails);

        } catch (error) {
            console.error("Error updateSubscriptions -> ", error);
        }
    }

    async checkAndUpdateInternetService(sfCustomerDetail, customerDetails) {
        try {
            const { Latest_Internet__c, LastModifiedDate, Internet__r } = sfCustomerDetail;
            const { internet_id, id, subscription_type } = customerDetails;

            let internetId = null;

            if (Internet__r && Internet__r?.records?.length && Latest_Internet__c) {
                const internetRec = Internet__r?.records;
                const findInternetDetails = internetRec.find(iR => iR.Id === Latest_Internet__c);
                if (findInternetDetails) internetId = await this.getServiceDetailId(findInternetDetails, "Internet__c", "customer_details_internets_eastlink", subscription_type);
            }

            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
            UPDATE contacts_customer_details 
            SET internet_id = ?, updatedAt = ?, sf_updatedAt = ? 
            WHERE id = ?`;

            await this.executeQuery(updateQuery, [internetId, updatedTime, formatedDate, id]);

            const query = `SELECT * FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
            let res = await this.executeQuery(query);

            res = res?.[0];
            if (res) await this.updateInternet(res, sfCustomerDetail, customerDetails, internetId);
        } catch (error) {
            console.error("Error checkAndUpdateInternetService -> ", error);
        }
    }

    async updateInternet(oldData, sfCustomerDetail, customerDetails, internetId) {
        try {
            const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order } = oldData;
            const { internet_id } = customerDetails;
            let shipping_id;

            if (creation_order) {
                const query = `SELECT shipping_id FROM internets_eastlink_creation_order WHERE id = '${creation_order}'`;
                let res = await this.executeQuery(query);
                shipping_id = res?.[0].shipping_id;
            }

            if (internet_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "internet_id", internet_id);
                if (!dataCount) await this.deleteQuery("customer_details_internets_eastlink", internet_id);
            }

            if (creation_order) {
                const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "creation_order", creation_order);
                if (!dataCount) await this.deleteQuery("internets_eastlink_creation_order", creation_order);
            }

            if (tech_appointment) {
                const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "tech_appointment", tech_appointment);
                if (!dataCount) await this.deleteQuery("internets_eastlink_tech_appointment", tech_appointment);
            }

            if (disconnect_order) {
                const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "disconnect_order", disconnect_order);
                if (!dataCount) await this.deleteQuery("internets_eastlink_disconnect_order", disconnect_order);
            }

            if (speed_change_order) {
                const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "speed_change_order", speed_change_order);
                if (!dataCount) await this.deleteQuery("internets_eastlink_speed_change_order", speed_change_order);
            }

            if (move_order) {
                const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "move_order", move_order);
                if (!dataCount) await this.deleteQuery("internets_eastlink_move_order", move_order);
            }

            if (shipping_id) {
                const { dataCount } = await this.checkDataCount("internets_eastlink_creation_order", "shipping_id", shipping_id);
                if (!dataCount) await this.deleteQuery("creation_order_shipping", shipping_id);
            }

        } catch (error) {
            console.error(`Error in updateInternet for -> `, error);
        }
    }

    async deleteQuery(tableName, id) {
        try {
            const deleteQuery = `DELETE FROM ${tableName} WHERE id = ?`;
            await this.executeQuery(deleteQuery, [id]);
        } catch (error) {
            console.error(`Error in deleteQuery -> `, error);
        }
    }

    async checkAndUpdateAddress(sfCustomerDetail, customerDetails, type) {
        try {
            const {
                LastModifiedDate
            } = sfCustomerDetail;

            const { id, service_address_id, mailing_address_id } = customerDetails;

            if (type === "mailing") await this.updateAddress(sfCustomerDetail, mailing_address_id, id, "Mailing_Address__c", LastModifiedDate, "mailing_address_id");
            if (type === "service") await this.updateAddress(sfCustomerDetail, service_address_id, id, "Service_Address__c", LastModifiedDate, "service_address_id");
        } catch (error) {
            console.error("Error updateMailingAddress -> ", error);
        }
    }

    async updateAddress(sfCustomerAndAdrsDetail, oldAddressId, customerId, addressType, lastModifiedDate, columnName) {
        try {
            const { Mailing_Addresses__r, Service_Address__c, Mailing_Address__c } = sfCustomerAndAdrsDetail;
            let addressId = null;
            if (addressType == "Mailing_Address__c") {
                if (Mailing_Addresses__r && Mailing_Addresses__r?.records?.length && Mailing_Address__c) {
                    const mailingAddress = Mailing_Addresses__r?.records;
                    const findmailingAdrs = mailingAddress.find(mA => mA.Id === Mailing_Address__c);
                    if (findmailingAdrs) addressId = await this.handleAddress(Mailing_Address__c, addressType, findmailingAdrs);
                }
            } else {
                if (Service_Address__c != "") {
                    addressId = await this.handleAddress(Service_Address__c, addressType);
                }
            }

            const sfUpdatedAt = getCurrentAtlanticTime(lastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
                            UPDATE contacts_customer_details 
                            SET ${columnName} = ?, updatedAt = ?, sf_updatedAt = ? 
                            WHERE id = ?`;

            await this.executeQuery(updateQuery, [addressId, updatedTime, sfUpdatedAt, customerId]);

            if (oldAddressId) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", columnName, oldAddressId);
                if (!dataCount) await this.deleteQuery("customer_details_addresses", oldAddressId);
            }
        } catch (error) {
            console.error(`Error in updateAddress for ${addressType} -> `, error);
        }
    }

    async insertCustomerSubscriptionFromChargebee(subscriptionData, customer_details_id, sfSubId) {
        try {

            const { subscription: { id: cb_subscription_id, billing_period_unit, plan_amount: amount, status, next_billing_at, created_at, activated_at: activated_on, total_dues } } = subscriptionData;

            // Check if a record with the same sf_record_id and address_type already exists
            const checkExQuery = `SELECT id 
                        FROM customer_details_subscriptions
                        WHERE cb_subscription_id = ${sanitizeValue(cb_subscription_id)}`;

            const existRecord = await this.executeQuery(checkExQuery);

            if (existRecord.length > 0) {
                const id = existRecord[0].id;
                const updatedTime = getCurrentAtlanticTime();
                const updateQuery = `
                                    UPDATE customer_details_subscriptions 
                                    SET customer_details_id = ${customer_details_id}, updatedAt = '${updatedTime}'
                                    WHERE id = ${id}`;

                await this.executeQuery(updateQuery);

                const updateInvQuery = `
                                        UPDATE subscriptions_invoices 
                                        SET customer_details_id = ${customer_details_id}, updatedAt = '${updatedTime}'
                                        WHERE customer_subscription_id = ${id}`;

                await this.executeQuery(updateInvQuery);

                return existRecord[0].id;
            }

            const subscription_type = billindTypeCbSubs(billing_period_unit);

            const customerSubsDetails = {
                customer_details_id,
                cb_subscription_id,
                balance_due: total_dues ? total_dues / 100 : 0.00,
                amount: amount ? amount / 100 : null,
                activated_on: activated_on ? moment.unix(activated_on).format('YYYY-MM-DD HH:mm:ss') : null,
                next_billing_at: next_billing_at ? moment.unix(next_billing_at).format('YYYY-MM-DD HH:mm:ss') : null,
                status: status,
                subscription_type
            };

            if (sfSubId) customerSubsDetails.sf_record_id = sfSubId;

            const createdAt = created_at ? moment.unix(created_at).format('YYYY-MM-DD HH:mm:ss') : null;

            return await this.insertRecord("customer_details_subscriptions", customerSubsDetails, createdAt);

        } catch (error) {
            console.error(`AuthService insertCustomerSubscriptionFromChargebee ->`, error);
            throw error;
        }
    }

    async checkAndDeleteDetails(deletedData) {
        try {
            for (const sltData of deletedData) {
                const { id: Id } = sltData;
                const sfCustomerDetail = { Id };
                const { customerExist, customerId } = await this.checkCustomerExist(sfCustomerDetail);

                if (customerExist) await this.deleteCustomerDetailsFromDb(customerId);
            }
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
        }
    }

    async deleteCustomerDetailsFromDb(customerDetails) {
        try {
            const { service_address_id, mailing_address_id, internet_id, tv_id, phone_id, id } = customerDetails;

            // Service address
            if (service_address_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "service_address_id", service_address_id);
                if (dataCount == 1) {
                    await this.updateNullQuery("contacts_customer_details", "service_address_id", id);
                    await this.deleteQuery("customer_details_addresses", service_address_id);
                }
            }

            // Mailing address
            if (mailing_address_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "mailing_address_id", mailing_address_id);
                if (dataCount == 1) {
                    await this.updateNullQuery("contacts_customer_details", "mailing_address_id", id);
                    await this.deleteQuery("customer_details_addresses", mailing_address_id);
                }
            }

            // Internet and related orders
            if (internet_id) {
                const query = `SELECT * FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
                const internetDetails = await this.executeQuery(query);
                if (internetDetails.length) {
                    const internetDetail = internetDetails[0];
                    const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order } = internetDetail;

                    // Creation order and shipping
                    if (creation_order) {
                        const query = `SELECT shipping_id FROM internets_eastlink_creation_order WHERE id = '${creation_order}'`;
                        let res = await this.executeQuery(query);
                        const shipping_id = res?.[0].shipping_id;
                        if (shipping_id) {
                            const { dataCount } = await this.checkDataCount("internets_eastlink_creation_order", "shipping_id", shipping_id);
                            if (dataCount == 1) {
                                await this.updateNullQuery("internets_eastlink_creation_order", "shipping_id", creation_order);
                                await this.deleteQuery("creation_order_shipping", shipping_id);
                            }
                        }
                        const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "creation_order", creation_order);
                        if (dataCount == 1) {
                            await this.updateNullQuery("customer_details_internets_eastlink", "creation_order", internet_id);
                            await this.deleteQuery("internets_eastlink_creation_order", creation_order);
                        }
                    }

                    // Tech appointment
                    if (tech_appointment) {
                        const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "tech_appointment", tech_appointment);
                        if (dataCount == 1) {
                            await this.updateNullQuery("customer_details_internets_eastlink", "tech_appointment", internet_id);
                            await this.deleteQuery("internets_eastlink_tech_appointment", tech_appointment);
                        }
                    }

                    // Disconnect order
                    if (disconnect_order) {
                        const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "disconnect_order", disconnect_order);
                        if (dataCount == 1) {
                            await this.updateNullQuery("customer_details_internets_eastlink", "disconnect_order", internet_id);
                            await this.deleteQuery("internets_eastlink_disconnect_order", disconnect_order);
                        }
                    }

                    // Speed change order
                    if (speed_change_order) {
                        const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "speed_change_order", speed_change_order);
                        if (dataCount == 1) {
                            await this.updateNullQuery("customer_details_internets_eastlink", "speed_change_order", internet_id);
                            await this.deleteQuery("internets_eastlink_speed_change_order", speed_change_order);
                        }
                    }

                    // Move order
                    if (move_order) {
                        const { dataCount } = await this.checkDataCount("customer_details_internets_eastlink", "move_order", move_order);
                        if (dataCount == 1) {
                            await this.updateNullQuery("customer_details_internets_eastlink", "move_order", internet_id);
                            await this.deleteQuery("internets_eastlink_move_order", move_order);
                        }
                    }
                }

                // Delete internet if only referenced here
                if (internet_id) {
                    const { dataCount } = await this.checkDataCount("contacts_customer_details", "internet_id", internet_id);
                    if (dataCount == 1) {
                        await this.updateNullQuery("contacts_customer_details", "internet_id", id);
                        await this.deleteQuery("customer_details_internets_eastlink", internet_id);
                    }
                }
            }

            // Phone
            if (phone_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "phone_id", phone_id);
                if (dataCount == 1) {
                    await this.updateNullQuery("contacts_customer_details", "phone_id", id);
                    await this.deleteQuery("customer_details_phones", phone_id);
                }
            }

            // TV
            if (tv_id) {
                const { dataCount } = await this.checkDataCount("contacts_customer_details", "tv_id", tv_id);
                if (dataCount == 1) {
                    await this.updateNullQuery("contacts_customer_details", "tv_id", id);
                    await this.deleteQuery("customer_details_tvs", tv_id);
                }
            }

            // Finally, delete the customer detail itself
            await this.deleteQuery("contacts_customer_details", id);
        } catch (error) {
            console.error("Error deleteCustomerDetailsFromDb -> ", error);
        }
    }

    // Helper: Set a column to NULL for a given row (by id)
    async updateNullQuery(table, column, id) {
        const query = `UPDATE ${table} SET ${column} = NULL WHERE id = ?`;
        await this.executeQuery(query, [id]);
    }

    async updateInternetDetails(internet_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_speed FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
            let res = await this.executeQuery(query);
            let internetDetails = res?.[0];

            if (internetDetails?.plan_speed) {
                const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                if (internetPlanDetails?.length) {
                    let updateObj = {};
                    const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
                    if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
                    if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                        updateObj.plan_price = getPrice.billing[0][subscription_type].price;
                    }

                    // Build the update query dynamically based on the available fields
                    let updateFields = [];
                    let queryValues = [];

                    if (updateObj.plan_name) {
                        updateFields.push('plan_name = ?');
                        queryValues.push(updateObj.plan_name);
                    }

                    if (updateObj.plan_price) {
                        updateFields.push('plan_price = ?');
                        queryValues.push(updateObj.plan_price);
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    updateFields.push('updatedAt = ?');
                    queryValues.push(updatedTime);

                    // Add the id at the end of queryValues for the WHERE clause
                    queryValues.push(internetDetails.id);

                    const updateQuery = `
                            UPDATE customer_details_internets_eastlink 
                            SET ${updateFields.join(', ')} 
                            WHERE id = ?`;

                    await this.executeQuery(updateQuery, queryValues);
                }
            }
        } catch (error) {
            console.error("Salesforce Customer Service updateInternetDetails -> ", error);
        }
    }

    async updateTvDetails(tv_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_name, single_channels, iptv_products, extra_packages FROM customer_details_tvs WHERE id = '${tv_id}'`;
            let res = await this.executeQuery(query);
            let tvDetails = res?.[0];

            if (tvDetails) {
                const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

                if (plan_name) {

                    const payload = {
                        plan_name: plan_name,
                        extra_packages: JSON.parse(extra_packages),
                        single_channels: JSON.parse(single_channels),
                        iptv_products: JSON.parse(iptv_products)
                    }

                    const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(payload, subscription_type);

                    const updatedTime = getCurrentAtlanticTime();

                    const updateQuery = `
                        UPDATE customer_details_tvs 
                        SET total_service_cost = ?, updatedAt = ?
                        WHERE id = ?`;
                    await this.executeQuery(updateQuery, [totalAmount, updatedTime, id]);
                }
            }
        } catch (error) {
            console.error("Salesforce Customer Service updateTvDetails -> ", error);
        }
    }

    async updatePhoneDetails(phone_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, sf_record_id, api_name FROM customer_details_phones WHERE id = '${phone_id}'`;
            let res = await this.executeQuery(query);
            let phoneDetails = res?.[0];

            if (phoneDetails) {
                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);

                let updateObj = {};
                const getPrice = phonePlanDetails.find(phone => phone.api_name === phoneDetails.api_name);
                if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                    updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                }

                // Build the update query dynamically based on the available fields
                let updateFields = [];
                let queryValues = [];

                if (updateObj.plan_price) {
                    updateFields.push('plan_price = ?');
                    queryValues.push(updateObj.plan_price);
                }

                const updatedTime = getCurrentAtlanticTime();

                updateFields.push('updatedAt = ?');
                queryValues.push(updatedTime);

                // Add the id at the end of queryValues for the WHERE clause
                queryValues.push(phoneDetails.id);

                const updateQuery = `
                    UPDATE customer_details_phones 
                    SET ${updateFields.join(', ')} 
                    WHERE id = ?`;

                await this.executeQuery(updateQuery, queryValues);

            }
        } catch (error) {
            console.error("Salesforce Customer Service updatePhoneDetails -> ", error);
        }
    }

    async checkDataCount(tableName, columnName, id) {
        try {
            const query = `SELECT count(*) as cnt FROM ${tableName} WHERE ${columnName} = '${id}'`;
            const res = await this.executeQuery(query);
            return {
                dataCount: res.length ? res[0].cnt : 0
            };
        } catch (error) {
            console.error("Check data Exist -> ", error);
            return {
                dataCount: 0
            };
        }
    }
}

module.exports = CustomerSyncServices;
