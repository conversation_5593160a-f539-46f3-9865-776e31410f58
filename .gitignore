# This file contains a list of files and folders to be ignored when
# committing to a git repository.  Ignored files are both Slate project
# specific files as well as commonly ignored files on any project.

# For more information on this .gitignore files, see GitHub's
# documentation: https://help.github.com/articles/ignoring-files/

# Project #
###################
node_modules
dist
dist-ssr
*.local

deploy.log
npm-debug.log
package-lock.json
yarn.lock
.env
*.env

# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Logs and databases #
######################
logs
log
*.log
tmp
*.tmp