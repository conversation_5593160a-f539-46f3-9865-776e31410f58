import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { nextPaymentDateReducerState } from "../../typings/typing";

const initialState = {
  nextPaymentDate: null,
  showNextPaymentDatePopup: false,
  showNextPaymentDateConfirmationPopup: false,
} satisfies nextPaymentDateReducerState as nextPaymentDateReducerState;

const nextPaymentDateReducer = createSlice({
  name: "NEXT_PAYMENT_DATE_REDUCER",
  initialState,
  reducers: {
    setNextPaymentDate: (state, action: PayloadAction<Date | null>) => {
      state.nextPaymentDate = action.payload;
    },
    toggleShowNextPaymentDatePopup: (state) => {
      state.showNextPaymentDatePopup = !state.showNextPaymentDatePopup;
    },
    toggleShowNextPaymentDateConfirmationPopup: (state) => {
      state.showNextPaymentDateConfirmationPopup =
        !state.showNextPaymentDateConfirmationPopup;
    },
    resetRenewalPopups: (state) => {
      state.showNextPaymentDatePopup = false;
      state.showNextPaymentDateConfirmationPopup = false;
    },
  },
});

export const {
  setNextPaymentDate,
  toggleShowNextPaymentDatePopup,
  toggleShowNextPaymentDateConfirmationPopup,
  resetRenewalPopups,
} = nextPaymentDateReducer.actions;

export default nextPaymentDateReducer.reducer;
