import { useEffect, useState } from "react";
import LocationSelectCard from "./LocationSelectCard";
import Popup from "../common/Popup";
import YourPlacesSkeleton from "./skeletons/YourPlacesSkeleton";
import { addNotification } from "../../store/reducers/toasterReducer";
import { logout } from "../../store/reducers/authenticationReducer";
import { useDispatch } from "react-redux";
import { useGetOutstandingListMutation } from "../../services/api";
import { useNavigate } from "react-router-dom";

type SelectServiceForPaymentPopupProps = {
  type: string;
  subtitle: string;
  closeHandler: () => void;
  nextHandler: (locationId: string) => void;
  subscriptionId?: number;
};

const SelectLocationForPaymentPopup: React.FC<
  SelectServiceForPaymentPopupProps
> = ({ type, subtitle, closeHandler, nextHandler, subscriptionId }) => {
  const [getLocations] = useGetOutstandingListMutation();
  const [allLocations, setAllLocations] = useState([]);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    handleGetLocations();
  }, []);

  // Fetch all locations
  const handleGetLocations = async () => {
    try {
      const query = {
        type,
        subscriptionId, // Ensure this is passed from the component props or state
      };
      // Ensure subscriptionId is defined only when necessary
      if (subscriptionId) {
        query.subscriptionId = subscriptionId;
      }
      const response = await getLocations(query).unwrap();
      if (response.status === 200) {
        setAllLocations(response?.data);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    } finally {
      setIsInitialLoading(false); // Mark initial loading as complete
    }
  };

  return (
    <Popup title="Select invoice" closeHandler={closeHandler}>
      <div className="flex flex-col gap-5">
        {isInitialLoading ? (
          [1, 2, 3].map((index) => <YourPlacesSkeleton key={index} />)
        ) : allLocations?.length > 0 ? (
          <>
            <div>
              <p className="text-base">{subtitle}</p>
            </div>
            {allLocations.map((location, i) => (
              <LocationSelectCard
                key={i}
                location={location}
                nextHandler={nextHandler}
              />
            ))}
          </>
        ) : (
          <p className="text-center font-semibold">No Outstanding Invoices!</p>
        )}
      </div>
    </Popup>
  );
};

export default SelectLocationForPaymentPopup;
