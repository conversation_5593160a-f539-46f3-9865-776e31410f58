const pool = require("../../db");
const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const ContactServices = require("../../services/contact-sync-services");
const CustomerServices = require("../../services/customer-sync-services");
const CustomerTestServices = require("./customer-sync-services");
const ReferralServices = require("../referral-services");
const ReferralSyncServices = require("./referral-services");
const SubscriptionInvoiceServices = require("../subscription-invoices-services");
const { getCurrentAtlanticTime } = require("../../helper/custom-helper");
const ElasticSearch = require("../../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const axios = require('axios');

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const AddressTestServices = require("./address-sync-services");
const InternetTestSyncServices = require("./internet-sync-services");
const ShippingOrderTestServices = require("./shipping-order-services");
const OrderTestServices = require("./order-services");
const TvPhoneSyncTestServices = require("./tv-phone-sync-services");
const SubscriptionTestServices = require("./subscription-service");
const DeleteDataServices = require("../delete-unwanted-data");

const addressTestServices = new AddressTestServices();
const internetTestSyncServices = new InternetTestSyncServices();
const shippingOrderTestServices = new ShippingOrderTestServices();
const orderTestServices = new OrderTestServices();
const tvPhoneSyncTestServices = new TvPhoneSyncTestServices();
const subscriptionTestServices = new SubscriptionTestServices();
const deleteDataServices = new DeleteDataServices();

const INTERVAL = CONFIG.interval;

class ContactSyncServices {

    async getContactDetails(email = null) {
        const startTime = getCurrentAtlanticTime(null, "elasticSearch");
        const isEmailSync = !!email;
        const syncType = isEmailSync ? "email" : "cron";
        const messageStart = isEmailSync ? `Email: ${email} sync started` : "24-hour sync started";

        const logStartObj = {
            "log.level": "INFO",
            type: syncType,
            message: messageStart,
            email: email || undefined,
            startTime,
            timestamp: startTime,
            module: "contact_sync_master"
        };

        const { _id, _index } = await elasticSearch.insertDocument("sync_logs", logStartObj);

        let syncRecords = [];
        let syncResults = {
            totalContactsFound: 0,
            modulesExecuted: [],
            successCount: 0,
            errorCount: 0,
            errors: []
        };

        try {
            const query = isEmailSync
                ? `SELECT id, sf_record_id FROM contacts where email = '${email}'`
                : `SELECT id, sf_record_id FROM contacts`;

            const res = await this.executeQuery(query);
            syncResults.totalContactsFound = res.length;

            if (res.length > 0) {
                if (isEmailSync) {
                    // Email-specific sync with detailed logging
                    try {
                        const singleContactRecords = await this.checkAndManageSingleContacts(res);
                        if (singleContactRecords.length) {
                            syncRecords = syncRecords.concat(singleContactRecords);
                            syncResults.modulesExecuted.push({
                                module: "singleContacts",
                                recordCount: singleContactRecords.length,
                                status: "success"
                            });
                        }
                    } catch (error) {
                        syncResults.errors.push({ module: "singleContacts", error: error.message });
                        syncResults.errorCount++;
                    }

                    try {
                        const cardDetails = await this.getAndMapCardDetails(res[0]);
                        if (cardDetails.length) {
                            syncRecords = syncRecords.concat(cardDetails);
                            syncResults.modulesExecuted.push({
                                module: "cardDetails",
                                recordCount: cardDetails.length,
                                status: "success"
                            });
                        }
                    } catch (error) {
                        syncResults.errors.push({ module: "cardDetails", error: error.message });
                        syncResults.errorCount++;
                    }

                } else {
                    // Full cron sync with detailed logging for each module
                    const modules = [
                        { name: "contacts", func: () => this.checkAndManageContacts(res), isArray: true },
                        { name: "cardDetails", func: () => this.getAndMapCardDetails(), isArray: true },
                        { name: "addresses", func: () => addressTestServices.getAddressDetails(), isArray: false },
                        { name: "tvPhone", func: () => tvPhoneSyncTestServices.getTvPhoneDetails(), isArray: false },
                        { name: "internet", func: () => internetTestSyncServices.getInternetDetails(), isArray: false },
                        { name: "shippingOrders", func: () => shippingOrderTestServices.getShippingOrderDetails(), isArray: false },
                        { name: "orders", func: () => orderTestServices.getOrderDetails(), isArray: false },
                        { name: "techAppointments", func: () => orderTestServices.getTechAppDetails(), isArray: false },
                        { name: "subscriptions", func: () => subscriptionTestServices.getSubdDetails(), isArray: false },
                        { name: "deletedDetails", func: () => this.deleteDetails(), isArray: false }
                    ];

                    for (const module of modules) {
                        try {
                            const result = await module.func();

                            if (module.isArray && result.length) {
                                syncRecords = syncRecords.concat(result);
                                syncResults.modulesExecuted.push({
                                    module: module.name,
                                    recordCount: result.length,
                                    status: "success"
                                });
                                syncResults.successCount++;
                            } else if (!module.isArray && result.execute) {
                                syncRecords.push(result);
                                syncResults.modulesExecuted.push({
                                    module: module.name,
                                    recordCount: result.contactCount || result.deleteCounts || 1,
                                    status: result.status ? "success" : "error"
                                });
                                if (result.status) {
                                    syncResults.successCount++;
                                } else {
                                    syncResults.errorCount++;
                                    if (result.error) syncResults.errors.push({ module: module.name, error: result.error });
                                }
                            }
                        } catch (error) {
                            syncResults.errors.push({ module: module.name, error: error.message });
                            syncResults.errorCount++;
                        }
                    }
                }
            } else {
                syncResults.errors.push("No contacts found in database");
                syncResults.errorCount++;
            }

            const endMessage = isEmailSync ? `Email: ${email} sync ended` : "24-hour sync ended";
            const endTime = getCurrentAtlanticTime(null, "elasticSearch");

            const hasError = syncRecords.some(record => record.status === false) || syncResults.errorCount > 0;
            const logLevel = hasError ? "ERROR" : "INFO";

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, {
                    "log.level": logLevel,
                    message: endMessage,
                    endTime,
                    response: { syncRecords, syncResults }
                });
            }

        } catch (error) {
            const endTime = getCurrentAtlanticTime(null, "elasticSearch");
            const errorMessage = isEmailSync ? `Email: ${email} sync failed` : "24-hour sync failed";

            const logErrorObj = {
                "log.level": "ERROR",
                message: errorMessage,
                endTime,
                response: { syncRecords, syncResults },
                error: [{ error: error.message }]
            };

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, logErrorObj);
            }
        }
    }


    async deleteDetails() {
        let deleteCounts = {
            contactDeleteCount: 0,
            customerDeleteCount: 0,
            referralDeleteCount: 0,
            invoiceDeleteCount: 0,
        };

        try {
            const { status: contactStatus, data: contactData } = await this.getDeleteDataFromSF("Contact");
            if (contactStatus) {
                deleteCounts.contactDeleteCount = contactData?.length || 0;
                const contactServices = new ContactServices();
                contactServices.checkAndDeleteDetails(contactData);
            }

            const { status: customerStatus, data: customerData } = await this.getDeleteDataFromSF("Customer_Details__c");
            if (customerStatus) {
                deleteCounts.customerDeleteCount = customerData?.length || 0;
                const customerServices = new CustomerServices();
                customerServices.checkAndDeleteDetails(customerData);
            }

            const { status: referralStatus, data: referralData } = await this.getDeleteDataFromSF("Referral__c");
            if (referralStatus) {
                deleteCounts.referralDeleteCount = referralData?.length || 0;
                const referralServices = new ReferralServices();
                referralServices.checkAndDeleteDetails(referralData);
            }

            const { status: invoiceStatus, data: invoiceData } = await this.getDeleteDataFromSF("chargebeeapps__CB_Invoice__c");
            if (invoiceStatus) {
                deleteCounts.invoiceDeleteCount = invoiceData?.length || 0;
                const subscriptionInvoiceServices = new SubscriptionInvoiceServices();
                subscriptionInvoiceServices.checkAndDeleteDetails(invoiceData);
            }

            return { execute: true, synctype: "deleteDetails", status: true, deleteCounts };
        } catch (error) {
            console.error("Sync service delete details -> ", error);
            return { execute: false, synctype: "deleteDetails", status: false, deleteCounts, error: error?.message || null };
        }
    }

    async getDeleteDataFromSF(apiName) {
        const returnResponse = { status: false, data: null };
        try {
            const { deleteStatus, deletedData } = await salesforceConnection.getDeletedData(apiName);

            if (deleteStatus && Array.isArray(deletedData) && deletedData.length) {
                returnResponse.status = true;
                returnResponse.data = deletedData;
            }
            return returnResponse;

        } catch (error) {
            console.error("Sync service delete details -> ", error);
            return returnResponse;
        }
    }

    // Get contacts status whether exist in database or not  
    async checkAndManageContacts(sfContactDetails) {
        let syncRecords = [];
        try {
            let contactSfRecId = [];
            for (const sfContactDetail of sfContactDetails) contactSfRecId.push(sfContactDetail.sf_record_id);
            if (!contactSfRecId?.length) return;

            // Split contactSfRecId into chunks of 2000 elements
            const chunkSize = 500;
            const chunks = [];
            for (let i = 0; i < contactSfRecId.length; i += chunkSize) {
                chunks.push(contactSfRecId.slice(i, i + chunkSize));
            }

            // Loop through chunks and query Salesforce in chunks of 2000
            for (const chunk of chunks) {

                let idsFormatted = chunk.map(id => `'${id}'`).join(",");

                let query = `SELECT Id, FirstName, LastName, Email, Company_Name__c, Primary_Phone__c, Alternate_Phone_Number__c, Contact_Name_if_different_than_end_user__c, Sticky_Sender__c, Name, Referral_Link_Full__c, Total_Referrals__c, Total_Paid_Referrals__c, Total_Earned_Referrals__c, CreatedDate, AccountName__c, LastModifiedDate, Account_Chargebee_Customer_ID__c  FROM Contact WHERE Id IN (${idsFormatted})`;

                if (INTERVAL === "regular") query += " AND LastModifiedDate >= YESTERDAY";
                query += " ORDER BY LastModifiedDate DESC";

                const contactData = await salesforceConnection.getBulkDetails(query);
                syncRecords.push({ synctype: "getBulkDetails", status: true, contactDataCount: contactData?.length || 0 })

                if (contactData?.length) {
                    const contactServices = new ContactServices();
                    await contactServices.checkAndManageContacts(contactData);
                }

                const customerTestServices = new CustomerTestServices();
                const customerDetailsRes = await customerTestServices.syncCustomerDetails(idsFormatted);
                if (customerDetailsRes?.execute) syncRecords.push({ synctype: "syncCustomerDetails", ...customerDetailsRes })

                const referralSyncServices = new ReferralSyncServices();
                const referralDetailsRes = await referralSyncServices.getReferralDetails(idsFormatted);
                if (referralDetailsRes?.execute) syncRecords.push({ synctype: "getReferralDetails", ...referralDetailsRes })
            }

            return syncRecords;
        } catch (error) {
            console.error("SalesforceService 100 check And Manage Contacts -> ", error);
            syncRecords.push({ synctype: "checkAndManageContacts", execute: true, status: false, error: error?.message || null })
            return syncRecords;
        }
    }

    async updateContacts(updateQuery) {
        try {
            const {
                id,
                contactData: {
                    Id,
                    FirstName,
                    LastName,
                    Company_Name__c,
                    Primary_Phone__c,
                    Alternate_Phone_Number__c,
                    Contact_Name_if_different_than_end_user__c,
                    Sticky_Sender__c,
                    Name,
                    Referral_Link_Full__c,
                    Account_Chargebee_Customer_ID__c,
                    Total_Referrals__c,
                    Total_Paid_Referrals__c,
                    Total_Earned_Referrals__c,
                    LastModifiedDate
                }
            } = updateQuery;

            // // Prepare values
            const firstName = this.cleanValue(FirstName, 'cleanName');
            const sf_record_id = this.cleanValue(Id, 'cleanName');
            const lastName = this.cleanValue(LastName, 'cleanName');
            const companyName = this.cleanValue(Company_Name__c, 'cleanName');
            const cellPhone = this.cleanValue(Primary_Phone__c, 'cleanPhone');
            const secondaryPhone = this.cleanValue(Alternate_Phone_Number__c, 'cleanPhone');
            const additionalName = this.cleanValue(Contact_Name_if_different_than_end_user__c);
            const sticky_sender = this.cleanValue(Sticky_Sender__c);
            const sf_name = this.cleanValue(Name);
            const referralLink = this.cleanValue(Referral_Link_Full__c);
            const cbCustomerId = this.cleanValue(Account_Chargebee_Customer_ID__c);
            const totalReferral = this.cleanValue(Total_Referrals__c, 'cleanPrice');
            const totalPaidReferral = this.cleanValue(Total_Paid_Referrals__c, 'cleanPrice');
            const totalEarnedReferral = this.cleanValue(Total_Earned_Referrals__c, 'cleanPrice');
            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const sfUpdatedAt = this.cleanValue(formatedDate);
            const updatedTime = this.cleanValue(getCurrentAtlanticTime());

            const updateStatement = `
                        UPDATE contacts
                        SET 
                            updatedAt = ${updatedTime},
                            first_name = ${firstName},
                            last_name = ${lastName},
                            company_name = ${companyName},
                            cell_phone = ${cellPhone},
                            secondary_phone = ${secondaryPhone},
                            additional_name = ${additionalName},
                            sticky_sender = ${sticky_sender},
                            sf_name = ${sf_name},
                            referral_link = ${referralLink},
                            cb_customer_id = ${cbCustomerId},
                            total_referral = ${totalReferral},
                            total_paid_referral = ${totalPaidReferral},
                            total_earned_referral = ${totalEarnedReferral},
                            sf_updatedAt = ${sfUpdatedAt}
                        WHERE sf_record_id = ${sf_record_id};
                    `;

            await this.executeQuery(updateStatement);
            return { execute: true, status: true, sf_record_id: updateQuery?.contactData?.Id || null }
        } catch (error) {
            console.error("SalesforceService bulkUpdateContacts -> ", error);
            return { execute: true, status: false, sf_record_id: updateQuery?.contactData?.Id || null, error }
        }
    }

    cleanValue(value, type) {
        if (value === undefined || value === null) return 'NULL';

        if (type === "cleanPhone") value = value.replace(/\D/g, '');
        if (type === "cleanPrice") value = isNaN(value) ? 0 : value;

        // Always escape single quotes for SQL safety
        if (typeof value === 'string') {
            value = value.replace(/'/g, "''");
        }

        // Optionally, still remove double quotes and backticks for cleanName
        if (type === "cleanName" && typeof value === 'string') {
            value = value.replace(/["`]/g, '');
        }

        return typeof value === 'string' ? (value === '' ? null : `'${value}'`) : value;
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async checkAndManageSingleContacts(sfContactDetails) {
        let syncRecords = [];

        try {
            const selectedObj = { Id: 1, FirstName: 1, LastName: 1, Email: 1, Company_Name__c: 1, Primary_Phone__c: 1, Alternate_Phone_Number__c: 1, Contact_Name_if_different_than_end_user__c: 1, Referral_Link_Full__c: 1, Account_Chargebee_Customer_ID__c: 1, Total_Referrals__c: 1, Total_Paid_Referrals__c: 1, Total_Earned_Referrals__c: 1, AccountName__c: 1, LastModifiedDate: 1, CreatedDate: 1, Sticky_Sender__c: 1, Name: 1 };
            for (const sfContactDetail of sfContactDetails) {
                const { id, sf_record_id } = sfContactDetail;
                const { returnStatus: cntRetSTatus, addresstData: contactData } = await salesforceConnection.getAddressDetailsById(sf_record_id, "Contact", selectedObj);

                if (cntRetSTatus && contactData) {
                    const updateContactsRes = await this.updateContacts({ id, contactData });
                    if (updateContactsRes?.execute) syncRecords.push({ synctype: "updateContacts", ...updateContactsRes })

                    const idCon = `'${sf_record_id}'`;

                    const customerTestServices = new CustomerTestServices();
                    const customerTestServicesRes = await customerTestServices.syncCustomerDetails(idCon);
                    if (customerTestServicesRes?.execute) syncRecords.push({ synctype: "syncCustomerDetails", ...customerTestServicesRes })

                    // Referrals
                    const refSelectedObj = { Id: 1, Contact__c: 1, Referral__c: 1, Referrals_Live_Date__c: 1, Credit_Added_to_Account__c: 1, Credit_Amount__c: 1, CreatedDate: 1, LastModifiedDate: 1, Referrals_Name__c: 1 };

                    const searchField = { Contact__c: sf_record_id };

                    const { returnStatus, salesforceData } = await salesforceConnection.getDetailsByFieldAndApi(searchField, "Referral__c", refSelectedObj, "interval");

                    if (returnStatus) {
                        const referralServices = new ReferralServices();
                        const referralServicesManageDetailsRes = referralServices.checkAndManageDetails(salesforceData);
                        if (referralServicesManageDetailsRes?.execute) syncRecords.push({ synctype: "referralServicesManageDetails", ...referralServicesManageDetailsRes })
                    }
                } else {
                    const contactServices = new ContactServices();
                    const deleteOtherContactDetails = await contactServices.deleteOtherContactDetails(id);
                    if (deleteOtherContactDetails?.execute) syncRecords.push({ synctype: "deleteOtherContactDetails", ...deleteOtherContactDetails })
                }
            }

            return syncRecords
        } catch (error) {
            console.error("SalesforceService Single Contacts Sync-> ", error);
            syncRecords.push({ synctype: "checkAndManageSingleContacts", status: false, error: error?.message || null })
            return syncRecords
        }
    }

    async getAndMapCardDetails(contactDetail) {
        let syncRecords = [];
        try {
            let baseUrl;

            if (CONFIG.NODE_ENV == "local") baseUrl = 'http://localhost:5009';
            else if (CONFIG.LOG_PREFIX == "dev") baseUrl = 'http://purplecow-dev-env.eba-fvxdwgfv.ca-central-1.elasticbeanstalk.com';
            else if (CONFIG.LOG_PREFIX == "prod") baseUrl = 'http://customer-portal-prod-env.eba-xicfwcbs.ca-central-1.elasticbeanstalk.com';

            if (!baseUrl) return;

            let url = `${baseUrl}/auth/map-subscription`;

            if (contactDetail) {
                const { id } = contactDetail;
                if (id) url = `${url}?id=${id}`;
            }

            const response = await axios.get(url, {
                auth: {
                    username: CONFIG.RESGISTRATION_USERNAME,
                    password: CONFIG.RESGISTRATION_PASSWORD,
                },
            });
            syncRecords.push({ synctype: "getAndMapCardDetails", status: true, contactID: contactDetail?.id || null })
            return syncRecords
        } catch (error) {
            console.error('Get And Map Card Details Sync:', error);
            syncRecords.push({ synctype: "getAndMapCardDetails", status: false, contactID: contactDetail?.id || null })
            return syncRecords
        }
    }
}

module.exports = ContactSyncServices;
