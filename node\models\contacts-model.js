const Sequelize = require('sequelize');
module.exports = (sequelize) => {
    return sequelize.define("contact", {
        id: {
            type: Sequelize.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: "Primary key, auto-incrementing identifier."
        },
        sf_record_id: {
            type: Sequelize.STRING(18),
            allowNull: false,
            comment: "Salesforce record ID"
        },
        cb_customer_id: {
            type: Sequelize.STRING(250),
            comment: "Chargebee customer ID"
        },
        aws_cognito_id: {
            type: Sequelize.STRING(40),
            comment: "AWS cognito customer ID"
        },
        sf_name: {
            type: Sequelize.STRING(50),
            allowNull: true,
            comment: "Associated with Name field of Contact in sf."
        },
        first_name: {
            type: Sequelize.STRING(50),
            allowNull: true,
            comment: "Associated with FirstName field of Contact in sf."
        },
        last_name: {
            type: Sequelize.STRING(50),
            allowNull: true,
            comment: "Associated with LastName field of Contact in sf."
        },
        email: {
            type: Sequelize.STRING(96),
            allowNull: false,
            comment: "Associated with Email field of Contact in sf."
        },
        cell_phone: {
            type: Sequelize.STRING(15),
            allowNull: true,
            comment: "Associated with Primary_Phone__c field of Contact in sf."
        },
        secondary_phone: {
            type: Sequelize.STRING(15),
            allowNull: true,
            comment: "Associated with Alternate_Phone_Number__c field of Contact in sf."
        },
        sf_updatedAt: {
            type: Sequelize.DATE,
            comment: "Associated with LastModifiedDate field of Contact in sf."
        },
        additional_name: {
            type: Sequelize.STRING(50),
            allowNull: true,
            comment: "Associated with Contact_Name_if_different_than_end_user__c field of Contact in sf."
        },
        sticky_sender: {
            type: Sequelize.STRING(100),
            allowNull: true,
            comment: "Associated with Sticky_Sender__c field of Contact in sf."
        },
        company_name: {
            type: Sequelize.STRING(50),
            allowNull: true,
            comment: "Associated with Company_Name__c field of Contact in sf."
        },
        image_url: {
            type: Sequelize.STRING(250),
            allowNull: true,
            comment: "Associated with the s3 url."
        },
        referral_link: {
            type: Sequelize.STRING(250),
            allowNull: true,
            comment: "Associated with Referral_Link_Full__c field of Contact in sf."
        },
        total_referral: {
            type: Sequelize.INTEGER(10),
            comment: "Associated with Total_Referrals__c field of Contact in sf."
        },
        total_paid_referral: {
            type: Sequelize.INTEGER(10),
            comment: "Associated with Total_Paid_Referrals__c field of Contact in sf."
        },
        total_earned_referral: {
            type: Sequelize.FLOAT(16, 2),
            comment: "Associated with Total_Earned_Referrals__c field of Contact in sf."
        },
        status: {
            type: Sequelize.ENUM('inactive', 'active'),
            defaultValue: 'inactive',
            comment: "Initially the value was inactive"
        },
        createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was created in the database."
        },
        updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was last updated in the database."
        }
    },
        {
            collate: 'utf8mb4_unicode_ci',
            timestamps: true,
            indexes: [
                {
                    name: "SalesforceContactIndex",
                    unique: true,
                    fields: ['sf_record_id']
                }, {
                    //     name: "ChargebeeCustomerIndex",
                    //     unique: true,
                    //     fields: ['cb_customer_id']
                    // }, {
                    name: "UniqueEmailIndex",
                    unique: true,
                    fields: ['email']
                }, {
                    name: "UniqueAwsCognitoIndex",
                    unique: true,
                    fields: ['aws_cognito_id']
                }
            ]
        });
}