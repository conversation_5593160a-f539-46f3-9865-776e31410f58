import React from "react";
import { ConfirmationMessageProps } from "../../typings/typing";
import Button from "./Button";
import Popup from "./Popup";
import useMediaQuery from "../../hooks/MediaQueryHook";
const ConfirmationMessagePopup: React.FC<ConfirmationMessageProps> = ({
  title,
  message,
  closeHandler,
  handleSubmit,
  isLoading,
  btnText = "Submit",
}) => {
  const isDesktop = useMediaQuery("(min-width:1300px");
  return (
    <div>
      <Popup
        title={title}
        closeHandler={() => {
          if (!isLoading) {
            closeHandler();
          }
        }}
        width={isDesktop ? "700px" : "550px"}
        height="501px"
      >
        <div className="flex flex-col gap-10">
          <div className="text-base">
            {Array.isArray(message) ? (
              message.map((text, index) => {
                return (
                  <p key={index} dangerouslySetInnerHTML={{ __html: text }}></p>
                );
              })
            ) : (
              <p dangerouslySetInnerHTML={{ __html: message }}></p>
            )}
          </div>
          <div className="flex gap-5 md:flex-row flex-col">
            <div className="basis-full">
              <Button
                title="Go back"
                btnType="transparent"
                type="button"
                className="disabled:opacity-50"
                attributes={{
                  disabled: isLoading,
                }}
                clickEvent={closeHandler}
              />
            </div>
            <div className="basis-full">
              <Button
                title={btnText}
                clickEvent={() => {
                  if (!isLoading) {
                    handleSubmit();
                  }
                }}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default ConfirmationMessagePopup;
