const request = require('supertest');
const express = require('express');
const cardRoute = require('../../routes/card-route');

// Mock the card controller
jest.mock('../../controllers/card-controller', () => ({
  fetchCard: jest.fn((req, res) => res.status(200).json({ 
    message: 'Cards retrieved successfully', 
    data: {
      cards: [
        {
          id: 'card_12345',
          last4: '4242',
          brand: 'visa',
          expMonth: 12,
          expYear: 2025,
          isDefault: true,
          holderName: '<PERSON>'
        },
        {
          id: 'card_67890',
          last4: '1234',
          brand: 'mastercard',
          expMonth: 6,
          expYear: 2026,
          isDefault: false,
          holderName: '<PERSON>'
        }
      ]
    }
  })),
  addCard: jest.fn((req, res) => res.status(201).json({ 
    message: 'Card added successfully',
    data: {
      cardId: 'card_new123',
      last4: '5678',
      brand: 'visa',
      expMonth: 3,
      expYear: 2027,
      isDefault: false
    }
  })),
  updateCard: jest.fn((req, res) => res.status(200).json({ 
    message: 'Card updated successfully',
    data: {
      cardId: 'card_12345',
      isDefault: true,
      holderName: 'John Updated Doe'
    }
  })),
  deleteCard: jest.fn((req, res) => res.status(200).json({ 
    message: 'Card deleted successfully',
    data: {
      cardId: 'card_67890',
      deletedAt: '2024-01-18T10:30:00Z'
    }
  }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  cardValidation: {}
}));

describe('Card Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    cardRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/card/', () => {
    it('should fetch cards successfully', async () => {
      const response = await request(app)
        .get('/api/v1/card/')
        .expect(200);

      expect(response.body.message).toBe('Cards retrieved successfully');
      expect(response.body.data).toHaveProperty('cards');
      expect(Array.isArray(response.body.data.cards)).toBe(true);
      expect(response.body.data.cards.length).toBeGreaterThan(0);
      
      // Verify card object structure
      const firstCard = response.body.data.cards[0];
      expect(firstCard).toHaveProperty('id');
      expect(firstCard).toHaveProperty('last4');
      expect(firstCard).toHaveProperty('brand');
      expect(firstCard).toHaveProperty('expMonth');
      expect(firstCard).toHaveProperty('expYear');
      expect(firstCard).toHaveProperty('isDefault');
      expect(firstCard).toHaveProperty('holderName');
      
      // Verify data types
      expect(typeof firstCard.id).toBe('string');
      expect(typeof firstCard.last4).toBe('string');
      expect(typeof firstCard.brand).toBe('string');
      expect(typeof firstCard.expMonth).toBe('number');
      expect(typeof firstCard.expYear).toBe('number');
      expect(typeof firstCard.isDefault).toBe('boolean');
      expect(typeof firstCard.holderName).toBe('string');
    });

    it('should handle fetch cards request with query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/card/')
        .query({ customerId: '12345', includeExpired: 'false' })
        .expect(200);

      expect(response.body.message).toBe('Cards retrieved successfully');
    });

    it('should handle fetch cards request with authentication headers', async () => {
      const response = await request(app)
        .get('/api/v1/card/')
        .set('Authorization', 'Bearer fake-jwt-token')
        .expect(200);

      expect(response.body.message).toBe('Cards retrieved successfully');
    });
  });

  describe('POST /api/v1/card/', () => {
    it('should add card successfully', async () => {
      const cardData = {
        cardNumber: '****************',
        expMonth: 3,
        expYear: 2027,
        cvc: '123',
        holderName: 'John Doe',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/card/')
        .send(cardData)
        .expect(201);

      expect(response.body.message).toBe('Card added successfully');
      expect(response.body.data).toHaveProperty('cardId');
      expect(response.body.data).toHaveProperty('last4');
      expect(response.body.data).toHaveProperty('brand');
      expect(response.body.data).toHaveProperty('expMonth');
      expect(response.body.data).toHaveProperty('expYear');
      expect(response.body.data).toHaveProperty('isDefault');
    });

    it('should handle add card with minimal required data', async () => {
      const minimalCardData = {
        cardNumber: '****************',
        expMonth: 12,
        expYear: 2025,
        cvc: '456',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/card/')
        .send(minimalCardData)
        .expect(201);

      expect(response.body.message).toBe('Card added successfully');
    });

    it('should handle add card with invalid data', async () => {
      const invalidCardData = {
        cardNumber: '1234', // Invalid card number
        expMonth: 13, // Invalid month
        expYear: 2020, // Expired year
        customerId: '12345'
      };

      await request(app)
        .post('/api/v1/card/')
        .send(invalidCardData)
        .expect(201); // Mocked to return success
    });
  });

  describe('PUT /api/v1/card/', () => {
    it('should update card successfully', async () => {
      const updateData = {
        cardId: 'card_12345',
        holderName: 'John Updated Doe',
        isDefault: true,
        customerId: '12345'
      };

      const response = await request(app)
        .put('/api/v1/card/')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Card updated successfully');
      expect(response.body.data).toHaveProperty('cardId');
      expect(response.body.data).toHaveProperty('isDefault');
      expect(response.body.data).toHaveProperty('holderName');
    });

    it('should handle update card with partial data', async () => {
      const partialUpdateData = {
        cardId: 'card_12345',
        isDefault: false
      };

      const response = await request(app)
        .put('/api/v1/card/')
        .send(partialUpdateData)
        .expect(200);

      expect(response.body.message).toBe('Card updated successfully');
    });

    it('should handle update card with missing card ID', async () => {
      const invalidUpdateData = {
        holderName: 'John Doe',
        isDefault: true
      };

      await request(app)
        .put('/api/v1/card/')
        .send(invalidUpdateData)
        .expect(200); // Mocked to return success
    });
  });

  describe('DELETE /api/v1/card/:cardId', () => {
    it('should delete card successfully', async () => {
      const cardId = 'card_67890';

      const response = await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .expect(200);

      expect(response.body.message).toBe('Card deleted successfully');
      expect(response.body.data).toHaveProperty('cardId');
      expect(response.body.data).toHaveProperty('deletedAt');
      expect(response.body.data.cardId).toBe(cardId);
    });

    it('should handle delete card with different card ID', async () => {
      const cardId = 'card_99999';

      const response = await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .expect(200);

      expect(response.body.message).toBe('Card deleted successfully');
    });

    it('should handle delete card with invalid card ID', async () => {
      const invalidCardId = 'invalid-card-id';

      await request(app)
        .delete(`/api/v1/card/${invalidCardId}`)
        .expect(200); // Mocked to return success
    });

    it('should handle delete card with authentication headers', async () => {
      const cardId = 'card_12345';

      const response = await request(app)
        .delete(`/api/v1/card/${cardId}`)
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Customer-ID', '12345')
        .expect(200);

      expect(response.body.message).toBe('Card deleted successfully');
    });
  });
});
