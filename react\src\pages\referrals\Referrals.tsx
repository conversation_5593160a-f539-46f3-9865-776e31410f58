import React, { useEffect, useRef, useState } from "react";
import { useReferralDetailsMutation } from "../../services/api";
import ReferralHistory from "../../components/referrals/ReferralHistory";
import ReferralLink from "../../components/referrals/ReferralLink";
import ReferralLinkSkeleton from "./skeletons/ReferralLinkSkeleton";
import TotalCard from "../../components/referrals/TotalCard";
import TotalCardSkeleton from "./skeletons/TotalCardSkeleton";
import { addNotification } from "../../store/reducers/toasterReducer";
import { formatCurrency } from "../../utils/helper";
import { logout } from "../../store/reducers/authenticationReducer";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import useMediaQuery from "../../hooks/MediaQueryHook";
import {
  LineIcon,
  TotalEarnedReferralsIcon,
  TotalPaidReferralsIcon,
  TotalReferralsIcon,
} from "../../assets/Icons";
import { ReferralDetails } from "../../typings/typing";

const Referrals: React.FC = () => {
  const [referralDetails, setReferralDetails] =
    useState<ReferralDetails | null>(null);

  const [swipeReferrals, setSwipereferrals] = useState<boolean>(false);
  const viewReferralsRef = useRef<HTMLDivElement>(null);
  const [startY, setStartY] = useState<number>(0);
  const [endY, setEndY] = useState<number>(0);
  const isDesktop = useMediaQuery("(min-width:1025px)");

  useEffect(() => {
    let load = true;
    setTimeout(() => {
      load = false;
    }, 500);

    // Function to handle click outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        viewReferralsRef.current &&
        !viewReferralsRef.current.contains(event.target as Node) &&
        viewReferralsRef
      ) {
        !load && setSwipereferrals(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [swipeReferrals]);

  // Handle touch start
  const handleTouchStart = (event: React.TouchEvent<HTMLDivElement>) => {
    setStartY(event.touches[0].clientY);
  };

  // Handle touch move
  const handleTouchMove = (event: React.TouchEvent<HTMLDivElement>) => {
    setEndY(event.touches[0].clientY);
  };

  // Handle touch end
  const handleTouchEnd = () => {
    const swipeDistance = startY - endY;
    if (swipeDistance > 10 && !swipeReferrals) {
      // Swipe up
      setSwipereferrals(true);
    } else if (swipeDistance < -10 || swipeReferrals) {
      // Swipe down
      setSwipereferrals(false);
    }
  };

  const [getReferralDetails, { isLoading: referralDetailsLoading }] =
    useReferralDetailsMutation();

  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch referral details on component mount
  useEffect(() => {
    handleGetReferralDetails();
  }, []);

  // Function to fetch referral details
  const handleGetReferralDetails = async () => {
    try {
      const response = await getReferralDetails({}).unwrap();
      if (response.status === 200) {
        setReferralDetails(response.data);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  return (
    <div className="referrals-container lg:pb-8">
      <div className="flex  gap-5 flex-col">
        <div
          className="
    grid 
    grid-cols-1          /* Mobile: 1 column */
    md:grid-cols-2       /* Tablet: 2 columns */
    lg:grid-cols-3       /* Large: 3 columns */
    gap-2.5 
    2xl:gap-5
  "
        >
          <div>
            {referralDetailsLoading ? (
              <TotalCardSkeleton />
            ) : (
              <TotalCard
                cardType="REFERRALS"
                value={referralDetails?.total_referrals ?? 0}
                icon={<TotalReferralsIcon />}
              />
            )}
          </div>

          <div>
            {referralDetailsLoading ? (
              <TotalCardSkeleton />
            ) : (
              <TotalCard
                cardType="PAID REFERRALS"
                value={referralDetails?.total_paid_referrals ?? 0}
                icon={<TotalPaidReferralsIcon />}
              />
            )}
          </div>

          <div className="md:col-span-2 lg:col-span-1">
            {referralDetailsLoading ? (
              <TotalCardSkeleton />
            ) : (
              <TotalCard
                cardType="EARNED"
                value={formatCurrency(
                  referralDetails?.total_earned
                    ? referralDetails?.total_earned
                    : 0.0,
                  true
                )}
                icon={<TotalEarnedReferralsIcon />}
              />
            )}
          </div>
        </div>

        <div className="max-lg:pb-[120px]">
          {referralDetailsLoading ? (
            <ReferralLinkSkeleton />
          ) : (
            <ReferralLink link={referralDetails?.referral_link ?? ""} />
          )}
        </div>
        <div>
          <div className="flex flex-col gap-5">
            {isDesktop && (
              <div>
                <p className="font-medium uppercase">Referrals</p>
              </div>
            )}
            <div
              ref={viewReferralsRef}
              className={`view-referral bg-white rounded-20 gap-5 lg:relative max-lg:pb-30 lg:px-5 px-2.5 shadow-cardShadow07 ${
                swipeReferrals && !isDesktop && "active popup-open"
              } ${isDesktop && "py-30"}`}
            >
              {!isDesktop && (
                <div
                  onTouchStart={handleTouchStart}
                  onTouchMove={handleTouchMove}
                  onTouchEnd={handleTouchEnd}
                  className="flex flex-col text-center justify-center items-center gap-5 pb-5 pt-30 cursor-pointer"
                >
                  <div>
                    <LineIcon />
                  </div>
                  <div className="font-medium">REFERRALS</div>
                </div>
              )}

              <ReferralHistory />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Referrals;
