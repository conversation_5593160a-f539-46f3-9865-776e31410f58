const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const jwt = require('jsonwebtoken');
const db = require("../models/index.js");
const CONFIG = require("./../config");
const { decodeAccessToken } = require("../helpers/privacyAlgorithms");
const CryptoJS = require("crypto-js");

// Middleware to authenticate user's access token
exports.authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const accessToken = authHeader && authHeader.split(' ')[1];
    if (!accessToken) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);

    const decodedToken = await this.decodeToken(accessToken);

    const currentTime = Math.floor(Date.now() / 1000);

    if (!decodedToken) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);

    // Check if the token is expired by comparing the expiration timestamp with the current time
    if (decodedToken?.exp && decodedToken?.exp < currentTime) throw new CustomError(RESPONSE_CODES.ACCESS_NOT, "Your session has expired. Please log in again.");

    const checkUserExist = await db.Contacts.findOne({
      where: { aws_cognito_id: decodedToken?.username },
      attributes: {
        exclude: ['updatedAt'],
        include: [
          [
            db.sequelize.literal('CONCAT(first_name, " ", last_name)'),
            'full_name'
          ]
        ]
      },
      raw: true // To get raw JSON data
    });

    if (!checkUserExist) throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);

    let elasticLog = {}
    elasticLog.url = `${req?.originalUrl}`;
    elasticLog.email = checkUserExist?.email;
    elasticLog.aws_cognito_id = checkUserExist?.aws_cognito_id;
    // Attach user details to request object
    req.userDetails = checkUserExist;
    req.elasticLogObj = { userinfo: { ...elasticLog } };

   // Conditionally decrypt payload if needed
   if (!(req.originalUrl === "/api/v1/customer" && req.method === "PUT") && req?.body?.payload) {
    req.body = { ...this.decryptData(req.body.payload) };
  }
    next();
  } catch (error) {
    next(error);
  }
};

// Middleware to authenticate registration token
exports.authenticateRegistrationToken = async (req, res, next) => {
  let elasticLog = {}
  elasticLog.url = `${req?.originalUrl}`;
  req.elasticLogObj = { userinfo: { ...elasticLog } };
  try {
    const authHeader = req.headers['authorization']
    const token = authHeader && authHeader.split(' ')[1];

    // Check if registration token exists and matches configured token
    if (!token || token !== CONFIG.RESGISTRATION_TOKEN) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);
    next();
  } catch (error) {
    next(error);
  }
};

exports.authenticateWebhook = (req, res, next) => {
  // Extract the Authorization header
  const authHeader = req.headers['authorization'];

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    return next(new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED));
  }

  // Extract the Base64 encoded credentials
  const base64Credentials = authHeader.split(' ')[1];
  const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
  const [username, password] = credentials.split(':');

  try {
    // Check if the provided username and password match the static values
    if (username === CONFIG.chargebee.webhookUsername && password === CONFIG.chargebee.webhookPassword) {
      return next();
    } else {
      throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);
    }
  } catch (error) {
    next(error);
  }
};

exports.authenticateRegistrationTokenV1 = (req, res, next) => {
  // Extract the Authorization header
  const authHeader = req.headers['authorization'];

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    return next(new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED));
  }

  const base64Credentials = authHeader.split(' ')[1];
  const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
  const [username, password] = credentials.split(':');

  let elasticLog = {}
  elasticLog.url = `${req?.originalUrl}`;
  req.elasticLogObj = { userinfo: { ...elasticLog } };

  try {
    if (username === CONFIG.RESGISTRATION_USERNAME && password === CONFIG.RESGISTRATION_PASSWORD) {
      return next();
    } else {
      throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);
    }
  } catch (error) {
    next(error);
  }
};

exports.decodeToken = async (accessToken, type) => {
  try {
    const firstLevelDecoded = decodeAccessToken(CONFIG.RESGISTRATION_TOKEN, accessToken);
    if (!firstLevelDecoded) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);

    const awsAccessToken = firstLevelDecoded?.data;
    if (!awsAccessToken) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, RESPONSE_MESSAGES.UNAUTHORIZED);

    return !type ? jwt.decode(awsAccessToken) : awsAccessToken;
  } catch (error) {
    throw error;
  }
}

exports.decryptData = (encryptedData) => {
  if (encryptedData === null || encryptedData === undefined) return encryptedData
  const bytes = CryptoJS.AES.decrypt(encryptedData, CONFIG.CRYPTO_SECRET);
  const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(decryptedData);
};

// Middleware to decode payload
exports.decodeBodyPayload = (req, res, next) => {
  try {
    if (req?.body?.payload) {
      const body = this.decryptData(req?.body?.payload);
      req.body = { ...body }; // merge the decrypted payload with the original request body
    }
    next();
  } catch (error) {
    next(error);
  }
};