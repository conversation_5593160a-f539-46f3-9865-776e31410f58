const Sequelize = require('sequelize');

module.exports = (sequelize) => {
    return sequelize.define("customer_promotional_receipt", {
        id: {
            type: Sequelize.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: "Primary key, auto-incrementing identifier."
        },
        promotion_id: {
            type: Sequelize.INTEGER(11),
            references: {
                model: 'promotions',
                key: 'id'
            },
            comment: "Foreign key mapping to the primary key 'id' in the 'promotions' table, with cascading delete functionality.",
            onDelete: 'CASCADE'
        },
        customer_details_id: {
            type: Sequelize.INTEGER(11),
            references: {
                model: 'contacts_customer_details',
                key: 'id'
            },
            comment: "Foreign key mapping to the primary key 'id' in the 'contacts_customer_details' table, with cascading delete functionality.",
            onDelete: 'CASCADE'
        },
        sf_record_id: {
            type: Sequelize.STRING(18),
            unique: 'sf_record_id',
            allowNull: false,
            comment: "Salesforce record ID"
        },
        promotional_offer_name: {
            type: Sequelize.STRING(30),
            comment: "Associated with Name field of Promotional_Offer__c in sf."
        },
        redeemed_date: {
            type: Sequelize.DATE,
            allowNull: true,
            defaultValue: null,
            comment: 'Associated with Redeemed_DateTime__c field of Promotional_Offer__c in sf.'
        },
        sf_updatedAt: {
            type: Sequelize.DATE,
            comment: "Associated with LastModifiedDate field of Promotional_Offer__c in sf."
        },
        createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was created in the database."
        },
        updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was last updated in the database."
        }
    },
        {
            collate: 'utf8mb4_unicode_ci',
            timestamps: true
        });
}