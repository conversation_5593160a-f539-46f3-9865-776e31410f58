const { getCurrentAtlanticTime } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
class ReferralWebhookServices {
    async getReferrals(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get referral details -> ", error);
        }
    }

    async checkAndManageDetails(referralDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Contact__c } = referralDetail;

            if (!Contact__c) return;

            const { dataExist: contactExist, dataId: contactId } = await this.checkExist(Contact__c, "contacts");
            if (!contactExist) return;

            const { dataExist: refExist, dataId: refData } = await this.checkExist(sf_record_id, "contacts_referrals");
            let type;
            if (refExist) {
                type = "UPDATE";
                await this.updateRefDetails(referralDetail, refData);
            } else {
                type = "CREATE";
                await this.createNewRefDetails(referralDetail, contactId);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateRefDetails(referralDetail, contactId) {
        try {
            const { Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, LastModifiedDate, Referrals_Name__c: referral_name, CreatedDate } = referralDetail;

            const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            const { sf_record_id: refSfRecId } = contactId;
            let queryParams = [];

            let query = `UPDATE contacts_referrals SET  referred_amount = ?, referral_name = ?, sf_updatedAt = ?, createdAt = ?, updatedAt = ?`;
            queryParams.push(referred_amount, referral_name, sf_updatedAt, createdAt, updatedTime);

            query += `, installed_on = ?`;
            if (installed_on) queryParams.push(installed_on);
            else queryParams.push(null);

            query += `, credit_added_on = ?`;
            if (credit_added_on) queryParams.push(credit_added_on);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(refSfRecId);

            await this.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService updateRefDetails -> ", error);
        }
    }

    async createNewRefDetails(referralDetail, contactId) {
        try {
            const { Id: sf_record_id, Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, CreatedDate, LastModifiedDate, Referrals_Name__c: referral_name, Name: sf_name } = referralDetail;

            const { id: contact_id } = contactId;
            let queryParams = [];

            const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let query = `INSERT IGNORE INTO contacts_referrals (contact_id, sf_name, sf_record_id, referral_name, referred_amount, createdAt, sf_updatedAt, updatedAt`;
            queryParams = [contact_id, sf_name, sf_record_id, referral_name, referred_amount, createdAt, sf_updatedAt, updatedTime];
            if (installed_on) {
                query += `, installed_on`;
                queryParams.push(installed_on);
            }

            if (credit_added_on) {
                query += `, credit_added_on`;
                queryParams.push(credit_added_on);
            }

            query += `) VALUES (?, ?, ?, ?, ?, ?, ?, ?`;
            if (installed_on) query += `, ?`;
            if (credit_added_on) query += `, ?`;

            query += `)`;

            await this.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService createNewRefDetails -> ", error);
        }
    }

    async checkExist(sf_record_id, tableName) {
        try {

            let selectClause = `id, sf_record_id`;

            const query = `SELECT ${selectClause} FROM ${tableName} WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = ReferralWebhookServices;
