const { getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

const sanitizeUpdateValue = (value) => (value ? `${value}` : null);

class AddressWebhookServices {
    async getServiceMailingAddressDetails(webhookData, _id, _index, sfApiName) {
        try {
            let type;

            if (sfApiName == "Service_Address__c") type = "service";
            else if (sfApiName == "Mailing_Address__c") type = "mailing";

            if (type) await this.checkAndManageDetails(webhookData, _id, _index, type);
        } catch (error) {
            console.error("Sync service get address details -> ", error);
        }
    }

    async checkAndManageDetails(sfAddressDetails, _id, _index, addresstype) {
        try {
            if (!Array.isArray(sfAddressDetails)) sfAddressDetails = [sfAddressDetails];
            for (const sfAddressDetail of sfAddressDetails) {
                const { Id: sf_record_id } = sfAddressDetail;
                if (!sf_record_id) return;

                const { addressExist } = await this.checkAddressExist(sfAddressDetail, addresstype);

                let type;
                if (addressExist) {
                    type = "UPDATE";
                    await this.updateAddressDetails(sfAddressDetail, addresstype);
                } else {
                    // type = "CREATE";
                    // await this.createAddressDetails(sfAddressDetail, addresstype);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateAddressDetails(sfAddressDetail, type) {
        try {
            const adrsObj = this.getAddressDetails(sfAddressDetail, type);

            const { name, suit_unit, street_number, street_name, town, province, postal_code, full_address, country, status, sf_updatedAt, sf_record_id, createdSfAt } = adrsObj;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(createdSfAt);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
            UPDATE customer_details_addresses 
            SET name = ?, suit_unit = ?, street_number = ? , street_name = ?, town = ?, province = ?, postal_code = ?, full_address = ?, country = ?, status = ? , sf_updatedAt = ?, createdAt = ?, updatedAt = ?
            WHERE sf_record_id = ?`;

            const updateValues = [
                sanitizeUpdateValue(name),
                sanitizeUpdateValue(suit_unit),
                sanitizeUpdateValue(street_number),
                sanitizeUpdateValue(street_name),
                sanitizeUpdateValue(town),
                sanitizeUpdateValue(province),
                sanitizeUpdateValue(postal_code),
                sanitizeUpdateValue(full_address),
                sanitizeUpdateValue(country),
                sanitizeUpdateValue(status),
                formatedDate,
                createdAt,
                updatedTime,
                sf_record_id
            ]

            await this.executeQuery(updateQuery, updateValues);

        } catch (error) {
            console.error("Error updateaddressDetails -> ", error);
        }
    }

    async createAddressDetails(sfAddressDetail, type) {
        try {
            const adrsObj = this.getAddressDetails(sfAddressDetail, type);

            const { name, suit_unit, street_number, street_name, town, province, postal_code, full_address, country, status, sf_updatedAt, sf_record_id, createdSfAt } = adrsObj;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(createdSfAt);
            const updatedTime = getCurrentAtlanticTime();

            const query = `INSERT IGNORE INTO customer_details_addresses 
                            (address_type, sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt, createdAt, updatedAt)
                            VALUES (
                                ${sanitizeValue(type)}, 
                                ${sanitizeValue(sf_record_id)}, 
                                ${sanitizeValue(name)}, 
                                ${sanitizeValue(suit_unit)}, 
                                ${sanitizeValue(street_number)}, 
                                ${sanitizeValue(street_name)}, 
                                ${sanitizeValue(province)}, 
                                ${sanitizeValue(town)}, 
                                ${sanitizeValue(country)}, 
                                ${sanitizeValue(postal_code)}, 
                                ${sanitizeValue(full_address)}, 
                                ${sanitizeValue(status)},
                                '${formatedDate}', 
                                '${createdAt}', 
                                '${updatedTime}'
                            )`;

            await this.executeQuery(query);

        } catch (error) {
            console.error("SalesforceService createAddressDetails -> ", error);
        }
    }

    async checkAddressExist(sfAddressDetail, type) {
        try {
            const { Id: sf_record_id } = sfAddressDetail;
            const query = `SELECT id
                            FROM customer_details_addresses
                            where address_type = '${type}' AND
                            sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                addressExist: res.length > 0,
                addressId: res.length ? res[0].id : null
            };
        } catch (error) {
            console.error("Check Address Exist -> ", error);
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    getAddressDetails(sfAddressDetails, type) {
        if (type === 'mailing') {
            const {
                Id: sf_record_id,
                Name: name,
                Mailing_Suite_Unit__c: suit_unit,
                Mailing_Street_Number__c: street_number,
                Mailing_Street_Name__c: street_name,
                Mailing_City_Town__c: town,
                Mailing_Province__c: province,
                Mailing_Postal_Code__c: postal_code,
                Full_Mailing_Address__c: full_address,
                Mailing_Country__c: country,
                Status__c: status,
                LastModifiedDate: sf_updatedAt,
                CreatedDate: createdSfAt
            } = sfAddressDetails;
            return { sf_record_id, name, suit_unit, street_number, street_name, town, province, postal_code, full_address, country, status, sf_updatedAt, createdSfAt };
        }

        if (type === 'service') {
            const {
                Id: sf_record_id,
                Name: name,
                Service_Suite_Unit__c: suit_unit,
                Service_Street_Number__c: street_number,
                Service_Street_Name__c: street_name,
                Service_City_Town__c: town,
                Service_Province__c: province,
                Service_Postal_Code__c: postal_code,
                Full_Service_Address__c: full_address,
                Service_Country__c: country,
                Status__c: status,
                LastModifiedDate: sf_updatedAt,
                CreatedDate: createdSfAt
            } = sfAddressDetails;
            return { sf_record_id, name, suit_unit, street_number, street_name, town, province, postal_code, full_address, country, status, sf_updatedAt, createdSfAt };
        }
    }
}

module.exports = AddressWebhookServices;
