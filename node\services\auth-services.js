const db = require('../models');
const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const SalesforceService = require("./salesforce-services");
const ChargebeeService = require("./chargebee-services.js");
const PlanServices = require("../services/plan-services");
const CognitoConnection = require("../clients/aws/cognito");
const awsCognitoConnection = new CognitoConnection();
const passwordGenerator = require('generate-password');
const salesforceServiceClient = new SalesforceService();
const CONFIG = require('../config');
const SubscriptionServices = require("./subscription-services");
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();
const { billindTypeCbSubs, getCurrentAtlanticTime, generateAccessToken } = require("../helpers/privacyAlgorithms");
const moment = require('moment');
const sqs = require('../config/aws-sqs');

// Service class for handling user authentication and other user related operations
class AuthServices {

  // Enqueue user signup request to SQS and log to ElasticSearch
  async userSignupQueue(requestBody, elasticLogObj) {
    // Log the request to ElasticSearch
    const { _id, _index } = await elasticSearch.insertDocument("registration_logs", { ...elasticLogObj, request: requestBody });
    try {
      const { email } = requestBody;
      // Check if the user already exists
      const { userExist } = await this.checkUserExist(email);
      if (userExist) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.EMAIL_ALREADY_REGISTERED);

      // Retrieve Salesforce contact details (external call)
      const sfContactDetails = await salesforceServiceClient.getContactDetails(email);
      if (!sfContactDetails?.Id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      // Prepare SQS message
      const elasticLogRef = { _id, _index };
      const params = {
        MessageBody: JSON.stringify({ requestBody, elasticLogObj: elasticLogRef }),
        QueueUrl: CONFIG.aws.registerQueueUrl,
      };

      // Send message to SQS
      const result = await sqs.sendMessage(params).promise();
      if (result?.MessageId) {
        const response = { status: true, data: requestBody, message: "User registration is queued and will be processed shortly." };
        if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });
        return { status: true, message: "User registration is queued and will be processed shortly." };
      }

      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: 'Failed to queue the user registration.' } });
      return { status: false, message: "Failed to queue the user registration." };
    } catch (error) {
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`AuthService userSignupQueue ->`, error);
      throw error;
    }
  }

  // Handle user registration logic (called directly or by worker)
  async signup(payload, elasticLogObj, isQueueProcess = false) {
    const { email, isCognito } = payload;
    let _id, _index;

    // Determine ElasticSearch logging context
    if (isQueueProcess && elasticLogObj && elasticLogObj._id && elasticLogObj._index) {
      _id = elasticLogObj._id;
      _index = elasticLogObj._index;
    } else {
      const esLog = await elasticSearch.insertDocument("registration_logs", { ...elasticLogObj, request: payload });
      _id = esLog._id;
      _index = esLog._index;
    }

    let sfContactDetails, sfCustomerDetails, cardCustomerId;
    try {
      // Check if the user already exists (DB call, safe outside transaction)
      const { userExist } = await this.checkUserExist(email);
      if (userExist) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.EMAIL_ALREADY_REGISTERED);

      // Retrieve Salesforce contact details (external call)
      sfContactDetails = await salesforceServiceClient.getContactDetails(email);
      if (!sfContactDetails?.Id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
      cardCustomerId = sfContactDetails.Account_Chargebee_Customer_ID__c;

      // Retrieve customer details from Salesforce (external call)
      sfCustomerDetails = await salesforceServiceClient.getCustomerDetails(sfContactDetails.Id);

      // Start transaction for DB operations only
      const transaction = await db.sequelize.transaction();
      try {
        // Insert customer details into the database
        const contactInsertionData = await this.insertContactInDB(sfContactDetails, transaction);
        if (!contactInsertionData?.id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);

        // Retrieve card details from Chargebee (external call, but DB update inside transaction)
        if (cardCustomerId) {
          const chargebeeServiceClient = new ChargebeeService();
          await chargebeeServiceClient.getAllCards(contactInsertionData.id, cardCustomerId, transaction);
        }

        // Insert Salesforce customer details into the database
        if (sfCustomerDetails.length) await this.insertCustomerDetailsinDB(contactInsertionData.id, sfCustomerDetails, transaction);

        // Insert referrals into the database
        await this.getAllReferrals(contactInsertionData, transaction);

        // Create Cognito user
        if (isCognito) await this.createUserInAwsCognito(email, transaction, "send", sfContactDetails.Id, _id, _index);

        await transaction.commit();
        const response = { status: true, data: payload, message: "Registration successful." };
        if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });



        return response;
      } catch (error) {
        await transaction.rollback();
        if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
        console.error(`AuthService signup ->`, error);
        throw error;
      }
    } catch (error) {
      console.error(`AuthService signup catch level 2 ->`, error);
      // For errors before transaction starts
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      throw error;
    }
  }

  // Referral Module
  async getAllReferrals(contactData, transaction) {
    try {
      const { id: contact_id, sf_record_id: contactSfId } = contactData
      const referralDetails = await salesforceServiceClient.getReferralDetails(contactSfId);
      if (!referralDetails?.length) return;
      for (const referralDetail of referralDetails) {

        const { Id: sf_record_id, Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, CreatedDate: createdAt, LastModifiedDate: sf_updatedAt, Referrals_Name__c: referral_name, Name: sf_name } = referralDetail;

        const referralObj = {
          contact_id,
          sf_name,
          referred_amount,
          referral_name,
          installed_on,
          credit_added_on,
          createdAt: getCurrentAtlanticTime(createdAt, "sfUpdate"),
          sf_updatedAt: getCurrentAtlanticTime(sf_updatedAt, "sfUpdate")
        };
        const checkReferralExist = await db.ContactsReferrals.count({ where: { sf_record_id }, transaction });
        if (!checkReferralExist) {
          referralObj.sf_record_id = sf_record_id;
          await db.ContactsReferrals.create(referralObj, { transaction });
        } else await db.ContactsReferrals.update(referralObj, { where: { sf_record_id }, transaction });
      }
    } catch (error) {
      console.error(`Auth service get referral details ->`, error);
      throw error;
    }
  }

  // Method to insert customer details into the database
  async insertContactInDB(sfContactDetails, transaction) {
    try {
      // Extract relevant fields from Salesforce contact details
      const {
        Id: sf_record_id,
        FirstName: first_name,
        LastName: last_name,
        Email: email,
        Company_Name__c: company_name,
        Primary_Phone__c: cell_phone,
        Alternate_Phone_Number__c: secondary_phone,
        Contact_Name_if_different_than_end_user__c: additional_name,
        Referral_Link_Full__c: referral_link,
        Account_Chargebee_Customer_ID__c: cb_customer_id,
        Total_Referrals__c: total_referral,
        Total_Paid_Referrals__c: total_paid_referral,
        Total_Earned_Referrals__c: total_earned_referral,
        LastModifiedDate: sf_updatedAt,
        CreatedDate: createdAt,
        Sticky_Sender__c: sticky_sender,
        Name: sf_name,
        CP_Cognito_ID__c: aws_cognito_id
      } = sfContactDetails;

      const userDetails = { sf_record_id, email };
      if (first_name) userDetails.first_name = first_name;
      if (last_name) userDetails.last_name = last_name;
      if (sf_name) userDetails.sf_name = sf_name;
      if (company_name) userDetails.company_name = company_name;
      if (cell_phone) userDetails.cell_phone = cell_phone.replace(/\D/g, '');
      if (secondary_phone) userDetails.secondary_phone = secondary_phone.replace(/\D/g, '');
      if (additional_name) userDetails.additional_name = additional_name;
      if (referral_link) userDetails.referral_link = referral_link;
      if (cb_customer_id) userDetails.cb_customer_id = cb_customer_id;
      if (total_referral) userDetails.total_referral = total_referral;
      if (total_paid_referral) userDetails.total_paid_referral = total_paid_referral;
      if (total_earned_referral) userDetails.total_earned_referral = total_earned_referral;
      if (sticky_sender) userDetails.sticky_sender = sticky_sender;
      if (aws_cognito_id) userDetails.aws_cognito_id = aws_cognito_id;
      if (sf_updatedAt) userDetails.sf_updatedAt = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");
      if (createdAt) userDetails.createdAt = getCurrentAtlanticTime(createdAt, "sfUpdate");

      // Only create, no need to check for existing contact
      return await db.Contacts.create(userDetails, { transaction });
    } catch (error) {
      console.error(`AuthService insert Customer in DB ->`, error);
      throw error;
    }
  }

  // Helper to resolve address and service IDs
  async resolveCustomerDetailIds(sfCustomerDetail, transaction) {
    const {
      Service_Address__c,
      Mailing_Address__c,
      Latest_TV__c,
      Latest_Internet__c,
      Latest_Phone_VOIP__c
    } = sfCustomerDetail;

    const addressPromises = [];
    addressPromises.push(Service_Address__c ? await this.getServiceAddressId(Service_Address__c, transaction) : Promise.resolve(null));
    addressPromises.push(Mailing_Address__c ? await this.getMailingAddressId(Mailing_Address__c, transaction) : Promise.resolve(null));

    const servicePromises = [];
    servicePromises.push(Latest_Internet__c ? this.getServiceDetailId(Latest_Internet__c, "Internet__c", "CustomerInternet", transaction) : Promise.resolve(null));
    servicePromises.push(Latest_TV__c ? this.getServiceDetailId(Latest_TV__c, "TV__c", "CustomerTv", transaction) : Promise.resolve(null));
    servicePromises.push(Latest_Phone_VOIP__c ? this.getServiceDetailId(Latest_Phone_VOIP__c, "Phone__c", "CustomerPhone", transaction) : Promise.resolve(null));

    return Promise.allSettled([...addressPromises, ...servicePromises]);
  }

  // Helper to upsert customer details
  async upsertCustomerDetails(customerDetailData, transaction) {
    const checkCustomerExist = await db.CustomerDetails.findOne({ where: { sf_record_id: customerDetailData.sf_record_id }, transaction });
    if (!checkCustomerExist) {
      return await db.CustomerDetails.create(customerDetailData, { transaction });
    } else {
      await db.CustomerDetails.update(customerDetailData, { where: { sf_record_id: customerDetailData.sf_record_id }, transaction });
      return checkCustomerExist;
    }
  }

  async insertCustomerDetailsinDB(contact_id, sfCustomerDetails, transaction) {
    try {
      const detailPromises = sfCustomerDetails.map(async sfCustomerDetail => {
        const {
          Id: sf_record_id,
          CP_Stage__c,
          LastModifiedDate: sf_updatedAt,
          CreatedDate: createdAt,
          Name: sf_name,
          Latest_CB_Subscription__c,
          CB_Subscription_Id__c
        } = sfCustomerDetail;

        const checkStage = CP_Stage__c ? CP_Stage__c.toLowerCase() : null;
        if (checkStage && checkStage !== "hidden") {
          const [serviceAddress, mailingAddress, internet, tv, phone] = await this.resolveCustomerDetailIds(sfCustomerDetail, transaction);

          const customerDetailData = {
            contact_id,
            sf_record_id,
            service_address_id: serviceAddress.status === 'fulfilled' ? serviceAddress.value : null,
            mailing_address_id: mailingAddress.status === 'fulfilled' ? mailingAddress.value : null,
            internet_id: internet.status === 'fulfilled' ? internet.value : null,
            tv_id: tv.status === 'fulfilled' ? tv.value : null,
            phone_id: phone.status === 'fulfilled' ? phone.value : null,
            stage: CP_Stage__c || "Hidden",
            sf_name: sf_name || null,
            sf_updatedAt: getCurrentAtlanticTime(sf_updatedAt, "sfUpdate"),
            createdAt: getCurrentAtlanticTime(createdAt, "sfUpdate")
          };

          const insertCustomerDetails = await this.upsertCustomerDetails(customerDetailData, transaction);

          if (insertCustomerDetails?.id) {
            if (Latest_CB_Subscription__c) {
              await this.getSubscriptionDetailsfromSf(Latest_CB_Subscription__c, insertCustomerDetails.id, transaction, "salesforce", customerDetailData);
            } else if (CB_Subscription_Id__c) {
              await this.getSubscriptionDetailsfromSf(CB_Subscription_Id__c, insertCustomerDetails.id, transaction, "chargebee", customerDetailData);
            }
          }
        }
      });
      await Promise.all(detailPromises);
    } catch (error) {
      console.error(`AuthService insert Customer Details in DB ->`, error);
      throw error;
    }
  }

  async getServiceAddressId(serviceAddressId, transaction) {
    try {
      if (serviceAddressId) {
        const { returnStatus, addresstData: sfServiceAddressDetails } = await salesforceServiceClient.getAddressDetails(serviceAddressId, "Service_Address__c");
        if (returnStatus) {

          const { Id: sf_record_id, Name: name, Service_Suite_Unit__c: suit_unit, Service_Street_Number__c: street_number, Service_Street_Name__c: street_name, Service_City_Town__c: town, Service_Province__c: province, Service_Postal_Code__c: postal_code, Full_Service_Address__c: full_address, Service_Country__c: country, Status__c: status, LastModifiedDate: sf_updatedAt, CreatedDate } = sfServiceAddressDetails;

          const sfUpdatedAt = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");
          const createdAt = getCurrentAtlanticTime(CreatedDate, "sfUpdate");

          let [adrsDetails, created] = await db.CustomerAddresses.findOrCreate({
            where: {
              sf_record_id
            },
            defaults: { address_type: "service", sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt, createdAt }, transaction
          });

          if (!created) await adrsDetails.update({ name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt }, { transaction });

          return adrsDetails?.id;
        }
      }
      return null;
    } catch (error) {
      console.error(`AuthService getServiceAddressId ->`, error);
      throw error;
    }
  }

  async getMailingAddressId(mailingAddressId, transaction) {
    try {
      if (mailingAddressId) {
        const { returnStatus, addresstData: sfmailingAddressDetails } = await salesforceServiceClient.getAddressDetails(mailingAddressId, "Mailing_Address__c");
        if (returnStatus) {

          const { Id: sf_record_id, Name: name, Mailing_Suite_Unit__c: suit_unit, Mailing_Street_Number__c: street_number, Mailing_Street_Name__c: street_name, Mailing_City_Town__c: town, Mailing_Province__c: province, Mailing_Postal_Code__c: postal_code, Full_Mailing_Address__c: full_address, Mailing_Country__c: country, Status__c: status, LastModifiedDate: sf_updatedAt, CreatedDate } = sfmailingAddressDetails;

          const sfUpdatedAt = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");
          const createdAt = getCurrentAtlanticTime(CreatedDate, "sfUpdate");

          let [adrsDetails, created] = await db.CustomerAddresses.findOrCreate({
            where: {
              sf_record_id
            },
            defaults: { address_type: "mailing", sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt, createdAt }, transaction
          });

          if (!created) await adrsDetails.update({ name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt }, { transaction });

          return adrsDetails?.id;
        }
      }
      return null;
    } catch (error) {
      console.error(`AuthService get Mailing AddressId ->`, error);
      throw error;
    }
  }

  async getServiceDetailId(serviceId, serviceType, dbModel, transaction) {
    try {
      if (!serviceId) return null;
      const serviceDetails = await salesforceServiceClient.getInternetTvPhoneDetails(serviceId, serviceType);
      if (!serviceDetails) return null;

      const sfUpdatedAt = getCurrentAtlanticTime(serviceDetails.LastModifiedDate, "sfUpdate");
      const createdAt = getCurrentAtlanticTime(serviceDetails.CreatedDate, "sfUpdate");

      const insertData = { sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, createdAt, sf_name: serviceDetails.Name };
      if (serviceType === "Internet__c") {
        if (!serviceDetails.CP_Speed__c) return;
        insertData.plan_speed = serviceDetails.CP_Speed__c;
        if (serviceDetails?.Live_Date__c) insertData.live_date = serviceDetails.Live_Date__c;
        if (serviceDetails?.Disconnected_Date__c) insertData.disconnected_date = serviceDetails.Disconnected_Date__c;

        // Status
        insertData.internal_processing_status = serviceDetails.CP_Status_Internal_Processing__c;
        insertData.ship_package_status = serviceDetails.CP_Status_Ship_Package__c;
        insertData.modem_installation_status = serviceDetails.CP_Status_Modem_Installation__c;
        insertData.modem_activation_status = serviceDetails.CP_Status_Modem_Activation__c;

        const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order, swap_order } = await this.getAllInternetOrderDetails(serviceDetails, transaction);
        if (creation_order) insertData.creation_order = creation_order;
        if (tech_appointment) insertData.tech_appointment = tech_appointment;
        if (disconnect_order) insertData.disconnect_order = disconnect_order;
        if (speed_change_order) insertData.speed_change_order = speed_change_order;
        if (move_order) insertData.move_order = move_order;
        if (swap_order) insertData.swap_order = swap_order;
      }
      if (serviceType === "TV__c") {
        const plan_name = serviceDetails.Current_Base_Package__c;
        if (serviceDetails.current_account_status__c == "REMOVED" && serviceDetails?.State_Text__c == "Complete") return;
        if (plan_name) {
          insertData.plan_name = plan_name;
          insertData.state_text = serviceDetails?.State_Text__c;
          insertData.extra_packages = serviceDetails?.Current_Extra_Packages__c ? JSON.stringify(serviceDetails.Current_Extra_Packages__c.split(";")) : "[]";
          insertData.single_channels = serviceDetails?.Current_Single_Channels__c ? JSON.stringify(serviceDetails.Current_Single_Channels__c.split(";")) : "[]";
          insertData.iptv_products = serviceDetails?.current_iptv_products__c ? JSON.stringify(serviceDetails.current_iptv_products__c.split(";")) : "[]";

          if (serviceDetails?.current_account_status__c) insertData.account_status = serviceDetails.current_account_status__c;
          if (serviceDetails?.Login_Details_Last_Sent__c) insertData.login_details_last_sent = getCurrentAtlanticTime(serviceDetails.Login_Details_Last_Sent__c, "sfUpdate");
          if (serviceDetails?.Requested_Cancellation_Date__c) insertData.requested_cancellation_date = serviceDetails.Requested_Cancellation_Date__c;
        }
      }
      if (serviceType === "Phone__c") {
        if (serviceDetails.Account_Status__c == "Deleted") return;
        insertData.api_name = serviceDetails.Calling_Plan__c;
        const phoneRes = await salesforceServiceClient.getPhoneApiValue(serviceDetails.Id);
        if (serviceDetails?.Requested_Cancellation_Date__c) {
          insertData.requested_cancellation_date = getCurrentAtlanticTime(serviceDetails.Requested_Cancellation_Date__c);
        }
        const { status: phStatus, data: pfData } = phoneRes;
        if (phStatus) {
          insertData.plan_name = pfData?.Calling_Plan__c;
        }
        if (serviceDetails?.Service_Start_Date__c) insertData.service_start_date = getCurrentAtlanticTime(serviceDetails.Service_Start_Date__c);
        insertData.account_status = serviceDetails.Account_Status__c;

        // Check the port type of home phone
        let sf_phone_type = { sf_phone_type: "new", phone_number: "" };
        if (serviceDetails?.Phone_Number_To_Port__c) {
          sf_phone_type.phone_number = serviceDetails.Phone_Number_To_Port__c;
          sf_phone_type.sf_phone_type = "existing";
        }
        insertData.sf_phone_type = JSON.stringify(sf_phone_type);
      }

      let [serviceInsert, created] = await db[dbModel].findOrCreate({
        where: {
          sf_record_id: serviceDetails?.Id
        },
        defaults: insertData, transaction
      });

      if (!created) await serviceInsert.update(insertData, { transaction });
      return serviceInsert?.id;
    } catch (error) {
      console.error(`AuthService getServiceDetailId ->`, error);
      throw error;
    }
  }

  async getSubscriptionDetailsfromSf(subscriptionId, customer_details_id, transaction, type, customerDetailData) {
    try {
      let cbSubId;
      if (type == "salesforce") {
        const subscriptionData = await salesforceServiceClient.getSubscriptionDetails(subscriptionId);
        if (!subscriptionData) return null;
        const { chargebeeapps__CB_Subscription_Id__c } = subscriptionData;
        cbSubId = chargebeeapps__CB_Subscription_Id__c;
      } else if (type == "chargebee") {
        cbSubId = subscriptionId;
      }

      if (!cbSubId) return;

      const chargebeeServiceClient = new ChargebeeService();
      const { status, subscriptionDetail } = await chargebeeServiceClient.getSubscriptions(cbSubId);

      if (!status) return;

      const insertCustomerSubsDetails = await this.insertCustomerSubscriptionFromChargebee(subscriptionDetail, customer_details_id, transaction);

      if (insertCustomerSubsDetails?.id) {
        const sfSubscriptionId = await this.updateCustomerSubscriptionFromSf(insertCustomerSubsDetails, transaction);
        const { internet_id, tv_id, phone_id } = customerDetailData;
        const subscription_type = insertCustomerSubsDetails?.subscription_type;
        const planServices = new PlanServices();
        if (internet_id) {
          const internetDetails = await db.CustomerInternet.findOne({
            where: { id: internet_id }, transaction
          });

          if (internetDetails?.plan_speed) {
            const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
            if (internetPlanDetails?.length) {
              let updateObj = {};
              const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
              if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
              if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                updateObj.plan_price = getPrice.billing[0][subscription_type].price;
              }
              await db.CustomerInternet.update(updateObj, { where: { id: internetDetails?.id }, transaction });
            }
          }
        }

        if (tv_id) {
          const tvDetails = await db.CustomerTv.findOne({
            where: { id: tv_id }, transaction
          });

          if (tvDetails) {
            const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

            if (plan_name) {

              const payload = {
                plan_name: plan_name,
                extra_packages: JSON.parse(extra_packages),
                single_channels: JSON.parse(single_channels),
                iptv_products: JSON.parse(iptv_products)
              }

              const subscriptionServices = new SubscriptionServices();

              const { totalAmount } = await subscriptionServices.getAddonsTelevisionPlanDetails(payload, subscription_type);
              await db.CustomerTv.update({ total_service_cost: totalAmount }, { where: { id }, transaction });
            }
          }
        }

        if (phone_id) {
          const phoneDetails = await db.CustomerPhone.findOne({
            where: { id: phone_id }, transaction
          });

          if (phoneDetails) {
            const { id, api_name } = phoneDetails;
            if (api_name) {
              const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
              if (phonePlanDetails?.length) {
                let updateObj = {};
                const getPrice = phonePlanDetails.find(phone => phone.api_name === api_name);
                if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                  updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                }
                await db.CustomerPhone.update(updateObj, { where: { id }, transaction });
              }
            }
          }
        }
        await chargebeeServiceClient.getInvoicesList(insertCustomerSubsDetails, transaction);
        if (sfSubscriptionId) await this.updateCustomerInvoice(sfSubscriptionId, transaction);
      }
    } catch (error) {
      console.error(`AuthService getSubscriptionDetailsfromSf ->`, error);
      throw error;
    }
  }

  async updateCustomerSubscriptionFromSf(customerSubsDetails, transaction) {
    try {

      const { cb_subscription_id, id } = customerSubsDetails;

      if (!cb_subscription_id) return;

      const subscriptionData = await salesforceServiceClient.getSubscriptionDetailsUsingCbSubId(cb_subscription_id);
      if (!subscriptionData) return null;
      const { Id: sf_record_id, LastModifiedDate } = subscriptionData;
      const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate, "sfUpdate");

      const updateObj = { sf_record_id, sf_updatedAt };

      await db.CustomerSubscriptions.update(updateObj, { where: { id }, transaction });

      return sf_record_id || null;

    } catch (error) {
      console.error(`AuthService updateCustomerSubscription ->`, error);
      throw error;
    }
  }

  async updateCustomerInvoice(sfSubscriptionId, transaction) {
    try {

      const invoiceDetails = await salesforceServiceClient.getInvoiceDetails(sfSubscriptionId);

      for (const invoiceData of invoiceDetails) {
        const { Id: sf_record_id, Expected_Payment_Date_Time__c: expected_payment_date, LastModifiedDate, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id } = invoiceData;

        const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate, "sfUpdate");

        const updateObj = { sf_record_id, sf_updatedAt, expected_payment_date };
        await db.SubscriptionInvoice.update(updateObj, { where: { cb_invoice_id, cb_subscription_id }, transaction });
      }
    } catch (error) {
      console.error(`AuthService updateCustomerInvoice ->`, error);
      throw error;
    }
  }

  // Method to create user in AWS Cognito
  async createUserInAwsCognito(email, transaction, type, sf_record_id, esId, esIndices) {
    try {
      const password = passwordGenerator.generate({
        length: 10, numbers: true, symbols: true, uppercase: true, lowercase: true, strict: true, exclude: '#`&+?()_={}[]:;"\'<>,./|\\'
      });
      const response = await awsCognitoConnection.register(email, password, type);
      if (esId && esIndices) elasticSearch.updateDocument(esIndices, esId, { response });
      if (type == "resend") return response;
      if (response && response?.$metadata?.httpStatusCode == 200) {
        const { User } = response;
        if (User && User?.Username) {
          await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: User.Username, sf_record_id, cp_created_dateTime: User?.UserCreateDate, cp_status: "inactive" }, "cognitoUpdate");
          if (type == "send") {
            return await db.Contacts.update({ aws_cognito_id: User.Username }, { where: { email }, transaction });
          } else {
            return await db.Contacts.update({ aws_cognito_id: User.Username }, { where: { email } });
          }
        }
      }
    } catch (error) {
      console.error(`AuthService create User In Aws Cognito ->`, error);
      throw error;
    }
  }

  // Method for user login
  async login(payload, elasticLogObj) {
    const { email, password } = payload;
    const { _id, _index } = await elasticSearch.insertDocument("login_logs", { ...elasticLogObj, request: { email } });
    try {
      const { userExist, userData } = await this.checkUserExist(email);

      if (!userExist) throw new CustomError(RESPONSE_CODES.NOT_FOUND, `Incorrect username or password.`);
      if (!userData.aws_cognito_id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, `Login Failed. Please contact support for assistance.`);

      const response = await awsCognitoConnection.login(email, password);
      let data = { email };
      let message;
      if (response && response?.$metadata?.httpStatusCode == 200) {
        const { ChallengeName, Session } = response;
        if (ChallengeName === "NEW_PASSWORD_REQUIRED") {
          data.redirectUrl = "/login#reset-password";
          data.accessToken = Session;
          message = "Please reset your password.";
        } else {
          const { AuthenticationResult } = response;
          const { AccessToken, ExpiresIn } = AuthenticationResult;
          if (AccessToken) {
            data.redirectUrl = "/";
            data.accessToken = generateAccessToken(CONFIG.RESGISTRATION_TOKEN, AccessToken);
            data.expiresIn = ExpiresIn;
          }
          message = "Login successful";
        }
      }
      if (_id && _index) elasticSearch.updateDocument(_index, _id, {
        "log.level": 'INFO',
        response: {
          success: {
            expiresIn: data.expiresIn, message
          }
        }
      });
      return { status: true, data, message };
    } catch (error) {
      console.error(`AuthService Login In Aws Cognito ->`, error);
      if (_id) elasticSearch.updateDocument(_index, _id, { "log.level": 'WARN', response: { error: error.message } });
      if (error?.message == "Temporary password has expired and must be reset by an administrator.") return await this.resendInvitation(email);
      else throw error;
    }
  }

  // Method to resend invitation for password reset
  async resendInvitation(email) {
    try {
      const returnStatus = { status: false, data: {} }
      const response = await this.createUserInAwsCognito(email, null, "resend");
      if (response && response?.$metadata?.httpStatusCode == 200) {
        returnStatus.status = true;
        returnStatus.message = "Your temporary password has expired. A new temporary password has been sent to your email address. Please log in using your new temporary password.";
      }
      return returnStatus;
    } catch (error) {
      console.error(`AuthService Resend Invitation In Aws Cognito->`, error);
      throw error;
    }
  }

  // Method to reset user password
  async resetPassword(payload, elasticLogObj) {
    const { email, password, access_token, type, confirmation_code } = payload;
    const { _id, _index } = await elasticSearch.insertDocument("update_password_logs", { ...elasticLogObj, request: { email, access_token, type }, type: "RESET" });
    try {
      const { userExist, userData } = await this.checkUserExist(email);
      if (!userExist) throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);

      // Call the new method to handle password reset and get the result
      const res = await this.handlePasswordReset(type, { email, password, access_token, confirmation_code, userData });

      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: res });
      return res;
    } catch (error) {
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`AuthService Reset Password For First Time In Aws Cognito ->`, error);
      throw error;
    }
  }

  // New function to handle the password reset process in AWS Cognito
  async handlePasswordReset(type, { email, password, access_token, confirmation_code, userData }, loginType) {

    try {
      // Determine which AWS Cognito method to use based on the type
      const response = type === "new"
        ? await awsCognitoConnection.newPasswordChallenge({ email, password, session: access_token })
        : await awsCognitoConnection.forgotPasswordConfirmation({ email, password, confirmation_code });

      // Prepare the default data object
      let data = { email, emailVerified: true };

      // If the response is successful, process further based on the type
      if (response && response?.$metadata?.httpStatusCode == 200) {

        if (type === "new") {
          // If it's a new password, verify the email address
          const { status } = await this.verifyEmailAddress(email, loginType);
          if (!status) data.emailVerified = false; // Update if email verification failed
          const { AuthenticationResult } = response;
          const { AccessToken, ExpiresIn } = AuthenticationResult;

          if (userData.sf_record_id) await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: userData.aws_cognito_id, sf_record_id: userData.sf_record_id, cp_status: "active", first_login: true }, "cognitoUpdate");

          if (AccessToken) {
            data.redirectUrl = "/";
            data.accessToken = generateAccessToken(CONFIG.RESGISTRATION_TOKEN, AccessToken);
            data.expiresIn = ExpiresIn;
          }
        }
      }

      // Return the final result object
      return { status: true, data, message: "Password reset successfully" };
    } catch (error) {
      throw error;
    }
  }

  // Method to verify user email address
  async verifyEmailAddress(email, loginType) {
    try {
      const returnStatus = { status: false }
      const response = await awsCognitoConnection.verifyEmail(email);
      if (response && response?.$metadata?.httpStatusCode == 200) {
        if (!loginType) await db.Contacts.update({ status: "active" }, { where: { email } });
        returnStatus.status = true;
      }
      return returnStatus;
    } catch (error) {
      console.error(`AuthService Email Verification In Aws Cognito ->`, error);
      throw error;
    }
  }

  // Method for forgot password functionality
  async forgotPassword(payload, elasticLogObj) {
    const { email } = payload;
    const { _id, _index } = await elasticSearch.insertDocument("update_password_logs", { ...elasticLogObj, request: { email }, type: "FORGOT" });
    try {
      const returnStatus = { status: false };
      const { userExist, userData } = await this.checkUserExist(email);
      if (!userExist) throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);
      if (userData?.status === "inactive") throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Your account is currently inactive. Please activate it using the temporary password provided.");
      const response = await awsCognitoConnection.forgotPassword(email);

      if (response && response?.$metadata?.httpStatusCode == 200) {
        returnStatus.status = true;
        returnStatus.data = payload;
        returnStatus.message = "Verification code sent to the registered email";
      }
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      return returnStatus;
    } catch (error) {
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`AuthService Forgot Password In Aws Cognito->`, error);
      throw error;
    }
  }

  // Method to check if user exists
  async checkUserExist(email) {
    try {
      const returnStatus = { userExist: false };
      const isUserExist = await db.Contacts.findOne({ where: { email } });
      if (isUserExist) {
        returnStatus.userExist = true;
        returnStatus.userData = isUserExist.toJSON();
      }
      return returnStatus;
    } catch (error) {
      console.error(`Check User Exist->`, error);
      throw error;
    }
  }

  async getAllInternetOrderDetails(internetDetails, transaction) {
    try {
      const { Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c } = internetDetails;

      const orderPromises = [
        Creation_Order__c ? this.getOrderDetailsId(Creation_Order__c, "InternetElCreationOrder", transaction) : Promise.resolve(null),
        Latest_Tech_Appointment__c ? this.getTechDetailsId(Latest_Tech_Appointment__c, "InternetElTechAppointment", transaction) : Promise.resolve(null),
        Latest_Disconnect_Order__c ? this.getOrderDetailsId(Latest_Disconnect_Order__c, "InternetElDisconnectOrder", transaction) : Promise.resolve(null),
        Latest_Speed_Change_Order__c ? this.getOrderDetailsId(Latest_Speed_Change_Order__c, "InternetElSpeedChangeOrder", transaction) : Promise.resolve(null),
        Latest_Move_Order__c ? this.getOrderDetailsId(Latest_Move_Order__c, "InternetElMoveOrder", transaction) : Promise.resolve(null),
        Latest_Modem_Swap_Order__c ? this.getOrderDetailsId(Latest_Modem_Swap_Order__c, "InternetElSwapOrder", transaction) : Promise.resolve(null)
      ];

      const [creationOrderId, techAppointmentId, disconnectOrderId, speedChangeOrderId, moveOrderId, swapOrderId] = await Promise.allSettled(orderPromises);

      const customerDetailData = {
        creation_order: creationOrderId.status === 'fulfilled' ? creationOrderId.value : null,
        tech_appointment: techAppointmentId.status === 'fulfilled' ? techAppointmentId.value : null,
        disconnect_order: disconnectOrderId.status === 'fulfilled' ? disconnectOrderId.value : null,
        speed_change_order: speedChangeOrderId.status === 'fulfilled' ? speedChangeOrderId.value : null,
        move_order: moveOrderId.status === 'fulfilled' ? moveOrderId.value : null,
        swap_order: swapOrderId.status === 'fulfilled' ? swapOrderId.value : null
      };

      return customerDetailData;
    } catch (error) {
      console.error("AuthService getAllInternetOrderDetails -> ", error);
      throw error;
    }
  }

  async getOrderDetailsId(orderId, dbModel, transaction) {

    try {
      if (!orderId) return null;
      const orderDetails = await salesforceServiceClient.getOrderDetails(orderId, dbModel);
      if (!orderDetails) return null;

      const sfUpdatedAt = getCurrentAtlanticTime(orderDetails?.LastModifiedDate, "sfUpdate");
      const createdAt = getCurrentAtlanticTime(orderDetails.CreatedDate, "sfUpdate");

      let insertOrderDetails = { sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, createdAt, sf_name: orderDetails?.Name };
      if (dbModel === "InternetElCreationOrder") {
        if (orderDetails?.Related_Shipping_Order__c) {
          const shippingData = await salesforceServiceClient.getShippingDetails(orderDetails.Related_Shipping_Order__c);
          let shipping_id = null;
          if (shippingData) {
            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingData;

            const modified_date = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");
            const formatedDate = getCurrentAtlanticTime(CreatedDate, "sfUpdate");

            const shipDetails = { sf_record_id, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt: modified_date, package_deliverted_at, createdAt: formatedDate, sf_name };

            const [creationResponse, created] = await db.CreationOrderShipping.findOrCreate({
              where: {
                sf_record_id
              },
              defaults: shipDetails,
              transaction
            });

            if (!created) await creationResponse.update({ full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt: modified_date, package_deliverted_at, createdAt: formatedDate }, { transaction });

            if (creationResponse) shipping_id = creationResponse?.id || null;
          }
          if (shipping_id) insertOrderDetails.shipping_id = shipping_id;
        }
        if (orderDetails?.Record_Type_Name__c) insertOrderDetails.record_type = orderDetails.Record_Type_Name__c;
        if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
      }

      if (dbModel === "InternetElDisconnectOrder" && orderDetails?.Requested_Disconnect_Date__c) insertOrderDetails.requested_disconnect_date = orderDetails?.Requested_Disconnect_Date__c;

      if (dbModel === "InternetElSwapOrder" && orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;

      if (dbModel === "InternetElSpeedChangeOrder") {
        if (orderDetails?.Expected_Completion_Date__c) insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;
        if (orderDetails?.Speed__c) insertOrderDetails.speed = orderDetails?.Speed__c;
        if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
        if (orderDetails?.Response_Date__c) insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);
      }

      if (dbModel === "InternetElMoveOrder") {
        if (orderDetails?.Requested_Move_Date__c) insertOrderDetails.requested_move_date = orderDetails?.Requested_Move_Date__c;
        if (orderDetails?.Requested_Install_Date__c) insertOrderDetails.requested_install_date = orderDetails?.Requested_Install_Date__c;
        if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
        if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
        if (orderDetails?.Install_Time__c) insertOrderDetails.install_time = orderDetails?.Install_Time__c;
        if (orderDetails?.Order_Reject_Reason__c) insertOrderDetails.reject_reason = orderDetails?.Order_Reject_Reason__c;
        if (orderDetails?.Order_Reject_Reason_Solved__c) insertOrderDetails.reject_reason_solved = orderDetails?.Order_Reject_Reason_Solved__c;
        if (orderDetails?.Service_Suite_Unit__c) insertOrderDetails.unit = orderDetails?.Service_Suite_Unit__c;
        if (orderDetails?.Service_Street_Number__c) insertOrderDetails.street_number = orderDetails?.Service_Street_Number__c;
        if (orderDetails?.Service_Street_Name__c) insertOrderDetails.street_name = orderDetails?.Service_Street_Name__c;
        if (orderDetails?.Service_City_Town__c) insertOrderDetails.city = orderDetails?.Service_City_Town__c;
        if (orderDetails?.Service_Province__c) insertOrderDetails.province = orderDetails?.Service_Province__c;
        if (orderDetails?.Service_Postal_Code__c) insertOrderDetails.postal_code = orderDetails?.Service_Postal_Code__c;
        if (orderDetails?.Submit_Date__c) insertOrderDetails.submit_date = getCurrentAtlanticTime(orderDetails.Submit_Date__c);
      }

      let [orderInsert, created] = await db[dbModel].findOrCreate({
        where: {
          sf_record_id: orderDetails?.Id
        },
        defaults: insertOrderDetails, transaction
      });

      if (!created) await orderInsert.update(insertOrderDetails, { transaction });
      return orderInsert?.id;
    } catch (error) {
      console.error("AuthService getOrderDetailsId -> ", error);
      throw error;
    }
  }
  async getTechDetailsId(techId, dbModel, transaction) {
    try {
      if (!techId) return null;
      const techDetails = await salesforceServiceClient.getTechAppDetails(techId);
      if (!techDetails) return null;
      const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = techDetails;

      const formatedDate = getCurrentAtlanticTime(CreatedDate, "sfUpdate");
      const modifiedDate = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");

      const techAppDetails = { sf_record_id, install_date, install_time, sf_updatedAt: formatedDate, createdAt: modifiedDate, sf_name };

      let [techInsert, created] = await db[dbModel].findOrCreate({
        where: {
          sf_record_id
        },
        defaults: techAppDetails, transaction
      });

      if (!created) await techInsert.update(techAppDetails, { transaction });
      return techInsert?.id;
    } catch (error) {
      console.error("AuthService getTechDetailsId -> ", error);
      throw error;
    }
  }

  async insertCustomerSubscriptionFromChargebee(subscriptionData, customer_details_id, transaction) {
    try {

      const { subscription: { id: cb_subscription_id, billing_period_unit, plan_amount: amount, status, next_billing_at, created_at, activated_at: activated_on, total_dues, payment_source_id } } = subscriptionData;

      const subscription_type = billindTypeCbSubs(billing_period_unit);

      const customerSubsDetails = {
        customer_details_id,
        cb_subscription_id,
        amount: amount ? amount / 100 : null,
        balance_due: total_dues ? total_dues / 100 : 0.00,
        activated_on: activated_on ? moment.unix(activated_on).format('YYYY-MM-DD HH:mm:ss') : null,
        next_billing_at: next_billing_at ? moment.unix(next_billing_at).format('YYYY-MM-DD HH:mm:ss') : null,
        createdAt: created_at ? moment.unix(created_at).format('YYYY-MM-DD HH:mm:ss') : null,
        status: status,
        subscription_type
      };

      let [subscriptionInsert, created] = await db.CustomerSubscriptions.findOrCreate({
        where: {
          cb_subscription_id
        },
        defaults: customerSubsDetails, transaction
      });

      if (!created) await subscriptionInsert.update(customerSubsDetails, { transaction });
      if (payment_source_id && subscriptionInsert?.id) await this.mapSubscriptionCard(payment_source_id, subscriptionInsert?.id, transaction);

      return subscriptionInsert;
    } catch (error) {
      console.error(`AuthService insertCustomerSubscriptionFromChargebee ->`, error);
      throw error;
    }
  }

  async mapSubscriptionCard(cb_card_id, customer_subscription_id, transaction) {
    try {
      const checkCardExist = await db.ContactsCardDetails.findOne({ where: { cb_card_id }, transaction });
      if (checkCardExist) {
        const { id: contact_card_id, contact_id } = checkCardExist;
        const cardMappingData = { contact_id, contact_card_id, customer_subscription_id, is_primary: "1" };
        let [cardMappingDetails, created] = await db.SubscriptionCardMapping.findOrCreate({
          where: { contact_id, contact_card_id, customer_subscription_id },
          defaults: cardMappingData,
          transaction, // Passing the transaction object
        });

        if (!created) await cardMappingDetails.update(cardMappingData, { transaction });
      }
    } catch (error) {
      console.error(`AuthService mapSubscriptionCard ->`, error);
      throw error;
    }
  }

  // Method for user cognito account creation
  async createCognitoAccount(payload, elasticLogObj) {
    const { email } = payload;
    const { _id, _index } = await elasticSearch.insertDocument("cognito_user_creation_logs", { ...elasticLogObj, request: payload });
    try {
      // Check if the user already exists
      const { userExist, userData } = await this.checkUserExist(email);
      if (!userExist) throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);
      const cGResponse = await this.checkUserInCognito(email);
      const { message, cognitoId, status } = cGResponse;
      let data;

      if (status && message === "Previously user exist in cognito. User updated.") {
        data = { cognitoId };
        if (userData.sf_record_id) await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: cognitoId, sf_record_id: userData.sf_record_id }, "cognitoUpdate");
        await this.updateUserCognitoId(cognitoId, userData.id);
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { response: cGResponse });
      }

      if (status && message === "User not exist in cognito. New user created.") {
        const password = passwordGenerator.generate({
          length: 10, numbers: true, symbols: true, uppercase: true, lowercase: true, strict: true, exclude: '#`&+?()_={}[]:;"\'<>,./|\\'
        });
        const response = await awsCognitoConnection.register(email, password, "send");
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { response });

        if (response && response?.$metadata?.httpStatusCode == 200) {
          const { User } = response;
          if (User && User?.Username) {
            data = { cognitoId: User?.Username };
            if (userData.sf_record_id) await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: User.Username, sf_record_id: userData.sf_record_id, cp_created_dateTime: User?.UserCreateDate, cp_status: "inactive" }, "cognitoUpdate");
            await this.updateUserCognitoId(User.Username, userData.id);
          }
        }
      }
      return { status: true, data, message };
    } catch (error) {
      if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`AuthService createCognitoAccount ->`, error);
      throw error;
    }
  }

  async checkUserInCognito(email) {
    const returnStatus = { status: false, cognitoId: null, message: null }
    try {
      const response = await awsCognitoConnection.getUserDetail(email);

      if (response && response?.$metadata?.httpStatusCode == 200) {
        const { Username } = response;
        if (Username) {
          returnStatus.status = true;
          returnStatus.cognitoId = Username;
          returnStatus.message = "Previously user exist in cognito. User updated."
        }
      }
    } catch (error) {
      if (error?.__type === "UserNotFoundException") {
        returnStatus.status = true;
        returnStatus.message = "User not exist in cognito. New user created.";
      } else {
        console.error(`AuthService checkUserInCognito ->`, error);
        throw error;
      }
    }
    return returnStatus;
  }
  async updateUserCognitoId(aws_cognito_id, id) {
    try {
      await db.Contacts.update({ aws_cognito_id }, { where: { id } });
    } catch (error) {
      throw error;
    }
  }

  // Delete a contact and all related data from the system
  async deleteContact(payload) {
    const { email } = payload;
    try {
      // Check if user exists
      const { userExist, userData } = await this.checkUserExist(email);
      if (!userExist) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.NOT_FOUND);

      let cognitoDeleted = false;

      // If user has aws_cognito_id, try to delete from Cognito
      if (userData.aws_cognito_id) {
        const response = await awsCognitoConnection.deleteUser(email);
        if (response && response?.$metadata?.httpStatusCode == 200) {
          cognitoDeleted = true;
        }
      } else {
        cognitoDeleted = true; // If no aws_cognito_id, consider it deleted
      }

      if (!cognitoDeleted) {
        return { status: false, message: "Delete Failed in aws cognito" };
      }

      // Always attempt to delete local/contact data
      const { id: contactId } = userData;

      // Update Salesforce profile if needed
      if (userData.sf_record_id) {
        await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: null, sf_record_id: userData.sf_record_id, cp_status: "inactive" }, "cognitoUpdate");
      }

      // Delete all related customer details and associated records
      const result = await this.deleteOtherContactDetails(contactId);

      if (result.status) {
        return { status: true, message: "Delete successful." };
      }
      return { status: false, message: "Delete failed. Please try again." };
    } catch (error) {
      console.error("deleteContact error:", error);
      throw error;
    }
  }

  // Delete all customer details and related records for a contact
  async deleteOtherContactDetails(contact_id) {
    try {
      // Find all customer details for the contact
      let customerDetails = await db.CustomerDetails.findAll({
        where: { contact_id }
      });

      // Delete each customer detail and its dependencies
      if (customerDetails.length) {
        for (const customerDetail of customerDetails) {
          await this.deleteCustomerDetailsFromDb(customerDetail);
        }
      }

      // If no more customer details, delete the contact itself
      const checkCount = await this.getCountFromDb("CustomerDetails", "contact_id", contact_id);
      if (checkCount === 0) {
        await this.deleteDataFromDb("Contacts", contact_id);
        return { status: true };
      }
      return { status: false };
    } catch (error) {
      throw error;
    }
  }

  // Delete a single customer detail and all associated records (addresses, services, etc.)
  async deleteCustomerDetailsFromDb(customerDetails) {
    try {
      const { service_address_id, mailing_address_id, internet_id, tv_id, phone_id, id } = customerDetails;

      // Handle service address deletion if only referenced by this customer detail
      if (service_address_id) {
        const checkCount = await this.getCountFromDb("CustomerDetails", "service_address_id", service_address_id)
        if (checkCount === 1) {
          // Set service_address_id to null before deleting address
          await this.updateNullinDb("CustomerDetails", "service_address_id", id);
          await this.deleteDataFromDb("CustomerAddresses", service_address_id);
        }
      }

      // Handle mailing address deletion if only referenced by this customer detail
      if (mailing_address_id) {
        const checkCount = await this.getCountFromDb("CustomerDetails", "mailing_address_id", mailing_address_id)
        if (checkCount === 1) {
          // Set mailing_address_id to null before deleting address
          await this.updateNullinDb("CustomerDetails", "mailing_address_id", id);
          await this.deleteDataFromDb("CustomerAddresses", mailing_address_id);
        }
      }

      // Handle internet service and its related orders
      if (internet_id) {
        const internetDetail = await db.CustomerInternet.findOne({ where: { id: internet_id } });

        if (internetDetail) {
          const { creation_order, tech_appointment, disconnect_order, speed_change_order } = internetDetail;

          // Handle creation order and its shipping
          if (creation_order) {
            const shipping_details = await db.InternetElCreationOrder.findOne({ where: { id: creation_order } });
            const shipping_id = shipping_details?.shipping_id;

            if (shipping_id) {
              const checkCount = await this.getCountFromDb("InternetElCreationOrder", "shipping_id", shipping_id)
              if (checkCount === 1) {
                // Set shipping_id to null before deleting shipping
                await this.updateNullinDb("InternetElCreationOrder", "shipping_id", creation_order);
                await this.deleteDataFromDb("CreationOrderShipping", shipping_id);
              }
            }
            const checkCount = await this.getCountFromDb("CustomerInternet", "creation_order", creation_order)
            if (checkCount === 1) {
              // Set creation_order to null before deleting order
              await this.updateNullinDb("CustomerInternet", "creation_order", internet_id);
              await this.deleteDataFromDb("InternetElCreationOrder", creation_order);
            }
          }

          // Handle tech appointment
          if (tech_appointment) {
            const checkCount = await this.getCountFromDb("CustomerInternet", "tech_appointment", tech_appointment)
            if (checkCount === 1) {
              await this.updateNullinDb("CustomerInternet", "tech_appointment", internet_id); //first "tech_appointment" set null in "CustomerInternet" table before deleting the appointment
              await this.deleteDataFromDb("InternetElTechAppointment", tech_appointment);
            }
          }

          // Handle disconnect order
          if (disconnect_order) {
            const checkCount = await this.getCountFromDb("CustomerInternet", "disconnect_order", disconnect_order)
            if (checkCount === 1) {
              await this.updateNullinDb("CustomerInternet", "disconnect_order", internet_id); //first "disconnect_order" set null in "CustomerInternet" table before deleting the order
              await this.deleteDataFromDb("InternetElDisconnectOrder", disconnect_order);
            }
          }

          // Handle speed change order
          if (speed_change_order) {
            const checkCount = await this.getCountFromDb("CustomerInternet", "speed_change_order", speed_change_order)
            if (checkCount === 1) {
              await this.updateNullinDb("CustomerInternet", "speed_change_order", internet_id); //first "speed_change_order" set null in "CustomerInternet" table before deleting the order
              await this.deleteDataFromDb("InternetElSpeedChangeOrder", speed_change_order);
            }
          }
        }

        // Delete internet service if only referenced by this customer detail
        const checkCount = await this.getCountFromDb("CustomerDetails", "internet_id", internet_id)
        if (checkCount === 1) {
          await this.updateNullinDb("CustomerDetails", "internet_id", id); //first "internet_id" set null in "CustomerDetails" table before deleting the internet
          await this.deleteDataFromDb("CustomerInternet", internet_id);
        }
      }

      // Handle phone service deletion
      if (phone_id) {
        const checkCount = await this.getCountFromDb("CustomerDetails", "phone_id", phone_id)
        if (checkCount === 1) {
          await this.updateNullinDb("CustomerDetails", "phone_id", id); //first "phone_id" set null in "CustomerDetails" table before deleting the phone
          await this.deleteDataFromDb("CustomerPhone", phone_id);
        }
      }

      // Handle TV service deletion
      if (tv_id) {
        const checkCount = await this.getCountFromDb("CustomerDetails", "tv_id", tv_id)
        if (checkCount === 1) {
          await this.updateNullinDb("CustomerDetails", "tv_id", id); //first "tv_id" set null in "CustomerDetails" table before deleting the tv
          await this.deleteDataFromDb("CustomerTv", tv_id);
        }
      }

      // Finally, delete the customer detail itself
      await this.deleteDataFromDb("CustomerDetails", id);
    } catch (error) {
      console.error("Error deleteCustomerDetailsFromDb -> ", error);
    }
  }

  // Helper: Delete a row from a table by id
  async deleteDataFromDb(tableName, id) {
    try {
      await db[tableName].destroy({
        where: { id }
      });
    } catch (error) {
      throw error;
    }
  }

  // Helper: Count rows in a table where column = value
  async getCountFromDb(tableName, columnName, value) {
    try {
      const count = await db[tableName].count({
        where: { [columnName]: value }
      });
      return count;
    } catch (error) {
      console.error(`getCountFromDb error:`, error);
      throw error;
    }
  }

  // Helper: Set a column to null for a given row (by id)
  async updateNullinDb(tableName, columnName, id) {
    try {
      await db[tableName].update(
        { [columnName]: null },
        { where: { id } }
      );
    } catch (error) {
      console.error(`updateNullinDb error:`, error);
      throw error;
    }
  }
}

module.exports = AuthServices;