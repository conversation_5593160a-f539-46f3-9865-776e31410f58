import { rootState } from "../reducers/rootReducer";

export const homePhoneState = (store: rootState) => store.homePhoneReducer;

export const haveHomePhonePlan = (store: rootState) =>
  store.homePhoneReducer.haveHomePhonePlan;

export const isNewPhoneNumber = (store: rootState) =>
  store.homePhoneReducer.isNewPhoneNumber;

export const phoneNumber = (store: rootState) =>
  store.homePhoneReducer.phoneNumber;

export const isPlanRemoved = (store: rootState) =>
  store.homePhoneReducer.isPlanRemoved;

export const showHomePhonePopup = (store: rootState) =>
  store.homePhoneReducer.showHomePhonePopup;

export const showDeleteOrderPopup = (store: rootState) =>
  store.homePhoneReducer.showDeleteOrderPopup;

export const showSelectNumberPopup = (store: rootState) =>
  store.homePhoneReducer.showSelectNumberPopup;

export const showHomePhoneChangeConfirmationPopup = (store: rootState) =>
  store.homePhoneReducer.showHomePhoneChangeConfirmationPopup;
