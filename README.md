# Purple-Cow Customer Portal

This README provides instructions for setting up, running, and deploying the **Purple-Cow Customer Portal** project on your local machine and AWS. The project includes a Node.js backend, a React frontend, Salesforce synchronization, and webhook integration, with CI/CD pipelines configured via GitHub Actions.

## Table of Contents

1. [Project Overview](#Project-Overview)
2. [Project Structure ](#Project-Structure)
3. [Local Setup](#Local-Setup)
   - Backend (Node.js)
   - Frontend (React)
   - Salesforce Sync
   - Webhook
4. [Deployment Guide](#Deployment-Guide)
   - AWS Configuration
   - GitHub Secrets Configuration
   - CI/CD Workflow Setup
5. [API Endpoints](#API-Endpoints)
   - Authentication
   - Auth Service
   - Contact Service
6. [Customer Stage and Move Order Status Logic](#Customer-Stage-and-Move-Order-Status-Logic)
7. [Troubleshooting](#Troubleshooting)
8. [Additional Notes](#Additional-Notes)

## Project Overview

The Purple-Cow Customer Portal is a web application that allows customers to manage their accounts, synchronize data with Salesforce, and process payments via Chargebee. It consists of:

- A **Node.js** backend for API services.
- A **React** frontend for the customer portal UI.
- A **Salesforce-sync** module for background data synchronization.
- A **Webhook** service for Salesforce integration.
- CI/CD pipelines for automated deployment to AWS Elastic Beanstalk.

## Project Structure

- `github`: CI/CD pipeline configurations (GitHub Actions).
- `node`: Backend code for the customer portal APIs (Node.js).
- `react`: Frontend code for the customer portal (React).
- `salesforce-sync`: Backend code for syncing data with Salesforce.
- `webhook`: Code for handling Salesforce webhooks.

## Local Setup

### Prerequisites

- **Node.js** (v20 or higher)
- **Git**
- **MySQL** (for local database)
- **AWS CLI** (for deployment, optional for local setup)
- **Salesforce account** credentials
- **Chargebee account** credentials

### 1. Clone the Repository

Clone the repository and checkout the `main` branch:

```bash
git clone <repository-url>
cd <repository-folder>
git checkout main
```

### Backend (Node.js)

1. **Navigate to the Node.js Directory**:

   ```bash
   cd node
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Configure Environment Variables**: Create a `.env` file in the `node` directory with the following content. Replace placeholders with actual values:

   ```env
   # Server Configuration
   HOST=localhost
   PORT=5000
   
   # Local Database
   DB_HOST=localhost
   DB_USER=root
   DB_PORT=3306
   DB_PASSWORD=<your_db_password>
   DB_NAME=purple-cow-dev
   DB_DIALECT=mysql
   DB_LOGGING=false
   RESGISTRATION_TOKEN=<your_registration_token>
   
   # Encryption Keys
   ENCRYPTION_ALGORITHM=aes-256-cbc
   ENCRYPTION_KEY=<your_encryption_key>
   ENCRYPTION_RANDOM_IV=<your_random_iv>
   
   # Chargebee
   CHARGEBEE_SITE=<your_chargebee_site>
   CHARGEBEE_APIKEY=<your_chargebee_apikey>
   GATEWAY_ACCOUNT_ID=<your_gateway_account_id>
   
   # Salesforce
   SALESFORCE_BASE_URL=https://<your_instance>.salesforce.com
   SALESFORCE_USERNAME=<your_salesforce_username>
   SALESFORCE_PASSWORD=<your_salesforce_password>
   SALESFORCE_TOKEN=<your_salesforce_token>
   
   # AWS Configuration
   DEV_AWS_ACCESS_KEY_ID=<your_aws_access_key_id>
   DEV_AWS_SECRET_ACCESS_KEY=<your_aws_secret_access_key>
   DEV_AWS_REGION=ca-central-1
   AWS_USER_POOL_ID=<your_user_pool_id>
   AWS_CLIENT_ID=<your_client_id>
   AWS_CLIENT_SECRET=<your_client_secret>
   AWS_BUCKET_NAME=<your_bucket_name>
   
   # Product Catalog URLs
   INTERNET_PLANS=<your_bucket_url>/internet/plans.json
   INTERNET_ADD_ONS=<your_bucket_url>/internet/addons.json
   INTERNET_ASSETS=<your_bucket_url>/internet/assets.json
   TV_PLANS=<your_bucket_url>/tv/plans.json
   TV_ADD_ONS=<your_bucket_url>/tv/addons.json
   TV_ASSETS=<your_bucket_url>/tv/assets.json
   PHONE_PLANS=<your_bucket_url>/home-phones/plans.json
   PHONE_ADD_ONS=<your_bucket_url>/home-phones/addons.json
   PHONE_ASSETS=<your_bucket_url>/home-phones/assets.json
   
   # Elastic Search
   ELASTIC_SEARCH_NODE=<your_elastic_search_node>
   ELASTIC_SEARCH_API_KEY=<your_elastic_search_api_key>
   
   CRYPTO_SECRET=<your_crypto_secret>
   RESGISTRATION_USERNAME=<your_username>
   RESGISTRATION_PASSWORD=<your_password>
   REGISTRATION_QUEUE_URL=<your_sqs_queue_url>
   ```

4. **Start the Node.js Server**:

   ```bash
   npm run server
   ```

### Frontend (React)

1. **Navigate to the React Directory**:

   ```bash
   cd react
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Start the Development Server**:

   ```bash
   npm run dev
   ```

### Salesforce Sync

1. **Navigate to the Salesforce Sync Directory**:

   ```bash
   cd salesforce-sync
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Configure Environment Variables**: Create a `.env` file in the `salesforce-sync` directory using the same variables as the `node` `.env` file (see above).

4. **Start the Sync Server**:

   ```bash
   npm run server
   ```

### Webhook

1. **Navigate to the Webhook Directory**:

   ```bash
   cd webhook
   ```

2. **Install Dependencies**:

   ```bash
   npm install
   ```

3. **Configure Environment Variables**: Create a `.env` file in the `webhook` directory using the same variables as the `node` `.env` file (see above).

4. **Start the Webhook Server**:

   ```bash
   npm run server
   ```

## Deployment Guide

### AWS Configuration

1. **Elastic Beanstalk**:

   - Create two Elastic Beanstalk environments with load balancing for the Node.js API and React frontend.
   - Create one Elastic Beanstalk worker environment for SQS background processing.
   - Set up an API Gateway and configure it to route requests to the Node.js API and webhook services.
   - Create an SQS queue and configure it with the worker environment for background processing.

2. **RDS Database**:

   - Create an RDS instance for the MySQL database.
   - Update the application code with the RDS credentials in the `.env` files.

3. **Environment Variables**:

   - Configure all environment variables in the three Elastic Beanstalk instances (two application instances and one worker instance) using the same variables listed in the `node` `.env` file.

### GitHub Secrets Configuration

1. **Add Secrets to GitHub**:
   - Go to your GitHub repository settings.
   - Navigate to **Settings &gt; Secrets and variables &gt; Actions &gt; Secrets**.
   - Add the following secrets:

     ```plaintext
     DEV_LAMBDA_FUNCTION_NAME=<your_lambda_function_name>
     DEV_AWS_ACCESS_KEY_ID=<your_aws_access_key_id>
     DEV_AWS_SECRET_ACCESS_KEY=<your_aws_secret_access_key>
     DEV_S3_BUCKET_NAME=<your_s3_bucket_name>
     DEV_EC2_HOST=<your_ec2_host>
     DEV_EC2_USER=<your_ec2_user>
     DEV_EC2_SSH_PRIVATE_KEY=<your_ec2_ssh_private_key>
     ```

### CI/CD Workflow Setup

1. **24-Hour Sync and Webhook Workflow**:

   - Configure the `<environment-name>-cicd-sync.yml` file in the `github` directory to deploy the `salesforce-sync` and `webhook` code to the Elastic Beanstalk instance.
   - Push code to the branch specified in `<environment-name>-cicd-sync.yml` to trigger deployment.

2. **Customer Portal Workflow**:

   - Configure the `<environment-name>-cicd-main.yml` file in the `github` directory to deploy the `react` and `node` code to Elastic Beanstalk.
   - Push code to the branch specified in `<environment-name>-cicd-main.yml` to trigger deployment.

3. **Worker Workflow**:

   - Configure the `<environment-name>-cicd-worker.yml` file in the `github` directory to deploy the `node` code to the Elastic Beanstalk worker environment for SQS background processing.
   - Push code to the branch specified in `<environment-name>-cicd-worker.yml` to trigger deployment.

## API Endpoints

### Authentication

All endpoints use **Basic Authentication** with the following credentials:

- **Username**: Set in `RESGISTRATION_USERNAME` environment variable.
- **Password**: Set in `RESGISTRATION_PASSWORD` environment variable.

### Auth Service (`{{base_url}}`)

1. **Register User**

   - **Method**: `POST`
   - **URL**: `{{base_url}}/auth/register`
   - **Description**: Registers a user with an email. Optionally triggers AWS Cognito flow.
   - **Payload**:

     ```json
     {
       "email": "<EMAIL>",
       "isCognito": true
     }
     ```

2. **Create Cognito User**

   - **Method**: `POST`
   - **URL**: `{{base_url}}/auth/cognito/create-user`
   - **Description**: Creates a user in AWS Cognito.
   - **Payload**:

     ```json
     {
       "email": "<EMAIL>"
     }
     ```

### Contact Service (`{{base_url}}`)

1. **Delete Contact**

   - **Method**: `POST`
   - **URL**: `{{base_url}}/auth/delete-contact`
   - **Description**: Deletes a contact from the database and AWS Cognito.
   - **Payload**:

     ```json
     {
       "email": "<EMAIL>"
     }
     ```

2. **Get Contacts and Sync**

   - **Method**: `GET`
   - **URL**: `{{base_url_2}}/get-contacts-and-sync?email=<EMAIL>`
   - **Description**: Retrieves and syncs user contacts from an external system.
   - **Behavior**:
     - If `email` is provided: Syncs contacts for the specified user.
     - If `email` is not provided: Syncs all contacts.
   - **Examples**:
     - Sync specific user:

       ```
       GET {{base_url_2}}/get-contacts-and-sync?email=<EMAIL>
       ```
     - Sync all users:

       ```
       GET {{base_url_2}}/get-contacts-and-sync
       ```

# Customer Stage and Move Order Status Logic

This document outlines the status logic based on the customer's stage and the presence of a move order, as well as additional conditions related to payment arrangements and outstanding balances.

## Table of Contents
1. [Overview](#overview)
2. [Status Logic](#status-logic)
    - 2.1 [Onboarding Stage](#onboarding-stage)
    - 2.2 [Online Stage](#online-stage)
3. [Conditions and Definitions](#conditions-and-definitions)

---

## Overview

The system determines the status of a customer based on the current stage of the customer and the conditions related to move orders, payment arrangements, and outstanding balances. This logic ensures that the correct status is displayed depending on the customer's journey.

---

## Status Logic

### 2.1 Onboarding Stage

When the customer's stage is "onboarding", the status will be set to:

- **Status: "In Progress"**  
  If a move order is present or not, the status will automatically be marked as "In Progress".

### 2.2 Online Stage

When the customer's stage is "online", the system will apply the following conditions:

#### Move Order Present and Stage is "Eligible For Submission" or "Pre Response"

- **Status: "Processing Move Request"**  
  If a move order is present and the customer is in either the "Eligible For Submission" or "Pre Response" stage, the status will be set to "Processing Move Request".

#### Move Order Not Present or Other Stages

If the move order is not present or the customer is in any stage other than "Eligible For Submission" or "Pre Response", the status will be determined by the following conditions:

1. **No Outstanding or Payment Arrangement Present:**
   - **Status: "Active"**  
     If no outstanding balance or payment arrangement is found, the status will be set to "Active".

2. **Outstanding Balance Present:**
   - **Status: "Outstanding"**  
     If an outstanding balance is present, the status will be set to "Outstanding".

3. **Payment Arrangement Present:**
   - **Status: "Payment Arrangement"**  
     If a payment arrangement exists, the status will be set to "Payment Arrangement".

4. **Both Outstanding Balance and Payment Arrangement Present:**
   - **Status: "Outstanding"**  
     If both an outstanding balance and a payment arrangement are present, the status will be determined by comparing the number of invoices of outstanding balances and payment arrangements:
     - If the number of outstanding balances is greater than the number of payment arrangements, the status will be "Outstanding".
     - Otherwise, the status will be "Payment Arrangement".

---

## Troubleshooting

- **Environment Variables**: Ensure all `.env` files and AWS environment variables are correctly set.
- **AWS Services**: Verify that Elastic Beanstalk, RDS, SQS, and API Gateway are properly configured and accessible.
- **CI/CD Failures**: Check GitHub Actions logs, Elastic Beanstalk logs, and Lambda logs for deployment issues.
- **Database Issues**: Confirm RDS credentials and connectivity. Ensure the database schema is correctly set up.
- **API Issues**: Verify Basic Authentication credentials and API Gateway routing.

## Additional Notes

- Do not commit sensitive information (e.g., `.env` files, AWS keys) to version control. Use `.gitignore` to exclude them.
- Regularly monitor AWS CloudWatch logs for runtime errors.
- For further assistance, contact the project team or refer to the AWS and Salesforce documentation.