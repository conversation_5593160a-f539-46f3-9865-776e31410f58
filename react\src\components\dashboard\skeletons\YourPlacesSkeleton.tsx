import React from "react";
import ContentLoader from "react-content-loader";

const YourPlacesSkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      width={"100%"}
      height={100}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {/* //left bar */}
      <rect x="4" y="8" rx="5" ry="3" width="8" height="84" />
      {/* top bar*/}
      <rect x="7" y="84" rx="5" ry="3" width={"100%"} height="8" />
      {/* bottom bar*/}
      <rect x="5" y="8" rx="5" ry="3" width={"100%"} height="7" />
      {/* right bar*/}
      <rect x="99.50%" y="9" rx="5" ry="3" width="8" height="84" />

      {/* location icon */}
      <rect x="22.5" y="29.67" rx="5" ry="5" width="29" height="25" />
      {/* address */}
      <rect x="80" y="30" rx="5" ry="3" width="200" height="9" />
      {/* address */}
      <rect x="80" y="50" rx="5" ry="3" width="200" height="22" />
      {/* status bar */}
      <rect x="82%" y="30" rx="6" ry="6" width="91" height="30" />
      {/* Manage button */}
      <rect x="85%" y="30" rx="12" ry="12" width="91" height="30" />
    </ContentLoader>
  );
};

export default YourPlacesSkeleton;
