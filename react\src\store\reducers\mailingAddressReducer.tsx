import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { LocationDataState, mailingReducerState } from "../../typings/typing";


const initialState = {
  currentAddress: {
    address: "",
    apartment: "",
    city: "",
    province: "",
    pinCode: "",
  },
  showMailingAddressPopup: false,
  showMailingAddressConfirmationPopup: false,
} satisfies mailingReducerState as mailingReducerState;

const mailingAddressReducer = createSlice({
  name: "MAILING_ADDRESS_REDUCER",
  initialState,
  reducers: {
    toggleShowMailingAddressPopup: (state) => {
      state.showMailingAddressPopup = !state.showMailingAddressPopup;
    },
    toggleShowMailingAddressConfirmationPopup: (state) => {
      state.showMailingAddressConfirmationPopup =
        !state.showMailingAddressConfirmationPopup;
    },
    setCurrentMailingAddress: (state, action: PayloadAction<LocationDataState>) => {
      state.currentAddress = action.payload;
    },
    resetMailPopups: (state) => {
      state.showMailingAddressPopup = false;
      state.showMailingAddressConfirmationPopup = false;
    },
  },
});

export const {
  toggleShowMailingAddressPopup,
  toggleShowMailingAddressConfirmationPopup,
  setCurrentMailingAddress,
  resetMailPopups,
} = mailingAddressReducer.actions;

export default mailingAddressReducer.reducer;
