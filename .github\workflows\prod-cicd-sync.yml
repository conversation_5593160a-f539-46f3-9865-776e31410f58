name: 🚀 Deploy Salesforce-Sync and Webhook to AWS Elastic Beanstalk

on:
  push:
    branches:
      - main
    paths:
      - 'salesforce-sync/**'
      - 'webhook/**'

env:
  AWS_REGION: ca-central-1
  DEPLOY_PACKAGE_NAME: "salesforce-webhook-deploy-${{ github.sha }}.zip"
  APP_NAME: Customer-portal-webhook
  APP_ENV_NAME: Customer-portal-webhook-env

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v3

    # Create temporary folder and move necessary files
    - name: 📂 Prepare files for deployment
      run: |
        mkdir sync-webhook-deploy
        cp -r salesforce-sync sync-webhook-deploy/
        cp -r webhook sync-webhook-deploy/
        cp Procfile sync-webhook-deploy/
        cp start-all.sh sync-webhook-deploy/
        cd sync-webhook-deploy
        zip -r ../${{ env.DEPLOY_PACKAGE_NAME }} .
        cd ..

    # Configure AWS credentials for Elastic Beanstalk
    - name: 🔐 Configure AWS credentials for Elastic Beanstalk
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    # Upload deployment package to S3
    - name: ☁️ Upload deployment package to S3
      run: aws s3 cp ${{ env.DEPLOY_PACKAGE_NAME }} s3://${{ secrets.S3_BUCKET_NAME }}/

    # Verify deployment package exists in S3
    - name: 🔍 Verify deployment package in S3
      run: aws s3 ls s3://${{ secrets.S3_BUCKET_NAME }}/${{ env.DEPLOY_PACKAGE_NAME }}

    # Deploy to Elastic Beanstalk
    - name: 🚀 Deploy to Elastic Beanstalk
      run: |
        aws elasticbeanstalk create-application-version \
          --application-name ${{ env.APP_NAME }} \
          --version-label ${{ github.sha }} \
          --source-bundle S3Bucket="${{ secrets.S3_BUCKET_NAME }}",S3Key="${{ env.DEPLOY_PACKAGE_NAME }}"

        aws elasticbeanstalk update-environment \
          --application-name ${{ env.APP_NAME }} \
          --environment-name ${{ env.APP_ENV_NAME }} \
          --version-label ${{ github.sha }}

    - name: 🎉 Print success message
      run: echo "Salesforce-Sync and Webhook deployment to AWS Elastic Beanstalk completed successfully"