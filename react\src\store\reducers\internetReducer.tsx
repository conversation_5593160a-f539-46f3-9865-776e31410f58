import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import { internetReducerState } from "../../typings/typing";

const initialState = {
  internetPlan: {},
  newInternetPlan: {},
  showInternetPopup: false,
  showDeleteOrderPopup: false,
  showInternetConfirmationPopup: false,
} satisfies internetReducerState as internetReducerState;

const internetReducer = createSlice({
  name: "INTERNET_REDUCER",
  initialState,
  reducers: {
    setInternetPlan: (state, action: PayloadAction<number>) => {
      state.internetPlan = action.payload;
    },
    setNewInternetPlan: (state, action: PayloadAction<number>) => {
      state.newInternetPlan = action.payload;
    },
    toggleShowInternetPopup: (state) => {
      state.showInternetPopup = !state.showInternetPopup;
    },
    toggleShowDeletePopup: (state) => {
      state.showDeleteOrderPopup = !state.showDeleteOrderPopup;
    },
    toggleShowInternetConfirmationPopup: (state) => {
      state.showInternetConfirmationPopup =
        !state.showInternetConfirmationPopup;
    },
    resetInternetPopups: (state) => {
      state.showInternetPopup = false;
      state.showDeleteOrderPopup = false;
      state.showInternetConfirmationPopup = false;
    },
  },
});

export const {
  setInternetPlan,
  setNewInternetPlan,
  toggleShowInternetPopup,
  toggleShowDeletePopup,
  toggleShowInternetConfirmationPopup,
  resetInternetPopups
} = internetReducer.actions;

export default internetReducer.reducer;
