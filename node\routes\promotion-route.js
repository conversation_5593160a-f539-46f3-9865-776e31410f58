const { fetchPromotionsList, fetchEligibleLocation, claimOffer } = require("../controllers/promotion-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// GET endpoint for fetching the list of promotions
	router.get("/list", fetchPromotionsList); //this is not used

	// POST endpoint for fetching locations based on eligible promotions
	router.post("/locations", fetchEligibleLocation); //this is not used

	router.post("/claim", claimOffer); //this is not used

	// Mount the router at the specified base path
	app.use(`${basePath}/promotions`, router);
};
