import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import PlanDetailCard from "../../components/manage-plan/PlanDetailCard";
import PlanDetailSkeleton from "../../components/manage-plan/PlanDetailSkeleton";
import ServiceBoxSkeletonComponent from "../../components/manage-plan/ServiceBoxSkeleton";
import TVPlanBox from "../../components/manage-plan/TV/TVPlanBox";
import HomePhoneBox from "../../components/manage-plan/home-phone/HomePhoneBox";
import InternetPlanBox from "../../components/manage-plan/internet/InternetPlanBox";
import MailAddressBox from "../../components/manage-plan/mail-address/MailAddressBox";
import ActiveStatus from "../../components/manage-plan/plan-status/ActiveStatus";
import OffBoardingStatus from "../../components/manage-plan/plan-status/OffBoardingStatus";
import OnBoardingStatus from "../../components/manage-plan/plan-status/OnBoardingStatus";
import OutstandingStatus from "../../components/manage-plan/plan-status/OutstandingStatus";
import StatusDescription from "../../components/manage-plan/plan-status/StatusDescription";
import RenewalDateBox from "../../components/manage-plan/renewal-date/RenewalDateBox";
import ServiceAddressBox from "../../components/manage-plan/service-address/ServiceAddressBox";
import useMediaQuery from "../../hooks/MediaQueryHook";
import { useManagePlanMutation } from "../../services/api";
import { logout } from "../../store/reducers/authenticationReducer";
import { setSubscriptionType } from "../../store/reducers/customerSubscriptionReducer";
import { setHaveHomePhonePlan } from "../../store/reducers/homePhoneReducer";
import { setInternetPlan } from "../../store/reducers/internetReducer";
import { setCurrentMailingAddress } from "../../store/reducers/mailingAddressReducer";
import { setNextPaymentDate } from "../../store/reducers/nextPaymentDateReducer";
import { setCurrentServiceAddress } from "../../store/reducers/serviceAddressReducer";
import { setPlan } from "../../store/reducers/televisionReducer";
import { addNotification } from "../../store/reducers/toasterReducer";
import PaymentArrangementStatus from "../../components/manage-plan/plan-status/PaymentArrangementStatus";
import Billing from "../billing/Billing";
// import ViewEquipmentPopup from "../../components/manage-plan/ViewEquipmentPopup";

const locationTabs = ["Subscription", "Service Details", "Billing"];
const ManagePlan: React.FC = () => {
  const { id } = useParams();
  // const [showViewEquipmentPopup, setShowViewEquipmentPopup] =
  //   useState<boolean>(false);

  const [activeTab, setActiveTab] = useState(locationTabs[0]);
  const [planData, setPlanData] = useState<any>();
  const [getManagePlan, planLoading] = useManagePlanMutation();
  const isMoveOrder = planData?.customerInternet?.internetElMoveOrder
    ? planData.customerInternet.internetElMoveOrder.stage !== "Complete"
      ? planData.customerInternet.internetElMoveOrder
      : false
    : false;
  const isTablet = useMediaQuery("(max-width:1024px)");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch manage plan data on component mount
  useEffect(() => {
    handelGetManagePlan();
  }, []);

  // Function to fetch manage plan data
  const handelGetManagePlan = async () => {
    try {
      const response = await getManagePlan(id).unwrap();
      if (response?.status === 200) {
        setPlanData(response?.data);
        dispatch(setInternetPlan(response?.data?.customerInternet));
        dispatch(
          setSubscriptionType(
            response?.data?.customerSubscription?.subscription_type
          )
        );
        dispatch(setPlan(response?.data?.customerTv));
        dispatch(setHaveHomePhonePlan(response?.data?.customerPhone));
        dispatch(setCurrentServiceAddress(response?.data?.serviceAddress));
        dispatch(setCurrentMailingAddress(response?.data?.mailingAddress));
        dispatch(setNextPaymentDate(response?.data?.customerSubscription));
      }
    } catch (error: any) {
      navigate("/"); // navigate to dashboard if user try to visit invalid id for plan
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  return (
    <div>
      {planLoading?.isLoading ? (
        <PlanDetailSkeleton />
      ) : (
        <PlanDetailCard planData={planData} />
      )}
      <div className="flex max-lg:flex-col lg:gap-5 lg:pb-10 pb-30">
        <div
          className={`bg-white p-5 rounded-20 flex flex-col lg::gap-[30px] gap-5 flex-wrap basis-full 2xl:max-w-[calc(100%-320px)] lg:max-w-[calc(100%-280px)] w-full my-5`}
        >
          {isTablet && (
            <div className="flex flex-col gap-5 w-full">
              <div className="flex max-lg:flex-col gap-5 p-5 border border-[#EBDBFF] h-fit rounded-20">
                <div>
                  <p className="font-medium">STATUS OF YOUR PLAN</p>
                </div>
                <div className="flex gap-5 overflow-auto scrollbarNone ">
                  {planLoading?.isLoading ? (
                    <ServiceBoxSkeletonComponent />
                  ) : (
                    <>
                      {planData?.stage === "Onboarding" && (
                        <OnBoardingStatus
                          isMoveOrder={isMoveOrder}
                          status={planData?.customerInternet}
                        />
                      )}
                      {planData?.stage === "Offboarding" && (
                        <OffBoardingStatus />
                      )}
                      {planData?.stage === "Online" && (
                        <ActiveStatus isMoveOrder={isMoveOrder} />
                      )}
                      {planData?.stage === "Outstanding" && (
                        <OutstandingStatus isMoveOrder={isMoveOrder} />
                      )}
                      {planData?.stage === "Payment arrangements" && (
                        <PaymentArrangementStatus
                          expectedDate={planData?.expected_payment_date}
                          isMoveOrder={isMoveOrder}
                        />
                      )}
                    </>
                  )}
                </div>
              </div>
              {planData?.stage === "Onboarding" &&
                (planLoading?.isLoading ? (
                  <div className="flex flex-col p-5 gap-2.5 rounded-30 shadow-cardShadow05">
                    <ServiceBoxSkeletonComponent />
                  </div>
                ) : (
                  <StatusDescription plan={planData?.customerInternet} />
                ))}
            </div>
          )}

          {/* SUBSCRIPTION, SERVICE DETAILS, BILLING tabs */}
          <div className="flex gap-1 md:gap-2 2xl:gap-5 w-full">
            {locationTabs.map((tab, i) => {
              return (
                <div
                  key={i}
                  className={`whitespace-nowrap transition-all duration-75 ${
                    activeTab === tab
                      ? "bg-[#ECDEFF] font-medium border border-[#AA7DE6]"
                      : "hover:bg-color_F7F7F7"
                  } p-[10px] text-xs sm:text-base rounded-20 cursor-pointer h-fit`}
                  onClick={() => setActiveTab(tab)}
                >
                  <p className="leading-none">{tab}</p>
                </div>
              );
            })}
          </div>

          <hr />
          {/* SUBSCRIPTION container */}
          {activeTab === "Subscription" && (
            <div className="subscriptions flex flex-col gap-5 2xl:gap-30 flex-wrap">
              <div className="flex lg:w-[calc(50%-10px)] flex-col gap-2.5 xl:gap-5 2xl:gap-30">
                <div>
                  <p className="2xl:text-base font-medium uppercase">Plan</p>
                </div>
                {planLoading?.isLoading ? (
                  <ServiceBoxSkeletonComponent />
                ) : (
                  <InternetPlanBox
                    refetch={handelGetManagePlan}
                    subscriptionID={planData?.customerSubscription?.id}
                    stage={planData?.stage}
                    isMoveOrder={isMoveOrder}
                  />
                )}
              </div>
              <div className="flex flex-col gap-2.5 xl:gap-5 2xl:gap-30">
                <div>
                  <p className="2xl:text-base font-medium uppercase">Add ons</p>
                </div>
                <div className="flex max-lg:flex-col gap-5 max-lg:gap-2.5">
                  {planLoading?.isLoading ? (
                    <ServiceBoxSkeletonComponent />
                  ) : (
                    <TVPlanBox
                      refetch={handelGetManagePlan}
                      subscriptionID={planData?.customerSubscription?.id}
                      stage={planData?.stage}
                      billingDate={
                        planData?.customerSubscription?.next_billing_at
                      }
                    />
                  )}

                  {planLoading?.isLoading ? (
                    <ServiceBoxSkeletonComponent />
                  ) : (
                    <HomePhoneBox
                      refetch={handelGetManagePlan}
                      subscriptionID={planData?.customerSubscription?.id}
                      stage={planData?.stage}
                      billingDate={
                        planData?.customerSubscription?.next_billing_at
                      }
                      isMoveOrder={isMoveOrder}
                    />
                  )}
                </div>
              </div>
            </div>
          )}

          {/* SERVICE DETAILS container */}
          {activeTab === "Service Details" && (
            <div className="flex flex-col gap-5 2xl:gap-30 flex-wrap">
              <div className="flex 2xl:gap-30 xl:gap-5 gap-2.5 flex-col lg:w-[calc(50%-10px)]">
                <p className="text-base font-medium uppercase">Renewal </p>
                {planLoading?.isLoading ? (
                  <ServiceBoxSkeletonComponent />
                ) : (
                  <RenewalDateBox refetch={handelGetManagePlan} />
                )}
              </div>
              <div className="flex flex-col gap-2.5 xl:gap-5 2xl:gap-30">
                <div>
                  <p className="text-base font-medium uppercase">Address</p>
                </div>
                <div className="flex max-lg:flex-col gap-5 max-lg:gap-2.5">
                  {planLoading?.isLoading ? (
                    <ServiceBoxSkeletonComponent />
                  ) : (
                    <ServiceAddressBox
                      planData={planData}
                      isMoveOrder={isMoveOrder}
                      refetch={handelGetManagePlan}
                    />
                  )}

                  {planLoading?.isLoading ? (
                    <ServiceBoxSkeletonComponent />
                  ) : (
                    <MailAddressBox refetch={handelGetManagePlan} />
                  )}
                </div>
              </div>
              {/* commented view equipment code as it is in face-2 */}
              {/* <div className="flex flex-col 2xl:gap-30 xl:gap-5 gap-2.5  lg:w-[calc(50%-10px)]">
              <div>
                <p className="text-base font-medium uppercase">Equipment</p>
              </div>
              <div
                className="bg-white shadow-cardShadow05 flex justify-between  p-2 lg:p-3 2xl:p-5  2xl:rounded-20 rounded-10 gap-5 cursor-pointer items-center"
                onClick={() =>
                  setShowViewEquipmentPopup(!showViewEquipmentPopup)
                }
              >
                <div className="">
                  <p className="text-xl 2xl:text-[26px] font-medium">
                    View Equipment
                  </p>
                </div>
                <div className="flex cursor-pointer px-1.5 py-2 justify-center items-center 2xl:w-[44px] 2xl:h-[44px] h-[34px] w-[38px] border border-black 2xl:rounded-[12px] rounded-[8px]">
                  <span>
                    <RightCaretIcon />
                  </span>
                </div>
              </div>
            </div> */}

              {/* commented Cancel Service code as it is in face-2 */}
              {/* <div className="flex flex-col 2xl:gap-30 xl:gap-5 gap-2.5 lg:w-[calc(50%-10px)]">
              <p className="text-base font-medium uppercase">Cancel Service </p>
              <CancelService />
            </div> */}
            </div>
          )}

          {/* BILLING container */}
          {activeTab === "Billing" && (
            <div className="flex flex-col gap-5 2xl:gap-30 flex-wrap max-w-full">
              <Billing planData={planData} />
            </div>
          )}
        </div>

        {!isTablet && (
          <div className="flex flex-col gap-5 2xl:w-[320px] lg:w-[280px]">
            <div className="sticky top-5">
              <div className="flex flex-col 2xl:gap-5 gap-5 px-5 2lx:pt-30 pt-5 pb-5 bg-white rounded-20 w-full mt-5">
                <div>
                  <p className="font-medium">STATUS OF YOUR PLAN</p>
                </div>
                {planLoading?.isLoading ? (
                  <ServiceBoxSkeletonComponent />
                ) : (
                  <>
                    {planData?.stage === "Onboarding" && (
                      <OnBoardingStatus
                        isMoveOrder={isMoveOrder}
                        status={planData?.customerInternet}
                      />
                    )}
                    {planData?.stage === "Offboarding" && <OffBoardingStatus />}
                    {planData?.stage === "Online" && (
                      <ActiveStatus isMoveOrder={isMoveOrder} />
                    )}
                    {planData?.stage === "Outstanding" && (
                      <OutstandingStatus isMoveOrder={isMoveOrder} />
                    )}
                    {planData?.stage === "Payment arrangements" && (
                      <PaymentArrangementStatus
                        expectedDate={planData?.expected_payment_date}
                        isMoveOrder={isMoveOrder}
                      />
                    )}
                  </>
                )}
              </div>
              {planData?.stage === "Onboarding" &&
                (planLoading?.isLoading ? (
                  <div className="flex flex-col 2xl:w-[320px] w-[280px] 2xl:p-30 lg:p-5 p-2.5 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 mt-3">
                    <ServiceBoxSkeletonComponent />
                  </div>
                ) : (
                  <StatusDescription plan={planData?.customerInternet} />
                ))}
            </div>
          </div>
        )}
        {/* View EquipmentPopup */}
        {/* {showViewEquipmentPopup && (
          <ViewEquipmentPopup
            closeHandler={() =>
              setShowViewEquipmentPopup(!showViewEquipmentPopup)
            }
          />
        )} */}
      </div>
    </div>
  );
};

export default ManagePlan;
