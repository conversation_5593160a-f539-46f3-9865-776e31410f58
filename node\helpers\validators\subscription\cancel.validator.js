const Joi = require('joi');

const cancelValidation = Joi.object({
    customer_details_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer details ID is required.',
            'number.base': 'Customer details ID must be a number.',
            'number.integer': 'Customer details ID must be an integer.'
        }),
    customer_subscription_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer subscription ID is required.',
            'number.base': 'Customer subscription ID must be a number.',
            'number.integer': 'Customer subscription ID must be an integer.'
        }),
    cancel_type: Joi.string()
        .valid('tv', 'phone')
        .required()
        .messages({
            'any.required': 'Cancel type ID is required.',
            'any.only': 'Cancel type ID must be either `tv` or `phone`.'
        })
});

module.exports = { cancelValidation };