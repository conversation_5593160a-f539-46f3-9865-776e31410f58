name: 🚀 Deploy to AWS Elastic Beanstalk Worker

on:
  push:
    branches:
      - development
    paths:
      - "node/**"

env:
  AWS_REGION: ca-central-1
  DEPLOY_PACKAGE_NAME: "purplecow-dev-worker-deploy-${{ github.sha }}.zip"
  APP_NAME: dev-user-register-worker
  APP_ENV_NAME: Dev-user-register-worker-env

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v3

      # zip the entire node folder
      - name: 🗂️ Zip node folder
        run: |
          cd node
          zip -r ../${{ env.DEPLOY_PACKAGE_NAME }} . -x '*.git*'

      # Configure AWS credentials for Elastic Beanstalk
      - name: 🔐 Configure AWS credentials for Elastic Beanstalk
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.DEV_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEV_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      # Upload React build to S3
      - name: ☁️ Upload React build to S3
        run: aws s3 cp ${{ env.DEPLOY_PACKAGE_NAME }} s3://${{ secrets.DEV_S3_BUCKET_NAME }}/

      # Verify React build exists in S3
      - name: 🔍 Verify React build exists in S3
        run: aws s3 ls s3://${{ secrets.DEV_S3_BUCKET_NAME }}/${{ env.DEPLOY_PACKAGE_NAME }}

      # Deploy to Elastic Beanstalk
      - name: 🚀 Deploy to Elastic Beanstalk Worker
        run: |
          aws elasticbeanstalk create-application-version \
            --application-name ${{ env.APP_NAME }} \
            --version-label ${{ github.sha }} \
            --source-bundle S3Bucket="${{ secrets.DEV_S3_BUCKET_NAME }}",S3Key="${{ env.DEPLOY_PACKAGE_NAME }}"

          aws elasticbeanstalk update-environment \
            --application-name ${{ env.APP_NAME }} \
            --environment-name ${{ env.APP_ENV_NAME }} \
            --version-label ${{ github.sha }}

      - name: 🎉 Print success message
        run: echo "AWS Elastic Beanstalk worker completed successfully"
