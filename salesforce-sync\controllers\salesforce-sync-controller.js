
const DeleteContactService = require("../services/delete-contact-service");
const ContactTestService = require("../services/process-services/contact-sync-services");
const OrderTestServices = require("../services/process-services/order-services");

/**
 * Controller method to sync customer details from Salesforce.
 * @param {object} req - Express request object.
 * @param {object} res - Express response object.
 * @param {function} next - Express next function.
 */
class SalesforceSyncController {

    async deleteContact(req, res, next) {
        try {
            const { email } = req.body;
            if (!email) throw new Error("Email not found");
            const deleteContactService = new DeleteContactService();
            deleteContactService.deleteContact(email);
            res.status(200).send({ message: `Success` });
        } catch (error) {
            console.error("Sync Controller delete contact-> ", error);
            next(error);
        }
    }

    async getContactsAndSync(req, res, next) {
        try {
            const { email } = req.query;
            const contactTestService = new ContactTestService();
            contactTestService.getContactDetails(email);
            res.status(200).send({ message: `Success` });
        } catch (error) {
            console.error("Sync Controller get contacts and sync-> ", error);
            next(error);
        }
    }
    
    async getOrderssAndSync(req, res, next) {
        try {
            const orderTestServices = new OrderTestServices();
            orderTestServices.getCreationOrderDetails();
            res.status(200).send({ message: `Success` });
        } catch (error) {
            console.error("Sync Controller get order and sync-> ", error);
            next(error);
        }
    }
}
// Export an instance of the SalesforceSyncController class
module.exports = new SalesforceSyncController();