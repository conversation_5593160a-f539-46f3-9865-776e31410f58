const Validator = require('../helpers/validators');
const { validator } = require("../middleware/validationMid");
const { managePlan, updateAddress, getAddress, getPlanDetails } = require("../controllers/plan-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// Register Manage Plan Route
	router.get("/manage/:custDetailsId", managePlan);
	// Update Address Route with validation middleware
	router.put("/update-address", validator(Validator.customerAddressValidation), updateAddress);
	// Plan Details Route with query validation middleware
	router.get("/details", validator(Validator.planDetails, "query"), getPlanDetails);
	// Use the base path passed from index.js
	app.use(`${basePath}/plan`, router); // Mount the router at the base path
};
