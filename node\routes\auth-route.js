const Validator = require('../helpers/validators')
const { validator } = require("../middleware/validationMid");
const { userLogin, userResetPassword, userForgotPassword, userCreateCognitoAccount, deleteContact, mapSubscription, userSignupQueue } = require("../controllers/auth-controller");
const router = require("express").Router();

module.exports = (app) => {

	// Endpoint for user registration
	router.post("/register", validator(Validator.signupValidation), userSignupQueue);
	// Endpoint for create cognito user
	router.post("/cognito/create-user", validator(Validator.cognitoCreateUserValidation), userCreateCognitoAccount);
	// Endpoint for user login
	router.post("/login", validator(Validator.loginValidation), userLogin);
	// Endpoint for resetting password
	router.post("/reset-password", validator(Validator.resetPasswordValidation), userResetPassword);
	// Endpoint for forgot password functionality
	router.post("/forgot-password", validator(Validator.forgotPasswordValidation), userForgotPassword);
	// Endpoint for delete user functionality
	router.post("/delete-contact", validator(Validator.forgotPasswordValidation), deleteContact); //need to change validation
	// Endpoint for migrate subscription card mapping
	router.get("/map-subscription", mapSubscription);

	app.use(`/auth`, router);
};
