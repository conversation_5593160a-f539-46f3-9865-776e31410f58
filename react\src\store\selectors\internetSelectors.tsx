import { rootState } from "../reducers/rootReducer";

export const internetPlan = (store: rootState) =>
  store.internetReducer.internetPlan;

export const showInternetPopup = (store: rootState) =>
  store.internetReducer.showInternetPopup;

export const newInternetPlan = (store: rootState) =>
  store.internetReducer.newInternetPlan;

export const showDeleteOrderPopup = (store: rootState) =>
  store.internetReducer.showDeleteOrderPopup;

export const showInternetConfirmationPopup = (store: rootState) =>
  store.internetReducer.showInternetConfirmationPopup;
