import { rootState } from "../reducers/rootReducer";

export const basePlan = (store: rootState) =>
  store.televisionReducer.plan.basePlan;

export const plan = (store: rootState) => store.televisionReducer.plan;

export const isPlanRemoved = (store: rootState) =>
  store.televisionReducer.plan.isPlanRemoved;

export const isPlanUpdated = (store: rootState) =>
  store.televisionReducer.plan.isPlanUpdated;

export const additionalPlan = (store: rootState) =>
  store.televisionReducer.plan.additionalPlan;

export const additionalChannels = (store: rootState) =>
  store.televisionReducer.plan.additionalChannels;

export const showTelevisionPopup = (store: rootState) =>
  store.televisionReducer.showTelevisionPopup;

export const showTelevisionConfirmationPopup = (store: rootState) =>
  store.televisionReducer.showTelevisionConfirmationPopup;

export const showDeleteOrderPopup = (store: rootState) =>
  store.televisionReducer.showDeleteOrderPopup;

export const dvr = (store: rootState) => store.televisionReducer.plan.DVR;
