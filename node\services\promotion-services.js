const db = require('../models/index.js');
const { Sequelize } = require("sequelize");
const { Op } = require('sequelize');
const CustomError = require("../utils/errors/CustomError");
const CONFIG = require('../config');
const { RESPONSES, RESPONSE_CODES, RESPONSE_MESSAGES } = require('../utils/ResponseCodes');
const PlanServices = require("../services/plan-services");
const planServices = new PlanServices();
const SubscriptionServices = require("../services/subscription-services");
const subscriptionServices = new SubscriptionServices();
const ChargebeeClient = require("../clients/chargebee/chargebee.js");
const callChargebee = new ChargebeeClient();
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();



class PromotionServices {
    /**
     * Fetches a list of promotions based on the contact_id
     * @param {number} contact_id - The ID of the contact to fetch promotions for
     * @returns {Object} Object containing status and data
     */
    async fetchPromotionsList(contact_id, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("fetch_promotions_list_logs", { ...elasticLogObj, request: { contact_id } });
        try {
            const data = {};

            // Fetch customer details
            const customerDetails = await db.CustomerDetails.findAll({
                where: { contact_id },
                order: [['id', 'DESC']],
                attributes: ["eligible_promotions"],
                raw: true
            });

            // Collect all eligible promotions and ensure uniqueness
            const allValues = customerDetails.reduce((acc, item) => {
                const promotions = JSON.parse(item.eligible_promotions);
                return acc.concat(promotions);
            }, []);

            const uniqueValues = [...new Set(allValues)];

            // Fetch promotions based on unique eligible promotions
            const excludeArray = ["sf_record_id", "sf_updatedAt", "createdAt", "updatedAt"];
            const promotions = await db.Promotion.findAll({
                attributes: { exclude: excludeArray },
                where: { promotion_name: uniqueValues },
                raw: true
            });

            // Add offer_service field based on promotion_name
            const promotionsData = promotions.map(promotion => {
                if (promotion.name === "Free TV") {
                    return { ...promotion, offer_service: '1_BPKG_LN' }; //change offer service
                }
                return promotion;
            });

            data.promotions = promotionsData;
            const response = { status: true, data };

            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });
            return response;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`PromotionServices fetchPromotionsList -> `, error);
            throw error;
        }
    }

    /**
     * Fetches eligible locations based on contact_id and promotion_name
     * @param {number} contact_id - The ID of the contact to fetch locations for
     * @param {Object} body - Request body containing promotion_name
     * @returns {Object} Object containing status and data
     */
    async fetchEligibleLocation(contact_id, body, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("fetch_promotions_eligible_location_logs", { ...elasticLogObj, request: { contact_id, body } });
        try {
            const { promotion_name } = body;
            if (!promotion_name) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'promotion_name is required');

            // Define query options to fetch eligible locations
            const findOptions = {
                include: {
                    model: db.CustomerAddresses,
                    as: 'serviceAddress',
                    attributes: [],
                    required: false
                },
                attributes: [
                    "id",
                    "eligible_promotions",
                    [Sequelize.literal('`serviceAddress`.`full_address`'), 'full_address'],
                    [Sequelize.literal('`serviceAddress`.`status`'), 'status']
                ],
                where: {
                    contact_id,
                    eligible_promotions: { [Op.substring]: promotion_name }
                },
                order: [['id', 'DESC']],
                raw: true
            };

            // Fetch customer details with eligible locations
            const CustomerDetails = await db.CustomerDetails.findAll(findOptions);

            const response = { status: true, data: { locations: CustomerDetails } };
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });
            return response;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`PromotionServices fetchEligibleLocation -> `, error);
            throw error;
        }
    }


    async claimOffer(body, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("claim_promotions_offer_logs", { ...elasticLogObj, request: { body } });
        try {
            const { offer_service, name, location_id } = body;
            if (!offer_service || !name || !location_id) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
            }

            const customerSubscriptionDetails = await db.CustomerSubscriptions.findOne({
                include: {
                    model: db.CustomerDetails,
                    as: 'customerDetails',
                    required: false
                },
                attributes: ["customer_details_id", "cb_subscription_id"],
                where: { customer_details_id: location_id }
            });

            let addons = [];
            let planURL = null;

            if (name === "Free TV") {
                if (customerSubscriptionDetails?.customerDetails?.tv_id) {
                    throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.ALREADY_EXIST);
                }
                planURL = CONFIG.tv.plans;
            }

            if (!planURL) {
                throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            }

            const planData = await planServices.getPlanDetailsFromJSON(planURL);
            const selectedPlan = planData.find(plan => plan.api_name === offer_service);

            if (selectedPlan) {
                const extraMonthly = selectedPlan?.billing_period?.[0].monthly;
                addons.push({ id: extraMonthly.api_name, billing_cycles: 1, unit_price: 0 });
            }

            const reqPayload = {
                change_option: "immediately",
                addons: addons
            };

            const info = await callChargebee.updateSubscription(customerSubscriptionDetails?.cb_subscription_id, reqPayload);

            if (info && name === "Free TV") {
                const tvObj = {
                    plan_name: offer_service,
                    total_service_cost: 0,
                    status: info?.status,
                };

                const payload = {
                    plan_name: offer_service,
                    extra_packages: [],
                    single_channels: [],
                    iptv_products: [],
                    orderType: "create",
                    customerSfId: customerSubscriptionDetails?.customerDetails?.sf_record_id
                };

                try {
                    const orderId = await subscriptionServices.manageTvOrderinSF(payload);
                    tvObj.sf_response_status = orderId ? "success" : "failure";
                    if (orderId) tvObj.sf_record_id = orderId;
                } catch (error) {
                    tvObj.sf_response_status = "failure";
                    tvObj.sf_error_log = error?.message;
                    tvObj.sf_response_type = error?.errorType === "TVObject" ? "create" : "change";
                    if (error?.sfTvId) tvObj.sf_record_id = error?.sfTvId;
                } finally {
                    try {
                        const customerTv = await db.CustomerTv.create(tvObj);
                        await db.CustomerDetails.update({ tv_id: customerTv.id }, { where: { id: location_id } });
                    } catch (err) {
                        throw err;
                    }
                }
            }

            const response = { status: true };
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });
            return response;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`PromotionServices claimOffer -> `, error);
            throw error;
        }
    }

}

module.exports = PromotionServices;