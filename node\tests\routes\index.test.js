/**
 * Test Suite Runner for All Route Tests
 * 
 * This file serves as the main entry point for running all route tests.
 * It imports and runs all individual route test files.
 */

// Import all route test files
require('./auth-route.test.js');
require('./billing-route.test.js');
require('./card-route.test.js');
require('./customer-route.test.js');
require('./dashboard-route.test.js');
require('./payment-route.test.js');
require('./plan-route.test.js');
require('./promotion-route.test.js');
require('./referral-route.test.js');
require('./retry-route.test.js');
require('./subscription-route.test.js');
require('./worker-route.test.js');

describe('All Routes Test Suite', () => {
  beforeAll(() => {
    console.log('🚀 Starting comprehensive route tests for Purple Cow Portal API');
    console.log('📋 Testing the following routes:');
    console.log('   - Auth Routes (/auth/*)');
    console.log('   - Billing Routes (/api/v1/billing/*)');
    console.log('   - Card Routes (/api/v1/card/*)');
    console.log('   - Customer Routes (/api/v1/customer/*)');
    console.log('   - Dashboard Routes (/api/v1/dashboard/*)');
    console.log('   - Payment Routes (/api/v1/payment/*)');
    console.log('   - Plan Routes (/api/v1/plan/*)');
    console.log('   - Promotion Routes (/api/v1/promotions/*)');
    console.log('   - Referral Routes (/api/v1/referrals/*)');
    console.log('   - Retry Routes (/retry/*)');
    console.log('   - Subscription Routes (/api/v1/subscription/*)');
    console.log('   - Worker Routes (/worker/*)');
  });

  afterAll(() => {
    console.log('✅ All route tests completed successfully!');
  });

  it('should have all route test files loaded', () => {
    // This test ensures all route test files are properly imported
    expect(true).toBe(true);
  });
});
