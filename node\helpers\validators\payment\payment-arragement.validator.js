const Joi = require('joi');

const paymentArrangementValidation = Joi.object({
    invoice_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Subscription inovoice ID is required.',
            'number.base': 'Subscription inovoice ID must be a number.',
            'number.integer': 'Subscription inovoice ID must be an integer.'
        }),
    date: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Payment arrangement date is required.'
        }),
});

module.exports = { paymentArrangementValidation };
