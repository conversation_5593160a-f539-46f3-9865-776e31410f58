import React from "react";
import CustomCalendar from "../../common/CustomCalendar";
import Button from "../../common/Button";
import { useSelector } from "react-redux";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";

type Props = {
  nextPaymentDate: Date | null;
  currentPaymentDate: Date | null;
  updateNextPaymentDate: (value: Date) => void;
  closeHandler: () => void;
  handleSubmit: () => void;
  isLoading: boolean;
};

const ChangeRenewalDatePopup: React.FC<Props> = ({
  currentPaymentDate,
  nextPaymentDate,
  updateNextPaymentDate,
  handleSubmit,
  closeHandler,
  isLoading,
}) => {
  const subscriptionType = useSelector(customerSubscriptionType);
  let minDate = currentPaymentDate?.next_billing_at
    ? new Date(currentPaymentDate.next_billing_at)
    : null;
  if (minDate) {
    minDate = new Date(
      minDate.getFullYear(),
      minDate.getMonth(),
      minDate.getDate() + 1
    );
  }

  let maxDate;
  if (subscriptionType === "monthly") {
    maxDate = minDate
      ? new Date(
          minDate.getFullYear(),
          minDate.getMonth() + 1,
          minDate.getDate()
        )
      : null;
  } else if (subscriptionType === "yearly") {
    maxDate = minDate
      ? new Date(
          minDate.getFullYear() + 1,
          minDate.getMonth(),
          minDate.getDate()
        )
      : null;
  }

  return (
    <div className="flex gap-5 flex-col">
      <div>
        <p className="text-base">
          Please update the date you would like your monthly bill to come out.
        </p>
      </div>
      <div className="flex justify-center text-base font-medium">
        <p className="lg:text-base text-sm uppercase">
          Select monthly payment date
        </p>
      </div>
      <div className={`${isLoading && "pointer-events-none"}`}>
        <CustomCalendar
          selectedDate={nextPaymentDate}
          setDate={updateNextPaymentDate}
          // timeRangeInDays={subscriptionType === 'monthly' ? 30 : 365}
          // timeRangeInDays={30}
          maxDate={maxDate}
          minDate={minDate}
          isLoading={isLoading}
        />
      </div>
      <div className="flex gap-2.5 lg:flex-row flex-col">
        <div className="basis-full">
          <Button
            title="Go back"
            btnType="transparent"
            attributes={{
              disabled: isLoading,
            }}
            type="button"
            clickEvent={closeHandler}
            className="disabled:opacity-50"
          />
        </div>
        <div className="basis-full">
          <Button
            title="Next"
            clickEvent={handleSubmit}
            className="disabled:opacity-50"
            attributes={{
              disabled: !nextPaymentDate,
            }}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default ChangeRenewalDatePopup;
