const moment = require('moment');
const db = require("../models");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require('../utils/ResponseCodes');
const CustomError = require('../utils/errors/CustomError');
const SalesforceService = require("./salesforce-services");
const axios = require('axios');
const CONFIG = require('../config');
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const momentTimezone = require('moment-timezone');
const { getCurrentAtlanticTime } = require('../helpers/privacyAlgorithms');
const { Op } = require('sequelize');
const elasticSearch = new ElasticSearch();
const SERVICE_ERROR = "Unable to update the service address. Please contact support for assistance";

class PlanServices {

    // Method to fetch and format plan details for a customer
    async managePlan(contact_id, cust_details_id, elasticLogObj) {
        try {
            const excludeArray = ["country", "sf_record_id", "address_type", "createdAt", "updatedAt"];
            let customerDetails = await db.CustomerDetails.findOne({
                include: this.getIncludeModels(excludeArray),
                attributes: ["id", "stage"],
                where: { contact_id, id: cust_details_id }
            });

            if (!customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            customerDetails = customerDetails.toJSON();

            if (customerDetails?.stage === 'Hidden') throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            const moveOrderstage = customerDetails?.customerInternet?.internetElMoveOrder?.stage;
            const moveOrderId = customerDetails?.customerInternet?.internetElMoveOrder?.sf_record_id;
            const creationOrderId = customerDetails?.customerInternet?.internetElCreationOrder?.sf_record_id;

            // if (moveOrderstage && (moveOrderstage !== 'Complete' || moveOrderId !== creationOrderId)) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

            // Format the date
            if (customerDetails?.customerSubscription?.next_billing_at) customerDetails.customerSubscription.next_billing_at = moment(customerDetails.customerSubscription.next_billing_at).format('MMMM D, YYYY');
            if (customerDetails?.customerTv) customerDetails.customerTv.packages = await this.getandUpdatePlanDetailsFromJSON(customerDetails.customerTv, "tv");

            if (customerDetails?.customerInternet) customerDetails.customerInternet = await this.getPlanStatus(customerDetails);
            customerDetails.cp_stage = customerDetails?.stage;

            const moveOrderExistId = customerDetails?.customerInternet?.internetElMoveOrder;

            if (moveOrderExistId) {
                if (moveOrderstage == "Eligible For Submission" || moveOrderstage == "Pre Response") customerDetails.moving_stage = "Processing move request";
            }

            if (customerDetails?.stage === 'Online') {
                const currentDate = new Date(getCurrentAtlanticTime(null, "sfUpdate"));
                currentDate.setHours(0, 0, 0, 0);
                const subscriptionInvoices = await db.SubscriptionInvoice.findAll({
                    where: {
                        status: ['PAYMENT_DUE', 'NOT_PAID'],
                        customer_details_id: customerDetails.id,
                    },
                    attributes: ["status", "expected_payment_date"],
                    order: [['createdAt', 'DESC']],
                    raw: true,
                });

                let hasPaymentArrangement = false;
                if (subscriptionInvoices.length) {
                    let paymentArrangementCount = 0;
                    for (const invoice of subscriptionInvoices) {
                        const { status, expected_payment_date } = invoice;
                        const paymentDate = new Date(expected_payment_date); // Convert to Date object
                        paymentDate.setHours(0, 0, 0, 0);
                        if ((status === "PAYMENT_DUE" || status === "NOT_PAID") && expected_payment_date !== null) {
                            if (paymentDate > currentDate) {
                                customerDetails.stage = "Payment arrangements";
                                customerDetails.expected_payment_date = expected_payment_date || null;
                                hasPaymentArrangement = true;
                                paymentArrangementCount++;
                            }
                        }
                    }

                    if (paymentArrangementCount != subscriptionInvoices.length) customerDetails.stage = "Outstanding";

                    // If no payment arrangement found, set to "Outstanding"
                    if (!hasPaymentArrangement) {
                        customerDetails.stage = "Outstanding";
                    }
                }
            }

            if (customerDetails?.customerPhone?.sf_phone_type) {
                customerDetails.customerPhone.sf_phone_type = JSON.parse(customerDetails.customerPhone.sf_phone_type);
            }

            const result = { status: true, data: customerDetails };

            const dateStr = customerDetails?.customerInternet?.internetElSpeedChangeOrder?.expected_completion_date;
            if (dateStr) {
                const formattedDate = momentTimezone.tz(dateStr, 'America/Halifax').format('MMM D, YYYY');
                result.data.customerInternet.internetElSpeedChangeOrder.expected_completion_date = formattedDate;
            }
            const total_outstanding = customerDetails?.customerSubscription?.balance_due;
            let payment_arrangement = false;
            if (total_outstanding > 0) {
                payment_arrangement = await this.fetchOutstanding(customerDetails?.customerSubscription?.id);;
            }
            result.data.payment_arrangement = payment_arrangement;

            return result;
        } catch (error) {
            console.error(`Plan service get details -> `, error);
            throw error;
        }
    }

    // Define included models for customer details query
    getIncludeModels(excludeArray) {
        return [{
            model: db.CustomerSubscriptions,
            as: 'customerSubscription',
            attributes: ["id", "amount", "next_billing_at", "subscription_type", "balance_due"],
            required: false
        },
        {
            model: db.CustomerAddresses,
            as: 'serviceAddress',
            attributes: { exclude: excludeArray },
            required: false
        },
        {
            model: db.CustomerAddresses,
            as: 'mailingAddress',
            attributes: { exclude: excludeArray },
            required: false
        },
        {
            model: db.CustomerInternet,
            as: 'customerInternet',
            attributes: ["plan_name", "creation_order", "tech_appointment", "plan_price", "plan_speed", "add_ons", "live_date", "internal_processing_status", "ship_package_status", "modem_installation_status", "modem_activation_status", "speed_change_order"],
            include: [{
                model: db.InternetElSpeedChangeOrder,
                as: 'internetElSpeedChangeOrder',
                attributes: ['expected_completion_date', 'stage', 'response_date', 'speed'],
                required: false
            },
            {
                model: db.InternetElCreationOrder,
                as: 'internetElCreationOrder',
                attributes: ['shipping_id', 'record_type', 'sf_record_id', 'install_date'],
                include: {
                    model: db.CreationOrderShipping,
                    as: 'creationOrderShipping',
                    attributes: ['ship_date', 'ship_drop_off_date', 'package_deliverted_at', 'tracking_url', 'courier'],
                    required: false
                },
                required: false
            },
            {
                model: db.InternetElMoveOrder,
                as: 'internetElMoveOrder',
                attributes: { exclude: ['createdAt', 'updatedAt', 'sf_updatedAt'] },
                required: false
            },
            {
                model: db.InternetElTechAppointment,
                as: 'internetElTechAppointment',
                attributes: { exclude: ['createdAt', 'updatedAt', 'sf_updatedAt'] },
                required: false
            },
            {
                model: db.InternetElSwapOrder,
                as: 'internetElSwapOrder',
                attributes: { exclude: ['createdAt', 'updatedAt', 'sf_updatedAt'] },
                required: false
            }],
            required: false
        },
        {
            model: db.CustomerTv,
            as: 'customerTv',
            attributes: ["plan_name", "total_service_cost", "extra_packages", "single_channels", "iptv_products", "account_status", "requested_cancellation_date", "state_text"],
            required: false
        },
        {
            model: db.CustomerPhone,
            as: 'customerPhone',
            where: {
                account_status: {
                    [Op.or]: [
                        { [Op.notIn]: ['DELETED'] },  // Exclude 'DELETED'
                        { [Op.is]: null }             // Include NULL
                    ]
                }
            },
            attributes: ["plan_name", "plan_price", "add_ons", "account_status", "requested_cancellation_date", "service_start_date", "sf_phone_type"],
            required: false
        }
        ];
    }

    // Fetch and format the plan status for customer internet
    async getPlanStatus(customerDetails) {
        const customerInternet = customerDetails.customerInternet;
        // if (customerInternet?.speed_change_order) {
        //     customerInternet.expected_completion_date = await db.InternetElSpeedChangeOrder.findOne({ attributes: ["expected_completion_date"], where: { id: customerInternet?.speed_change_order } });
        // }
        if (customerDetails?.stage === "Onboarding") {
            let creationOrderDetails = null;
            if (customerInternet?.internal_processing_status === 'In Progress' || customerInternet?.ship_package_status === 'In Progress') {
                creationOrderDetails = await db.InternetElCreationOrder.findOne({
                    include: [
                        {
                            model: db.CreationOrderShipping,
                            as: 'creationOrderShipping',
                            attributes: ['full_mailing_address', 'courier', 'package_deliverted_at', 'tracking_url', 'ship_date'],
                            required: true
                        }
                    ],
                    where: { id: customerInternet?.creation_order }
                });
            }

            if (customerInternet?.internal_processing_status === 'In Progress' && creationOrderDetails) {
                customerInternet.internalProcessing = {
                    full_mailing_address: creationOrderDetails.creationOrderShipping.full_mailing_address,
                    ship_date: creationOrderDetails.creationOrderShipping.ship_date
                };
            }

            if (customerInternet?.ship_package_status === 'In Progress' && creationOrderDetails) {
                customerInternet.shipPackage = {
                    courier: creationOrderDetails.creationOrderShipping.courier,
                    ship_date: creationOrderDetails.creationOrderShipping.ship_date,
                    tracking_url: creationOrderDetails.creationOrderShipping.tracking_url
                };
            }

            if (customerInternet?.modem_installation_status === 'In Progress') {
                const techAppointmentDetails = await db.InternetElTechAppointment.findOne({
                    where: { id: customerInternet?.tech_appointment }
                });

                if (techAppointmentDetails) {
                    customerInternet.modemInstallation = {
                        live_date: customerInternet?.live_date,
                        install_date: techAppointmentDetails.install_date,
                        install_time: techAppointmentDetails.install_time
                    };
                }
            }
        }
        return customerInternet;
    }

    // Method to update customer address
    async updateAddress(payload, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("update_address_logs", { ...elasticLogObj, request: payload?.body });

        try {
            const returnRes = { status: false };
            const { status, addressDetails: { address_type, customerSfRecordId, id: adrsTableId, customerInternetId, stage } } = await this.checkAddressValidation(payload);
            if (_id && _index && address_type) await elasticSearch.updateDocument(_index, _id, { type: address_type.toUpperCase() });
            if (!status) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
            const { body: { customer_details_id, address_id, ...dataToBeUpdate }, userDetails: { sf_record_id: cust_sf_rec_id, sticky_sender } } = payload;
            if (!cust_sf_rec_id || !customerSfRecordId) return returnRes;
            const salesforceServiceClient = new SalesforceService();

            switch (address_type) {
                case 'service':
                if (!customerInternetId) throw new CustomError(RESPONSE_CODES.NOT_ACCEPTABLE, SERVICE_ERROR);
                if (!dataToBeUpdate.service_change_date) throw new CustomError(RESPONSE_CODES.UNPROCESSABLE_ENTITY, "Please provide service change date.");
                await this.updateServiceAddress(dataToBeUpdate, customerInternetId, salesforceServiceClient, sticky_sender, stage);
                const reqMsg = dataToBeUpdate?.type == "cancel" ? "Successfully cancelled request to change service address." : "Successfully requested service address change";
                returnRes.status = true;
                returnRes.message = reqMsg;
                break;

                case 'mailing':
                    await this.updateMailingAddress(dataToBeUpdate, cust_sf_rec_id, customerSfRecordId, adrsTableId, salesforceServiceClient);
                    returnRes.status = true;
                    returnRes.message = 'Mailing address has been updated successfully.';
                    break;

                default:
                    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Invalid address type.');
            }

            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnRes });
            return returnRes;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Plan service update address -> `, error);
            throw error;
        }
    }

    // Method to update the service address

    async updateServiceAddress(dataToBeUpdate, customerInternetId, salesforceServiceClient, sticky_sender, stage) {
        try {
            let internetDetails = await db.CustomerInternet.findOne({
                attributes: ['sf_record_id'],
                where: { id: customerInternetId },
                include: [{
                    model: db.InternetElSpeedChangeOrder,
                    as: 'internetElSpeedChangeOrder',
                    attributes: ['id', 'stage'],
                    required: false
                },
                {
                    model: db.InternetElMoveOrder,
                    as: 'internetElMoveOrder',
                    attributes: { exclude: ['createdAt', 'updatedAt', 'sf_updatedAt'] },
                    required: false
                },
                {
                    model: db.InternetElSwapOrder,
                    as: 'internetElSwapOrder',
                    attributes: ['id', 'stage'],
                    required: false
                }]
            });

            if (!internetDetails || !internetDetails?.sf_record_id) {
                const msg = this.getnotification(sticky_sender, "change");
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, msg);
            }

            internetDetails = internetDetails.toJSON();
            await this.checkAddressModification({ dataToBeUpdate, customerInternetId, salesforceServiceClient, sticky_sender, internetDetails, stage });

        } catch (error) {
            console.error(`Plan service update service address -> `, error);
            throw error;
        }
    }

    async checkAddressModification(payload) {
        try {
            const { dataToBeUpdate, customerInternetId, salesforceServiceClient, sticky_sender, internetDetails, stage } = payload;
            if (dataToBeUpdate?.type == "cancel" && !internetDetails?.internetElMoveOrder?.submit_date && internetDetails?.internetElMoveOrder?.sf_record_id) {
                const { returnStatus } = await salesforceServiceClient.deleteSpeedChangeOrder(internetDetails?.internetElMoveOrder?.sf_record_id);
                if (!returnStatus) {
                    const msg = this.getnotification(sticky_sender, "cancel");
                    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, msg);
                } else await this.cancelMoveOrder(internetDetails);
            } else {
                let serviceAdresType;

                if (stage == "Online" && (!internetDetails?.internetElSpeedChangeOrder || (internetDetails?.internetElSpeedChangeOrder && internetDetails?.internetElSpeedChangeOrder?.stage == "Complete")) &&
                    (!internetDetails?.internetElMoveOrder || (internetDetails?.internetElMoveOrder && internetDetails?.internetElMoveOrder?.stage == "Complete")) &&
                    (!internetDetails?.internetElSwapOrder || (internetDetails?.internetElSwapOrder && internetDetails?.internetElSwapOrder?.stage == "Complete"))) serviceAdresType = "create";

                if (!internetDetails?.internetElMoveOrder?.submit_date && internetDetails?.internetElMoveOrder?.sf_record_id) serviceAdresType = "update";

                if (!serviceAdresType) {
                    const msg = this.getnotification(sticky_sender, "change");
                    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, msg);
                }

                if (serviceAdresType == "create") {
                    const { returnStatus: updateStatus, orderId } = await salesforceServiceClient.createMoveOrderinSf(dataToBeUpdate, internetDetails.sf_record_id);
                    if (!updateStatus) {
                        const msg = this.getnotification(sticky_sender, "change");
                        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, msg);
                    } else await this.updateMoveOrder(orderId, customerInternetId);
                } else if (serviceAdresType == "update") {
                    const addressMatchCheck = this.addressMatch(internetDetails?.internetElMoveOrder, dataToBeUpdate);
                    const { returnStatus: updateStatus, orderId } = await salesforceServiceClient.updateMoveOrderinSf(dataToBeUpdate, internetDetails.internetElMoveOrder.sf_record_id, addressMatchCheck);
                    if (!updateStatus) {
                        const msg = this.getnotification(sticky_sender, "change");
                        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, msg);
                    } else await this.updateMoveOrder(orderId, customerInternetId);
                }
            }
        } catch (error) {
            console.error(`Plan service checkAddressModification -> `, error);
            throw error;
        }
    }

    addressMatch(previousAddress, requestedAddress) {

        // Check address match
        const isAddressMatch =
            previousAddress.street_number === requestedAddress.street_number &&
            previousAddress.street_name === requestedAddress.street_name &&
            previousAddress.unit === requestedAddress.suit_unit &&
            previousAddress.city === requestedAddress.town &&
            previousAddress.postal_code === requestedAddress.postal_code;

        return isAddressMatch;
    }

    // Method to update the mailing address
    async updateMailingAddress(dataToBeUpdate, cust_sf_rec_id, customerSfRecordId, adrsTableId, salesforceServiceClient) {
        try {
            const { returnStatus: createResponse, addressId } = await salesforceServiceClient.createMailingAddress(dataToBeUpdate, cust_sf_rec_id, customerSfRecordId);
            if (!createResponse || !addressId) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Failed to create mailing address.');
            }

            const { returnStatus: updateStatus } = await salesforceServiceClient.updateMailingAdrsinSf(addressId, customerSfRecordId);
            if (!updateStatus) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Failed to update mailing address.');
            }

            const { returnStatus, addresstData: sfAddressDetails } = await salesforceServiceClient.getAddressDetails(addressId, 'Mailing_Address__c');
            if (!returnStatus) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Failed to retrieve address details.');
            }

            const updateDbRes = await this.updateAddressInDatabase(adrsTableId, sfAddressDetails, customerSfRecordId);
            if (!updateDbRes) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Failed to update address.');
            }
        } catch (error) {
            console.error(`Plan service update mailing address -> `, error);
            throw error;
        }
    }

    // Method to validate customer address
    async checkAddressValidation(payload) {
        try {
            const { userDetails: { id: contact_id }, body: { customer_details_id, address_id } } = payload;
            const customerDetails = await db.CustomerDetails.findOne({ where: { contact_id, id: customer_details_id } });
            if (!customerDetails || !(customerDetails.service_address_id == address_id || customerDetails.mailing_address_id == address_id)) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            let addressDetails = await db.CustomerAddresses.findByPk(address_id);
            if (!addressDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            addressDetails = addressDetails.toJSON();
            addressDetails.customerSfRecordId = customerDetails.sf_record_id;
            addressDetails.customerInternetId = customerDetails.internet_id;
            addressDetails.stage = customerDetails.stage;
            return { status: true, addressDetails };
        } catch (error) {
            console.error(`Plan service address validation -> `, error);
            throw error;
        }
    }

    // Method to update address details in database
    async updateAddressInDatabase(id, sfAddressDetails, customerSfRecordId) {
        try {
            const { Id: mailing_sf_record_id, Name: name, Mailing_Suite_Unit__c: suit_unit, Mailing_Street_Number__c: street_number, Mailing_Street_Name__c: street_name, Mailing_City_Town__c: town, Mailing_Province__c: province, Mailing_Postal_Code__c: postal_code, Full_Mailing_Address__c: full_address, Mailing_Country__c: country, Status__c: status, LastModifiedDate: sf_updatedAt } = sfAddressDetails;

            const customerDetails = await db.CustomerDetails.count({ where: { mailing_address_id: id } });

            if (customerDetails == 1) {
                await db.CustomerAddresses.update({ sf_record_id: mailing_sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt }, { where: { id } });
            } else {
                const mailingAddressDetails = await db.CustomerAddresses.create({ address_type: "mailing", mailing_sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt });

                if (mailingAddressDetails?.id) {
                    db.CustomerDetails.update({ mailing_address_id: mailingAddressDetails.id }, { where: { sf_record_id: customerSfRecordId } });
                }
            }
            return true;
        } catch (error) {
            console.error(`Plan service update address in database -> `, error);
            throw error;
        }
    }

    // Method to fetch plan details based on query type
    async getPlanDetails(query) {
        try {
            const { type } = query;
            const planUrl = CONFIG[type]?.plans;
            const addOnsUrl = CONFIG[type]?.addons;
            const assetsUrl = CONFIG[type]?.assets;
            let data = {};
            if (planUrl && addOnsUrl && assetsUrl) {
                data.planDetails = await this.getPlanDetailsFromJSON(planUrl);
                data.addonsDetails = await this.getPlanDetailsFromJSON(addOnsUrl);
                data.assetsDetails = await this.getPlanDetailsFromJSON(assetsUrl);
            }
            const response = { status: true, data };
            return response;
        } catch (error) {
            console.error(`Customer service get plan details -> `, error);
            throw error;
        }
    }

    // Method to fetch plan details from JSON data
    async getPlanDetailsFromJSON(url) {
        try {
            const response = await axios.get(url);
            let results = response?.data;
            if (results) results = results.filter(item => item.status == "ACTIVE")
            return results || [];
        } catch (error) {
            return [];
        }
    }

    // Method to fetch and update plan details from JSON
    async getandUpdatePlanDetailsFromJSON(planData, type) {
        try {
            const planUrl = CONFIG[type]?.plans;
            if (!planUrl) return [];

            const planDetails = await this.getPlanDetailsFromJSON(planUrl);
            if (!planDetails || !planData?.plan_name) return [];

            const selectedPlan = this.filterDataFromJson(planDetails, planData.plan_name);
            if (!selectedPlan || type !== "tv") return [];

            const { name, optional_iptv_products, optional_extra_packages, optional_single_channels } = selectedPlan;
            let newPlanNames = [name];
            newPlanNames = [...newPlanNames, ...this.filterOptionalData(planData.extra_packages, optional_extra_packages)];
            newPlanNames = [...newPlanNames, ...this.filterOptionalData(planData.single_channels, optional_single_channels)];
            newPlanNames = [...newPlanNames, ...this.filterOptionalData(planData.iptv_products, optional_iptv_products)];
            return newPlanNames;
        } catch (error) {
            return [];
        }
    }

    // Helper method to filter plan data from JSON
    filterDataFromJson(planDetails, planName) {
        return planDetails.find(item => item.api_name === planName);
    }

    // Helper method
    filterOptionalData(currentData, optionalData) {
        if (!currentData || !optionalData) return [];

        const parsedCurrentData = JSON.parse(currentData);
        const filteredData = optionalData.filter(item => parsedCurrentData.includes(item.api_name));
        return filteredData.map(item => item.name);
    }

    async updateMoveOrder(orderId, id) {
        try {
            if (!orderId) return null;
            const salesforceServiceClient = new SalesforceService();
            const orderDetails = await salesforceServiceClient.getOrderDetails(orderId, "InternetElMoveOrder");
            if (!orderDetails) return null;
            const sfUpdatedAt = getCurrentAtlanticTime(orderDetails?.LastModifiedDate, "sfUpdate");
            const createdAt = getCurrentAtlanticTime(orderDetails?.CreatedDate, "sfUpdate");
            let insertOrderDetails = { sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, createdAt };
            if (orderDetails?.Requested_Move_Date__c) insertOrderDetails.requested_move_date = orderDetails?.Requested_Move_Date__c;
            if (orderDetails?.Requested_Install_Date__c) insertOrderDetails.requested_install_date = orderDetails?.Requested_Install_Date__c;
            if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
            if (orderDetails?.Submit_Date__c) insertOrderDetails.submit_date = getCurrentAtlanticTime(orderDetails?.Submit_Date__c);
            if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
            if (orderDetails?.Install_Time__c) insertOrderDetails.install_time = orderDetails?.Install_Time__c;
            if (orderDetails?.Order_Reject_Reason__c) insertOrderDetails.reject_reason = orderDetails?.Order_Reject_Reason__c;
            if (orderDetails?.Order_Reject_Reason_Solved__c) insertOrderDetails.reject_reason_solved = orderDetails?.Order_Reject_Reason_Solved__c;
            if (orderDetails?.Service_Suite_Unit__c) insertOrderDetails.unit = orderDetails?.Service_Suite_Unit__c;
            if (orderDetails?.Service_Street_Number__c) insertOrderDetails.street_number = orderDetails?.Service_Street_Number__c;
            if (orderDetails?.Service_Street_Name__c) insertOrderDetails.street_name = orderDetails?.Service_Street_Name__c;
            if (orderDetails?.Service_City_Town__c) insertOrderDetails.city = orderDetails?.Service_City_Town__c;
            if (orderDetails?.Service_Province__c) insertOrderDetails.province = orderDetails?.Service_Province__c;
            if (orderDetails?.Service_Postal_Code__c) insertOrderDetails.postal_code = orderDetails?.Service_Postal_Code__c;
            if (orderDetails?.Name) insertOrderDetails.sf_name = orderDetails?.Name;
            let [orderInsert, created] = await db.InternetElMoveOrder.findOrCreate({
                where: {
                    sf_record_id: orderDetails?.Id
                },
                defaults: insertOrderDetails
            });
            if (!created) await orderInsert.update(insertOrderDetails);
            if (orderInsert?.id) await db.CustomerInternet.update({ move_order: orderInsert?.id }, { where: { id } });
        } catch (error) {
            console.error("updateMoveOrder -> ", error)
        }
    }
    async fetchOutstanding(customer_subscription_id) {
        try {

            let whereClause = {
                status: ["PAYMENT_DUE", "NOT_PAID"],
                customer_subscription_id
            };

            whereClause.createdAt = {
                [Op.gte]: moment().subtract(28, "days").toDate(),
            };
            whereClause.sf_record_id = {
                [Op.and]: [
                    { [Op.not]: null },
                    { [Op.ne]: "" }
                ]
            };

            const data = await db.SubscriptionInvoice.count({
                include: [
                    {
                        model: db.CustomerDetails,
                        as: "customerDetail",
                        attributes: [],
                    },
                ],
                attributes: [],
                where: whereClause,
                raw: true,
            });

            return data ? true : false;
        } catch (error) {
            console.error(`PaymentServices fetch Outstanding -> `, error);
            throw error;
        }
    }

    getnotification(sticky_sender, type) {
        const reqType = type === "cancel" ? "cancel" : "change";
        return `If you wish to make a ${reqType} to this request, please reach out to our customer service team texting ${sticky_sender || "-"} or call us at 19028002660.`
    }

    async cancelMoveOrder(internetDetails) {
        try {
            const moveOrderCount = await db.CustomerInternet.count({ where: { move_order: internetDetails?.internetElMoveOrder?.id } });
            await db.CustomerInternet.update({ move_order: null }, { where: { sf_record_id: internetDetails?.sf_record_id } });
            if (moveOrderCount == 1) await db.InternetElMoveOrder.destroy({ where: { id: internetDetails?.internetElMoveOrder?.id } })
        } catch (error) {
            console.error(`Plan service cancel Move Order -> `, error);
        }
    }
}

module.exports = PlanServices;
