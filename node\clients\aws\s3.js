const { S3Client, PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const CustomError = require('../../utils/errors/CustomError');
const CONFIG = require("../../config");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require('../../utils/ResponseCodes');

// S3Uploader class to handle file upload and deletion in AWS S3
class S3Uploader {
    // Constructor to initialize the S3 client and bucket name
    constructor() {
        this.s3 = new S3Client({
            region: CONFIG.aws.region,
            credentials: {
                accessKeyId: CONFIG.aws.accessKeyId,
                secretAccessKey: CONFIG.aws.secretAccessKey
            }
        });
        this.bucketName = CONFIG.aws.bucketName
    }


    // Method to upload a file to S3
    async uploadFile(key, imageStream) {
        try {
            // Get the content type based on the file extension
            const contentType = await this.getContentType(key);

            // Parameters for the S3 upload
            const uploadParams = {
                Bucket: this.bucketName,
                Key: key,
                Body: imageStream,
                ACL: 'public-read',
                ContentType: contentType
            };

            // Create a command to put (upload) the object
            const putObjectCommand = new PutObjectCommand(uploadParams);

            // Send the command to S3
            const data = await this.s3.send(putObjectCommand);

            // Check if the upload was successful
            if (data.$metadata?.httpStatusCode !== 200) throw new CustomError(RESPONSE_CODES.SERVER_ERROR, RESPONSE_MESSAGES.FILE_ERROR);

            // Construct and return the file's URL
            let location = `https://${this.bucketName}.s3.amazonaws.com/${key}`;
            return location;
        } catch (error) {
            console.error('Error uploading file:', error);
            throw error;
        }
    }

    // Method to delete a file from S3
    async deleteFile(key) {
        try {
            // Parameters for the S3 delete
            const deleteParams = {
                Bucket: this.bucketName,
                Key: key
            };

            // Create a command to delete the object
            const deleteObjectCommand = new DeleteObjectCommand(deleteParams);

            // Send the command to S3
            const data = await this.s3.send(deleteObjectCommand);

            // Optionally, you can check if deletion was successful
            if (data.$metadata?.httpStatusCode !== 204) throw new CustomError(RESPONSE_CODES.SERVER_ERROR, RESPONSE_MESSAGES.DELETE_ERROR);
            return true; // Deletion successful
        } catch (error) {
            console.error('Error deleting file:', error);
            throw error;
        }
    }

    // Method to get the content type based on the file extension

    async getContentType(key) {
        const extension = key.split('.').pop(); // Get the file extension
        const mimeTypes = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'svg': 'image/svg+xml',
            // Add other file extensions and MIME types as needed
        };
        return mimeTypes[extension] || 'application/octet-stream'; // Default MIME type if not found
    }
}

module.exports = S3Uploader;