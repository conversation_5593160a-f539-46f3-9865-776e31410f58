const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const customerServices = new CustomerServices();
const SubscriptionInvoiceServices = require("../../services/subscription-invoices-services");
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class InvoiceTestServices {

    async getInvoiceDetails() {
        try {
            const query = `SELECT sf_record_id, cb_invoice_id FROM subscriptions_invoices where sf_record_id IS NOT NULL or cb_invoice_id IS NOT NULL`;

            const res = await customerServices.executeQuery(query);
            if (res.length > 0) {
                await this.checkAndManageInvoice(res);
            }
        } catch (error) {
            console.error("Sync service get invoice details -> ", error);
        }
    }

    async checkAndManageInvoice(sfInvoiceDetails) {
        try {
            const subscriptionInvoiceServices = new SubscriptionInvoiceServices();
            for (const sfInvoiceDetail of sfInvoiceDetails) {
                const { sf_record_id, cb_invoice_id } = sfInvoiceDetail;
                const selectedObj = { Id: 1, chargebeeapps__CB_Invoice_Id__c: 1, chargebeeapps__Due_Amount__c: 1, chargebeeapps__Amount__c: 1, Expected_Payment_Date_Time__c: 1, chargebeeapps__Status__c: 1, chargebeeapps__Subscription_CB_Id__c: 1, LastModifiedDate: 1, chargebeeapps__Invoice_Date__c: 1, CreatedDate: 1 };

                const searchField = sf_record_id ? { Id: sf_record_id } : { chargebeeapps__CB_Invoice_Id__c: cb_invoice_id };

                const { returnStatus, salesforceData: invoiceData } = await salesforceConnection.getDetailsByFieldAndApi(searchField, "chargebeeapps__CB_Invoice__c", selectedObj, "interval");

                if (returnStatus) {
                    await subscriptionInvoiceServices.checkAndManageDetails(invoiceData);
                }
            }
        } catch (error) {
            console.error("SalesforceService 100 check And Manage invoice -> ", error);
        }
    }
}

module.exports = InvoiceTestServices;
