import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { CheckIcon, ErrorIcon } from "../../assets/Icons";
import Button from "../../components/common/Button";
import InputFields from "../../components/forms/InputFields";
import VerificationCodeInput from "../../components/forms/VerificationCodeInput";
import { useResetPasswordMutation } from "../../services/api";
import { addNotification } from "../../store/reducers/toasterReducer";
import { CloseHandlerProps } from "../../typings/typing";
import { regEx } from "../../utils/helper";
import { login } from "../../store/reducers/authenticationReducer";

// Interface for the form data
interface FormData {
  password: string;
  cnfPassword: string;
  type: string;
  email: string;
  confirmation_code: string;
}

// Interface for the validation response
interface ValidationResponse {
  isValid: boolean;
  errors: Partial<FormData>;
}

// Function to validate the password reset form
const validatePassword = (data: FormData): ValidationResponse => {
  const errors: Partial<FormData> = {};
  let isValid = true;

  if (!data.password) {
    errors.password = "Password is required";
    isValid = false;
  } else if (!regEx.PASSWORD.test(data.password)) {
    errors.password =
      "Password must be between 8 to 24 characters, should contain at least one lowercase letter, one uppercase letter, one digit, and one special character";
    isValid = false;
  }
  if (!data.cnfPassword) {
    errors.cnfPassword = "Confirm password is required";
    isValid = false;
  } else if (!regEx.PASSWORD.test(data.cnfPassword)) {
    errors.cnfPassword =
      "Password must be between 8 to 24 characters, should contain at least one lowercase letter, one uppercase letter, one digit, and one special character";
    isValid = false;
  } else if (data.password !== data.cnfPassword) {
    errors.cnfPassword = "Password doesn't match";
    isValid = false;
  }
  if (data?.type == "existing" && !data.confirmation_code) {
    errors.confirmation_code = "Confirmation code is required";
    isValid = false;
  } else if (data?.type == "existing" && data.confirmation_code?.length < 6) {
    errors.confirmation_code = "Confirmation code must be 6 digit";
    isValid = false;
  }

  return { isValid, errors };
};
const ResetPassword: React.FC<CloseHandlerProps> = ({
  handleOPenForgotPasswordPopup,
}) => {
  const [formData, setFormData] = useState<FormData>({
    password: "",
    cnfPassword: "",
    email: "",
    type: "",
    confirmation_code: "",
  });
  const [focusedField, setFocusedField] = useState<string | null>(null); // Track focused input
  const [verificationCode, setVerificationCode] = useState<string[]>([
    "",
    "",
    "",
    "",
    "",
    "",
  ]);
  const [formError, setFormError] = useState<Partial<FormData>>({});
  const [resetPassword, resetPasswordLoading] = useResetPasswordMutation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  // Effect to set the form type based on location state
  useEffect(() => {
    setFormData((state) => ({ ...state, type: location?.state?.type }));
  }, [location?.state]);

  // Define criteria for password validation
  const validationCriteria = {
    length: (password: string) => password.length >= 8 && password.length <= 24,
    hasUppercase: (password: string) => /[A-Z]/.test(password),
    hasLowercase: (password: string) => /[a-z]/.test(password),
    hasSpecialCharacter: (password: string) =>
      /[!@#$%^&*(),.?":{}|<>]/.test(password),
    hasNumber: (password: string) => /\d/.test(password),
  };

  // Validate password field based on defined criteria
  const validatePasswordField = (password: string) =>
    Object.entries(validationCriteria).map(([key, check]) => ({
      key,
      valid: check(password),
    }));

  // Validation results for each password field
  const newPasswordValidation = validatePasswordField(formData.password);
  const confirmPasswordValidation = validatePasswordField(formData.cnfPassword);

  // Check if the password meets all criteria
  const isPasswordValid = newPasswordValidation.every(
    (criteria) => criteria.valid
  );
  const isConfirmPasswordValid = confirmPasswordValidation.every(
    (criteria) => criteria.valid
  );

  // Function to handle change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((state) => ({ ...state, [name]: value }));
    setFormError((state) => ({ ...state, [name]: "" }));
  };

  // Effect to update confirmation code in form data
  useEffect(() => {
    if (verificationCode) {
      setFormData((state) => ({
        ...state,
        confirmation_code: verificationCode.join(""),
      }));
    }
  }, [verificationCode]);

  // Handle focus on input fields
  const handleFocus = (name: string) => {
    setFocusedField(name);
  };

  // Handle blur event to reset focused field
  const handleBlur = () => {
    setFocusedField(null);
  };

  // Function to handle password reset
  const handleResetPassword = async () => {
    try {
      let data: Record<string, any> = {
        password: formData?.password,
        confirm_password: formData?.cnfPassword,
        email: localStorage.getItem("email"),
      };
      if (formData?.type === "existing") {
        data = {
          ...data,
          type: formData?.type,
          confirmation_code: formData?.confirmation_code,
        };
      } else {
        data = {
          ...data,
          type: formData?.type,
          access_token: location?.state?.token,
        };
      }
      if (localStorage.getItem("email")) {
        const response = await resetPassword(data).unwrap();
        if (response.status === 200) {
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );

          if (response?.data?.accessToken) {
            // first time reset password
            localStorage.setItem("access_token", response?.data?.accessToken);
            dispatch(login());
            navigate("/");
          } else {
            // existing user reset password
            navigate(window.location.pathname + window.location.search, {
              replace: true,
            });
          }
        }
      } else {
        dispatch(
          addNotification({ type: "error", message: "Email is required!" })
        );
      }
    } catch (error: any) {
      if (error?.data?.error?.length > 0) {
        dispatch(
          addNotification({
            type: "error",
            message: error?.data?.error?.[0]?.message,
          })
        );
      } else {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
      }
    }
  };

  // Handle Submit
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { isValid, errors } = validatePassword(formData);
    setFormError(errors);
    if (isValid && !resetPasswordLoading?.isLoading) {
      setFormError({});
      handleResetPassword();
    }
  };

  // Render validation criteria list based on the focused field
  const renderValidationList = (
    validation: { key: string; valid: boolean }[]
  ) => (
    <ul className="password-regex mt-3">
      {validation.map(({ key, valid }) => (
        <li key={key} className={valid ? "valid" : "invalid"}>
          {key === "length" && "Password must be 8 to 24 characters long."}
          {key === "hasUppercase" &&
            "Must include at least one uppercase letter."}
          {key === "hasLowercase" &&
            "Must include at least one lowercase letter."}
          {key === "hasSpecialCharacter" &&
            "Must include at least one special character."}
          {key === "hasNumber" && "Must include at least one number."}
        </li>
      ))}
    </ul>
  );

  return (
    <div>
      <form className="" onSubmit={handleSubmit}>
        <div>
          <p className="sub-text ">
            Your new password must be different from previous used passwords.
          </p>
        </div>
        <div className="flex xl:gap-5 gap-4 flex-col lg:pt-10 pt-30">
          {formData?.type === "existing" && (
            <VerificationCodeInput
              setVerificationCode={setVerificationCode}
              verificationCode={verificationCode}
              isErrorMsg={formError?.confirmation_code}
            />
          )}
          <InputFields
            placeHolder="New Password"
            changeEvent={handleChange}
            isErrorMsg={formError.password}
            type="password"
            valid={!isPasswordValid && formData.password ? true : false}
            attributes={{
              name: "password",
              onFocus: () => handleFocus("password"),
              onBlur: handleBlur,
            }}
            className="!bg-[#F5EFFF]"
          />
          <InputFields
            placeHolder="Confirm New Password"
            changeEvent={handleChange}
            isErrorMsg={formError.cnfPassword}
            type="password"
            valid={
              !isConfirmPasswordValid && formData.cnfPassword ? true : false
            }
            attributes={{
              name: "cnfPassword",
              onFocus: () => handleFocus("cnfPassword"),
              onBlur: handleBlur,
            }}
            className="!bg-[#F5EFFF]"
          />
          <div className="mt-3">
            <p className="font-medium">Password must be:</p>
            {focusedField === "password" ? (
              renderValidationList(newPasswordValidation)
            ) : focusedField === "cnfPassword" ? (
              renderValidationList(confirmPasswordValidation)
            ) : (
              <ul className="password-regex mt-3">
                <li>Password must be 8 to 24 characters long.</li>
                <li>Must include at least one uppercase letter.</li>
                <li>Must include at least one lowercase letter.</li>
                <li>Must include at least one special character.</li>
                <li>Must include at least one number.</li>
              </ul>
            )}
          </div>

          {formData.password &&
            formData.cnfPassword &&
            formData.password === formData.cnfPassword &&
            !formError.cnfPassword &&
            !formError.password && (
              <div className="2xl:py-5 lg:py-2 py-1.5 flex items-center gap-2.5">
                <span className="">
                  <CheckIcon />
                </span>
                <p className="sub-text">Both passwords must match</p>
              </div>
            )}
          {formData.password &&
            formData.cnfPassword &&
            formData.password !== formData.cnfPassword && (
              <div className="2xl:py-5 lg:py-2 py-1.5 flex items-center gap-2.5">
                <span className="">
                  <ErrorIcon />
                </span>
                <p className="sub-text">Both passwords don't match</p>
              </div>
            )}
          <div className="flex items-center lg:gap-5 gap-2.5 max-md:flex-col">
            <Button
              title="Go back"
              type="button"
              clickEvent={handleOPenForgotPasswordPopup}
              btnType="transparent"
              className="!border-black !text-black"
            />
            <Button
              title="Reset password"
              type="submit"
              isLoading={resetPasswordLoading?.isLoading}
            />
          </div>
        </div>
      </form>
    </div>
  );
};

export default ResetPassword;
