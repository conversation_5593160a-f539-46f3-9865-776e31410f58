const crypto = require("crypto");
const multer = require('multer');
const moment = require('moment');
const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES } = require("../utils/ResponseCodes");
const momentTimezone = require('moment-timezone');
const _TIMEZONE = "America/Halifax";
const jwt = require('jsonwebtoken');

/**
 * Generates HMAC-SHA256 hash for given email, clientId, and secretKey.
 * @param {string} email - Email of the user.
 * @param {string} clientId - Client ID.
 * @param {string} secretKey - Secret key used for hashing.
 * @returns {string} Base64 encoded HMAC-SHA256 hash.
 */
exports.generateSecretHashValues = (email, clientId, secretKey) => {
    const signingKey = Buffer.from(email + clientId, 'utf-8');
    const secret = Buffer.from(secretKey, 'utf-8');

    return crypto.createHmac('sha256', secret)
        .update(signingKey)
        .digest('base64');
};

exports.getPagingData = (count, resultPerPage, page) => {
    const totalPages = Math.ceil(count / resultPerPage) || 0;
    const pageData = {
        totalPage: totalPages,
        page: page,
        total: count,
        limit: resultPerPage
    };
    return pageData;
};

/**
 * Multer middleware setup for handling file uploads in memory.
 */
const storage = multer.memoryStorage();
exports.upload = multer({ storage: storage });

/**
 * Retrieves file extension from a given filename.
 * @param {string} filename - Name of the file.
 * @returns {string} File extension.
 */
exports.getFileExtension = (filename) => {
    return filename.slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2);
};

/**
* Checks if a given file extension corresponds to an image file.
* @param {string} extension - File extension (e.g., 'jpg', 'png').
* @returns {boolean} True if the extension represents an image file; false otherwise.
*/
exports.isImageFile = (extension) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif']; // Add more extensions if needed
    return imageExtensions.includes(extension.toLowerCase());
}


/**
* Calculates membership duration from given createDate.
* @param {string} createDate - Date in 'YYYY-MM-DD HH:mm:ss' format.
* @returns {string} Formatted duration string.
*/

exports.calculateMembershipDuration = (createDate) => {
    const today = moment();
    const start = moment(createDate, 'YYYY-MM-DD HH:mm:ss');

    const diffDays = today.diff(start, 'days');

    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    const days = diffDays % 30;

    let duration = { year: null, month: null, day: null };

    if (years > 0) {
        duration.year = years;
        if (months > 0) {
            duration.month = months;
        } else if (days > 0) {
            duration.day = days;
        }
    } else if (months > 0) {
        duration.month = months;
        if (days > 0) {
            duration.day = days;
        }
    } else {
        duration.day = days;
    }

    return duration;
};

/**
* Parses pagination parameters from the query object.
* Throws an error for invalid parameters.
* @param {Object} query - Query object containing `page` and `limit` parameters.
* @returns {Object} Object with `page`, `limit`, and `offset` properties.
* @throws {CustomError} Throws a CustomError if page or limit is invalid.
*/
exports.parsePagination = (query) => {
    const { page = 1, limit = 10 } = query;

    // Validation
    if (isNaN(page) || page < 1) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid page number");
    if (isNaN(limit) || limit < 1) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid limit number");

    // Convert page and limit to integers
    const pageInt = parseInt(page, 10);
    const limitInt = parseInt(limit, 10);

    // Calculate offset
    const offset = (pageInt - 1) * limitInt;

    return { page: pageInt, limit: limitInt, offset };
}

exports.getCurrentAtlanticTime = (date = null, type) => {
    if (date) {
        if (type == "sfUpdate") return momentTimezone(date).tz(_TIMEZONE).format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
        else return momentTimezone(date).tz(_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
    } else {
        const nowUtc = momentTimezone.utc();
        // Convert to Canada time zone
        if (type == "sfUpdate") return nowUtc.tz(_TIMEZONE).format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
        else return nowUtc.tz(_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
    }
};

exports.formatDateToAtlanticTime = (date) => {
    return momentTimezone(date).tz(_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
};

exports.billindTypeCbSubs = (type) => {
    let subsType;
    switch (type) {
        case "month":
            subsType = "monthly";
            break;
        case "year":
            subsType = "yearly";
            break;
        default:
            break;
    }
    return subsType;
};

exports.rearrangeLocations = (locations) => {
    const priorityMap = {
        "Online": 1,
        "Payment arrangements": 2,
        "Outstanding": 3,
        "Onboarding": 4,
        "Offboarding": 5,
        "Inactive": 6
    };

    return locations.sort((a, b) => {
        return priorityMap[a.stage] - priorityMap[b.stage];
    });
}

exports.generateAccessToken = (key, dataToEncode) => {
    return jwt.sign({ data: dataToEncode }, key, { expiresIn: '60d' });
};

exports.decodeAccessToken = (key, dataToDecode) => {
    try {
        const decoded = jwt.verify(dataToDecode, key);
        return decoded;
    } catch (error) {
        return null;
    }
};

exports.checkSpeedChange = (currentSpeed, requestedSpeed) => {
    const speeds = ["100W", "300W", "GIG15W"];  // Declaring the speeds array

    // If the current speed is null and requested is valid, it's an upgrade
    if (!currentSpeed) {
        return speeds.includes(requestedSpeed) ? "UPGRADE" : "NO CHANGE";
    }

    // If current speed and requested speed are the same, no change
    if (currentSpeed === requestedSpeed) return "NO CHANGE";

    // If current speed is less than requested speed, it's an upgrade
    if (speeds.indexOf(currentSpeed) < speeds.indexOf(requestedSpeed)) {
        return "UPGRADE";
    }
    // Otherwise, it's a downgrade
    return "DOWNGRADE";
}

exports.checkTVChange = (currentPackage, requestedPackage) => {
    const speeds = ["1_BPKG_LN", "2_BPKG_LNP", "6_BPKG_BCCO"];
    if (!currentPackage) return speeds.includes(requestedPackage) ? "UPGRADE" : "NO CHANGE";
    if (currentPackage === requestedPackage) return "NO CHANGE";
    if (speeds.indexOf(currentPackage) < speeds.indexOf(requestedPackage)) return "UPGRADE";
    return "DOWNGRADE";
}