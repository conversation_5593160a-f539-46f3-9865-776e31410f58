//Database Connection Build
const CONFIG = require("../config")
const Sequelize = require("sequelize");
const moment = require('moment-timezone');
const currentOffset = moment.tz('America/Halifax').format('Z');

// Initialize Sequelize with database configurations
const sequelize = new Sequelize(CONFIG.database.db, CONFIG.database.user, CONFIG.database.password, {
  host: CONFIG.database.host,
  dialect: CONFIG.database.dialect,
  logging: CONFIG.database.logging == 'true',
  port: CONFIG.database.port,
  timezone: currentOffset,
  pool: {
    max: CONFIG.database.pool.max,
    min: CONFIG.database.pool.min,
    acquire: CONFIG.database.pool.acquire,
    idle: CONFIG.database.pool.idle
  }
});

// Initialize db object to hold Sequelize models
const db = {};
db.sequelize = sequelize;

// Define Sequelize models and associations
db.Contacts = require("./contacts-model")(sequelize)
db.CustomerDetails = require("./contacts_customer_details-model")(sequelize)
db.ContactsCardDetails = require("./contacts_card_details-model")(sequelize)
db.CustomerAddresses = require("./customer_details_addresses-model")(sequelize)
db.InternetElSwapOrder = require("./internets_eastlink_swap_order-model")(sequelize)
db.CustomerInternet = require("./customer_details_internets_eastlink-model")(sequelize)
db.CustomerTv = require("./customer_details_tv-model")(sequelize)
db.CustomerPhone = require("./customer_details_phone-model")(sequelize)
db.CustomerSubscriptions = require("./customer_details_subscriptions-model")(sequelize)
db.SubscriptionInvoice = require("./subscriptions_invoices-model")(sequelize)
db.ContactsReferrals = require("./contacts_referral-model")(sequelize)
db.DuplicateCustomers = require("./duplicate-customers-model")(sequelize)
db.CreationOrderShipping = require("./creation_order_shipping-model")(sequelize)
db.InternetElCreationOrder = require("./internets_eastlink_creation-model")(sequelize)
db.InternetElDisconnectOrder = require("./internets_eastlink_disconnect-model")(sequelize)
db.InternetElSpeedChangeOrder = require("./internets_eastlink_speed_change-model")(sequelize)
db.InternetElTechAppointment = require("./internets_eastlink_tech_appointment-model")(sequelize)
db.Promotion = require("./promotion-model")(sequelize)
db.CustomerPromotionalReceipt = require("./customer_promotional_receipt-model")(sequelize)
db.SubscriptionCardMapping = require("./subscription_card_mapping-model")(sequelize)
db.InternetElMoveOrder = require("./internets_eastlink_move_order-model")(sequelize)

db.Contacts.hasMany(db.CustomerDetails, { foreignKey: "contact_id", sourceKey: "id" })
db.CustomerDetails.belongsTo(db.Contacts, { foreignKey: "contact_id", sourceKey: "id" })

db.Contacts.hasMany(db.ContactsCardDetails, { foreignKey: "contact_id", sourceKey: "id" })
db.ContactsCardDetails.belongsTo(db.Contacts, { foreignKey: "contact_id", sourceKey: "id" })

db.CustomerAddresses.hasOne(db.CustomerDetails, { foreignKey: "service_address_id", sourceKey: "id", as: 'serviceDetails' });
db.CustomerDetails.belongsTo(db.CustomerAddresses, { foreignKey: "service_address_id", targetKey: "id", as: 'serviceAddress' });

db.CustomerAddresses.hasOne(db.CustomerDetails, { foreignKey: "mailing_address_id", sourceKey: "id", as: 'mailingDetails' });
db.CustomerDetails.belongsTo(db.CustomerAddresses, { foreignKey: "mailing_address_id", targetKey: "id", as: 'mailingAddress' });

db.CustomerDetails.hasMany(db.ContactsReferrals, { foreignKey: "contact_id", sourceKey: "id" })
db.ContactsReferrals.belongsTo(db.CustomerDetails, { foreignKey: "contact_id", sourceKey: "id" })

db.CustomerDetails.hasOne(db.CustomerSubscriptions, { foreignKey: "customer_details_id", sourceKey: "id", as: 'customerSubscription' })
db.CustomerSubscriptions.belongsTo(db.CustomerDetails, { foreignKey: "customer_details_id", sourceKey: "id", as: 'customerDetails' })

db.CustomerInternet.hasOne(db.CustomerDetails, { foreignKey: "internet_id", sourceKey: "id", as: 'customerDetails' })
db.CustomerDetails.belongsTo(db.CustomerInternet, { foreignKey: "internet_id", sourceKey: "id", as: 'customerInternet' })

db.CustomerTv.hasOne(db.CustomerDetails, { foreignKey: "tv_id", sourceKey: "id", as: 'customerDetails' })
db.CustomerDetails.belongsTo(db.CustomerTv, { foreignKey: "tv_id", sourceKey: "id", as: 'customerTv' })

db.CustomerPhone.hasOne(db.CustomerDetails, { foreignKey: "phone_id", sourceKey: "id", as: 'customerDetails' })
db.CustomerDetails.belongsTo(db.CustomerPhone, { foreignKey: "phone_id", sourceKey: "id", as: 'customerPhone' })

db.CustomerDetails.hasMany(db.SubscriptionInvoice, { foreignKey: "customer_details_id", sourceKey: "id", as: 'customerInvoices' })
db.SubscriptionInvoice.belongsTo(db.CustomerDetails, { foreignKey: "customer_details_id", sourceKey: "id", as: 'customerDetail' })

db.CustomerSubscriptions.hasMany(db.SubscriptionInvoice, { foreignKey: "customer_subscription_id", sourceKey: "id" })
db.SubscriptionInvoice.belongsTo(db.CustomerSubscriptions, { foreignKey: "customer_subscription_id", sourceKey: "id" })

db.InternetElCreationOrder.hasOne(db.CustomerInternet, { foreignKey: "creation_order", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElCreationOrder, { foreignKey: "creation_order", sourceKey: "id", as: 'internetElCreationOrder' })

db.InternetElTechAppointment.hasOne(db.CustomerInternet, { foreignKey: "tech_appointment", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElTechAppointment, { foreignKey: "tech_appointment", sourceKey: "id", as: 'internetElTechAppointment' })

db.InternetElDisconnectOrder.hasOne(db.CustomerInternet, { foreignKey: "disconnect_order", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElDisconnectOrder, { foreignKey: "disconnect_order", sourceKey: "id", as: 'internetElDisconnectOrder' })

db.InternetElSwapOrder.hasOne(db.CustomerInternet, { foreignKey: "swap_order", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElSwapOrder, { foreignKey: "swap_order", sourceKey: "id", as: 'internetElSwapOrder' })

db.InternetElSpeedChangeOrder.hasOne(db.CustomerInternet, { foreignKey: "speed_change_order", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElSpeedChangeOrder, { foreignKey: "speed_change_order", sourceKey: "id", as: 'internetElSpeedChangeOrder' })

db.CreationOrderShipping.hasOne(db.InternetElCreationOrder, { foreignKey: "shipping_id", sourceKey: "id", as: 'internetElCreationOrder' })
db.InternetElCreationOrder.belongsTo(db.CreationOrderShipping, { foreignKey: "shipping_id", sourceKey: "id", as: 'creationOrderShipping' })

db.InternetElMoveOrder.hasOne(db.CustomerInternet, { foreignKey: "move_order", sourceKey: "id", as: 'customerInternet' })
db.CustomerInternet.belongsTo(db.InternetElMoveOrder, { foreignKey: "move_order", sourceKey: "id", as: 'internetElMoveOrder' })

db.SubscriptionCardMapping.hasMany(db.ContactsCardDetails, { foreignKey: "id", sourceKey: "contact_card_id" })
db.ContactsCardDetails.belongsTo(db.SubscriptionCardMapping, { foreignKey: "id", sourceKey: "contact_card_id" })

module.exports = db;