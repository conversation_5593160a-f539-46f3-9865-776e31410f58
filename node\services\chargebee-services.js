const ChargebeeClient = require("../clients/chargebee/chargebee");
const callChargebee = new ChargebeeClient();
const db = require("../models");
const moment = require('moment');

class ChargebeeServices {
  // Retrieves invoice details from chargeeBee
  async getInvoicesList(customerSubscription, transaction) {
    try {
      let hasMore = true;
      let offset = null;
      const options = transaction ? { transaction } : {};

      if (!customerSubscription) return { status: true };

      while (hasMore) {
        const invoicesData = await callChargebee.getInvoicesList(customerSubscription?.cb_subscription_id, offset);

        if (invoicesData?.list) {
          for (const invoiceData of invoicesData.list) {
            const invoice = invoiceData.invoice;
            const credit_issue = invoice?.issued_credit_notes.reduce((sum, note) => {
              if (note?.cn_total) return sum + note.cn_total;
              return sum;
            }, 0);
            const invData = {
              customer_details_id: customerSubscription.customer_details_id,
              customer_subscription_id: customerSubscription.id,
              cb_subscription_id: invoice?.subscription_id,
              cb_invoice_id: invoice?.id,
              amount: invoice?.total ? invoice.total / 100 : 0,
              credit_issue: credit_issue ? credit_issue / 100 : 0,
              amount_adjusted: invoice?.amount_adjusted ? invoice.amount_adjusted / 100 : 0,
              expected_payment_date: invoice?.expected_payment_date ? moment.unix(invoice?.expected_payment_date).format('YYYY-MM-DD HH:mm:ss') : null,
              status: invoice?.status,
              total_outstanding: invoice?.amount_due ? invoice.amount_due / 100 : 0,
              createdAt: invoice?.date ? moment.unix(invoice?.date).format('YYYY-MM-DD HH:mm:ss') : moment.now(),
            };

            const [invoiceDetails, created] = await db.SubscriptionInvoice.findOrCreate({
              where: {
                cb_invoice_id: invData.cb_invoice_id,
                cb_subscription_id: invoice?.subscription_id
              },
              defaults: invData,
              ...options
            });

            if (!created) {
              await invoiceDetails.update(invData, options);
            }
          }
        }

        offset = invoicesData?.next_offset;
        hasMore = Boolean(offset);
      }

      return { status: true };
    } catch (error) {
      console.error(`ChargebeeServices getInvoicesList -> `, error);
      throw error;
    }
  }

  // Retrieves card details from chargeeBee
  async getAllCards(contact_id, cb_customer_id, transaction) {
    try {
      let hasMore = true;
      let isCardExist = false;
      let offset = null;
      const options = transaction ? { transaction } : {};

      if (!contact_id || !cb_customer_id) return { status: false };
      let cardIds = [];
      while (hasMore) {
        const cardsData = await callChargebee.retrievePaymentSource(cb_customer_id, offset);
        if (cardsData?.list?.length) {
          isCardExist = true;
          for (const cardEntry of cardsData.list) {
            const cardData = cardEntry?.payment_source?.card;
            if (!cardData) continue;

            const crdData = {
              contact_id, //need to change
              cb_card_id: cardEntry?.payment_source?.id,
              card_number: cardData?.last4,
              expiry_month: cardData?.expiry_month,
              expiry_year: cardData?.expiry_year,
              status: "active",
            };
            const firstName = cardData?.first_name || "";
            const lastName = cardData?.last_name || "";
            const cardName = firstName || lastName ? `${firstName} ${lastName}`.trim() : null;
            if (cardName) crdData.card_name = cardName;

            cardIds.push(cardEntry?.payment_source?.id);

            let [cardDetails, created] = await db.ContactsCardDetails.findOrCreate({
              where: {
                contact_id: crdData.contact_id,
                cb_card_id: crdData?.cb_card_id
              },
              defaults: crdData,
              ...options
            });

            if (!created) await cardDetails.update(crdData, options);
          }
        }
        offset = cardsData?.next_offset;
        hasMore = Boolean(offset);
      }

      if (isCardExist) await this.setPrimaryCard(cb_customer_id, contact_id, transaction);

      return { status: true, cardIds };
    } catch (error) {
      console.error(`ChargebeeServices getAllCards -> `, error);
      throw error;
    }
  }

  async setPrimaryCard(cbCustomerId, contact_id, transaction) {
    try {
      const options = transaction ? { transaction } : {};
      const customerData = await callChargebee.getCustomerData(cbCustomerId);
      if (customerData?.card) {
        const cardDetails = await db.ContactsCardDetails.findOne({ where: { cb_card_id: customerData.card.payment_source_id, contact_id }, transaction });
        if (cardDetails) {
          await db.ContactsCardDetails.update({ is_primary: "0" }, {
            where: { contact_id },
            ...options, // Passing the transaction object
          });
          await db.ContactsCardDetails.update({ is_primary: "1" }, {
            where: { id: cardDetails.id },
            ...options, // Passing the transaction object
          });
        }
      }
    } catch (error) {
      console.error(`ChargebeeServices setPrimaryCard -> `, error);
      throw error;
    }
  }

  async chargebeeWebhook(payload) {
    try {
      if (!payload || !payload.event_type) { return { status: true }; }

      let response;
      switch (payload.event_type) {
        case "invoice_updated":
        case "invoice_generated":
          response = await this.invoiceUpdateOrCreate(payload.content?.invoice);
          break;
        case "subscription_changed":
          response = await this.subscriptionUpdate(payload.content?.subscription);
          break;
        case "card_added":
        case "card_updated":
          response = await this.cardAddedOrUpdated(payload.content);
          break;
        case "invoice_deleted":
          response = await this.invoiceDeleted(payload.content);
          break;
        default:
          response = { status: true };
          break;
      }
      return response;
    } catch (error) {
      console.error(`ChargebeeServices chargebeeWebhook -> `, error);
      throw error;
    }
  }

  async invoiceUpdateOrCreate(invoice) {
    try {
      if (!invoice || !invoice.subscription_id) { throw new Error("Invalid invoice data"); }

      const customerSubscription = await db.CustomerSubscriptions.findOne({ where: { cb_subscription_id: invoice.subscription_id } });

      if (!customerSubscription) { throw new Error("Customer subscription not found"); }

      const invData = {
        customer_details_id: customerSubscription.customer_details_id,
        customer_subscription_id: customerSubscription.id,
        cb_subscription_id: invoice.subscription_id,
        cb_invoice_id: invoice.id,
        amount: invoice?.total ? invoice.total / 100 : null,
        expected_payment_date: invoice?.expected_payment_date ? moment.unix(invoice.expected_payment_date).format('YYYY-MM-DD HH:mm:ss') : null,
        status: invoice.status,
        total_outstanding: invoice?.amount_due ? invoice.amount_due / 100 : null,
        createdAt: invoice?.date ? moment.unix(invoice.date).format('YYYY-MM-DD HH:mm:ss') : moment.now(),
      };

      const [invoiceDetails, created] = await db.SubscriptionInvoice.findOrCreate({
        where: {
          cb_invoice_id: invData.cb_invoice_id,
          customer_subscription_id: invData.customer_subscription_id,
          customer_details_id: invData.customer_details_id,
        },
        defaults: invData
      });

      if (!created) {
        await invoiceDetails.update(invData);
      }

      return { status: true };
    } catch (error) {
      console.error(`ChargebeeServices invoiceUpdateOrCreate -> `, error);
      throw error;
    }
  }

  async subscriptionUpdate(subscription) {
    try {
      if (!subscription) { throw new Error("Invalid subscription data"); }

      const customerSubscription = await db.CustomerSubscriptions.findOne({ attributes: ["id", "customer_details_id", "cb_subscription_id"], where: { cb_subscription_id: subscription.id } });

      if (!customerSubscription) {
        throw new Error("Customer subscription not found");
      }

      const subscriptionData = {
        amount: subscription.plan_amount ? subscription.plan_amount / 100 : null,
        activated_on: subscription.activated_at ? moment.unix(subscription.activated_at).format('YYYY-MM-DD HH:mm:ss') : null,
        next_billing_at: subscription.next_billing_at ? moment.unix(subscription.next_billing_at).format('YYYY-MM-DD HH:mm:ss') : null,
        status: subscription.status,
      };

      await customerSubscription.update(subscriptionData);

      return { status: true };
    } catch (error) {
      console.error(`ChargebeeServices subscriptionUpdate -> `, error);
      throw error;
    }
  }

  async cardAddedOrUpdated(payload) {
    try {
      const cardData = payload?.card;
      const customerData = payload?.customer;
      if (!cardData || !customerData) {
        throw new Error("Invalid card data");
      }

      const contactsData = await db.Contacts.findOne({ where: { cb_customer_id: customerData?.id } });
      if (!contactsData) {
        throw new Error("Contact Data not found");
      }

      const crdData = {
        contact_id: contactsData?.id,
        cb_card_id: cardData?.payment_source_id,
        card_number: cardData?.last4,
        expiry_month: cardData?.expiry_month,
        expiry_year: cardData?.expiry_year,
        status: "active",
      };

      const firstName = cardData?.first_name || "";
      const lastName = cardData?.last_name || "";
      const cardName = firstName || lastName ? `${firstName} ${lastName}`.trim() : null;
      if (cardName) crdData.card_name = cardName;

      let [cardDetails, created] = await db.ContactsCardDetails.findOrCreate({
        where: {
          contact_id: crdData.contact_id,
          cb_card_id: crdData?.cb_card_id
        },
        defaults: crdData,
      });

      if (!created) await cardDetails.update(crdData);

      await this.setPrimaryCard(customerData?.id, contactsData?.id);

      return { status: true };
    } catch (error) {
      console.error(`ChargebeeServices cardAdded -> `, error);
      throw error;
    }
  }

  async invoiceDeleted(payload) {
    try {
      // Extract the deleted invoice from the payload
      const deletedInvoice = payload?.invoice;

      // Check if deletedInvoice is available
      if (!deletedInvoice) {
        throw new Error('No invoice found in the payload');
      }

      // Perform the delete operation using Sequelize
      await db.SubscriptionInvoice.destroy({
        where: {
          cb_invoice_id: deletedInvoice.id,
          cb_subscription_id: deletedInvoice.subscription_id,
        }
      });

      // Return success status
      return { status: true };
    } catch (error) {
      console.error(`Error in invoiceDeleted function: `, error);
      throw error; // Re-throw error to propagate it further
    }
  }

  async getSubscriptions(subscriptionId) {
    try {
      const subscriptionData = await callChargebee.getSubscriptions(subscriptionId);
      if (subscriptionData.length) return { status: true, subscriptionDetail: subscriptionData[0] };
      return { status: false };
    } catch (error) {
      console.error(`ChargebeeServices getSubscriptions -> `, error);
      throw error;
    }
  }
}

module.exports = ChargebeeServices;