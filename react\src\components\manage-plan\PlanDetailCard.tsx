import "../../assets/scss/pages/yourBalanceCard.scss";

import { LocationIcon, RightCaretIcon } from "../../assets/Icons";
import {
  formatCurrency,
  formatDate,
  removeApostrophes,
} from "../../utils/helper";
import {
  useDeleteCardMutation,
  usePaymentArrangementsMutation,
} from "../../services/api";
import { useDispatch, useSelector } from "react-redux";

import ConfirmationMessagePopup from "../common/ConfirmationMessagePopup";
import MakePaymentArrangementsPopup from "../dashboard/MakePaymentArrangementsPopup";
import { MakePaymentPopUp } from "../dashboard/MakePaymentPopup";
import Popup from "../common/Popup";
import SelectLocationForPaymentPopup from "../dashboard/SelectLocationForPaymentPopup";
import { addNotification } from "../../store/reducers/toasterReducer";
import { customerSubscriptionType } from "../../store/selectors/customerSubscriptionSelectors";
import { logout } from "../../store/reducers/authenticationReducer";
import useMediaQuery from "../../hooks/MediaQueryHook";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

const PlanDetailCard = ({ planData }: { planData: any }) => {
  const [
    showSelectServiceForPaymentPopup,
    setShowSelectServiceForPaymentPopup,
  ] = useState<boolean>(false);
  const [
    showSelectServiceForPaymentArrangementPopup,
    setShowSelectServiceForPaymentArrangementPopup,
  ] = useState<boolean>(false);
  const [isMakePayment, setIsMakePayment] = useState<boolean | number>(false);
  const [showNestedPopup, setShowNestedPopup] = useState<boolean>(false);
  const [customIndex, setCustomIndex] = useState<number | null>(null);
  const [cardCustomData, setCardCustomData] = useState({});
  const [showPaymentArrangement, setShowPaymentArrangement] =
    useState<boolean>(false);
  const [confirmationPopup, setConfirmationPopup] = useState<boolean>(false);
  const [outstandingData, setOutstandingData] = useState({});
  const [paymentDate, setPaymentDate] = useState<Date | null>(null);
  const [locationArrangement, setLocationArrangement] = useState<any>({});

  const [deleteCard, deleteCardLoading] = useDeleteCardMutation();
  const [arrangePayment, arrangePaymentLoading] =
    usePaymentArrangementsMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const subscriptionType = useSelector(customerSubscriptionType);
  const isDesktop = useMediaQuery("screen and (min-width: 1281px)");

  let totalAmount: number = 0;
  if (planData?.customerInternet?.plan_price) {
    totalAmount =
      totalAmount + parseFloat(planData?.customerInternet?.plan_price);
  }
  if (planData?.customerPhone?.plan_price) {
    totalAmount = totalAmount + parseFloat(planData?.customerPhone?.plan_price);
  }
  if (planData?.customerTv?.total_service_cost) {
    totalAmount =
      totalAmount + parseFloat(planData?.customerTv?.total_service_cost);
  }

  // Toggles the visibility of the Select Location for Payment popup
  const handleLocationSelectPaymentPopup = () => {
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
  };
  // Toggles the visibility of the Select Location for Payment Arrangement popup
  const handleLocationSelectPaymentArrangementPopup = () => {
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
  };

  const handleCloseNestedPopup = () => {
    setShowNestedPopup(!showNestedPopup);
    setCustomIndex(null);
  };

  // Handles the next step after selecting a location for payment
  const handleLocationSelectPaymentNext = (location: any) => {
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
    setOutstandingData(location);
    setIsMakePayment(location?.customer_subscription_id);
  };

  // Toggles the visibility of the Make Payment popup
  const handleMakePaymentPopup = () => {
    setIsMakePayment(!isMakePayment);
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
  };

  const handleRefetchData = () => {
    // handleGetUserData();
    // refreshLocation();
  };

  // Delete card from nested popup
  const handleDeleteCard = async () => {
    try {
      const response = await deleteCard(
        cardCustomData[customIndex]?.id
      ).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({
            type: "success",
            message: response?.message,
          })
        );
        handleRefetchData();
        setShowNestedPopup(false);
        setCustomIndex(null);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };
  // Toggles the visibility of the Payment Arrangements popup
  const handlePaymentArrangementPopup = () => {
    setShowPaymentArrangement(!showPaymentArrangement);
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
    setPaymentDate(null);
  };

  // Handle the confirmation on payment
  const handleConfirmation = () => {
    setConfirmationPopup(!confirmationPopup);
    setShowPaymentArrangement(false);
  };

  // Handle the submission on payment
  const HandleSavePaymentDate = async () => {
    try {
      const paymentDateWithTime = new Date(paymentDate);
      const currentDateTime = new Date();
      paymentDateWithTime.setHours(currentDateTime.getHours());
      paymentDateWithTime.setMinutes(currentDateTime.getMinutes());
      paymentDateWithTime.setSeconds(currentDateTime.getSeconds());
      const data = {
        invoice_id: locationArrangement?.id,
        date: paymentDateWithTime,
      };

      const response = await arrangePayment(data).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
        setShowPaymentArrangement(false);
        setConfirmationPopup(false);
        setPaymentDate(new Date());
        // handleGetUserData();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Handles the next step after selecting a location for payment arrangements
  const handleLocationSelectPaymentArrangementNext = (location: string) => {
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
    setLocationArrangement(location);
    setShowPaymentArrangement(!showPaymentArrangement);
  };

  return (
    <div className="balance-card bg-medium_purple 2xl:px-30 2xl:py-[38px] relative rounded-[20px]">
      <div className="relative z-[2] flex justify-between md:gap-2 gap-5 md:flex-row flex-col">
        <div className="flex flex-col justify-between 2xl:gap-[20px] gap-30">
          <div className="flex text-white gap-5 items-center">
            <div className="w-5 h-5 flex items-center justify-center">
              <LocationIcon color="white" />
            </div>
            <div>
              <p className="2xl:text-[26px] lg:text-xl text-base font-medium">
                {removeApostrophes(planData?.serviceAddress?.full_address)}
              </p>
            </div>
          </div>

          <div className="flex xl:gap-10 gap-5 max-lg:flex-col">
            <div className="flex text-white lg:flex-col max-lg:items-center gap-2 max-lg:justify-between">
              <p className="text-sm font-medium uppercase">Your Subscription</p>
              <p className="2xl:text-[26px] text-xl font-medium">
                {subscriptionType ? (
                  <span className="flex items-center whitespace-nowrap gap-1.5">
                    <span className="font-medium text-xl xl:text-xl 2xl:text-[26px]">
                      {formatCurrency(totalAmount)}
                    </span>
                    <span className="">
                      / {subscriptionType == "monthly" ? "month" : "year"}
                    </span>
                  </span>
                ) : (
                  <span>NA</span>
                )}
              </p>
            </div>
            <div className="flex lg:flex-col max-lg:items-center text-white gap-2 max-lg:justify-between">
              <p className="text-sm font-medium uppercase">Your balance due</p>
              <span className="2xl:text-[26px] text-xl font-medium  flex items-baseline gap-1">
                {formatCurrency(
                  planData?.customerSubscription?.balance_due
                    ? planData?.customerSubscription?.balance_due
                    : 0,
                  true
                )}
              </span>
            </div>
          </div>
        </div>
        {planData?.customerSubscription?.id &&
          planData?.customerSubscription?.balance_due > 0 && (
            <div className="flex flex-col md:gap-5 gap-2.5 w-full max-w-[279px]">
              {planData?.customerSubscription?.balance_due > 0 && (
                <div
                  className="btn btn-fill !bg-white !border-white flex items-center justify-between gap-2 cursor-pointer"
                  onClick={handleLocationSelectPaymentPopup}
                >
                  <span>Make payment</span>
                  <RightCaretIcon />
                </div>
              )}
              {planData?.payment_arrangement && (
                <div
                  className="btn btn-white flex items-center hover:bg-transparent hover:text-white justify-between gap-2 cursor-pointer"
                  onClick={handleLocationSelectPaymentArrangementPopup}
                >
                  <span>Make payment arrangements</span>
                  <RightCaretIcon />
                </div>
              )}
            </div>
          )}
      </div>

      {showSelectServiceForPaymentPopup && (
        <SelectLocationForPaymentPopup
          type="payment"
          subscriptionId={planData?.customerSubscription?.id} // for particular subscription
          subtitle="Please select invoice you would like to pay"
          closeHandler={() =>
            setShowSelectServiceForPaymentPopup(
              !showSelectServiceForPaymentPopup
            )
          }
          nextHandler={handleLocationSelectPaymentNext}
        />
      )}

      {isMakePayment && !showNestedPopup && (
        <MakePaymentPopUp
          isMakePayment={isMakePayment}
          outstandingData={outstandingData}
          closeHandler={handleMakePaymentPopup}
          refetch={handleRefetchData}
          showNestedPopup={showNestedPopup}
          setShowNestedPopup={setShowNestedPopup}
          setCustomIndex={setCustomIndex}
          setCardCustomData={setCardCustomData}
        />
      )}

      {showNestedPopup && (
        <ConfirmationMessagePopup
          title={"Important: Delete Card?"}
          message={
            "Deleting this card will remove it from your account and you will no longer be able to use it for transactions. Are you sure you want to proceed?"
          }
          closeHandler={handleCloseNestedPopup}
          handleSubmit={handleDeleteCard}
          isLoading={deleteCardLoading?.isLoading}
        />
      )}
      {showPaymentArrangement && (
        <Popup
          title="Payment arrangements"
          width={isDesktop ? "700px" : "600px"}
          height={"501px"}
          closeHandler={handlePaymentArrangementPopup}
        >
          <MakePaymentArrangementsPopup
            closeHandler={handlePaymentArrangementPopup}
            setPaymentDate={setPaymentDate}
            paymentDate={paymentDate}
            locationData={locationArrangement}
            onSubmit={handleConfirmation}
          />
        </Popup>
      )}
      {confirmationPopup && (
        <ConfirmationMessagePopup
          title="Confirm payment arrangements"
          message={`You have made arrangement for your account on file to be bill ${formatCurrency(
            locationArrangement?.total_outstanding
              ? locationArrangement?.total_outstanding
              : 0,
            true
          )} on ${formatDate(paymentDate)}`}
          handleSubmit={HandleSavePaymentDate}
          closeHandler={() => {
            setConfirmationPopup(false);
            setShowPaymentArrangement(true);
          }}
          isLoading={arrangePaymentLoading?.isLoading}
          btnText="Confirm"
        />
      )}
      {showSelectServiceForPaymentArrangementPopup && (
        <SelectLocationForPaymentPopup
          type="payment_arrangement"
          subtitle="Please select invoice you wish to arrange payment on"
          closeHandler={() =>
            setShowSelectServiceForPaymentArrangementPopup(
              !showSelectServiceForPaymentArrangementPopup
            )
          }
          nextHandler={handleLocationSelectPaymentArrangementNext}
          subscriptionId={planData?.customerSubscription?.id} // for particular subscription
        />
      )}
    </div>
  );
};

export default PlanDetailCard;
