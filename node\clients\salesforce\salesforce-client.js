const jsforce = require('jsforce');
const { RESPONSE_CODES } = require("../../utils/ResponseCodes");
const CustomError = require('../../utils/errors/CustomError.js');
const { once } = require('events');
const CONFIG = require("../../config");

class SalesforceConnection {

    constructor(baseurl, username, password, token) {
        this.baseurl = baseurl;
        this.username = username;
        this.password = password;
        this.token = token;
        this.conn = new jsforce.Connection({ loginUrl: this.baseurl });
        this.login();
    }

    /**
     * Retry utility function with exponential backoff for Salesforce operations
     * @param {Function} operation - The operation to retry
     * @param {Object} retryConfig - Retry configuration
     * @param {string} operationName - Name of the operation for logging
     * @returns {Promise} - Result of the operation
     */
    async retryWithExponentialBackoff(operation, retryConfig, operationName = 'Salesforce operation') {
        const { maxAttempts, baseDelayMs, maxDelayMs, backoffMultiplier } = retryConfig;
        let lastError;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;

                // Check if this is a retryable error
                const isRetryableError = error?.errorCode === 'UNABLE_TO_LOCK_ROW' ||
                                       (error?.message && error.message.includes('unable to obtain exclusive access'));

                if (!isRetryableError || attempt === maxAttempts) {
                    console.error(`${operationName} failed after ${attempt} attempts:`, error);
                    throw error;
                }

                // Calculate delay with exponential backoff
                const delay = Math.min(baseDelayMs * Math.pow(backoffMultiplier, attempt - 1), maxDelayMs);

                console.warn(`${operationName} failed (attempt ${attempt}/${maxAttempts}): ${error.message}. Retrying in ${delay}ms...`);

                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw lastError;
    }

    async login() {
        try {
            await this.conn.login(this.username, this.password + this.token);
        } catch (error) {
            console.error(`Salesforce login failed -->`, error);
        }
    }

    async getContactDetailsByEmail(email) {
        try {
            const existingContact = await this.conn.sobject('Contact').find({ Email: email }, { Id: 1, FirstName: 1, LastName: 1, Email: 1, Company_Name__c: 1, Primary_Phone__c: 1, Alternate_Phone_Number__c: 1, Contact_Name_if_different_than_end_user__c: 1, Referral_Link_Full__c: 1, Account_Chargebee_Customer_ID__c: 1, Total_Referrals__c: 1, Total_Paid_Referrals__c: 1, Total_Earned_Referrals__c: 1, AccountName__c: 1, LastModifiedDate: 1, CreatedDate: 1, Sticky_Sender__c: 1, Name: 1, CP_Cognito_ID__c: 1 });

            if (existingContact?.length) return { returnStatus: true, userData: existingContact };
            else throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found in Salesforce!");
        } catch (error) {
            console.error(`Error fetching contact details from Salesforce ->`, error);
            throw error;
        }
    }

    async getCustomerDetailsByContactId(Contact__c) {
        try {
            const returnResponse = { returnStatus: false, customerData: null };
            const customerDetails = await this.conn.sobject('Customer_Details__c').find({ Contact__c }, { Id: 1, Service_Address__c: 1, Mailing_Address__c: 1, Latest_TV__c: 1, Latest_Internet__c: 1, Latest_Phone_VOIP__c: 1, Latest_CB_Subscription__c: 1, CP_Stage__c: 1, LastModifiedDate: 1, CB_Subscription_Id__c: 1, CreatedDate: 1, Name: 1 });
            if (Array.isArray(customerDetails) && customerDetails.length) {
                returnResponse.returnStatus = true;
                returnResponse.customerData = customerDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer details from Salesforce ->`, error);
            throw error;
        }
    }

    async getInternetTvPhoneDetailsByServiceId(serviceId, apiName, selectedObj) {
        try {
            const returnResponse = { returnStatus: false, internetTvPhoneData: null };
            const internetTvPhoneDetails = await this.conn.sobject(apiName).findOne({ Id: serviceId }, selectedObj);
            if (internetTvPhoneDetails) {
                returnResponse.returnStatus = true;
                returnResponse.internetTvPhoneData = internetTvPhoneDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching internet tv phone details from Salesforce -> `, error);
            throw error;
        }
    }

    async updateAddressDetails(apiName, data) {
        try {
            const returnResponse = { returnStatus: false };
            const updateResponse = await this.conn.sobject(apiName).update(data);
            if (updateResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.addressId = updateResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error update address details from Salesforce ->`, error);
            throw error;
        }
    }

    async getAddressDetailsById(Id, apiName, selectedObj) {
        try {
            const returnResponse = { returnStatus: false, addresstData: null };
            const addressDetails = await this.conn.sobject(apiName).findOne({ Id }, selectedObj);
            if (addressDetails) {
                returnResponse.returnStatus = true;
                returnResponse.addresstData = addressDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching address details from Salesforce ->`, error);
            throw error;
        }
    }

    async getBulkContactDetails(soql) {
        try {
            const query = this.conn.bulk.query(soql);
            const records = [];
            query.on('record', record => records.push(record));
            await once(query, 'end');
            return records;
        } catch (error) {
            console.error(`Error fetching customer bulk details from Salesforce ->`, error);
            throw error;
        }
    }

    async updateProfileDetails(data) {
        const operation = async () => {
            const returnResponse = { returnStatus: false };
            const updateResponse = await this.conn.sobject('Contact').update(data);
            if (updateResponse?.success) {
                returnResponse.returnStatus = true;
            }
            return returnResponse;
        };

        try {
            return await this.retryWithExponentialBackoff(
                operation,
                CONFIG.salesforce.lockRowRetry,
                'updateProfileDetails'
            );
        } catch (error) {
            console.error(`Error update profile details from Salesforce ->`, error);
            throw error;
        }
    }

    async getSubscriptionDetails(Id) {
        try {
            const returnResponse = { returnStatus: false, subscriptionData: null };
            const subscriptionDetails = await this.conn.sobject('chargebeeapps__CB_Subscription__c').findOne({ Id }, { Id: 1, chargebeeapps__CB_Subscription_Id__c: 1, chargebeeapps__Plan_Amount__c: 1, chargebeeapps__Subcription_Activated_At__c: 1, chargebeeapps__Next_billing__c: 1, chargebeeapps__Subscription_status__c: 1, LastModifiedDate: 1 });
            if (subscriptionDetails) {
                returnResponse.returnStatus = true;
                returnResponse.subscriptionData = subscriptionDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer subscription details from Salesforce ->`, error);
            throw error;
        }
    }

    async getInvoiceDetails(chargebeeapps__SubscriptionId__c) {
        try {
            const returnResponse = { returnStatus: false, invoiceData: null };
            const invoiceDetails = await this.conn.sobject('chargebeeapps__CB_Invoice__c').find({ chargebeeapps__SubscriptionId__c }, { Id: 1, chargebeeapps__CB_Invoice_Id__c: 1, chargebeeapps__Due_Amount__c: 1, chargebeeapps__Amount__c: 1, Expected_Payment_Date_Time__c: 1, chargebeeapps__Status__c: 1, chargebeeapps__Subscription_CB_Id__c: 1, LastModifiedDate: 1 });
            if (Array.isArray(invoiceDetails) && invoiceDetails.length) {
                returnResponse.returnStatus = true;
                returnResponse.invoiceData = invoiceDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer subscription invoice details from Salesforce ->`, error);
            throw error;
        }
    }

    async createMailingAddress(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Mailing_Address__c').create(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.addressId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error create mailing address in Salesforce ->`, error);
            throw error;
        }
    }

    async updateMailingAdrsinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Customer_Details__c').update(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error update mailing details in Salesforce ->`, error);
            throw error;
        }
    }

    async createOrderinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Order__c').create(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.orderId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error create order in Salesforce ->`, error);
            throw error;
        }
    }

    async createNewTvObjectinSf(data, type) {
        try {
            const returnResponse = { returnStatus: false };
            let createResponse;
            if (type === "create") createResponse = await this.conn.sobject('TV__c').create(data);
            else createResponse = await this.conn.sobject('TV__c').update(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.orderId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error create tv object in Salesforce -> `, error);
            throw error;
        }
    }

    async createNewTvOrderinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('TV_Order__c').create(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.orderId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error create tv order in Salesforce ->`, error);
            throw error;
        }
    }

    async createNewPhoneinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Phone__c').create(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.phoneId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error new phone in Salesforce ->`, error);
            throw error;
        }
    }

    async getReferralDetails(Contact__c) {
        try {
            const returnResponse = { returnStatus: false, referralDetails: null };
            const referralDetails = await this.conn.sobject('Referral__c').find({ Contact__c }, { Id: 1, Contact__c: 1, Referral__c: 1, Referrals_Live_Date__c: 1, Credit_Added_to_Account__c: 1, Credit_Amount__c: 1, CreatedDate: 1, LastModifiedDate: 1, Referrals_Name__c: 1, Name: 1 });
            if (referralDetails) {
                returnResponse.returnStatus = true;
                returnResponse.referralDetails = referralDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching internet tv phone details from Salesforce ->`, error);
            throw error;
        }
    }

    async getContact() {

        const Id = 'a37Sv000000NOJNIA4';
        // const existingContact = await this.conn.query(`Select Id, ToLabel(Calling_plan__c) From Phone__c WHERE Id = '${Id}' LIMIT 1`);
        // let existingContact = await this.conn.sobject('Referral__c').find({}, { Id: 1, Contact__c: 1, Referral__c: 1, Referrals_Live_Date__c: 1, Credit_Added_to_Account__c: 1, Credit_Amount__c: 1, Referrals_Live_Date__c: 1 });

        // const query = this.conn.bulk.query("SELECT Id FROM Referral__c WHERE IsDeleted = TRUE ALL ROWS");

        // const records = [];
        // const query = this.conn.query("SELECT Id, Referrals_Live_Date__c FROM Referral__c WHERE IsDeleted = TRUE")
        //   .on("record", (record) => {
        //     records.push(record);
        //   })
        //   .on("end", () => {
        //     console.log("total in database : " + query.totalSize);
        //     console.log("total fetched : " + query.totalFetched);
        //   })
        //   .on("error", (err) => {
        //     console.error(err);
        //   })
        //   .run({ autoFetch : true, maxFetch : 4000 }); // synonym of Query.execute();


        console.log("call ----")
        const ids = ["0122R0000002ckKQAQ", "0122R0000002ckKQAQ", "0122R0000002ckLQAQ", "0122R0000002ckPQAQ"];
        // const existingContact = await this.conn.sobject('RecordType').find({ Id: { $in: ["0122R0000002ckNQAQ"] } }, { Name: 1 });
        // const existingContact = await this.conn.sobject('RecordType').find({ Id: { $in: ids } }, { Name: 1 });
        // const existingContact = await this.conn.sobject('Phone_Order__c').findOne({ Id : "a3aSv000000PrsLIAS" });


        // const existingContact = await this.conn.sobject('Contact').find({});
        // const existingContact = await this.conn.sobject('Internet__c').findOne({Id : "a2dSv000000KrUnIAK"});
        const existingContact = await this.conn.sobject('Order__c').findOne({ Id: "a2kSv000000RgpZIAS" });
        // const existingContact = await this.conn.sobject('Customer_Details__c').findOne({});
        // const existingContact = await this.conn.sobject('Referral__c').findOne({Id: "a1tSv000006XYFa" });
        // const existingContact = await this.conn.sobject('chargebeeapps__CB_Invoice__c').find({Id: "a1WSv000000F1a6MAC"});
        // const existingContact = await this.conn.sobject('chargebeeapps__CB_Subscription__c').findOne({ Id: "a1hSv000000FIMPIA4" });
        return existingContact;
    }

    async getShippingDetails(Id) {
        try {
            const returnResponse = { returnStatus: false, shippingData: null };
            const shippingDetails = await this.conn.sobject('Shipping__c').findOne({ Id }, { Id: 1, Name: 1, Full_Mailing_Address__c: 1, Ship_Date__c: 1, Ship_Drop_Off_Date__c: 1, Tracking_URL__c: 1, Courier__c: 1, LastModifiedDate: 1, Package_Delivered__c: 1 });
            if (shippingDetails) {
                returnResponse.returnStatus = true;
                returnResponse.shippingData = shippingDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer shipping details from Salesforce ->`, error);
            throw error;
        }
    }

    async getOrderDetails(Id, selectedObj) {
        try {
            const returnResponse = { returnStatus: false, orderData: null };
            const orderDetails = await this.conn.sobject('Order__c').findOne({ Id }, selectedObj);
            if (orderDetails) {
                returnResponse.returnStatus = true;
                returnResponse.orderData = orderDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer order details from Salesforce ->`, error);
            throw error;
        }
    }

    async getTechAppDetails(Id) {
        try {
            const returnResponse = { returnStatus: false, techData: null };
            const techDetails = await this.conn.sobject('Tech_Appointment__c').findOne({ Id }, { Id: 1, LastModifiedDate: 1, Install_Date__c: 1, Install_Time__c: 1, CreatedDate: 1, Name: 1 });
            if (techDetails) {
                returnResponse.returnStatus = true;
                returnResponse.techData = techDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer tech app details from Salesforce -> `, error);
            throw error;
        }
    }

    async updateExpectedPaymentDate(data) {
        try {
            const returnResponse = { returnStatus: false };
            const updateResponse = await this.conn.sobject('chargebeeapps__CB_Invoice__c').update(data);
            if (updateResponse?.success) {
                returnResponse.returnStatus = true;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error update expected payment date in Salesforce ->`, error);
            throw error;
        }
    }

    async getPhoneApiValue(query) {
        try {
            return await this.conn.query(query);
        } catch (error) {
            console.error(`Error fetching phone api value from Salesforce ->`, error);
            throw error;
        }
    }

    async createCancelPhoneOrderinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Phone_Order__c').create(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.orderId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error create phone cancel order in Salesforce -> `, error);
            throw error;
        }
    }

    async updateOrderSpeedinSf(data) {
        try {
            const returnResponse = { returnStatus: false };
            const createResponse = await this.conn.sobject('Order__c').update(data);
            if (createResponse?.success) {
                returnResponse.returnStatus = true;
                returnResponse.orderId = createResponse?.id;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error update order in Salesforce ->`, error);
            throw error;
        }
    }

    async deleteSpeedChangeOrder(Id) {
        try {
            const returnResponse = { returnStatus: false };
            const deleteResponse = await this.conn.sobject('Order__c').delete(Id);
            if (deleteResponse?.success) {
                returnResponse.returnStatus = true;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error delete order in Salesforce ->`, error);
            throw error;
        }
    }

    async getRecordDetails(Id) {
        try {
            const returnResponse = { returnStatus: false, recordData: null };

            const recDetails = await this.conn.sobject('RecordType').findOne({ Id }, { Name: 1 });
            if (recDetails) {
                returnResponse.returnStatus = true;
                returnResponse.recordData = recDetails
            }

            return returnResponse;
        } catch (error) {
            console.error("Error fetching record details from Salesforce -> ", error);
        }
    }

    async getSubscriptionDetailsUsingCbSubId(chargebeeapps__CB_Subscription_Id__c) {
        try {
            const returnResponse = { returnStatus: false, subscriptionData: null };
            const subscriptionDetails = await this.conn.sobject('chargebeeapps__CB_Subscription__c').findOne({ chargebeeapps__CB_Subscription_Id__c }, { Id: 1, LastModifiedDate: 1 });
            if (subscriptionDetails) {
                returnResponse.returnStatus = true;
                returnResponse.subscriptionData = subscriptionDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching customer subscription details from Salesforce using sb subs id ->`, error);
            throw error;
        }
    }
}

// Export the SalesforceConnection class
module.exports = SalesforceConnection;

