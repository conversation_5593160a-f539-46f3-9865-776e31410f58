import { YourPlacesProps } from "../../typings/typing";
import PlaceAccordion from "./PlaceAccordion";

const YourPlaces: React.FC<YourPlacesProps> = ({ places }) => {
  return (
    <div className="bg-white rounded-3xl p-5">
        <h4 className="uppercase  text-base font-medium text-primary">
          Your Places
        </h4>
      <div className="lg:mt-30 mt-2 relative rounded-20">
        <hr />
        <div className="flex 2xl:gap-5 gap-2.5 mt-5 flex-col 2xl:max-h-[450px] overflow-auto">
          {Array.isArray(places) &&
            places?.length > 0 &&
            places?.map((data, index) => {
              return <PlaceAccordion data={data} key={index} />;
            })}
        </div>
      </div>
    </div>
  );
};

export default YourPlaces;
