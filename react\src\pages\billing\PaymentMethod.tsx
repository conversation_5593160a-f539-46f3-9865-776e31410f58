import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "../../assets/Icons";
import { AutomaticPaymentPopup } from "../../components/billing/AutomaticPaymentPopup";
import Button from "../../components/common/Button";
import ConfirmationMessagePopup from "../../components/common/ConfirmationMessagePopup";
import PaymentCard from "../../components/common/PaymentCard";
import { useGetCardsMutation, useUpdateCardMutation } from "../../services/api";
import { logout } from "../../store/reducers/authenticationReducer";
import { addNotification } from "../../store/reducers/toasterReducer";
import PaymentMethodSkeleton from "../../components/billing/skeleton/PaymentMethodSkeleton";

interface PaymentMethodProps {
  planData: any;
}

const PaymentMethod: React.FC<PaymentMethodProps> = ({ planData }) => {
  const [paymentPopup, setPaymentPopup] = useState<boolean>(false);
  const [showTrash, setShowTrash] = useState<boolean>(false);
  const [getCards, getCardLoading] = useGetCardsMutation();
  const [updateCard, updateCardLoading] = useUpdateCardMutation();
  const [cardsData, setCardsData] = useState([]);
  const [isSelected, setIsSelected] = useState<any>("");
  const [primaryConfirmation, setPrimaryConfirmation] =
    useState<boolean>(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch cards data when the component mounts
  useEffect(() => {
    handleGetCards();
  }, []);

  //  Function to get cards data from the API
  const handleGetCards = async () => {
    try {
      const response = await getCards(
        planData?.customerSubscription?.id
      ).unwrap();
      if (response?.status === 200) {
        const cardDetailsArray = response?.data;
        setCardsData(cardDetailsArray);
        setIsSelected("");
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle card selection
  const handleSelectCard = (data: any) => {
    if (isSelected?.id == data?.id) {
      setIsSelected("");
    } else {
      setIsSelected(data);
    }
  };

  // Function to handle the edit payment popup
  const handleEditPayment = () => {
    setPaymentPopup(!paymentPopup);
  };

  // Function to toggle the trash icon visibility
  const handleShowTrash = () => {
    setShowTrash(!showTrash);
    setIsSelected("");
  };

  // Function to update the selected card
  const handleUpdateCard = async (data: any) => {
    if (!updateCardLoading?.isLoading) {
      try {
        const updatedData = {
          card_id: data.id,
          is_primary: "1",
          subscription_id: planData?.customerSubscription?.id,
        };

        const response = await updateCard({
          id: data?.id,
          data: updatedData,
        }).unwrap();
        if (response?.status === 200) {
          dispatch(
            addNotification({
              type: "success",
              message: response?.message,
            })
          );
          handlePrimaryConfirmation();
          setIsSelected("");
          handleGetCards();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to toggle the primary confirmation popup
  const handlePrimaryConfirmation = () => {
    setPrimaryConfirmation(!primaryConfirmation);
  };

  if (getCardLoading?.isLoading || !planData?.id) {
    return <PaymentMethodSkeleton />;
  }
  return (
    <>
      <div className="flex justify-between items-center gap-2 max-lg:pt-5">
        <h5 className="font-medium text-base uppercase">
          Automatic Payment Methods
        </h5>
        <span className="cursor-pointer lg:hidden" onClick={handleShowTrash}>
          <EditIcon />
        </span>
      </div>
      <div className="bg-[#FBF8FF] pb-5 lg:px-5 px-2 rounded-20 border border-[#EBDBFF]">
        <div>
          <div className="flex gap-4 overflow-x-auto">
            {cardsData?.length > 0 &&
              cardsData.map((card: any, i) => {
                return (
                  <React.Fragment key={i}>
                    <PaymentCard
                      data={card}
                      closeBtn={showTrash}
                      isPrimary={card?.is_primary === "1" ? true : false}
                      handleSelectCard={handleSelectCard}
                      isSelected={isSelected?.id === card?.id}
                      refetch={handleGetCards}
                    />
                  </React.Fragment>
                );
              })}
          </div>
          {isSelected && (
            <div className="mt-[20px]">
              <Button
                title="Set as primary"
                clickEvent={handlePrimaryConfirmation}
                isLoading={updateCardLoading?.isLoading}
              />
            </div>
          )}
          <div className="flex justify-center">
            <div
              className="flex items-center w-max justify-center lg:p-5 p-3  h-[45px] gap-2.5 border border-black rounded-20 cursor-pointer mt-4"
              onClick={handleEditPayment}
            >
              <span className="md:text-base text-sm font-medium text-black">
                Add New Card &nbsp; +
              </span>
            </div>
          </div>
        </div>
      </div>
      {paymentPopup && (
        <AutomaticPaymentPopup
          closeHandler={handleEditPayment}
          refetch={handleGetCards}
          subscription_id={planData?.customerSubscription?.id}
        />
      )}
      {primaryConfirmation && (
        <ConfirmationMessagePopup
          title={"Confirm Primary Card"}
          message={
            "By making this your primary card, it will be the default card used for purchases and payments. Are you sure you want to proceed?"
          }
          closeHandler={handlePrimaryConfirmation}
          handleSubmit={() => handleUpdateCard(isSelected)}
          isLoading={updateCardLoading?.isLoading}
        />
      )}
    </>
  );
};

export default PaymentMethod;
