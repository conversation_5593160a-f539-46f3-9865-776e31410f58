const { getCurrentAtlanticTime, sanitize<PERSON><PERSON>ue, hasValid<PERSON><PERSON><PERSON> } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const CONFIG = require('../config');
const CustomerService = require("./webhook-customer-services");

const SalesforceClient = require('../clients/salesforce-client');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class OrderWebhookServices {
    async getOrderDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get order details -> ", error);
        }
    }

    async checkAndManageDetails(orderDetails, _id, _index) {
        try {
            if (!Array.isArray(orderDetails)) orderDetails = [orderDetails];
            for (const orderDetail of orderDetails) {
                const { Id: sf_record_id, Record_Type_Name__c: Name } = orderDetail;
                if (!sf_record_id || !Name) return;

                await this.checkTableToUpdate(Name, orderDetail, _id, _index);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async checkTableToUpdate(Name, orderDetail, _id, _index) {
        try {
            switch (Name) {
                case "Speed Change Order":
                    await this.checkSpeedChangeOrder(Name, orderDetail, _id, _index);
                    break;
                case "Disconnect Order":
                    await this.checkDisconnectOrder(Name, orderDetail, _id, _index);
                    break;
                case "Move Order":
                    await this.checkMoveOrder(Name, orderDetail, _id, _index);
                    break;
                case "Modem Swap Order":
                    await this.checkSwapOrder(Name, orderDetail, _id, _index);
                    break;
                default:
                    await this.checkAddOrder(Name, orderDetail, _id, _index);
                    break;
            }
        } catch (error) {
            console.error("SalesforceService checkTableToUpdate -> ", error);
        }
    }

    async checkSpeedChangeOrder(Name, orderDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Expected_Completion_Date__c: expected_completion_date, LastModifiedDate: sf_updatedAt, Stage__c: stage, Response_Date__c: response_date, Speed__c: speed, CreatedDate, Name: sf_name } = orderDetail;
            const { dataExist } = await this.checkExist(sf_record_id, "internets_eastlink_speed_change_order");

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let type;
            if (dataExist) {
                type = "UPDATE";
                let queryParams = [];
                let query = `UPDATE internets_eastlink_speed_change_order SET 
                sf_updatedAt = ?, 
                createdAt = ?, 
                updatedAt = ?,
                stage = ?,
                speed = ?,
                response_date = ?,
                expected_completion_date = ?`;

                queryParams.push(formatedDate, createdAt, updatedTime);

                if (stage) queryParams.push(stage.replace(/['"`]/g, ''));
                else queryParams.push(null);

                if (speed) queryParams.push(speed);
                else queryParams.push(null);

                if (response_date) queryParams.push(getCurrentAtlanticTime(response_date));
                else queryParams.push(null);

                if (expected_completion_date) queryParams.push(expected_completion_date);
                else queryParams.push(null);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);
                await this.executeQuery(query, queryParams);
            } else {
                if (expected_completion_date) {
                    type = "CREATE";
                    const customerService = new CustomerService();
                    let insertOrderDetails = { sf_record_id, sf_updatedAt: formatedDate, expected_completion_date, sf_name };
                    if (speed) insertOrderDetails.speed = speed;
                    let value = stage;
                    if (value) insertOrderDetails.stage = value.replace(/['"`]/g, '');
                    if (response_date) insertOrderDetails.response_date = getCurrentAtlanticTime(response_date);
                    await customerService.insertRecord("internets_eastlink_speed_change_order", insertOrderDetails, CreatedDate);
                }
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type, sf_record_type: Name });
        } catch (error) {
            console.error("SalesforceService checkSpeedChangeOrder -> ", error);
        }
    }

    async checkDisconnectOrder(Name, orderDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Requested_Disconnect_Date__c: requested_disconnect_date, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = orderDetail;
            const { dataExist } = await this.checkExist(sf_record_id, "internets_eastlink_disconnect_order");

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let type;
            if (dataExist) {
                type = "UPDATE";
                let queryParams = [];
                let query = `UPDATE internets_eastlink_disconnect_order SET 
                sf_updatedAt = ?, 
                createdAt = ?, 
                updatedAt = ?,
                requested_disconnect_date = ?`;

                queryParams.push(formatedDate, createdAt, updatedTime);

                if (requested_disconnect_date) queryParams.push(requested_disconnect_date);
                else queryParams.push(null);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);

                await this.executeQuery(query, queryParams);
            } else {
                type = "CREATE";
                const customerService = new CustomerService();
                let insertOrderDetails = { sf_record_id, sf_updatedAt: formatedDate, sf_name };
                if (requested_disconnect_date) insertOrderDetails.requested_disconnect_date = requested_disconnect_date;
                await customerService.insertRecord("internets_eastlink_disconnect_order", insertOrderDetails, CreatedDate);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type, sf_record_type: Name });
        } catch (error) {
            console.error("SalesforceService checkDisconnectOrder -> ", error);
        }
    }

    async checkAddOrder(record_type, orderDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Related_Shipping_Order__c, LastModifiedDate: sf_updatedAt, CreatedDate, Name, Install_Date__c: install_date } = orderDetail;
            const { dataExist, orderData } = await this.checkCreationOrderExist(sf_record_id);
            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let type;
            if (dataExist) {
                type = "UPDATE";
                const { creationSfId, shippingSfId } = orderData;
                let queryParams = [];
                let query = `UPDATE internets_eastlink_creation_order SET 
                sf_updatedAt = ?, 
                createdAt = ?, 
                updatedAt = ?,
                install_date = ?,
                record_type = ?`;

                queryParams.push(formatedDate, createdAt, updatedTime);

                if (install_date) queryParams.push(install_date);
                else queryParams.push(null);

                if (record_type) queryParams.push(record_type);
                else queryParams.push("None");

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(creationSfId);

                await this.executeQuery(query, queryParams);
                if (hasValidChanges(Related_Shipping_Order__c, shippingSfId)) this.updateDetails(Related_Shipping_Order__c, orderData);
            } else {
                let insertOrderDetails = { sf_record_id: sf_record_id, sf_updatedAt: formatedDate, sf_name: Name };
                type = "CREATE";
                const customerService = new CustomerService();
                if (Related_Shipping_Order__c) {
                    let shipping_id = null;
                    // Check if a record with the same sf_record_id and address_type already exists
                    const checkQuery = `SELECT id 
                              FROM creation_order_shipping 
                              WHERE sf_record_id = ${sanitizeValue(Related_Shipping_Order__c)}`;

                    const existingRecord = await this.executeQuery(checkQuery);

                    if (existingRecord.length > 0) shipping_id = existingRecord[0].id;
                    else {
                        const { shippingData } = await salesforceConnection.getShippingDetails(Related_Shipping_Order__c);

                        if (shippingData) {
                            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updated_At, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingData;

                            const modified_date = getCurrentAtlanticTime(sf_updated_At);
                            const formatedCreatedDate = getCurrentAtlanticTime(CreatedDate);
                            const updatedTime = getCurrentAtlanticTime();

                            const query = `INSERT IGNORE INTO creation_order_shipping 
                                    (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                                    VALUES (
                                    ${sanitizeValue(sf_record_id)},
                                    ${sanitizeValue(sf_name)},
                                    ${sanitizeValue(full_mailing_address)}, 
                                    ${sanitizeValue(ship_date)}, 
                                    ${sanitizeValue(ship_drop_off_date)}, 
                                    ${sanitizeValue(tracking_url)}, 
                                    ${sanitizeValue(courier)}, 
                                    ${sanitizeValue(modified_date)}, 
                                    ${sanitizeValue(package_deliverted_at)}, 
                                    '${formatedCreatedDate}', 
                                    '${updatedTime}'
                                    )`;

                            const insertStatus = await this.executeQuery(query);
                            if (insertStatus) shipping_id = insertStatus?.insertId || null;
                        }
                    }
                    if (shipping_id) insertOrderDetails.shipping_id = shipping_id;
                }
                if (record_type) insertOrderDetails.record_type = record_type;
                if (install_date) insertOrderDetails.install_date = install_date;
                await customerService.insertRecord("internets_eastlink_creation_order", insertOrderDetails, CreatedDate);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type, sf_record_type: record_type });
        } catch (error) {
            console.error("SalesforceService checkAddOrder -> ", error);
        }
    }

    async checkMoveOrder(Name, orderDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Requested_Install_Date__c, LastModifiedDate: sf_updatedAt, Install_Date__c, Install_Time__c, Order_Reject_Reason__c, Order_Reject_Reason_Solved__c, Stage__c, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c, CreatedDate, Submit_Date__c, Requested_Move_Date__c, Name: sf_name } = orderDetail;
            const { dataExist } = await this.checkExist(sf_record_id, "internets_eastlink_move_order");
            let type;
            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();
            if (dataExist) {
                type = "UPDATE";

                let queryParams = [];
                let query = `UPDATE internets_eastlink_move_order SET 
                sf_updatedAt = ?, 
                createdAt = ?, 
                updatedAt = ?,
                requested_install_date = ?,
                requested_move_date = ?,
                install_date = ?,
                submit_date = ?,
                stage = ?,
                install_time = ?,
                reject_reason = ?,
                reject_reason_solved = ?,
                unit = ?,
                street_number = ?,
                street_name = ?,
                city = ?,
                province = ?,
                postal_code = ?`;

                queryParams.push(formatedDate, createdAt, updatedTime);

                if (Requested_Install_Date__c) queryParams.push(Requested_Install_Date__c);
                else queryParams.push(null);

                if (Requested_Move_Date__c) queryParams.push(Requested_Move_Date__c);
                else queryParams.push(null);

                if (Install_Date__c) queryParams.push(Install_Date__c);
                else queryParams.push(null);

                if (Submit_Date__c) queryParams.push(getCurrentAtlanticTime(Submit_Date__c));
                else queryParams.push(null);

                if (Stage__c) queryParams.push(Stage__c);
                else queryParams.push(null);

                if (Install_Time__c) queryParams.push(Install_Time__c);
                else queryParams.push(null);

                if (Order_Reject_Reason__c) queryParams.push(Order_Reject_Reason__c);
                else queryParams.push(null);

                if (Order_Reject_Reason_Solved__c) queryParams.push(Order_Reject_Reason_Solved__c);
                else queryParams.push(null);

                if (Service_Suite_Unit__c) queryParams.push(Service_Suite_Unit__c);
                else queryParams.push(null);

                if (Service_Street_Number__c) queryParams.push(Service_Street_Number__c);
                else queryParams.push(null);

                if (Service_Street_Name__c) queryParams.push(Service_Street_Name__c);
                else queryParams.push(null);

                if (Service_City_Town__c) queryParams.push(Service_City_Town__c);
                else queryParams.push(null);

                if (Service_Province__c) queryParams.push(Service_Province__c);
                else queryParams.push(null);

                if (Service_Postal_Code__c) queryParams.push(Service_Postal_Code__c);
                else queryParams.push(null);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);

                await this.executeQuery(query, queryParams);
            } else {
                type = "CREATE";
                const customerService = new CustomerService();
                let insertOrderDetails = { sf_record_id, sf_updatedAt: formatedDate, sf_name };

                if (Requested_Install_Date__c) insertOrderDetails.requested_install_date = Requested_Install_Date__c;
                if (Requested_Move_Date__c) insertOrderDetails.requested_move_date = Requested_Move_Date__c;
                if (Install_Date__c) insertOrderDetails.install_date = Install_Date__c;
                if (Stage__c) insertOrderDetails.stage = Stage__c;
                if (Install_Time__c) insertOrderDetails.install_time = Install_Time__c;
                if (Order_Reject_Reason__c) insertOrderDetails.reject_reason = Order_Reject_Reason__c;
                if (Order_Reject_Reason_Solved__c) insertOrderDetails.reject_reason_solved = Order_Reject_Reason_Solved__c;
                if (Submit_Date__c) insertOrderDetails.submit_date = getCurrentAtlanticTime(Submit_Date__c);
                if (Service_Suite_Unit__c) insertOrderDetails.unit = Service_Suite_Unit__c;
                if (Service_Street_Number__c) insertOrderDetails.street_number = Service_Street_Number__c;
                if (Service_Street_Name__c) insertOrderDetails.street_name = Service_Street_Name__c;
                if (Service_City_Town__c) insertOrderDetails.city = Service_City_Town__c;
                if (Service_Province__c) insertOrderDetails.province = Service_Province__c;
                if (Service_Postal_Code__c) insertOrderDetails.postal_code = Service_Postal_Code__c;
                await customerService.insertRecord("internets_eastlink_move_order", insertOrderDetails, CreatedDate);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type, sf_record_type: Name });
        } catch (error) {
            console.error("SalesforceService checkMoveOrder -> ", error);
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async checkExist(sf_record_id, tableName) {
        try {

            let selectClause = `id, sf_record_id`;

            const query = `SELECT ${selectClause} FROM ${tableName} WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }
    async checkCreationOrderExist(sf_record_id) {
        try {
            const query = `SELECT ieco.id as creationId, ieco.sf_record_id as creationSfId, cos.id as shippingId, cos.sf_record_id as shippingSfId  
            FROM internets_eastlink_creation_order ieco
            left join creation_order_shipping cos on cos.id = ieco.shipping_id 
            where ieco.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                orderData: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check checkCreationOrderExist -> ", error);
            return {
                dataExist: 0,
                orderData: null
            };
        }
    }

    async updateDetails(Related_Shipping_Order__c, orderData) {
        try {
            const { creationId, shippingId: previousShippingId } = orderData;
            const updatedTime = getCurrentAtlanticTime();

            const shipping_id = await this.getAndInsertShippingDetails(Related_Shipping_Order__c);

            const updateQuery = `
                        UPDATE internets_eastlink_creation_order 
                        SET shipping_id = ?, updatedAt = ?
                        WHERE id = ?`;

            await this.executeQuery(updateQuery, [shipping_id, updatedTime, creationId]);

            if (previousShippingId) {
                const customerService = new CustomerService();
                const { dataCount } = await customerService.checkDataCount("internets_eastlink_creation_order", "shipping_id", previousShippingId);
                if (!dataCount) await customerService.deleteQuery("creation_order_shipping", previousShippingId);
            }
        } catch (error) {
            console.error(`Error checkAndUpdateDetails -> `, error);
        }
    }

    async getAndInsertShippingDetails(Related_Shipping_Order__c) {
        try {
            let shipping_id = null;
            if (!Related_Shipping_Order__c) return null;

            const checkQuery = `SELECT id 
                        FROM creation_order_shipping 
                        WHERE sf_record_id = ${sanitizeValue(Related_Shipping_Order__c)}`;

            const existingRecord = await this.executeQuery(checkQuery);
            if (existingRecord.length > 0) shipping_id = existingRecord[0].id;
            else {
                const { shippingData } = await salesforceConnection.getShippingDetails(Related_Shipping_Order__c);

                if (shippingData) {
                    const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingData;

                    const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
                    const createdAt = getCurrentAtlanticTime(CreatedDate);
                    const updatedTime = getCurrentAtlanticTime();

                    const query = `INSERT IGNORE INTO creation_order_shipping 
                                        (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                                        VALUES (
                                        ${sanitizeValue(sf_record_id)}, 
                                        ${sanitizeValue(sf_name)}, 
                                        ${sanitizeValue(full_mailing_address)}, 
                                        ${sanitizeValue(ship_date)}, 
                                        ${sanitizeValue(ship_drop_off_date)}, 
                                        ${sanitizeValue(tracking_url)}, 
                                        ${sanitizeValue(courier)}, 
                                        '${formatedDate}', 
                                        ${sanitizeValue(package_deliverted_at)}, 
                                        '${createdAt}', 
                                        '${updatedTime}'
                                        )`;

                    const insertStatus = await this.executeQuery(query);
                    if (insertStatus) shipping_id = insertStatus?.insertId || null
                }
            }
            return shipping_id;
        } catch (error) {
            console.error(`Error getAndInsertShippingDetails -> `, error);
        }
    }

    async checkSwapOrder(Name, orderDetail, _id, _index) {
        try {
            const { Id: sf_record_id, Stage__c, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = orderDetail;
            const { dataExist } = await this.checkExist(sf_record_id, "internets_eastlink_swap_order");

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let value = Stage__c;
            let stage = null;
            if (value) stage = value.replace(/['"`]/g, '');

            let type;
            if (dataExist) {
                type = "UPDATE";
                let queryParams = [];
                let query = `UPDATE internets_eastlink_swap_order SET 
                sf_updatedAt = ?, 
                createdAt = ?, 
                updatedAt = ?,
                sf_name = ?,
                stage = ?`;

                queryParams.push(formatedDate, createdAt, updatedTime, sf_name, stage);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);

                await this.executeQuery(query, queryParams);
            } else {
                type = "CREATE";
                const customerService = new CustomerService();
                let insertOrderDetails = { sf_record_id, sf_updatedAt: formatedDate, sf_name };
                if (stage) insertOrderDetails.stage = stage;
                await customerService.insertRecord("internets_eastlink_swap_order", insertOrderDetails, CreatedDate);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type, sf_record_type: Name });
        } catch (error) {
            console.error("SalesforceService checkSwapOrder -> ", error);
        }
    }
}

module.exports = OrderWebhookServices;
