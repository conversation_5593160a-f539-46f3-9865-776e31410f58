// rateLimiter.js
const rateLimit = require('express-rate-limit');
const { RESPONSE_CODES } = require("./../utils/ResponseCodes");

const rateLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 minutes
    max: 500, // limit each IP to 500 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req, res, next, options) => {
        const retryAfter = Math.ceil((options.windowMs - (Date.now() - req.rateLimit.resetTime)) / 1000 / 60); // Time remaining in minutes
        res.status(options.statusCode).json({
            code: RESPONSE_CODES.TOO_MANY_REQUESTS,
            message: `Too many requests, please try again after ${retryAfter} minute(s).`,
            rateLimit: {
                windowMs: options.windowMs,
                max: options.max
            }
        });
    }
});

module.exports = rateLimiter;
