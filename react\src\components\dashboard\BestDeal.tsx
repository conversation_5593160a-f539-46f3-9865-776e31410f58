import React, { useEffect, useState } from "react";
import { RightCaretIcon } from "../../assets/Icons";
import bestDealImg from "../../assets/images/BestDealTV.png";
import useMediaQuery from "../../hooks/MediaQueryHook";
import BestDealPopup from "./BestDealPopup";
import SelectAddressForBestDeal from "./SelectAddressForBestDeal";
import ImageUploadSkeleton from "./skeletons/ImageUploadSkeleton";

// Sample locations data
const locations = [
  {
    id: 2,
    full_address: "U 100 Ilsley Ave Dartmouth NS B3B 1L3",
    status: "outstanding",
  },
  {
    id: 1,
    full_address: "8th Floor-6543 DCIS-123 Zundal-876 Ahmedabad-108 NS B1A1A1",
    status: "outstanding",
  },
];

export const BestDeal: React.FC = () => {
  // State variables
  const [knowMorePopup, setKnowMorePopup] = useState<boolean>(false);
  const [showSelectAddressPopup, setShowSelectAddressPopup] =
    useState<boolean>(false);
  const [imgLoading, setImgLoading] = useState<boolean>(true);

  // Media queries
  const isLaptop = useMediaQuery("(max-width:1300px)");
  const isTablet = useMediaQuery("(max-width:1024px)");

  // Close popups on tablet view
  useEffect(() => {
    if (isTablet) {
      setKnowMorePopup(false);
      setShowSelectAddressPopup(false);
    }
  }, [isTablet]);

  // Toggle know more popup
  const handleKnowMorePopup = () => {
    setKnowMorePopup(!knowMorePopup);
  };

  // Dummy functions
  const handleAddPlan = () => {};
  const handleClaimDeal = () => {
    setKnowMorePopup(!knowMorePopup);
    setShowSelectAddressPopup(!showSelectAddressPopup);
  };

  const handleImageLoad = () => {
    setImgLoading(false);
  };
  return (
    <>
      <div className="bg-primary rounded-20 shadow-cardShadow05">
        <div className="flex flex-col 2xl:gap-[22px] gap-4 p-5 2xl:p-15">
          <div className="lg:mt-2.5 bg-primary-gradient p-1.5 rounded-lg w-max">
            <p className="text-white text-sm text-[10px] font-bold leading-none">
              Limited Time Offer
            </p>
          </div>
          <div>
            <h4 className="xl:text-xl text-lg text-[15px] text-white">
              <span className="font-bold">Get your first month Free</span> when
              you try Purple Cow TV,
            </h4>
          </div>
          <div className="2xl:h-[190px]">
            <img
              src={bestDealImg}
              alt="best-deal"
              className="w-full h-full object-contain object-center"
              loading="lazy"
            />
          </div>
          <div
            className="btn btn-white flex items-center justify-between gap-2 cursor-pointer"
            onClick={handleKnowMorePopup}
          >
            <span className="lg:text-sm text-xs">More info</span>
            <RightCaretIcon />
          </div>
        </div>
      </div>
      {knowMorePopup && (
        <BestDealPopup
          closeHandler={handleKnowMorePopup}
          width={isLaptop ? "620px" : "724px"}
        >
          <div
            className={`flex flex-col  ${isLaptop ? "gap-4" : "gap-[22px]"}`}
          >
            <div className="p-1.5 bg-primary-gradient text-white w-fit rounded-[6px]">
              <p className="text-white text-sm text-[10px] font-bold leading-none">
                Limited Time Offer
              </p>
            </div>
            <div>
              <p>Give Purple Cow TV a test on us with your first month free.</p>
            </div>
            <div className="relative w-full items-center flex justify-center lg:h-[438px]">
              {imgLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <ImageUploadSkeleton height={300} width={300} />
                </div>
              )}
              <img
                src={
                  "https://purple-cow-cp-images-dev.s3.ca-central-1.amazonaws.com/BestDealTV2.png"
                }
                alt="best-deal"
                width={isLaptop ? "360px" : "100%"}
                loading="lazy"
                className="h-full object-contain object-center"
                onLoad={handleImageLoad}
              />
            </div>
            <div>
              <p>
                Included in this freeness is the Herd Essential package which
                has 30 Channels including all your local news like ATV, CTV, &
                Global. Regular price is $20 a month.{" "}
              </p>
            </div>
            <div
              className="flex justify-between max-xl:p-2 2xl:p-5 p-3  border border-white max-xl:rounded-[6px] rounded-[12px] lg:w-[280px] items-center cursor-pointer"
              onClick={handleClaimDeal}
            >
              <div>
                <p>Claim your free month</p>
              </div>
              <div className="items-center">
                <RightCaretIcon />
              </div>
            </div>
          </div>
        </BestDealPopup>
      )}
      {showSelectAddressPopup && (
        <SelectAddressForBestDeal
          locations={locations}
          closeHandler={() =>
            setShowSelectAddressPopup(!showSelectAddressPopup)
          }
          addPlanHandler={handleAddPlan}
        />
      )}
    </>
  );
};
