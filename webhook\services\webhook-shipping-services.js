const { getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
class ShippingWebhookServices {
    async getShippingDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get shipping details -> ", error);
        }
    }

    async checkAndManageDetails(shippingDetails, _id, _index) {
        try {
            if (!Array.isArray(shippingDetails)) shippingDetails = [shippingDetails];
            for (const shippingDetail of shippingDetails) {
                const { Id: sf_record_id } = shippingDetail;
                if (!sf_record_id) return;

                const { dataExist } = await this.checkExist(sf_record_id);

                let type;
                if (dataExist) {
                    type = "UPDATE";
                    await this.updateShippingDetails(shippingDetail);
                } else {
                    type = "CREATE";
                    await this.createShippingDetails(shippingDetail);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateShippingDetails(shippingOrderDetail) {
        try {
            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate } = shippingOrderDetail;

            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];

            let query = `UPDATE creation_order_shipping SET 
                            full_mailing_address = ?, 
                            courier = ?,
                            sf_updatedAt = ?,
                            createdAt = ?,
                            updatedAt = ?,
                            tracking_url = ?,
                            ship_date = ?,
                            ship_drop_off_date = ?,
                            package_deliverted_at = ?`;

            if (full_mailing_address) queryParams.push(full_mailing_address);
            else queryParams.push(null);

            if (courier) queryParams.push(courier);
            else queryParams.push(null);

            queryParams.push(modifiedDate, createdAt, updatedTime);

            if (tracking_url) queryParams.push(tracking_url);
            else queryParams.push(null);

            if (ship_date) queryParams.push(ship_date);
            else queryParams.push(null);

            if (ship_drop_off_date) queryParams.push(ship_drop_off_date);
            else queryParams.push(null);

            if (package_deliverted_at) queryParams.push(package_deliverted_at);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await this.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService Update Shipping Details -> ", error);
        }
    }

    async createShippingDetails(shippingOrderDetail) {
        try {
            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingOrderDetail;

            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            const query = `INSERT IGNORE INTO creation_order_shipping 
                            (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                            VALUES (
                            ${sanitizeValue(sf_record_id)}, 
                            ${sanitizeValue(sf_name)}, 
                            ${sanitizeValue(full_mailing_address)}, 
                            ${sanitizeValue(ship_date)}, 
                            ${sanitizeValue(ship_drop_off_date)}, 
                            ${sanitizeValue(tracking_url)}, 
                            ${sanitizeValue(courier)}, 
                            '${modifiedDate}',
                            ${sanitizeValue(package_deliverted_at)},
                            '${createdAt}',
                            '${updatedTime}'
                            )`;

            await this.executeQuery(query);

        } catch (error) {
            console.error("SalesforceService Create New Shipping Details -> ", error);
        }
    }

    async checkExist(sf_record_id) {
        try {

            const query = `SELECT id FROM creation_order_shipping WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = ShippingWebhookServices;
