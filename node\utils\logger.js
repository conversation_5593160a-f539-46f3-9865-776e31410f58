const { createLogger, format, transports } = require('winston');
const fs = require('fs');
const DailyRotate = require('winston-daily-rotate-file');

const logDir = '/tmp';

class Logger {
	constructor() {
		// Ensure log directory exists
		if (!fs.existsSync(logDir)) {
			fs.mkdirSync(logDir);
		}

		// Define common log format
		const commonFormat = format.combine(
			format.timestamp({
				format: 'YYYY-MM-DD HH:mm:ss',
			}),
			format.json()
		);

		// Common transports for both info and error loggers
		const commonTransports = [
			new transports.Console({
				format: format.combine(
					format.json()
				),
			}),
			new DailyRotate({
				filename: `${logDir}/%DATE%-results.log`,
				datePattern: 'YYYY-MM-DD',
				maxFiles: '1d'
			}),
		];

		// Logger for info logs
		this.infoLogger = createLogger({
			format: commonFormat,
			transports: [
				...commonTransports,
				new transports.File({ level: 'info', filename: `${logDir}/info.log` }), // Add filename here
			],
			exitOnError: false,
		});

		// Logger for error logs
		this.errorLogger = createLogger({
			format: commonFormat,
			transports: [
				...commonTransports,
				new transports.File({ level: 'error', filename: `${logDir}/error.log` }), // Add filename here
			],
			exitOnError: false,
		});
	}

	// Method to log messages
	log(message, severity, data) {
		severity = severity || 'info';
		const logger = (severity === 'info') ? this.infoLogger : this.errorLogger;
		logger.log(severity, message, data);
	}
}

module.exports = Logger;
