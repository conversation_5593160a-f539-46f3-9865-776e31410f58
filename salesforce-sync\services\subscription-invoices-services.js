const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const CustomerServices = require("./customer-sync-services");
const customerServices = new CustomerServices();
const PromotionsServices = require("../services/promotion-services");
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

class SubscriptionInvoiceServices {
    async getSubscriptionInvoiceDetails(interval) {
        try {
            console.time("getSubscriptionInvoiceDetails"); // Start measuring execution time
            const { status, data } = await this.getAllSubsInvoiceDetails(interval);
            await elasticSearch.insertDocument("sync_subscription_invoice_logs", { "log.level": "INFO", total_document_fetched: data?.length });
            if (status && Array.isArray(data) && data.length) {
                await this.checkAndManageDetails(data);
            }
            console.timeEnd("getSubscriptionInvoiceDetails"); // End measuring execution time
        } catch (error) {
            console.error("Sync service get bulk Subscription Invoice Details -> ", error);
        }
    }

    async getAllSubsInvoiceDetails(interval) {
        try {
            let query = "SELECT Id, chargebeeapps__CB_Invoice_Id__c, chargebeeapps__Due_Amount__c, chargebeeapps__Amount__c, Expected_Payment_Date_Time__c, chargebeeapps__Status__c, LastModifiedDate, chargebeeapps__Subscription_CB_Id__c, chargebeeapps__Invoice_Date__c FROM chargebeeapps__CB_Invoice__c";

            if (interval === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const data = await salesforceConnection.getBulkDetails(query);

            return { status: true, data };
        } catch (error) {
            console.error("SalesforceService getAllSubsInvoiceDetails -> ", error);
            return { status: false, data: [] };
        }
    }

    async checkAndManageDetails(invDetails) {
        try {
            for (const invDetail of invDetails) {

                const { Id: sf_record_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id, chargebeeapps__Due_Amount__c: total_outstanding, chargebeeapps__Amount__c: amount, Expected_Payment_Date_Time__c: expected_payment_date, chargebeeapps__Status__c: status, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, LastModifiedDate: sf_updatedAt, chargebeeapps__Invoice_Date__c: createdDate } = invDetail;

                const subsQuery = `SELECT id, customer_details_id FROM customer_details_subscriptions WHERE cb_subscription_id = '${cb_subscription_id}' LIMIT 1`;

                const { dataExist: subExist, dataId: subDetail } = await this.checkExist(subsQuery);
                if (!subExist) continue;

                const customer_details_id = subDetail.customer_details_id;
                const customer_subscription_id = subDetail.id;

                const invoiceQuery = `
                SELECT id, status FROM subscriptions_invoices
                WHERE cb_invoice_id = '${cb_invoice_id}'
                AND customer_subscription_id = '${customer_subscription_id}'
                AND customer_details_id = '${customer_details_id}'
                LIMIT 1
              `;

                const { dataExist: invoiceExist, dataId: invoiceDetail } = await this.checkExist(invoiceQuery);

                let query;
                let queryParams = [];

                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const expectedPaymentDate = getCurrentAtlanticTime(expected_payment_date);
                const updatedTime = getCurrentAtlanticTime();

                if (invoiceExist) {

                    query = `UPDATE subscriptions_invoices SET sf_record_id = ?, sf_updatedAt = ?, updatedAt = ?`;

                    queryParams.push(sf_record_id, modifiedDate, updatedTime);
                    query += `, expected_payment_date = ?`;
                    if (expected_payment_date) queryParams.push(expectedPaymentDate);
                    else queryParams.push(null);

                    query += ` WHERE id = ?`;
                    queryParams.push(invoiceDetail.id);
                }
                await customerServices.executeQuery(query, queryParams);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async checkExist(query) {
        try {
            const res = await customerServices.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async checkAndDeleteDetails(deletedData) {
        try {
            const ids = deletedData.map(record => record.id);
            const placeholders = ids.map(() => '?').join(', ');
            const deleteQuery = `DELETE FROM subscriptions_invoices WHERE sf_record_id IN (${placeholders})`;
            await customerServices.executeQuery(deleteQuery, ids);
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
        }
    }
}

module.exports = SubscriptionInvoiceServices;
