import { useDispatch } from "react-redux";
import {
  toggleShowConfirmPopup,
  toggleShowOfferPopup,
} from "../../../store/reducers/cancelServiceReducer";
import Button from "../../common/Button";
import Popup from "../../common/Popup";

const OfferPopup = () => {
  const dispatch = useDispatch();
  const title =
    "We’d love to make Purple Cow work for you. Get 50% off your next 2 months on us!";
  const message = "Stick with us and we can make it right";
  return (
    <div>
      <Popup
        title={title}
        closeHandler={() => dispatch(toggleShowOfferPopup())}
      >
        <div className="flex flex-col gap-10">
          <p className="text-base">{message}</p>
          <div className="flex gap-5 lg:flex-row flex-col">
            <div className="basis-full">
              <Button
                title="Confirm cancellation"
                btnType="transparent"
                type="button"
                clickEvent={() => {
                  dispatch(toggleShowOfferPopup());
                  dispatch(toggleShowConfirmPopup());
                }}
                
              />
            </div>
            <div className="basis-full">
              <Button
                title="Claim offer"
                clickEvent={() => dispatch(toggleShowConfirmPopup())}
              />
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default OfferPopup;
