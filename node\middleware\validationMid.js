const { RESPONSE_CODES } = require("../utils/ResponseCodes");

/**
* Middleware function to validate request data against a schema.
* @param {Joi.Schema} schema - Joi schema object for validation.
* @param {string} [source='body'] - Source of data in the request object (default: 'body').
* @returns {Function} Express middleware function.
*/

const validator = (schema, source = 'body') => (req, res, next) => {
  const validationValue = schema.validate(req[source], {
    abortEarly: false,
    allowUnknown: true,
    convert: false,
    skipFunctions: true
  });
  if (validationValue.error) {
    const errorMessages = validationValue.error.details.map((error) => {
      return {
        path: error?.path?.[0],
        message: error.message
      };
    });
    return res.status(RESPONSE_CODES.UNPROCESSABLE_ENTITY).json({ error: errorMessages });
  }

  return next();
};

module.exports = { validator };
