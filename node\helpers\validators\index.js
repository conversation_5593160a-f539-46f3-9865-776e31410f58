const { signupValidation } = require('./auth/signup.validator')
const { cognitoCreateUserValidation } = require('./auth/cognito-create-user.validator')
const { loginValidation } = require('./auth/login.validator')
const { resetPasswordValidation } = require('./auth/reset-password.validator')
const { cardValidation } = require('./card/card.validator')
const { forgotPasswordValidation } = require('./auth/forgot-password.validator')
const { customerResetPasswordValidation } = require('./customer/reset-password.validator')
const { customerUpdateValidation } = require('./customer/customer.validator')
const { customerAddressValidation } = require('./plan/address.validator')
const { planDetails } = require('./plan/details.validator')

 //subscription validation
const { subscriptionRenewalValidation } = require('./subscription/renewal.validator')
const { internetUpdateValidation } = require('./subscription/internet.validator')
const { televisionUpdateValidation } = require('./subscription/television.validator')
const { phoneUpdateValidation } = require('./subscription/phone.validator')
const { cancelValidation } = require('./subscription/cancel.validator')

//payment validation
const { outstandingDetailValidation } = require('./payment/outstanding.validator')
// const { subscriptionRenewalValidation } = require('./payment/payment.validator')
const { paymentArrangementValidation } = require('./payment/payment-arragement.validator')

module.exports = {
    signupValidation,
    cognitoCreateUserValidation,
    loginValidation,
    resetPasswordValidation,
    cardValidation,
    forgotPasswordValidation,
    customerResetPasswordValidation,
    customerUpdateValidation,
    customerAddressValidation,
    planDetails,

    //subscription validation
    subscriptionRenewalValidation,
    internetUpdateValidation,
    televisionUpdateValidation,
    phoneUpdateValidation,
    cancelValidation,

    // payment validation
    outstandingDetailValidation,
    paymentArrangementValidation
}