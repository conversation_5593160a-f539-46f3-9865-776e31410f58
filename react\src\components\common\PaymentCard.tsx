import { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import "../../assets//scss/pages/paymentCard.scss";
import {
  CloseIcon,
  SelectedCardIcon,
  UnSelectedCardIcon,
} from "../../assets/Icons";
import { useDeleteCardMutation } from "../../services/api";
import { addNotification } from "../../store/reducers/toasterReducer";
import ConfirmationMessagePopup from "./ConfirmationMessagePopup";
import { logout } from "../../store/reducers/authenticationReducer";
import { formatMonth } from "../../utils/helper";

interface PaymentCardProps {
  closeBtn?: boolean;
  data: any;
  isPrimary?: boolean;
  isSelected?: boolean;
  handleSelectCard: (card: any) => void;
  cardId?: number;
  refetch: () => void;
  showNestedPopup?: boolean;
  setShowNestedPopup?: (show: boolean) => void;
  setCustomIndex?: (index: number) => void;
  index?: number;
}
const PaymentCard: React.FC<PaymentCardProps> = ({
  data,
  closeBtn,
  isPrimary,
  isSelected,
  handleSelectCard,
  refetch,
  showNestedPopup,
  setShowNestedPopup,
  setCustomIndex,
  index,
}) => {
  const [deleteCard, deleteCardLoading] = useDeleteCardMutation();
  const [deleteConfirmation, setDeleteConfirmation] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Function to handle the card deletion
  const handleDeleteCard = async () => {
    try {
      const response = await deleteCard(data?.id).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({
            type: "success",
            message: response?.message,
          })
        );
        refetch();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Toggle delete confirmation popup visibility
  const handleDeleteConfirmation = () => {
    if (setCustomIndex !== undefined) {
      index && setCustomIndex(index);
    }
    if (setShowNestedPopup !== undefined) {
      setShowNestedPopup(!showNestedPopup);
    } else {
      setDeleteConfirmation(!deleteConfirmation);
    }
  };

  return (
    <>
      <div
        className="payment-card group xs:min-w-[250px] min-w-[230px] max-w-[267px] h-[156px] m-1 bg-[linear-gradient(90deg,#A86CF0_0%,#8B5CF6_100%)]
 mt-[20px] p-[20px] rounded-[10px] flex flex-col justify-between select-none"
        id={`card-${data?.id}`}
        key={data?.id}
      >
        {isPrimary ? null : (
          <div
            onClick={handleDeleteConfirmation}
            className={`close-icon transition-all duration-300 ${
              !isSelected && "opacity-0 lg:group-hover:opacity-100"
            } ${
              closeBtn && "opacity-100"
            } cursor-pointer w-6 h-6 rounded-full bg-white flex items-center justify-center shadow-cardShadow25 absolute -top-3 -right-3 z-[13]`}
          >
            <CloseIcon width={15} height={15} />
          </div>
        )}
        {isPrimary && (
          <div className="p-[3px] font-medium bg-color_FFCE22 text-[12px] rounded-l-md absolute w-[57px] h-[22px] right-0 top-5 flex justify-center items-center shadow-[-4px_4px_7.8px_0_rgba(0,0,0,0.2)]">
            Primary
          </div>
        )}
        <div className="card-holder-name max-w-[70%]">
          <p className="text-white text-xl font-anton uppercase">{data?.card_name}</p>
        </div>
        <div>
          <div className="card-ending-number">
            <p className=" text-white text-base">
              MC ending in{" "}
              <span className="font-bold">{data?.card_number}</span>
            </p>
          </div>
          <div className="card-expiry-date flex justify-between">
            <div>
              <p className=" text-white text-base">
                Exp: {formatMonth(data?.expiry_month)}/
                {String(data?.expiry_year).slice(-2)}
              </p>
            </div>
            {isPrimary ? null : isSelected ? (
              <div
                className=""
                onClick={() => {
                  handleSelectCard(data);
                }}
                title="Select to unset primary"
              >
                <SelectedCardIcon />
              </div>
            ) : (
              <div
                className={`transition-all duration-300 opacity-0 lg:group-hover:opacity-100 ${
                  closeBtn ? "opacity-100" : "max-lg:pointer-events-none"
                } `}
                onClick={() => {
                  handleSelectCard(data);
                }}
                title="Select to set primary"
              >
                <UnSelectedCardIcon />
              </div>
            )}
          </div>
        </div>
      </div>
      {((setShowNestedPopup && showNestedPopup) || deleteConfirmation) && (
        <ConfirmationMessagePopup
          title={"Important: Delete Card?"}
          message={
            "Deleting this card will remove it from your account and you will no longer be able to use it for transactions. Are you sure you want to proceed?"
          }
          closeHandler={handleDeleteConfirmation}
          handleSubmit={handleDeleteCard}
          isLoading={deleteCardLoading?.isLoading}
        />
      )}
    </>
  );
};

export default PaymentCard;
