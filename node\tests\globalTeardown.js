/**
 * Jest Global Teardown
 * 
 * This file runs once after all tests complete.
 * Use it for global cleanup.
 */

module.exports = async () => {
  console.log('🧹 Global test teardown starting...');
  
  // Clean up any global resources
  // For example: close database connections, stop mock servers, etc.
  
  // Reset environment variables if needed
  delete process.env.MOCK_EXTERNAL_SERVICES;
  
  // Log test completion
  console.log('✅ Global test teardown completed');
  console.log('🎉 All tests finished successfully!');
};
