import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { customerSubscriptionReducerState } from "../../typings/typing";

const initialState = {
  subscriptionType: "",
} satisfies customerSubscriptionReducerState as customerSubscriptionReducerState;

const customerSubscriptionReducer = createSlice({
  name: "Customer_Subscription_Reducer",
  initialState,
  reducers: {
    setSubscriptionType: (state, action: PayloadAction<Date | null>) => {
      state.subscriptionType = action.payload;
    },
  },
});

export const { setSubscriptionType } = customerSubscriptionReducer.actions;

export default customerSubscriptionReducer.reducer;
