/**
 * Jest Test Results Processor
 * 
 * This file processes test results and can be used to generate custom reports.
 */

module.exports = (results) => {
  // Extract test statistics
  const {
    numTotalTests,
    numPassedTests,
    numFailedTests,
    numPendingTests,
    testResults
  } = results;

  // Calculate test coverage
  const passRate = ((numPassedTests / numTotalTests) * 100).toFixed(2);
  
  // Log test summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('========================');
  console.log(`Total Tests: ${numTotalTests}`);
  console.log(`✅ Passed: ${numPassedTests}`);
  console.log(`❌ Failed: ${numFailedTests}`);
  console.log(`⏳ Pending: ${numPendingTests}`);
  console.log(`📈 Pass Rate: ${passRate}%`);
  
  // Log route-specific results
  console.log('\n🛣️  ROUTE TEST RESULTS');
  console.log('======================');
  
  const routeResults = {};
  
  testResults.forEach(testResult => {
    const testPath = testResult.testFilePath;
    const routeName = testPath.match(/([^/]+)\.test\.js$/)?.[1] || 'unknown';
    
    if (!routeResults[routeName]) {
      routeResults[routeName] = {
        passed: 0,
        failed: 0,
        total: 0
      };
    }
    
    testResult.testResults.forEach(test => {
      routeResults[routeName].total++;
      if (test.status === 'passed') {
        routeResults[routeName].passed++;
      } else if (test.status === 'failed') {
        routeResults[routeName].failed++;
      }
    });
  });
  
  // Display route results
  Object.keys(routeResults).sort().forEach(routeName => {
    const result = routeResults[routeName];
    const routePassRate = ((result.passed / result.total) * 100).toFixed(1);
    const status = result.failed === 0 ? '✅' : '❌';
    
    console.log(`${status} ${routeName.padEnd(20)} ${result.passed}/${result.total} (${routePassRate}%)`);
  });
  
  // Log failed tests details if any
  if (numFailedTests > 0) {
    console.log('\n❌ FAILED TESTS DETAILS');
    console.log('=======================');
    
    testResults.forEach(testResult => {
      testResult.testResults.forEach(test => {
        if (test.status === 'failed') {
          console.log(`\n🔴 ${test.fullName}`);
          if (test.failureMessages && test.failureMessages.length > 0) {
            test.failureMessages.forEach(message => {
              console.log(`   ${message.split('\n')[0]}`);
            });
          }
        }
      });
    });
  }
  
  // Performance metrics
  const totalTime = testResults.reduce((sum, result) => sum + (result.perfStats?.end - result.perfStats?.start || 0), 0);
  console.log(`\n⏱️  Total Test Time: ${(totalTime / 1000).toFixed(2)}s`);
  
  // Coverage reminder
  if (results.coverageMap) {
    console.log('\n📋 Coverage report generated in ./coverage directory');
  }
  
  console.log('\n🎯 Test execution completed!');
  
  return results;
};
