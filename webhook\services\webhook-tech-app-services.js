const { getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
class TechAppWebhookServices {
    async getTechAppDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get tech appointment details -> ", error);
        }
    }

    async checkAndManageDetails(techAppDetails, _id, _index) {
        try {
            if (!Array.isArray(techAppDetails)) techAppDetails = [techAppDetails];
            for (const techAppDetail of techAppDetails) {
                const { Id: sf_record_id } = techAppDetail;
                if (!sf_record_id) return;

                const { dataExist } = await this.checkExist(sf_record_id);

                let type;
                if (dataExist) {
                    type = "UPDATE";
                    await this.updateTechDetails(techAppDetail);
                } else {
                    type = "CREATE";
                    await this.createTechDetails(techAppDetail);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateTechDetails(techAppDetail) {
        try {
            const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate } = techAppDetail;

            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];

            let query = `UPDATE internets_eastlink_tech_appointment SET 
                        sf_updatedAt = ?, 
                        createdAt = ?, 
                        updatedAt = ?,
                        install_date = ?,
                        install_time = ?`;

            queryParams.push(modifiedDate, createdAt, updatedTime);

            if (install_date) queryParams.push(install_date);
            else queryParams.push(null);

            if (install_time) queryParams.push(install_time);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await this.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService Update Tech App Details -> ", error);
        }
    }

    async createTechDetails(techAppDetail) {
        try {
            const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = techAppDetail;

            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            const query = `INSERT IGNORE INTO internets_eastlink_tech_appointment 
            (sf_record_id, sf_name, install_date, install_time, sf_updatedAt, createdAt, updatedAt)
            VALUES ('${sf_record_id}', '${sf_name}', ${sanitizeValue(install_date)}, ${sanitizeValue(install_time)}, '${modifiedDate}', '${createdAt}', '${updatedTime}')`;

            await this.executeQuery(query);

        } catch (error) {
            console.error("SalesforceService Create New Tech App Details -> ", error);
        }
    }

    async checkExist(sf_record_id) {
        try {

            const query = `SELECT id FROM internets_eastlink_tech_appointment WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = TechAppWebhookServices;
