const e = require('express');
const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const CustomerServices = require("./customer-sync-services");
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const customerServices = new CustomerServices();

const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class PromotionServices {

    async getpromotionDetails(interval) {
        const startTime = getCurrentAtlanticTime(null, "elasticSearch");
        const syncType = interval || "manual";
        const messageStart = `Promotion sync started - ${syncType}`;

        const logStartObj = {
            "log.level": "INFO",
            type: syncType,
            message: messageStart,
            startTime,
            timestamp: startTime,
            module: "promotions"
        };

        const { _id, _index } = await elasticSearch.insertDocument("sync_promotions_logs", logStartObj);

        let syncResults = {
            totalFetched: 0,
            successCount: 0,
            errorCount: 0,
            errors: []
        };

        try {
            console.time("getpromotionDetails"); // Start measuring execution time
            const { status, data } = await this.getAllPromotionDetails(interval, "promotion");

            syncResults.totalFetched = data?.length || 0;

            if (status && Array.isArray(data) && data.length) {
                const processResult = await this.checkAndManageDetails(data);
                syncResults.successCount = processResult?.successCount || data.length;
                syncResults.errorCount = processResult?.errorCount || 0;
                if (processResult?.errors) syncResults.errors = processResult.errors;
            } else if (!status) {
                syncResults.errorCount = 1;
                syncResults.errors.push("Failed to fetch promotion data from Salesforce");
            }

            console.timeEnd("getpromotionDetails"); // End measuring execution time

            const endTime = getCurrentAtlanticTime(null, "elasticSearch");
            const endMessage = `Promotion sync completed - ${syncType}`;
            const hasError = syncResults.errorCount > 0;
            const logLevel = hasError ? "ERROR" : "INFO";

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, {
                    "log.level": logLevel,
                    message: endMessage,
                    endTime,
                    response: { syncResults }
                });
            }

        } catch (error) {
            console.error("Sync service get bulk promotion details -> ", error);

            const endTime = getCurrentAtlanticTime(null, "elasticSearch");
            const errorMessage = `Promotion sync failed - ${syncType}`;

            const logErrorObj = {
                "log.level": "ERROR",
                message: errorMessage,
                endTime,
                response: { syncResults },
                error: [{ error: error.message }]
            };

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, logErrorObj);
            }
        }
    }

    async getpromotionReceiptDetails(interval) {
        const startTime = getCurrentAtlanticTime(null, "elasticSearch");
        const syncType = interval || "manual";
        const messageStart = `Promotion receipt sync started - ${syncType}`;

        const logStartObj = {
            "log.level": "INFO",
            type: syncType,
            message: messageStart,
            startTime,
            timestamp: startTime,
            module: "promotion_receipts"
        };

        const { _id, _index } = await elasticSearch.insertDocument("sync_promotion_receipts_logs", logStartObj);

        let syncResults = {
            totalFetched: 0,
            successCount: 0,
            errorCount: 0,
            errors: []
        };

        try {
            console.time("getpromotionReceiptDetails"); // Start measuring execution time
            const { status, data } = await this.getAllPromotionDetails(interval, "receipt");

            syncResults.totalFetched = data?.length || 0;

            if (status && Array.isArray(data) && data.length) {
                const processResult = await this.checkAndManageReceiptDetails(data);
                syncResults.successCount = processResult?.successCount || data.length;
                syncResults.errorCount = processResult?.errorCount || 0;
                if (processResult?.errors) syncResults.errors = processResult.errors;
            } else if (!status) {
                syncResults.errorCount = 1;
                syncResults.errors.push("Failed to fetch promotion receipt data from Salesforce");
            }

            console.timeEnd("getpromotionReceiptDetails"); // End measuring execution time

            const endTime = getCurrentAtlanticTime(null, "elasticSearch");
            const endMessage = `Promotion receipt sync completed - ${syncType}`;
            const hasError = syncResults.errorCount > 0;
            const logLevel = hasError ? "ERROR" : "INFO";

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, {
                    "log.level": logLevel,
                    message: endMessage,
                    endTime,
                    response: { syncResults }
                });
            }

        } catch (error) {
            console.error("Sync service get bulk promotion receipts details -> ", error);

            const endTime = getCurrentAtlanticTime(null, "elasticSearch");
            const errorMessage = `Promotion receipt sync failed - ${syncType}`;

            const logErrorObj = {
                "log.level": "ERROR",
                message: errorMessage,
                endTime,
                response: { syncResults },
                error: [{ error: error.message }]
            };

            if (_id && _index) {
                await elasticSearch.updateDocument(_index, _id, logErrorObj);
            }
        }
    }

    async getAllPromotionDetails(interval, type) {
        try {
            let query;
            if (type == "promotion") {
                query = "SELECT Id, Name, Name__c, Active__c, LastModifiedDate, CreatedDate FROM Promotion__c";
            } else {
                query = "SELECT Id, Promotion__c,  Name, Customer_Details__c, LastModifiedDate, CreatedDate, Redeemed_DateTime__c FROM Promotional_Offer__c";
            }

            if (interval === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const data = await salesforceConnection.getBulkDetails(query);

            return { status: true, data };
        } catch (error) {
            console.error("SalesforceService getAllPromotionDetails -> ", error);
            return { status: false, data: [] };
        }
    }

    async checkAndManageDetails(promotionDetails) {
        try {
            for (const promotionDetail of promotionDetails) {
                const { Id: sf_record_id, Name, Name__c, Active__c, LastModifiedDate, CreatedDate } = promotionDetail;

                const { dataExist, dataId } = await this.checkExist(sf_record_id, "promotions");

                let query;

                const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
                const createdAt = getCurrentAtlanticTime(CreatedDate);

                if (dataExist) {
                    query = `UPDATE promotions SET promotion_name = '${Name}', name = '${Name__c}', status = '${Active__c}', sf_updatedAt = '${formatedDate}' , updatedAt = NOW() WHERE sf_record_id = '${dataId.sf_record_id}'`;
                } else {
                    query = `INSERT IGNORE INTO promotions (sf_record_id, promotion_name, name, status, sf_updatedAt, createdAt, updatedAt) VALUES ('${sf_record_id}','${Name}','${Name__c}','${Active__c}','${formatedDate}','${createdAt}', NOW())`;
                }
                await customerServices.executeQuery(query);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async checkExist(sf_record_id, tableName) {
        try {

            let selectClause = `id, sf_record_id`;
            if (tableName === 'contacts_customer_details') selectClause += `, eligible_promotions`;
            if (tableName === 'promotions') selectClause += `, promotion_name`;

            const query = `SELECT ${selectClause} FROM ${tableName} WHERE sf_record_id = '${sf_record_id}'`;

            const res = await customerServices.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async checkAndManageReceiptDetails(promotionReceiptDetails) {
        try {
            for (const promotionReceiptDetail of promotionReceiptDetails) {
                const { Id, Promotion__c, Name, Customer_Details__c, LastModifiedDate, CreatedDate, Redeemed_DateTime__c } = promotionReceiptDetail;
                if (!Customer_Details__c || !Promotion__c) continue;

                const { dataExist: customerExist, dataId: customerDetails } = await this.checkExist(Customer_Details__c, "contacts_customer_details");

                if (!customerExist) continue;
                const customer_details_id = customerDetails.id;

                const { dataExist: promotionExist, dataId: promotionalDetails } = await this.checkExist(Promotion__c, "promotions");
                if (!promotionExist) continue;

                const promotion_id = promotionalDetails.id;

                const { dataExist, dataId } = await this.checkExist(Id, "customer_promotional_receipts");

                let query;
                let queryParams = [];

                const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
                if (dataExist) {
                    query = `UPDATE customer_promotional_receipts SET promotional_offer_name = ?, sf_updatedAt = ?, updatedAt = NOW()`;
                    queryParams.push(Name, formatedDate);
                    query += `, redeemed_date = ?`;
                    if (Redeemed_DateTime__c) queryParams.push(Redeemed_DateTime__c);
                    else queryParams.push(null);
                    
                    query += ` WHERE sf_record_id = ?`;
                    queryParams.push(dataId.sf_record_id);
                } else {
                    const createdAt = getCurrentAtlanticTime(CreatedDate);
                    query = `INSERT IGNORE INTO customer_promotional_receipts (sf_record_id, promotion_id, customer_details_id, promotional_offer_name, sf_updatedAt, createdAt, updatedAt`;
                    queryParams = [Id, promotion_id, customer_details_id, Name, formatedDate, createdAt, new Date()];
                    if (Redeemed_DateTime__c) {
                        query += `, redeemed_date`;
                        queryParams.push(Redeemed_DateTime__c);
                    }
                    query += `) VALUES (?, ?, ?, ?, ?, ?, NOW()`;
                    if (Redeemed_DateTime__c) {
                        query += `, ?`;
                    }
                    query += `)`;
                }

                await customerServices.executeQuery(query, queryParams);
                await this.insertEligiblePromotions(Redeemed_DateTime__c, customerDetails, promotionalDetails);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async insertEligiblePromotions(Redeemed_DateTime__c, customerDetails, promotionalDetails) {
        try {
            let { id, eligible_promotions } = customerDetails;
            const { promotion_name } = promotionalDetails;

            // Parse eligible_promotions from JSON string to array
            eligible_promotions = JSON.parse(eligible_promotions);

             // Check if promotion_name is already in eligible_promotions array
            const isExist = eligible_promotions.includes(promotion_name);

            // Manage promotions based on Redeemed_DateTime__c condition
            if (!Redeemed_DateTime__c) {

                 // If Redeemed_DateTime__c is falsy and promotion_name is not in eligible_promotions, add it
                if (!isExist) eligible_promotions.push(promotion_name);
            } else {
                 // If Redeemed_DateTime__c is truthy and promotion_name is in eligible_promotions, remove it
                if (isExist) eligible_promotions = eligible_promotions.filter(promo => promo !== promotion_name);
            }

            // Convert eligible_promotions array back to JSON string
            eligible_promotions = JSON.stringify(eligible_promotions);

            // Construct SQL query to update database with new eligible_promotions and updatedAt timestamp
            let query = `UPDATE contacts_customer_details SET eligible_promotions = '${eligible_promotions}', updatedAt = NOW() WHERE id = '${id}'`;

            // Execute the query using customerServices.executeQuery (assuming this is your database service)
            await customerServices.executeQuery(query);
        } catch (error) {
            console.error("SalesforceService insertEligiblePromotions -> ", error);
        }
    }
}

module.exports = PromotionServices;
