import { DownCaretIcon, MenuIcon } from "../../assets/Icons";
import { useEffect, useState } from "react";
import { useGetCustomerMutation, useLogoutMutation } from "../../services/api";
import { useLocation, useNavigate } from "react-router-dom";
import Button from "./Button";
import ContactInfoPopup from "../dashboard/ContactInfoPopup";
import ContactProfileSkeleton from "../dashboard/skeletons/ContactProfileSkeleton";
import Loader from "./Loader";
import Popup from "./Popup";
import UpdatePassword from "../../pages/account/UpdatePassword";
import { addNotification } from "../../store/reducers/toasterReducer";
import { logout } from "../../store/reducers/authenticationReducer";
import profileImg from "../../assets/images/userProfile.png";
import { setUser } from "../../store/reducers/userReducer";
import { useDispatch } from "react-redux";

interface AppHeaderProps {
  title: string;
  menuHandler: () => void;
}

export const AppHeader: React.FC<AppHeaderProps> = ({ title, menuHandler }) => {
  const [showOptions, setShowOptions] = useState<boolean>(false);
  const [showContactInfo, setShowContactInfo] = useState<boolean>(false);
  const [showSetting, setShowSetting] = useState<boolean>(false);
  const [resetPasswordPopup, setResetPasswordPopup] = useState<boolean>(false);
  const [isSticky, setIsSticky] = useState<boolean>(false);
  const [getCustomer, customerLoading] = useGetCustomerMutation();
  const [customerData, setCustomerData] = useState<any>({});
  const [getLogout, logoutLoading] = useLogoutMutation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { pathname } = useLocation();

  // Toggle contact info popup visibility
  const handleContactInfoPopup = () => {
    setShowContactInfo(!showContactInfo);
    setShowOptions(false);
  };

  // Toggle settings popup visibility
  const handleSettingPopup = () => {
    setShowSetting(!showSetting);
    setShowOptions(false);
  };

  // Toggle account options visibility
  const handleMyAccount = () => {
    setShowOptions(!showOptions);
  };

  // Fetch customer data on component mount and pathname change
  useEffect(() => {
    handleGetCustomer();
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 0) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [pathname]);

  // Handle click outside of account options to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showOptions &&
        !(event.target as HTMLElement).closest(".my-account-box")
      ) {
        setShowOptions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showOptions]);

  // Fetch customer data from the API
  const handleGetCustomer = async () => {
    try {
      const response = await getCustomer({}).unwrap();
      if (response.status === 200) {
        setCustomerData(response?.data);
        dispatch(
          setUser({
            id: response?.data?.id,
            name: response?.data?.full_name,
            email: response?.data?.email,
          })
        );
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );

      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        handleLogout();
      }
    }
  };

  // Handle password reset popup visibility
  const handleSentEmail = async () => {
    setResetPasswordPopup(!resetPasswordPopup);
    setShowSetting(false);
  };

  // Handle user sign out
  const handleSignOut = async () => {
    try {
      if (!logoutLoading?.isLoading) {
        const response = await getLogout({}).unwrap();
        if (response.status === 200) {
          handleLogout();
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
        }
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
    }
  };

  const handleLogout = () => {
    localStorage.clear();
    dispatch(logout());
    navigate("/login");
  };

  return (
    <>
      <div
        className={`pt-30 px-[20px] xl:pb-5 xl:px-5 ${
          isSticky
            ? "sticky top-0 pt-[15px] pb-5 bg-[#FEFEFE] z-[99]"
            : "2xl:p-6 p-5"
        } `}
      >
        <div
          className={`flex bg-white items-center justify-between p-2 lg:px-5 rounded-xl`}
        >
          <div
            className="lg:hidden w-10 h-10 flex items-center justify-center bg-[#EFEEEF] rounded-10 cursor-pointer"
            onClick={menuHandler}
          >
            <div
              className={`inline-block MenuButton transition-all duration-700 lg:opacity-0 lg:pt-[20px] lg:pl-[16px]`}
            >
              <MenuIcon />
            </div>
          </div>
          <div>
            <h3 className="lg:text-[26px] text-xl leading-1_35 capitalize">
              {title}
            </h3>
          </div>
          <div
            className={`my-account-box lg:p-2.5 lg:bg-white relative ${
              showOptions ? "rounded-t-2xl" : " rounded-2xl"
            }`}
          >
            <div
              className="flex items-center cursor-pointer lg:gap-2.5"
              onClick={handleMyAccount}
            >
              <div className="w-[2px] mr-3 hidden sm:block h-6 bg-[#A9A9A9]" />

              <div className="w-10 h-10 rounded-10">
                {customerLoading?.isLoading ? (
                  <ContactProfileSkeleton />
                ) : (
                  <img
                    src={customerData?.image_url || profileImg}
                    alt="profile picture"
                    width={40}
                    height={40}
                    className="w-10 h-10 object-cover object-center rounded-full p-0.5 bg-primary"
                  />
                )}
              </div>
              <div className="max-lg:hidden">
                <p className="text-base text-primary leading-none">
                  {customerData?.full_name}
                </p>
              </div>
              <div
                className={`w-[30px] flex items-center justify-center transition-all duration-300 max-lg:hidden ${
                  showOptions && "rotate-180"
                }`}
              >
                <DownCaretIcon />
              </div>
            </div>
            {showOptions && (
              <>
                <div className="account-option px-2.5 pt-15 pb-[18px] absolute lg:top-full top-[calc(100%+10px)] lg:left-0 right-0 bg-white lg:w-full lg:rounded-b-2xl rounded-2xl z-[102] flex gap-1 flex-col max-lg:w-[194px]">
                  <div className="flex items-center gap-2.5 cursor-pointer lg:hidden">
                    <div className="w-10 h-10 rounded-10">
                      {customerLoading?.isLoading ? (
                        <ContactProfileSkeleton />
                      ) : (
                        <img
                          src={customerData?.image_url || profileImg}
                          alt="profile picture"
                          width={40}
                          height={40}
                          className="w-10 h-10 object-cover object-center rounded-10 p-0.5 bg-primary"
                        />
                      )}
                    </div>
                    <p className="text-base text-primary leading-none">
                      {customerData?.full_name}
                    </p>
                  </div>
                  <div onClick={handleContactInfoPopup}>
                    <p className="text-base text-primary leading-none cursor-pointer py-1">
                      My Account
                    </p>
                  </div>
                  <div onClick={handleSettingPopup}>
                    <p className="text-base text-primary leading-none cursor-pointer py-1">
                      Settings
                    </p>
                  </div>
                  <div onClick={handleSignOut}>
                    <p className="text-base text-primary leading-none cursor-pointer py-1">
                      Sign Out
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {showContactInfo && (
        <Popup
          title="My Account"
          closeHandler={handleContactInfoPopup}
          height="1000px"
          width="650px"
        >
          <ContactInfoPopup
            data={customerData}
            refreshData={handleGetCustomer}
            closeHandler={handleContactInfoPopup}
          />
        </Popup>
      )}

      {showSetting && (
        <Popup title="My Settings" closeHandler={handleSettingPopup}>
          <p className="text-base text-color_A9A9A9">
            Need to change password?{" "}
            <span
              className="text-primary underline cursor-pointer"
              onClick={handleSentEmail}
            >
              Click here
            </span>
          </p>
          <div className="flex items-center lg:gap-5 gap-2.5 mt-10">
            <Button
              title="Cancel"
              type="button"
              clickEvent={handleSettingPopup}
              btnType="transparent"
            />
            <Button title="Done" clickEvent={handleSettingPopup} />
          </div>
        </Popup>
      )}
      {resetPasswordPopup && (
        <Popup
          title="Update Password"
          closeHandler={() => setResetPasswordPopup(!resetPasswordPopup)}
        >
          <UpdatePassword
            handleOPenForgotPasswordPopup={() => setResetPasswordPopup(!resetPasswordPopup)}
          />
        </Popup>
      )}
      {logoutLoading?.isLoading && <Loader />}
    </>
  );
};
