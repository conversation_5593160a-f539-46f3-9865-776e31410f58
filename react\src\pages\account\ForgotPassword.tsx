import React, { ChangeEvent, useState } from "react";
import Button from "../../components/common/Button";
import InputFields from "../../components/forms/InputFields";
import { LoginFormState } from "../../typings/typing";
import { addNotification } from "../../store/reducers/toasterReducer";
import { regEx } from "../../utils/helper";
import { useDispatch } from "react-redux";
import { useForgotPasswordMutation } from "../../services/api";
import { useNavigate } from "react-router-dom";

// Function to validate form data
const validateForgetForm = (data: LoginFormState) => {
  const errors: Partial<LoginFormState> = {};
  let isValid = true;

  if (!data?.email) {
    errors.email = "Email is required";
    isValid = false;
  } else if (!regEx.EMAIl.test(data?.email)) {
    errors.email = "Please enter a valid email address";
    isValid = false;
  }
  return { isValid, errors };
};

const initialState = {
  email: "",
  password: "",
};
type ForgotPasswordProps = {
  closeHandler: () => void;
};
const ForgotPassword: React.FC<ForgotPasswordProps> = ({ closeHandler }) => {
  const [formData, setFormData] = useState<LoginFormState>(initialState);
  const [formError, setFormError] = useState<Partial<LoginFormState>>({});
  const [forgotPassword, forgotPasswordLoading] = useForgotPasswordMutation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Function to handle change
  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setFormData((state) => ({ ...state, [name]: value }));
    setFormError((state) => ({ ...state, [name]: "" }));
  };

  // Function to handle forgot password action
  const handleForgotUser = async () => {
    try {
      const data = {
        email: formData?.email,
      };
      const response = await forgotPassword(data).unwrap();
      if (response.status === 200) {
        localStorage.setItem("email", response?.data?.email);
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
        // closeHandler();
        navigate("#reset-password", { state: { type: "existing" } });
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
    }
  };

  // Function to handle form submission
  const handleSubmit = (e: ChangeEvent<HTMLFormElement>): void => {
    e.preventDefault();
    const { isValid, errors } = validateForgetForm(formData);
    setFormError(errors);

    if (isValid && !forgotPasswordLoading?.isLoading) {
      setFormError({});
      handleForgotUser();
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div>
          <h3 className="text-5xl md:whitespace-nowrap md:text-7xl lg:text-8xl font-anton uppercase text-white text-center md:text-left">
            Forgot Password
          </h3>
        </div>
        <div className="flex gap-3 flex-col pt-10">
          <p className="text-xl   text-white text-center md:text-left">
            Enter your email address below to reset your password.
          </p>
          <InputFields
            placeHolder="<EMAIL>"
            changeEvent={handleChange}
            isErrorMsg={formError.email}
            attributes={{ name: "email" }}
          />
          <p className="text-lg font-normal  text-white text-left">
            We’ll send you an email with instructions on how to reset your
            password.
          </p>
          <Button
            title="Send OTP"
            isLoading={forgotPasswordLoading?.isLoading}
          />
          <Button
            title="Go back"
            type="button"
            clickEvent={closeHandler}
            btnType="transparent"
            className="!border-white !text-white"
            
          />
        </div>
      </form>
    </div>
  );
};

export default ForgotPassword;
