import React from "react";
import "../../assets/scss/pages/pagination.scss";
import { PaginationProps } from "../../typings/typing";

const Pagination: React.FC<PaginationProps> = ({
  id = 0,
  currentPage,
  totalPageCount,
  onChange,
}) => {
  // Handle edge cases (e.g., clicking on the first page when already on the first page)
  const handlePageChange = (pageNumber: number) => {
    if (
      pageNumber === currentPage ||
      pageNumber < 1 ||
      pageNumber > totalPageCount
    ) {
      return;
    }
    onChange(pageNumber, id);
  };

  // Generate page numbers
  const gap = 2;
  const pageNumbers = Array.from({ length: totalPageCount }, (_, i) => i + 1);
  return (
    <div className="flex justify-center mt-5">
      <ul className="pagination flex gap-5">
        {pageNumbers.map((pageNumber) =>
          (pageNumber <
            Math.min(
              currentPage +
                (currentPage === 1 ? 4 : currentPage === 2 ? gap + 1 : gap),
              totalPageCount
            ) &&
            pageNumber >
              Math.max(currentPage - (currentPage === 1 ? 4 : gap), 1)) ||
          pageNumber === totalPageCount ||
          pageNumber === 1 ? (
            <li
              key={pageNumber}
              className={`page-item ${
                currentPage === pageNumber
                  ? "active"
                  : "text-color_A9A9A9 hover:bg-light-grey"
              } inline-block min-h-30 min-w-30 px-2 py-1 font-normal items-center text-center  text-base rounded-[6px] duration-300`}
            >
              <button
                className="page-link"
                onClick={() => handlePageChange(pageNumber)}
              >
                {pageNumber > 9 ? pageNumber : "0" + pageNumber}
              </button>
            </li>
          ) : pageNumber ===
              currentPage +
                (currentPage === 1 ? 4 : currentPage === 2 ? gap + 1 : gap) ||
            pageNumber === currentPage - (currentPage === 1 ? 4 : gap) ? (
            <li
              key={pageNumber}
              className=" inline-block min-h-30 min-w-30 px-2 py-1 font-normal items-center text-center  text-base rounded-[6px] text-color_A9A9A9"
            >
              {"......."}
            </li>
          ) : null
        )}
      </ul>
    </div>
  );
};

export default Pagination;
