const { getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const PlanServices = require("../services/plan-services");
const CustomerService = require("./webhook-customer-services");

class TVWebhookServices {
    async getTVDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get tv details -> ", error);
        }
    }

    async checkAndManageDetails(sfServiceDetails, _id, _index) {
        try {
            if (!Array.isArray(sfServiceDetails)) sfServiceDetails = [sfServiceDetails];
            for (const sfServiceDetail of sfServiceDetails) {
                const { serviceExist, serviceId } = await this.checkServiceExist(sfServiceDetail);

                let type;
                if (serviceExist) {
                    type = "UPDATE";
                    await this.updateTVDetails(sfServiceDetail, serviceId);
                } else {
                    type = "CREATE";
                    await this.createTVDetails(sfServiceDetail, serviceId);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateTVDetails(sfServiceDetail, existingDet) {
        try {
            const { id: existingId, sf_record_id: tvId, subsType } = existingDet;
            const updatedTime = getCurrentAtlanticTime();
            const {
                Current_Base_Package__c: plan_name,
                Current_Extra_Packages__c,
                Current_Single_Channels__c,
                current_iptv_products__c,
                current_account_status__c: account_status,
                LastModifiedDate,
                Login_Details_Last_Sent__c,
                Requested_Cancellation_Date__c,
                State_Text__c,
                CreatedDate
            } = sfServiceDetail;

            if (account_status == "REMOVED" && State_Text__c == "Complete") {
                const updateQuery = `
                UPDATE contacts_customer_details 
                SET tv_id = ?, updatedAt = ?
                WHERE tv_id = ?`;

                await this.executeQuery(updateQuery, [null, updatedTime, existingId]);
                const customerService = new CustomerService();

                const { dataCount } = await customerService.checkDataCount("contacts_customer_details", "tv_id", existingId);
                if (dataCount == 1) await customerService.deleteQuery("customer_details_tvs", existingId);
                return;
            }

            if (!plan_name) return;

            const extra_packages = Current_Extra_Packages__c ? JSON.stringify(Current_Extra_Packages__c.split(";")) : "[]";
            const single_channels = Current_Single_Channels__c ? JSON.stringify(Current_Single_Channels__c.split(";")) : "[]";
            const iptv_products = current_iptv_products__c ? JSON.stringify(current_iptv_products__c.split(";")) : "[]";

            const getCostObj = {
                plan_name,
                extra_packages: Current_Extra_Packages__c ? Current_Extra_Packages__c.split(";") : [],
                single_channels: Current_Single_Channels__c ? Current_Single_Channels__c.split(";") : [],
                iptv_products: current_iptv_products__c ? current_iptv_products__c.split(";") : []
            };

            const planServices = new PlanServices();
            let total_service_cost = null;

            if (subsType && plan_name) {
                const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(getCostObj, subsType);
                total_service_cost = totalAmount;
            } else total_service_cost = 0;

            let updateQuery = "UPDATE customer_details_tvs SET ";
            let updateFields = [];
            let updateValues = [];

            const modifiedDate = getCurrentAtlanticTime(LastModifiedDate);
            const createdAt = getCurrentAtlanticTime(CreatedDate);

            if (account_status !== "REMOVED") {
                updateFields.push("plan_name = ?");
                updateValues.push(plan_name);

                updateFields.push("extra_packages = ?");
                updateValues.push(extra_packages);

                updateFields.push("single_channels = ?");
                updateValues.push(single_channels);

                updateFields.push("iptv_products = ?");
                updateValues.push(iptv_products);

                updateFields.push("total_service_cost = ?");
                updateValues.push(total_service_cost);

            }

            if (Login_Details_Last_Sent__c) {
                updateFields.push("login_details_last_sent = ?");
                updateValues.push(getCurrentAtlanticTime(Login_Details_Last_Sent__c));
            }

            if (Requested_Cancellation_Date__c) {
                updateFields.push("requested_cancellation_date = ?");
                updateValues.push(Requested_Cancellation_Date__c);
            }

            if (account_status) {
                updateFields.push("account_status = ?");
                updateValues.push(account_status);
            }

            updateFields.push("sf_updatedAt = ?");
            updateValues.push(modifiedDate);

            updateFields.push("createdAt = ?");
            updateValues.push(createdAt);

            updateFields.push("state_text = ?");
            updateValues.push(State_Text__c);

            updateFields.push("updatedAt = ?");
            updateValues.push(updatedTime);

            updateQuery += updateFields.join(", ") + " WHERE sf_record_id = ?";
            updateValues.push(tvId);

            await this.executeQuery(updateQuery, updateValues);
        } catch (error) {
            console.error("Error updateTVDetails -> ", error);
        }
    }

    async createTVDetails(serviceDetails) {
        try {
            const sfUpdatedAt = getCurrentAtlanticTime(serviceDetails.LastModifiedDate);
            const insertData = { sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, sf_name: serviceDetails.Name };
            const plan_name = serviceDetails.Current_Base_Package__c;
            if (!plan_name) return;
            if (serviceDetails.current_account_status__c == "REMOVED" && serviceDetails?.State_Text__c == "Complete") return;

            insertData.plan_name = plan_name;
            insertData.state_text = serviceDetails?.State_Text__c;
            insertData.extra_packages = serviceDetails?.Current_Extra_Packages__c ? JSON.stringify(serviceDetails.Current_Extra_Packages__c.split(";")) : "[]";
            insertData.single_channels = serviceDetails?.Current_Single_Channels__c ? JSON.stringify(serviceDetails.Current_Single_Channels__c.split(";")) : "[]";
            insertData.iptv_products = serviceDetails?.current_iptv_products__c ? JSON.stringify(serviceDetails.current_iptv_products__c.split(";")) : "[]";
            if (serviceDetails?.current_account_status__c) insertData.account_status = serviceDetails.current_account_status__c;
            if (serviceDetails?.Login_Details_Last_Sent__c) insertData.login_details_last_sent = getCurrentAtlanticTime(serviceDetails.Login_Details_Last_Sent__c);
            if (serviceDetails?.Requested_Cancellation_Date__c) insertData.requested_cancellation_date = serviceDetails.Requested_Cancellation_Date__c;

            const customerService = new CustomerService();
            await customerService.insertRecord("customer_details_tvs", insertData, serviceDetails.CreatedDate);
        } catch (error) {
            console.error("SalesforceService createTVDetails -> ", error);
        }
    }

    async checkServiceExist(sfServiceDetail) {
        try {
            const { Id: sf_record_id } = sfServiceDetail;

            const query = `SELECT cd.id, cd.sf_record_id, cds.subscription_type as subsType
                            FROM customer_details_tvs cd 
                            left join contacts_customer_details ccd on cd.id = ccd.tv_id 
                            left join customer_details_subscriptions cds on ccd.id = cds.customer_details_id 
                            WHERE cd.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);

            return {
                serviceExist: res.length > 0,
                serviceId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Service Exist -> ", error);
            return {
                serviceExist: 0,
                serviceId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = TVWebhookServices;
