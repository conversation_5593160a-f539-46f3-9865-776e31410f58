import React from "react";

type additionalTVChannelsProps = {
  selectedChannel: Array<string>;
  additionalChannels: any;
  handleChannelSelect: (item: number) => void;
};
const AdditionalTVChannels: React.FC<additionalTVChannelsProps> = ({
  selectedChannel,
  additionalChannels,
  handleChannelSelect,
}) => {
  return (
    <div
      className={`flex justify-center rounded-20 lg:p-5 p-2.5 2xl:gap-y-5 gap-y-2.5 gap-2 flex-wrap `}
    >
      {additionalChannels?.map((item: any, index: number) => (
        <div
          key={index}
          className={`select-none w-[65px] cursor-pointer h-[45px] sm:w-[85px] sm:h-[47px] lg:w-[100px] lg:h-[60px] bg-white shadow-lg scale-95 hover:scale-100  rounded-2xl border border-[#DDDDDD] flex items-center justify-center ${
            selectedChannel.includes(item?.api_name)
              ? "!border-[#FBC400] !border-[3px]"
              : ""
          }`}
          onClick={() => handleChannelSelect(item)}
        >
          <img
            src={item?.image_url}
            alt={item?.name}
            className="max-h-[60%] max-w-[80%] object-contain"
            title={item?.name}
          />
        </div>
      ))}
    </div>
  );
};

export default AdditionalTVChannels;
