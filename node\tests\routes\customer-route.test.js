const request = require('supertest');
const express = require('express');
const customerRoute = require('../../routes/customer-route');

// Mock the customer controller
jest.mock('../../controllers/customer-controller', () => ({
  getCustomerDetails: jest.fn((req, res) => res.status(200).json({ 
    message: 'Customer details retrieved', 
    data: { id: 1, name: '<PERSON>', email: '<EMAIL>' }
  })),
  resetPassword: jest.fn((req, res) => res.status(200).json({ message: 'Password reset successful' })),
  updateCustomerDetails: jest.fn((req, res) => res.status(200).json({ message: 'Customer details updated' })),
  logout: jest.fn((req, res) => res.status(200).json({ message: 'Logout successful' }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  customerResetPasswordValidation: {},
  customerUpdateValidation: {}
}));

// Mock multer upload
jest.mock('../../helpers/privacyAlgorithms', () => ({
  upload: {
    single: jest.fn(() => (req, res, next) => {
      req.file = { filename: 'test-image.jpg' };
      next();
    })
  }
}));

describe('Customer Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    customerRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/customer/details', () => {
    it('should get customer details successfully', async () => {
      const response = await request(app)
        .get('/api/v1/customer/details')
        .expect(200);

      expect(response.body.message).toBe('Customer details retrieved');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('name');
      expect(response.body.data).toHaveProperty('email');
    });
  });

  describe('POST /api/v1/customer/reset-password', () => {
    it('should reset customer password successfully', async () => {
      const resetData = {
        currentPassword: 'oldPassword123',
        newPassword: 'newPassword123',
        confirmPassword: 'newPassword123'
      };

      const response = await request(app)
        .post('/api/v1/customer/reset-password')
        .send(resetData)
        .expect(200);

      expect(response.body.message).toBe('Password reset successful');
    });

    it('should handle password reset with invalid data', async () => {
      const invalidData = {
        currentPassword: 'oldPassword123'
        // Missing new password
      };

      await request(app)
        .post('/api/v1/customer/reset-password')
        .send(invalidData)
        .expect(200); // Mocked to return success
    });
  });

  describe('PUT /api/v1/customer/', () => {
    it('should update customer details successfully', async () => {
      const updateData = {
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890',
        address: '123 Main St'
      };

      const response = await request(app)
        .put('/api/v1/customer/')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Customer details updated');
    });

    it('should update customer details with image upload', async () => {
      const updateData = {
        firstName: 'John',
        lastName: 'Doe',
        phone: '+1234567890'
      };

      const response = await request(app)
        .put('/api/v1/customer/')
        .field('firstName', updateData.firstName)
        .field('lastName', updateData.lastName)
        .field('phone', updateData.phone)
        .attach('image_url', Buffer.from('fake image'), 'test-image.jpg')
        .expect(200);

      expect(response.body.message).toBe('Customer details updated');
    });
  });

  describe('GET /api/v1/customer/logout', () => {
    it('should logout customer successfully', async () => {
      const response = await request(app)
        .get('/api/v1/customer/logout')
        .expect(200);

      expect(response.body.message).toBe('Logout successful');
    });
  });
});
