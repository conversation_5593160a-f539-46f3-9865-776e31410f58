const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("customer_details_subscription", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    customer_details_id: {
      type: Sequelize.INTEGER(11),
      allowNull: false,
      references: {
        model: 'contacts_customer_details',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'contacts_customer_details' table, with cascading delete functionality.",
      onDelete: 'CASCADE'
    },
    cb_subscription_id: {
      type: Sequelize.STRING(30),
      unique: 'cb_subscription_id',
      comment: "Chargebee subscription ID"
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    amount: {
      type: Sequelize.FLOAT(16, 2),
      comment: "Associated with chargebeeapps__Plan_Amount__c field of chargebeeapps__CB_Subscription__c in sf."
    },
    balance_due: {
      type: Sequelize.FLOAT(16, 2),
      defaultValue: 0,
      comment: "Associated with total_dues field of chargebee subscription api."
    },
    activated_on: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
      comment: "Associated with chargebeeapps__Subcription_Activated_At__c field of chargebeeapps__CB_Subscription__c in sf."
    },
    next_billing_at: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
      comment: "Associated with chargebeeapps__Next_billing__c field of chargebeeapps__CB_Subscription__c in sf."
    },
    scheduled_changes_date: {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null,
      comment: "To be decided."
    },
    status: {
      type: Sequelize.ENUM('ACTIVE', 'IN_TRAIL', '_UNKNOWN', 'FUTURE', 'NON_RENEWING', 'PAUSED', 'CANCELLED', 'TRANSFERRED'),
      defaultValue: '_UNKNOWN',
      comment: "Associated with chargebeeapps__Subscription_status__c field of chargebeeapps__CB_Subscription__c in sf."
    },
    subscription_type: {
      type: Sequelize.ENUM('monthly', 'yearly'),
      allowNull: true,
      comment: "Associated with billing_period_unit field of subscription api in chargebee."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of chargebeeapps__CB_Subscription__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}