const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const CustomerServices = require("./customer-sync-services");
const customerServices = new CustomerServices();
const PromotionsServices = require("../services/promotion-services");
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);
const ChargebeeClient = require('../clients/chargebee-client');
const { billindTypeCbSubs, getCurrentAtlanticTime } = require('../helper/custom-helper');
const PlanServices = require('./plan-services');

const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

const InvoicesSyncService = require("./../services/chargebee/invoice-sync-services");

class SubscriptionServices {
    async getSubscriptionDetails(interval) {
        try {
            console.time("getSubscriptionDetails"); // Start measuring execution time
            const { status, data } = await this.getAllSubsDetails(interval);
            await elasticSearch.insertDocument("sync_subscriptions_logs", { "log.level": "INFO", total_document_fetched: data?.length });
            if (status && Array.isArray(data) && data.length) {
                await this.checkAndManageDetails(data);
            }
            console.timeEnd("getSubscriptionDetails"); // End measuring execution time
        } catch (error) {
            console.error("Sync service get bulk Subscription Details -> ", error);
        }
    }

    async getAllSubsDetails(interval) {
        try {
            let query = "SELECT Id, chargebeeapps__CB_Subscription_Id__c, chargebeeapps__Plan_Amount__c, chargebeeapps__Subcription_Activated_At__c, chargebeeapps__Next_billing__c, chargebeeapps__Subscription_status__c, LastModifiedDate, chargebeeapps__Subscription_Created_At__c, Customer_Details__c FROM chargebeeapps__CB_Subscription__c";

            if (interval === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const data = await salesforceConnection.getBulkDetails(query);

            return { status: true, data };
        } catch (error) {
            console.error("SalesforceService getAllSubsDetails -> ", error);
            return { status: false, data: [] };
        }
    }

    async checkAndManageDetails(subsDetails) {
        try {
            for (const subsDetail of subsDetails) {
                await this.updateSubsDetails(subsDetail);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateSubsDetails(subsDetail) {
        try {
            const updatedTime = getCurrentAtlanticTime();
            const { Id: sf_record_id, chargebeeapps__CB_Subscription_Id__c: cb_subscription_id, chargebeeapps__Subcription_Activated_At__c: activated_on, chargebeeapps__Next_billing__c: next_billing_at, chargebeeapps__Subscription_status__c: status, LastModifiedDate: sf_updatedAt, chargebeeapps__Subscription_Created_At__c: createdAt, Customer_Details__c: customer_sf_id } = subsDetail;

            const subsQuery = `SELECT id, subscription_type, customer_details_id FROM customer_details_subscriptions
                                WHERE cb_subscription_id = '${cb_subscription_id}'
                                LIMIT 1
                            `;

            const { dataExist: subsExist, dataId: subscriptionData } = await this.checkExist(subsQuery);

            let query;
            let queryParams = [];

            if (subsExist) {

                const chargebeeClient = new ChargebeeClient();
                let cbSubscriptionDetail = await chargebeeClient.getSubscriptions(cb_subscription_id);

                if (cbSubscriptionDetail?.length) cbSubscriptionDetail = cbSubscriptionDetail?.[0];

                // Defensive: check if cbSubscriptionDetail.subscription exists
                if (!cbSubscriptionDetail?.subscription) {
                    console.warn(`No subscription data found for cb_subscription_id: ${cb_subscription_id}`);
                    return;
                }

                const { billing_period_unit, total_dues, plan_amount } = cbSubscriptionDetail.subscription;
                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const created_at = getCurrentAtlanticTime(createdAt);

                const balance_due = total_dues ? total_dues / 100 : 0.00;
                const amount = plan_amount ? plan_amount / 100 : 0.00;

                query = `UPDATE customer_details_subscriptions SET 
                                sf_record_id = ?, 
                                amount = ?, 
                                balance_due = ?, 
                                status = ?, 
                                sf_updatedAt = ?, 
                                createdAt = ?, 
                                updatedAt = ?,
                                activated_on = ?, 
                                next_billing_at = ?`;

                queryParams.push(sf_record_id, amount, balance_due, status, modifiedDate, created_at, updatedTime);

                if (activated_on) queryParams.push(getCurrentAtlanticTime(activated_on));
                else queryParams.push(null);

                if (next_billing_at) queryParams.push(getCurrentAtlanticTime(next_billing_at));
                else queryParams.push(null);

                let subscription_type;
                if (billing_period_unit) {
                    subscription_type = billindTypeCbSubs(billing_period_unit);
                    query += ', subscription_type = ?';
                    queryParams.push(subscription_type);
                }

                query += ` WHERE id = ?`;
                queryParams.push(subscriptionData.id);

                await customerServices.executeQuery(query, queryParams);
                const invoicesSyncService = new InvoicesSyncService();
                if (cb_subscription_id) await invoicesSyncService.getInvoicesList(cb_subscription_id);
                if (subscription_type != subscriptionData.subscription_type) {
                    // Update subscription type in tv, internet and phone
                    await this.updateSubscriptionType(subscriptionData.customer_details_id, subscription_type);
                }
            }
        } catch (error) {
            console.error("SalesforceService updateSubsDetails -> ", error);
        }
    }

    async checkExist(query) {
        try {
            const res = await customerServices.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async updateSubscriptionType(customer_details_id, subscription_type) {
        try {
            const query = `SELECT internet_id, tv_id, phone_id FROM contacts_customer_details where id = '${customer_details_id}'`;
            const res = await customerServices.executeQuery(query);
            if (res?.length) {
                const customerDetailData = res[0];
                const { internet_id, tv_id, phone_id } = customerDetailData;
                if (internet_id) this.updateInternetDetails(internet_id, subscription_type);
                if (tv_id) this.updateTvDetails(tv_id, subscription_type);
                if (phone_id) this.updatePhoneDetails(phone_id, subscription_type);
            }
        } catch (error) {
            console.error("SalesforceService updateSubscriptionType -> ", error);
        }
    }

    async updateInternetDetails(internet_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_speed FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
            let res = await customerServices.executeQuery(query);
            let internetDetails = res?.[0];

            if (internetDetails?.plan_speed) {
                const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                if (internetPlanDetails?.length) {
                    let updateObj = {};
                    const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
                    if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
                    if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                        updateObj.plan_price = getPrice.billing[0][subscription_type].price;
                    }

                    // Build the update query dynamically based on the available fields
                    let updateFields = [];
                    let queryValues = [];

                    if (updateObj.plan_name) {
                        updateFields.push('plan_name = ?');
                        queryValues.push(updateObj.plan_name);
                    }

                    if (updateObj.plan_price) {
                        updateFields.push('plan_price = ?');
                        queryValues.push(updateObj.plan_price);
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    updateFields.push('updatedAt = ?');
                    queryValues.push(updatedTime);

                    // Only execute if there are fields to update
                    if (updateFields.length > 0) {
                        // Add the id at the end of queryValues for the WHERE clause
                        queryValues.push(internetDetails.id);
                        const updateQuery = `
                                UPDATE customer_details_internets_eastlink 
                                SET ${updateFields.join(', ')} 
                                WHERE id = ?`;
                        await customerServices.executeQuery(updateQuery, queryValues);
                    }
                }
            }
        } catch (error) {
            console.error("SalesforceService updateInternetDetails -> ", error);
        }
    }

    async updateTvDetails(tv_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_name, single_channels, iptv_products, extra_packages FROM customer_details_tvs WHERE id = '${tv_id}'`;
            let res = await customerServices.executeQuery(query);
            let tvDetails = res?.[0];

            if (tvDetails) {
                const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

                if (plan_name) {

                    const payload = {
                        plan_name: plan_name,
                        extra_packages: JSON.parse(extra_packages),
                        single_channels: JSON.parse(single_channels),
                        iptv_products: JSON.parse(iptv_products)
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(payload, subscription_type);

                    // Only execute if totalAmount is defined
                    if (typeof totalAmount !== 'undefined') {
                        const updateQuery = `
                            UPDATE customer_details_tvs 
                            SET total_service_cost = ?, updatedAt = ?
                            WHERE id = ?`;
                        await customerServices.executeQuery(updateQuery, [totalAmount, updatedTime, id]);
                    }
                }
            }
        } catch (error) {
            console.error("SalesforceService updateTvDetails -> ", error);
        }
    }

    async updatePhoneDetails(phone_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, sf_record_id FROM customer_details_phones WHERE id = '${phone_id}'`;
            let res = await customerServices.executeQuery(query);
            let phoneDetails = res?.[0];

            if (phoneDetails) {
                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
                const phoneRes = await salesforceConnection.getPhoneSingleValue(phoneDetails.sf_record_id);
                const { status: phStatus, data: pfData } = phoneRes;
                if (phStatus) {
                    const _PHONE_PLAN = pfData?.Calling_Plan__c;

                    let updateObj = {};
                    const getPrice = phonePlanDetails.find(phone => phone.api_name === _PHONE_PLAN);
                    if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                        updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                    }

                    // Build the update query dynamically based on the available fields
                    let updateFields = [];
                    let queryValues = [];

                    if (updateObj.plan_price) {
                        updateFields.push('plan_price = ?');
                        queryValues.push(updateObj.plan_price);
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    updateFields.push('updatedAt = ?');
                    queryValues.push(updatedTime);

                    // Only execute if there are fields to update
                    if (updateFields.length > 0) {
                        // Add the id at the end of queryValues for the WHERE clause
                        queryValues.push(phoneDetails.id);

                        const updateQuery = `
                        UPDATE customer_details_phones 
                        SET ${updateFields.join(', ')} 
                        WHERE id = ?`;
                        await customerServices.executeQuery(updateQuery, queryValues);
                    }
                }
            }
        } catch (error) {
            console.error("SalesforceService updatePhoneDetails -> ", error);
        }
    }
}

module.exports = SubscriptionServices;
