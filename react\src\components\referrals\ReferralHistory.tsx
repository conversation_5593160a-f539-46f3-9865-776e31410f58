import React, { useState, useEffect } from "react";
import { formatCurrency, formatDate, formatUTCDate } from "../../utils/helper";
import Pagination from "../common/Pagination";
import { useReferralListMutation } from "../../services/api";
import { addNotification } from "../../store/reducers/toasterReducer";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { PaginationData, ReferralItem } from "../../typings/typing";
import ReferralHistorySkeleton from "../../pages/referrals/skeletons/ReferralHistorySkeleton";
import { logout } from "../../store/reducers/authenticationReducer";



const ReferralHistory: React.FC = ({}) => {
  const [pagination, setPagination] = useState<PaginationData>({
    limit: 5,
    page: 1,
    total: 0,
    totalPage: 0,
  });
  const [referralList, setReferralList] = useState<ReferralItem[]>([]);
  const [getReferralList, { isLoading: referralListLoading }] =
    useReferralListMutation();
  const dispatch = useDispatch();

  const navigate = useNavigate();

  // Function to handle pagination change
  const handlePaginationChange = (pageNumber: number) => {
    setPagination((prevState) => ({
      ...prevState,
      page: pageNumber,
    }));
  };

  // Function to fetch referral list
  const handleGetReferralList = async () => {
    try {
      const query = {
        limit: 5,
        page: pagination.page,
      };
      const response = await getReferralList(query).unwrap();
      if (response.status === 200) {
        setReferralList(response.data.referrals);
        setPagination((prevState) => ({
          ...prevState,
          ...response.data.pageData,
        }));
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Fetch referral list when pagination changes
  useEffect(() => {
    handleGetReferralList();
  }, [pagination.page]);

  if (referralListLoading) {
    return <ReferralHistorySkeleton />;
  }

  return (
    <div className="flex flex-col">
      <table className="referral-history-table w-full">
        <thead>
          <tr className="align-top">
            <th className="font-medium">Name</th>
            <th className="font-medium">Created on</th>
            <th className="font-medium">Credit added on</th>
            <th className="font-medium">Referral amount</th>
          </tr>
        </thead>
        <tbody>
          {referralList?.length > 0 ? (
            referralList.map((item: any) => (
              <tr key={item?.id}>
                <td>{item?.referral_name || "---"}</td>
                <td>
                  {item?.createdAt ? formatUTCDate(item?.createdAt) : "---"}
                </td>
                <td>
                  {item.credit_added_on
                    ? formatDate(item?.credit_added_on)
                    : "---"}
                </td>
                <td>
                  {formatCurrency(
                    item?.referred_amount ? item?.referred_amount : 0,
                    true
                  )}
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td className="!text-center font-semibold !pt-8" colSpan={100000}>
                No Referrals Found
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {pagination.total > 5 ? (
        <Pagination
          totalPageCount={pagination.totalPage}
          currentPage={pagination.page}
          onChange={handlePaginationChange}
        />
      ) : null}
    </div>
  );
};

export default ReferralHistory;
