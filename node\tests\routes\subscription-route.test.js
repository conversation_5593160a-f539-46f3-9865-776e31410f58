const request = require('supertest');
const express = require('express');
const subscriptionRoute = require('../../routes/subscription-route');

// Mock the subscription controller
jest.mock('../../controllers/subscription-controller', () => ({
  getRenewalEstimate: jest.fn((req, res) => res.status(200).json({ 
    message: 'Renewal estimate calculated successfully', 
    data: {
      currentAmount: 89.99,
      renewalAmount: 94.99,
      savings: 0,
      renewalDate: '2024-03-01'
    }
  })),
  renewalBillingDate: jest.fn((req, res) => res.status(200).json({ 
    message: 'Renewal billing date updated successfully',
    data: {
      newBillingDate: '2024-03-15',
      prorationAmount: 15.50
    }
  })),
  getEstimateForInternetUpdate: jest.fn((req, res) => res.status(200).json({ 
    message: 'Internet update estimate calculated successfully',
    data: {
      currentPlan: 'Internet 100',
      newPlan: 'Internet 300',
      priceDifference: 20.00,
      effectiveDate: '2024-02-01'
    }
  })),
  updateInternet: jest.fn((req, res) => res.status(200).json({ 
    message: 'Internet subscription updated successfully',
    data: {
      subscriptionId: 'sub_12345',
      newPlan: 'Internet 300',
      effectiveDate: '2024-02-01'
    }
  })),
  getEstimateForTelevisionUpdate: jest.fn((req, res) => res.status(200).json({ 
    message: 'Television update estimate calculated successfully',
    data: {
      currentPackage: 'Basic TV',
      newPackage: 'Premium TV',
      priceDifference: 30.00,
      effectiveDate: '2024-02-01'
    }
  })),
  updateTelevision: jest.fn((req, res) => res.status(200).json({ 
    message: 'Television subscription updated successfully',
    data: {
      subscriptionId: 'sub_67890',
      newPackage: 'Premium TV',
      effectiveDate: '2024-02-01'
    }
  })),
  getEstimateForupdatePhone: jest.fn((req, res) => res.status(200).json({ 
    message: 'Phone update estimate calculated successfully',
    data: {
      currentPlan: 'Basic Phone',
      newPlan: 'Unlimited Phone',
      priceDifference: 15.00,
      effectiveDate: '2024-02-01'
    }
  })),
  updatePhone: jest.fn((req, res) => res.status(200).json({ 
    message: 'Phone subscription updated successfully',
    data: {
      subscriptionId: 'sub_11111',
      newPlan: 'Unlimited Phone',
      effectiveDate: '2024-02-01'
    }
  })),
  cancelAddonsSubscription: jest.fn((req, res) => res.status(200).json({ 
    message: 'Subscription cancelled successfully',
    data: {
      subscriptionId: 'sub_22222',
      cancellationDate: '2024-02-15',
      refundAmount: 25.50
    }
  }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  subscriptionRenewalValidation: {},
  internetUpdateValidation: {},
  televisionUpdateValidation: {},
  phoneUpdateValidation: {},
  cancelValidation: {}
}));

describe('Subscription Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    subscriptionRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/v1/subscription/estimate-renewal', () => {
    it('should calculate renewal estimate successfully', async () => {
      const renewalData = {
        subscriptionId: 'sub_12345',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/subscription/estimate-renewal')
        .send(renewalData)
        .expect(200);

      expect(response.body.message).toBe('Renewal estimate calculated successfully');
      expect(response.body.data).toHaveProperty('currentAmount');
      expect(response.body.data).toHaveProperty('renewalAmount');
    });
  });

  describe('POST /api/v1/subscription/renewal', () => {
    it('should update renewal billing date successfully', async () => {
      const renewalData = {
        subscriptionId: 'sub_12345',
        newBillingDate: '2024-03-15'
      };

      const response = await request(app)
        .post('/api/v1/subscription/renewal')
        .send(renewalData)
        .expect(200);

      expect(response.body.message).toBe('Renewal billing date updated successfully');
      expect(response.body.data).toHaveProperty('newBillingDate');
    });
  });

  describe('POST /api/v1/subscription/estimate-internet-update', () => {
    it('should calculate internet update estimate successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_12345',
        newPlanId: 'internet_300',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/subscription/estimate-internet-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Internet update estimate calculated successfully');
      expect(response.body.data).toHaveProperty('priceDifference');
    });
  });

  describe('POST /api/v1/subscription/internet-update', () => {
    it('should update internet subscription successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_12345',
        newPlanId: 'internet_300',
        effectiveDate: '2024-02-01'
      };

      const response = await request(app)
        .post('/api/v1/subscription/internet-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Internet subscription updated successfully');
      expect(response.body.data).toHaveProperty('subscriptionId');
    });
  });

  describe('POST /api/v1/subscription/estimate-television-update', () => {
    it('should calculate television update estimate successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_67890',
        newPackageId: 'premium_tv',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/subscription/estimate-television-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Television update estimate calculated successfully');
      expect(response.body.data).toHaveProperty('priceDifference');
    });
  });

  describe('POST /api/v1/subscription/television-update', () => {
    it('should update television subscription successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_67890',
        newPackageId: 'premium_tv',
        effectiveDate: '2024-02-01'
      };

      const response = await request(app)
        .post('/api/v1/subscription/television-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Television subscription updated successfully');
      expect(response.body.data).toHaveProperty('subscriptionId');
    });
  });

  describe('POST /api/v1/subscription/estimate-phone-update', () => {
    it('should calculate phone update estimate successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_11111',
        newPlanId: 'unlimited_phone',
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/subscription/estimate-phone-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Phone update estimate calculated successfully');
      expect(response.body.data).toHaveProperty('priceDifference');
    });
  });

  describe('POST /api/v1/subscription/phone-update', () => {
    it('should update phone subscription successfully', async () => {
      const updateData = {
        subscriptionId: 'sub_11111',
        newPlanId: 'unlimited_phone',
        effectiveDate: '2024-02-01'
      };

      const response = await request(app)
        .post('/api/v1/subscription/phone-update')
        .send(updateData)
        .expect(200);

      expect(response.body.message).toBe('Phone subscription updated successfully');
      expect(response.body.data).toHaveProperty('subscriptionId');
    });
  });

  describe('POST /api/v1/subscription/cancel', () => {
    it('should cancel subscription successfully', async () => {
      const cancelData = {
        subscriptionId: 'sub_22222',
        cancellationDate: '2024-02-15',
        reason: 'Moving to different location'
      };

      const response = await request(app)
        .post('/api/v1/subscription/cancel')
        .send(cancelData)
        .expect(200);

      expect(response.body.message).toBe('Subscription cancelled successfully');
      expect(response.body.data).toHaveProperty('cancellationDate');
    });
  });
});
