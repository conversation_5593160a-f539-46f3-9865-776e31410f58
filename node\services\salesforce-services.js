const SalesforceClient = require('../clients/salesforce/salesforce-client');
const CONFIG = require('../config');
const { RESPONSE_CODES } = require('../utils/ResponseCodes.js');
const CustomError = require('../utils/errors/CustomError.js');
const salesforceConnection = new SalesforceClient(CONFIG.salesforce.baseurl, CONFIG.salesforce.username, CONFIG.salesforce.password, CONFIG.salesforce.token);
const { getCurrentAtlanticTime } = require("../helpers/privacyAlgorithms");

class SalesforceServices {

  // Retrieves contact details from Salesforce based on email
  async getContactDetails(email) {
    try {
      const { returnStatus, userData } = await salesforceConnection.getContactDetailsByEmail(email);
      if (userData?.length > 1) {
        const contactIds = userData?.map(cid => cid.Id)?.join(",");
        const message = {
          error_description: `The registration and sync process for the email address ${email} has been failed due to the presence of multiple contacts in Salesforce associated with this same email address. This duplication needs to be resolved for successful registration and synchronization.`,
          sf_record_id: contactIds
        }
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, message);
      }
      if (returnStatus) return userData[0];
    } catch (error) {
      console.error(`SalesforceService getContactDetails ->`, error);
      throw error;
    }
  }

  // Retrieves customer details from Salesforce based on contact ID
  async getCustomerDetails(contactId) {
    try {
      const { returnStatus, customerData } = await salesforceConnection.getCustomerDetailsByContactId(contactId);
      if (returnStatus) return customerData;
      return [];
    } catch (error) {
      console.error(`SalesforceService getContactDetails ->`, error);
      throw error;
    }
  }

  // Retrieves internet, TV, or phone details
  async getInternetTvPhoneDetails(serviceId, apiName, type) {
    try {
      let selectedObj;
      if (type == "liveCheck") {
        selectedObj = { Stage__c: 1 }
      } else {
        selectedObj = {
          Id: 1,
          Name: 1,
          LastModifiedDate: 1,
          CreatedDate: 1,
          ...(apiName === "Internet__c" && {
            CP_Speed__c: 1, Status__c: 1, Live_Date__c: 1, Disconnected_Date__c: 1, Creation_Order__c: 1, Latest_Tech_Appointment__c: 1, Latest_Disconnect_Order__c: 1, Latest_Speed_Change_Order__c: 1,
            CP_Status_Internal_Processing__c: 1, CP_Status_Ship_Package__c: 1, CP_Status_Modem_Activation__c: 1, CP_Status_Modem_Installation__c: 1, Speed__c: 1, Latest_Move_Order__c: 1, Latest_Modem_Swap_Order__c: 1
          }),
          ...(apiName === "TV__c" && {
            Current_Base_Package__c: 1,
            Current_Extra_Packages__c: 1,
            Current_Single_Channels__c: 1,
            current_iptv_products__c: 1,
            current_account_status__c: 1,
            Login_Details_Last_Sent__c: 1,
            Requested_Cancellation_Date__c: 1,
            State_Text__c: 1
          }),
          ...(apiName === "Phone__c" && {
            Account_Status__c: 1,
            Calling_Plan__c: 1,
            Latest_Cancel_Phone_Order__c: 1,
            Requested_Cancellation_Date__c: 1,
            Service_Start_Date__c: 1,
            Phone_Number_To_Port__c: 1
          })
        };
      }

      const { returnStatus, internetTvPhoneData } = await salesforceConnection.getInternetTvPhoneDetailsByServiceId(serviceId, apiName, selectedObj);
      if (returnStatus) return internetTvPhoneData;
      return false;
    } catch (error) {
      console.error(`SalesforceService getInternetTvPhoneDetails ->`, error);
      throw error;
    }
  }

  // Updates address details in Salesforce based on payload
  async updateAddressDetails(payload) {
    try {
      const {
        apiName,
        addressDetails: { sf_record_id, address_type },
        body: { street_number: Street_Number, street_name: Street_Name, suit_unit: Suite_Unit, town: City_Town, province: Province, postal_code: Postal_Code, country: Country }
      } = payload;

      const updateDetails = { Id: sf_record_id };
      const fieldMappings = { Street_Number, Street_Name, Suite_Unit, City_Town, Province, Country, Postal_Code };
      const prefix = address_type.charAt(0).toUpperCase() + address_type.slice(1);

      Object.entries(fieldMappings).forEach(([key, value]) => {
        updateDetails[`${prefix}_${key}__c`] = value;
      });
      return await salesforceConnection.updateAddressDetails(apiName, updateDetails);
    } catch (error) {
      console.error(`SalesforceService updateAddressDetails ->`, error);
      throw error;
    }
  }

  // Retrieves address details from Salesforce
  async getAddressDetails(addressId, apiName) {
    try {

      const prefix = apiName.split('_')[0];
      const selectedObj = {
        Id: 1,
        Name: 1,
        [`${prefix}_Suite_Unit__c`]: 1,
        [`${prefix}_Street_Number__c`]: 1,
        [`${prefix}_Street_Name__c`]: 1,
        [`${prefix}_City_Town__c`]: 1,
        [`${prefix}_Province__c`]: 1,
        [`${prefix}_Postal_Code__c`]: 1,
        [`Full_${prefix}_Address__c`]: 1,
        [`${prefix}_Country__c`]: 1,
        Status__c: 1,
        LastModifiedDate: 1
      };

      return await salesforceConnection.getAddressDetailsById(addressId, apiName, selectedObj);
    } catch (error) {
      console.error(`SalesforceService getAddressDetails ->`, error);
      throw error;
    }
  }

  // Retrieves all contact details from Salesforce
  async getAllContactDetails(interval) {
    try {
      let query = "SELECT Id, FirstName, LastName, Email, Company_Name__c, Primary_Phone__c, Alternate_Phone_Number__c, Contact_Name_if_different_than_end_user__c, Sticky_Sender__c, Referral_Link_Full__c, Account_Chargebee_Customer_ID__c, Total_Referrals__c, Total_Paid_Referrals__c, Name, Total_Earned_Referrals__c, CreatedDate, LastModifiedDate, CP_Cognito_ID__c FROM Contact";

      if (interval === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
      query += " ORDER BY LastModifiedDate DESC";

      const data = await salesforceConnection.getBulkContactDetails(query);

      return { status: true, data };
    } catch (error) {
      console.error(`SalesforceService getAllContactDetails -> `, error);
      throw error;
    }
  }

  // Updates profile details in Salesforce based on payload
  async updateProfileDetails(payload, type) {
    try {
      let updateDetails;

      if (type == "cognitoUpdate") {
        const { sf_record_id: Id, aws_cognito_id: CP_Cognito_ID__c, cp_created_dateTime, cp_status: CP_Status__c, first_login } = payload;
        updateDetails = { Id, CP_Cognito_ID__c };
        if (cp_created_dateTime) {
          const modifiedTime = getCurrentAtlanticTime(cp_created_dateTime, "sfUpdate");
          updateDetails.CP_Created_DateTime__c = modifiedTime;
        }
        if (CP_Status__c) updateDetails.CP_Status__c = CP_Status__c;
        if (first_login) {
          const modifiedTime = getCurrentAtlanticTime(null, "sfUpdate");
          updateDetails.First_Login_DateTime__c = modifiedTime;
        }
      } else {
        const {
          sf_record_id: Id,
          updateObj: { secondary_phone: Alternate_Phone_Number__c, additional_name: Contact_Name_if_different_than_end_user__c, company_name: Company_Name__c }
        } = payload;

        updateDetails = { Id, Alternate_Phone_Number__c, Contact_Name_if_different_than_end_user__c, Company_Name__c };
      }
      return await salesforceConnection.updateProfileDetails(updateDetails);
    } catch (error) {
      console.error(`SalesforceService updateProfileDetails ->`, error);
      throw error;
    }
  }

  // Retrieves subscription details from Salesforce
  async getSubscriptionDetails(subscriptionId) {
    try {
      const { returnStatus, subscriptionData } = await salesforceConnection.getSubscriptionDetails(subscriptionId);
      if (returnStatus) return subscriptionData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get subscription details -> `, error);
      throw error;
    }
  }

  // Retrieves invoice details from Salesforce based on subscription ID
  async getInvoiceDetails(subscriptionId) {
    try {
      const { returnStatus, invoiceData } = await salesforceConnection.getInvoiceDetails(subscriptionId);
      if (returnStatus) return invoiceData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get invoice details -> `, error);
      throw error;
    }
  }

  // Create mailing address
  async createMailingAddress(payload, cust_sf_rec_id, customerSfRecordId) {
    try {
      const { street_number, street_name, suit_unit, town, province, postal_code } = payload;
      const createObj = {
        Mailing_Suite_Unit__c: suit_unit,
        Mailing_Street_Number__c: street_number,
        Mailing_Street_Name__c: street_name,
        Mailing_City_Town__c: town,
        Mailing_Province__c: province,
        Mailing_Postal_Code__c: postal_code,
        Contact__c: cust_sf_rec_id,
        Customer_Details__c: customerSfRecordId
      }

      return await salesforceConnection.createMailingAddress(createObj);
    } catch (error) {
      console.error(`SalesforceService createMailingAddress ->`, error);
      throw error;
    }
  }

  // Updates mailing adrs id in Salesforce based on customer details
  async updateMailingAdrsinSf(addressId, customerSfRecordId) {
    try {
      const updateDetails = { Id: customerSfRecordId, Mailing_Address__c: addressId, Mailing_Address_Confirmed__c: CONFIG.salesforce.recordTypeId.mailing.confirmed }
      return await salesforceConnection.updateMailingAdrsinSf(updateDetails);
    } catch (error) {
      console.error(`SalesforceService updateMailingAdrsinSf -> `, error);
      throw error;
    }
  }

  // Create move order
  async createMoveOrderinSf(payload, internet_sf_rec_id) {
    try {
      const { street_number, street_name, suit_unit, town, province, postal_code, service_change_date } = payload;
      const createObj = {
        RecordTypeId: CONFIG.salesforce.recordTypeId.service.moveOrderId,
        Internet__c: internet_sf_rec_id,
        Requested_Install_Date__c: service_change_date,
        Requested_Install_Time__c: CONFIG.salesforce.recordTypeId.service.installTime,
        Update_Shipping_Address__c: CONFIG.salesforce.recordTypeId.service.shipppingAdrs,
        Service_Suite_Unit__c: suit_unit,
        Service_Street_Number__c: street_number,
        Service_Street_Name__c: street_name,
        Service_City_Town__c: town,
        Service_Province__c: province,
        Service_Postal_Code__c: postal_code,
        Address_Check_On_Website_And_Salesforce__c: CONFIG.salesforce.recordTypeId.service.adrsCheck
      }

      return await salesforceConnection.createOrderinSf(createObj);
    } catch (error) {
      console.error(`SalesforceService createMoveOrderinSf -> `, error);
      throw error;
    }
  }

  // Create new speed order
  async createNewSpeedOrderinSf(internet_sf_rec_id, speedName) {
    try {
      const createObj = {
        RecordTypeId: CONFIG.salesforce.recordTypeId.internet.speedChange,
        Internet__c: internet_sf_rec_id,
        Speed__c: speedName
      }

      return await salesforceConnection.createOrderinSf(createObj);
    } catch (error) {
      console.error(`SalesforceService create new speed Order in Sf ->`, error);
      throw error;
    }
  }

  // Create New Tv
  async createOrUpdateTVObject(sfCustomerId, payload, type, tvSfId) {
    try {
      const { plan_name, extra_packages, single_channels, iptv_products, submitTime } = payload;
      let createObj = {}

      if (type === "create") {
        createObj.RecordTypeId = CONFIG.salesforce.recordTypeId.tv.create;
        createObj.Customer_Details__c = sfCustomerId;
        createObj.Confirm_New_Create_TV_Order__c = true;
      }

      if (type === "update" || type === "create") {
        createObj.Input_Base_Package__c = plan_name;
        if (Array.isArray(extra_packages) && extra_packages.length) createObj.Input_Extra_Packages__c = extra_packages.join(";");
        if (Array.isArray(single_channels) && single_channels.length) createObj.Input_Single_Channels__c = single_channels.join(";");
        if (Array.isArray(iptv_products) && iptv_products.length) createObj.input_iptv_products__c = iptv_products.join(";");
      }

      if (type === "update" || type === "cancel") createObj.Id = tvSfId;
      if (type === "update") createObj.Confirm_New_Change_TV_Order__c = true;

      if (type === "cancel") {
        const dateTime = getCurrentAtlanticTime(submitTime * 1000, "sfUpdate");
        createObj.Confirm_New_Cancel_TV_Order__c = true;
        createObj.Requested_Submit_Date_Time__c = dateTime;
      }

      return await salesforceConnection.createNewTvObjectinSf(createObj, type);
    } catch (error) {
      console.error(`SalesforceService create new speed Order in Sf -> `, error);
      throw error;
    }
  }

  // Create New Tv Order
  async createTVOrderObject(payload, orderId, type) {
    try {
      const { plan_name, extra_packages, single_channels, iptv_products, submitTime } = payload;
      let createObj = { TV__c: orderId };
      if (type === 'create' || type === "update") {
        createObj.RecordTypeId = type === "create" ? CONFIG.salesforce.recordTypeId.tv.createOrder : CONFIG.salesforce.recordTypeId.tv.changeOrder;
        createObj.base_package__c = plan_name;
        const dateTime = getCurrentAtlanticTime(null, "sfUpdate");
        createObj.Submit_Date_Time__c = dateTime;
        if (Array.isArray(extra_packages) && extra_packages.length) createObj.extra_packages__c = extra_packages.join(";")
        if (Array.isArray(single_channels) && single_channels.length) createObj.single_channels__c = single_channels.join(";")
        if (Array.isArray(iptv_products) && iptv_products.length) createObj.iptv_products__c = iptv_products.join(";")
      }
      if (type === "cancel") {
        const formattedDate = getCurrentAtlanticTime(submitTime * 1000, "sfUpdate");
        createObj.RecordTypeId = CONFIG.salesforce.recordTypeId.tv.cancelOrder;
        createObj.Submit_Date_Time__c = formattedDate;
      }

      return await salesforceConnection.createNewTvOrderinSf(createObj);
    } catch (error) {
      console.error(`SalesforceService create new speed Order in Sf ->`, error);
      throw error;
    }
  }

  // Create New Phone
  async createNewPhoneOrderinSf(type, phone_number, customerSfId) {
    try {
      let createObj = {};
      createObj.RecordTypeId = type === "existing" ? CONFIG.salesforce.recordTypeId.phone.porting.recId : CONFIG.salesforce.recordTypeId.phone.newNumber.recId;
      if (type == "existing") createObj.Phone_Number_To_Port__c = phone_number;
      createObj.Customer_Details__c = customerSfId;
      createObj.Stage__c = type === "existing" ? CONFIG.salesforce.recordTypeId.phone.porting.staging : CONFIG.salesforce.recordTypeId.phone.newNumber.staging;
      return await salesforceConnection.createNewPhoneinSf(createObj);
    } catch (error) {
      console.error(`SalesforceService create new speed Order in Sf ->`, error);
      throw error;
    }
  }

  // Get referral details
  async getReferralDetails(contactSfId) {
    try {
      const { returnStatus, referralDetails } = await salesforceConnection.getReferralDetails(contactSfId);
      if (returnStatus) return referralDetails;
      return false;
    } catch (error) {
      console.error(`SalesforceService get referrel details ->`, error);
      throw error;
    }
  }

  // Retrieves shipping details from Salesforce
  async getShippingDetails(salesforceCustomerId) {
    try {
      const { returnStatus, shippingData } = await salesforceConnection.getShippingDetails(salesforceCustomerId);
      if (returnStatus) return shippingData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get shipping details ->`, error);
      throw error;
    }
  }

  async getOrderDetails(orderId, orderType) {
    try {
      let selectedObj = {
        Id: 1,
        Name: 1,
        LastModifiedDate: 1,
        CreatedDate: 1,
        ...(orderType === "InternetElCreationOrder" && { Record_Type_Name__c: 1, Related_Shipping_Order__c: 1, Install_Date__c: 1 }),
        ...(orderType === "InternetElSwapOrder" && { Stage__c: 1 }),
        ...(orderType === "InternetElDisconnectOrder" && { Requested_Disconnect_Date__c: 1 }),
        ...(orderType === "InternetElSpeedChangeOrder" && { Expected_Completion_Date__c: 1, Stage__c: 1, Response_Date__c: 1, Speed__c: 1 }),
        ...(orderType === "InternetElMoveOrder" && { Requested_Move_Date__c: 1, Requested_Install_Date__c: 1, Install_Date__c: 1, Install_Time__c: 1, Order_Reject_Reason__c: 1, Order_Reject_Reason_Solved__c: 1, Service_Suite_Unit__c: 1, Service_Street_Number__c: 1, Service_Street_Name__c: 1, Service_City_Town__c: 1, Service_Province__c: 1, Service_Postal_Code__c: 1, Stage__c: 1, Submit_Date__c: 1 })
      };
      const { returnStatus, orderData } = await salesforceConnection.getOrderDetails(orderId, selectedObj);
      if (returnStatus) return orderData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get order details ->`, error);
      throw error;
    }
  }

  // Retrieves shipping details from Salesforce
  async getTechAppDetails(techId) {
    try {
      const { returnStatus, techData } = await salesforceConnection.getTechAppDetails(techId);
      if (returnStatus) return techData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get shipping details ->`, error);
      throw error;
    }
  }

  // update expected payment date
  async updateExpectedPaymentDate(Id, Expected_Payment_Date_Time__c) {
    try {
      const updateDetails = { Id, Expected_Payment_Date_Time__c }
      return await salesforceConnection.updateExpectedPaymentDate(updateDetails);
    } catch (error) {
      console.error(`SalesforceService update expected payment date in Sf ->`, error);
      throw error;
    }
  }

  // Get Phone details
  async getPhoneApiValue(Id) {
    try {
      let query = `Select Id, ToLabel(Calling_plan__c) From Phone__c WHERE Id = '${Id}' LIMIT 1`;
      let data = await salesforceConnection.getPhoneApiValue(query);
      data = data?.records?.[0];

      return { status: true, data: data ? data : null };
    } catch (error) {
      console.error(`SalesforceService getPhoneApiValue ->`, error);
      throw error;
    }
  }

  // Create Cancel Tv Order
  async createPhoneCancelObject(sfCustomerId, submitTime) {
    try {
      const formattedDate = getCurrentAtlanticTime(submitTime * 1000, "sfUpdate");
      const createObj = {
        RecordTypeId: CONFIG.salesforce.recordTypeId.phone.cancelOrder,
        Phone__c: sfCustomerId,
        Requested_Cancellation_Date__c: formattedDate
      }
      return await salesforceConnection.createCancelPhoneOrderinSf(createObj);
    } catch (error) {
      console.error(`SalesforceService create new speed Order in Sf -> `, error);
      throw error;
    }
  }

  // update speed in order
  async updateOrderSpeedinSf(Id, Speed__c) {
    try {
      const updateDetails = { Id, Speed__c }
      return await salesforceConnection.updateOrderSpeedinSf(updateDetails);
    } catch (error) {
      console.error(`SalesforceService update order speed date in Sf ->`, error);
      throw error;
    }
  }

  // delete speed change order
  async deleteSpeedChangeOrder(Id) {
    try {
      return await salesforceConnection.deleteSpeedChangeOrder(Id);
    } catch (error) {
      console.error(`SalesforceService update order speed date in Sf ->`, error);
      throw error;
    }
  }

  // Retrieves record name
  async getRecordDetails(Id) {
    try {
      return await salesforceConnection.getRecordDetails(Id);
    } catch (error) {
      console.error(`SalesforceService getContactDetails ->`, error);
      throw error;
    }
  }

  // Retrieves subscription details from Salesforce using cb subs id
  async getSubscriptionDetailsUsingCbSubId(subscriptionId) {
    try {
      const { returnStatus, subscriptionData } = await salesforceConnection.getSubscriptionDetailsUsingCbSubId(subscriptionId);
      if (returnStatus) return subscriptionData;
      return false;
    } catch (error) {
      console.error(`SalesforceService get subscription details using cb subd id-> `, error);
    }
  }

  // Get Phone cancellation date 
  async getPhoneOrderCancelDate(phoneOrderId) {
    try {
      return await salesforceConnection.getAddressDetailsById(phoneOrderId, "Phone_Order__c", { Requested_Cancellation_Date__c: 1 });
    } catch (error) {
      console.error(`SalesforceService getPhoneApiValue ->`, error);
      throw error;
    }
  }

  // Create move order
  async updateMoveOrderinSf(payload, Id, isAddressMatch) {
    try {
      const { street_number, street_name, suit_unit, town, province, postal_code, service_change_date } = payload;
      const updateObj = {
        Id,
        Requested_Install_Date__c: service_change_date,
        Service_Suite_Unit__c: suit_unit,
        Service_Street_Number__c: street_number,
        Service_Street_Name__c: street_name,
        Service_City_Town__c: town,
        Service_Province__c: province,
        Service_Postal_Code__c: postal_code
      }
      if (!isAddressMatch) updateObj.Reviewed_At__c = null;
      return await salesforceConnection.updateOrderSpeedinSf(updateObj);
    } catch (error) {
      console.error(`SalesforceService createMoveOrderinSf -> `, error);
      throw error;
    }
  }
}

module.exports = SalesforceServices;
