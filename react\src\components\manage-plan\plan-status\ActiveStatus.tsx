import { WifiIcon } from "../../../assets/Icons";
import StatusCard from "../StatusCard";
import MoveOrderStatus from "./MoveOrderStatus";

interface ActiveStatusProps {
  isMoveOrder: any;
}

const ActiveStatus: React.FC<ActiveStatusProps> = ({ isMoveOrder }) => {
  return (
    <div>
      <StatusCard
        Icon={<WifiIcon />}
        label="Subscription"
        status="Active"
        isMoveOrder={isMoveOrder}
      />

      {isMoveOrder && <MoveOrderStatus isMoveOrder={isMoveOrder} />}
    </div>
  );
};

export default ActiveStatus;
