name: 🚀 Deploy to AWS Elastic Beanstalk

on:
  push:
    branches:
      - main
    paths:
      - 'react/**'
      - 'node/**'

env:
  AWS_REGION: ca-central-1
  DEPLOY_PACKAGE_NAME: "purplecow-prod-deploy-${{ github.sha }}.zip"
  APP_NAME: customer-portal-prod
  APP_ENV_NAME: Customer-portal-prod-env

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v3

    # Build React app
    - name: 🏗️ Build React app
      run: |
        cd react
        npm install
        npm run build

    # Move back to root directory and zip the entire node folder
    - name: 🗂️ Zip node folder including React build
      run: |
        cd node
        zip -r ../${{ env.DEPLOY_PACKAGE_NAME }} . -x '*.git*'

    # Configure AWS credentials for Elastic Beanstalk
    - name: 🔐 Configure AWS credentials for Elastic Beanstalk
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    # Upload React build to S3
    - name: ☁️ Upload React build to S3
      run: aws s3 cp ${{ env.DEPLOY_PACKAGE_NAME }} s3://${{ secrets.S3_BUCKET_NAME }}/

    # Verify React build exists in S3
    - name: 🔍 Verify React build exists in S3
      run: aws s3 ls s3://${{ secrets.S3_BUCKET_NAME }}/${{ env.DEPLOY_PACKAGE_NAME }}

    # Deploy to Elastic Beanstalk
    - name: 🚀 Deploy to Elastic Beanstalk
      run: |
        aws elasticbeanstalk create-application-version \
          --application-name ${{ env.APP_NAME }} \
          --version-label ${{ github.sha }} \
          --source-bundle S3Bucket="${{ secrets.S3_BUCKET_NAME }}",S3Key="${{ env.DEPLOY_PACKAGE_NAME }}"

        aws elasticbeanstalk update-environment \
          --application-name ${{ env.APP_NAME }} \
          --environment-name ${{ env.APP_ENV_NAME }} \
          --version-label ${{ github.sha }}

    - name: 🎉 Print success message
      run: echo "React deployment to AWS Elastic Beanstalk completed successfully"