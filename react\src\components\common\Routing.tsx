import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { Route, Routes } from "react-router-dom";
import Login from "../../pages/account/Login.tsx";
import { login, logout } from "../../store/reducers/authenticationReducer.tsx";
import { ProtectedRoutes } from "./ProtectedRoutes.tsx";

const Routing: React.FC = () => {
  const dispatch = useDispatch();

  // Get the access token from local storage
  const accessToken = localStorage.getItem("access_token");

  useEffect(() => {
    // If an access token is present, dispatch the login action
    if (accessToken) {
      dispatch(login());
    } else {
      // If no access token, dispatch the logout action
      dispatch(logout());
    }
  }, [accessToken]);

  return (
    <>
      <Routes>
        <Route path="login" element={<Login />} />
        <Route path="/*" element={<ProtectedRoutes />} />
      </Routes>
    </>
  );
};

export default Routing;
