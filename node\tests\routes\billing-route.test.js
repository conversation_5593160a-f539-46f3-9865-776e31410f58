const request = require('supertest');
const express = require('express');
const billingRoute = require('../../routes/billing-route');

// Mock the billing controller
jest.mock('../../controllers/billing-controller', () => ({
  fetchLocation: jest.fn((req, res) => res.status(200).json({ 
    message: 'Location fetched successfully', 
    data: { 
      locations: [
        { id: 1, name: 'Halifax', province: 'NS' },
        { id: 2, name: 'Sydney', province: 'NS' }
      ]
    }
  })),
  fetchStatements: jest.fn((req, res) => res.status(200).json({ 
    message: 'Statements fetched successfully', 
    data: {
      statements: [
        { id: 1, date: '2024-01-01', amount: 89.99, status: 'paid' },
        { id: 2, date: '2024-02-01', amount: 89.99, status: 'pending' }
      ]
    }
  })),
  downloadInvoice: jest.fn((req, res) => {
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=invoice.pdf');
    res.status(200).send(Buffer.from('fake pdf content'));
  })
}));

describe('Billing Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    billingRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/billing/location', () => {
    it('should fetch locations successfully', async () => {
      const response = await request(app)
        .get('/api/v1/billing/location')
        .expect(200);

      expect(response.body.message).toBe('Location fetched successfully');
      expect(response.body.data).toHaveProperty('locations');
      expect(Array.isArray(response.body.data.locations)).toBe(true);
      expect(response.body.data.locations.length).toBeGreaterThan(0);
    });
  });

  describe('GET /api/v1/billing/statements/:custDetailsId', () => {
    it('should fetch statements for valid customer ID', async () => {
      const custDetailsId = '12345';

      const response = await request(app)
        .get(`/api/v1/billing/statements/${custDetailsId}`)
        .expect(200);

      expect(response.body.message).toBe('Statements fetched successfully');
      expect(response.body.data).toHaveProperty('statements');
      expect(Array.isArray(response.body.data.statements)).toBe(true);
    });

    it('should handle statements request with different customer ID', async () => {
      const custDetailsId = '67890';

      const response = await request(app)
        .get(`/api/v1/billing/statements/${custDetailsId}`)
        .expect(200);

      expect(response.body.message).toBe('Statements fetched successfully');
    });

    it('should handle statements request with invalid customer ID format', async () => {
      const custDetailsId = 'invalid-id';

      await request(app)
        .get(`/api/v1/billing/statements/${custDetailsId}`)
        .expect(200); // Mocked to return success
    });
  });

  describe('GET /api/v1/billing/invoice/:invoiceId', () => {
    it('should download invoice successfully', async () => {
      const invoiceId = 'INV-12345';

      const response = await request(app)
        .get(`/api/v1/billing/invoice/${invoiceId}`)
        .expect(200);

      expect(response.headers['content-type']).toBe('application/pdf');
      expect(response.headers['content-disposition']).toBe('attachment; filename=invoice.pdf');
    });

    it('should handle invoice download with different invoice ID', async () => {
      const invoiceId = 'INV-67890';

      const response = await request(app)
        .get(`/api/v1/billing/invoice/${invoiceId}`)
        .expect(200);

      expect(response.headers['content-type']).toBe('application/pdf');
    });

    it('should handle invoice download with invalid invoice ID', async () => {
      const invoiceId = 'invalid-invoice-id';

      await request(app)
        .get(`/api/v1/billing/invoice/${invoiceId}`)
        .expect(200); // Mocked to return success
    });
  });
});
