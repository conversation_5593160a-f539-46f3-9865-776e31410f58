const moment = require('moment');
const pool = require('../../db');
const ChargebeeClient = require('../../clients/chargebee-client');
const chargebeeClient = new ChargebeeClient();
const { getCurrentAtlanticTime } = require("./../../helper/custom-helper");

class InvoicesSyncService {
  async getInvoicesList(subscriptionId) {
    try {
      let hasMore = true;
      let offset = null;

      while (hasMore) {
        let invoicesData;

        if (!subscriptionId) invoicesData = await chargebeeClient.getInvoicesList(offset);
        else invoicesData = await chargebeeClient.getSubInvoicesList(subscriptionId, offset);

        if (invoicesData?.list) {
          for (const invoiceData of invoicesData.list) {
            const invoice = invoiceData.invoice;
            const credit_issue = invoice?.issued_credit_notes.reduce((sum, note) => {
              if (note?.cn_total) return sum + note.cn_total;
              return sum;
            }, 0);

            const subscriptionId = invoice?.subscription_id;
            const customerSubscriptionQuery = `SELECT * FROM customer_details_subscriptions WHERE cb_subscription_id = ? LIMIT 1`;
            const customerSubscriptionResults = await this.executeQuery(customerSubscriptionQuery, [subscriptionId]);

            if (customerSubscriptionResults.length > 0) {

              const customerSubscriptionData = customerSubscriptionResults[0];
              const invData = {
                customer_details_id: customerSubscriptionData.customer_details_id,
                customer_subscription_id: customerSubscriptionData.id,
                cb_subscription_id: invoice?.subscription_id,
                cb_invoice_id: invoice?.id,
                amount: invoice?.total ? invoice.total / 100 : 0,
                credit_issue: credit_issue ? credit_issue / 100 : 0,
                amount_adjusted: invoice?.amount_adjusted ? invoice.amount_adjusted / 100 : 0,
                expected_payment_date: invoice?.expected_payment_date ? moment.unix(invoice.expected_payment_date).format('YYYY-MM-DD HH:mm:ss') : null,
                status: invoice?.status,
                total_outstanding: invoice?.amount_due ? invoice.amount_due / 100 : 0,
                createdAt: invoice?.date ? moment.unix(invoice.date).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss'),
                // updatedAt: invoice?.updated_at ? moment.unix(invoice.updated_at).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss'),
                updatedAt: getCurrentAtlanticTime()
              };

              const invoiceQuery = `
                SELECT id, status FROM subscriptions_invoices
                WHERE cb_invoice_id = ? 
                AND customer_subscription_id = ? 
                AND customer_details_id = ?
                LIMIT 1
              `;
              const invoiceDetails = await this.executeQuery(invoiceQuery, [
                invData.cb_invoice_id,
                invData.customer_subscription_id,
                invData.customer_details_id,
              ]);

              if (!invoiceDetails?.length) {
                const insertInvoiceQuery = `
                  INSERT IGNORE INTO subscriptions_invoices 
                  (customer_details_id, customer_subscription_id, cb_subscription_id, cb_invoice_id, amount, credit_issue, amount_adjusted, expected_payment_date, status, total_outstanding, createdAt, updatedAt)
                  VALUES 
                  (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;
                await this.executeQuery(insertInvoiceQuery, [
                  invData.customer_details_id,
                  invData.customer_subscription_id,
                  invData.cb_subscription_id,
                  invData.cb_invoice_id,
                  invData.amount,
                  invData.credit_issue,
                  invData.amount_adjusted,
                  invData.expected_payment_date,
                  invData.status,
                  invData.total_outstanding,
                  invData.createdAt,
                  invData.updatedAt,
                ]);
              } else {
                const invoiceId = invoiceDetails?.[0]?.id;
                let updateSts = invData.status.toLowerCase();
                const updateInvoiceQuery = `
                  UPDATE subscriptions_invoices 
                  SET amount = ?, status = ?, total_outstanding = ?, createdAt = ?, updatedAt = ?, credit_issue = ?, amount_adjusted = ?
                  WHERE id = ?`;

                await this.executeQuery(updateInvoiceQuery, [
                  invData.amount,
                  updateSts,
                  invData.total_outstanding,
                  invData.createdAt,
                  invData.updatedAt,
                  invData.credit_issue,
                  invData.amount_adjusted,
                  invoiceId
                ]);
              }
            }
          }
        }

        offset = invoicesData?.next_offset;
        hasMore = Boolean(offset);
      }

      return { status: true };
    } catch (error) {
      console.error(`ChargebeeServices getInvoicesList ->`, error);
      return { status: false };
    }
  }

  async executeQuery(query, values) {
    return new Promise((resolve, reject) => {
      pool.query(query, values, (err, results) => {
        if (err) {
          return reject(err);
        }
        resolve(results);
      });
    });
  }
}

module.exports = InvoicesSyncService;
