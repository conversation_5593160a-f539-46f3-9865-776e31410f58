const cron = require('node-cron')
const timezone = "America/Halifax";
const CONFIG = require('../config');

const scheduled = CONFIG.NODE_ENV == "local" ? false : true;

// 100 TEST
const ContactTestService = require("../services/process-services/contact-sync-services");
const contactTestService = new ContactTestService();

module.exports = () => {

  // Cron for inventory sync from unicommerce and send the inventory to shopify 
  // Run at every day
  cron.schedule("0 0 * * *", async function () {
    try {
      contactTestService.getContactDetails();
    } catch (error) {
      console.error("Error in syncing contact details");
    }
  }, {
    scheduled,
    timezone
  })
}