import React from "react";
import ContentLoader from "react-content-loader";

const PaymentMethodSkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      height={"356px"}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {/* //left bar */}
      <rect x="8" y="8" rx="10" ry="10" width="8" height="296" />
      {/* bottom bar*/}
      <rect x="8" y="296px" rx="10" ry="10" width="100%" height="8" />
      {/* top bar*/}
      <rect x="8" y="8" rx="10" ry="10" width="100%" height="7" />
      {/* right bar*/}
      <rect x="99.50%" y="9" rx="10" ry="10" width="8" height="296" />

      {/* first Card  y -> 156 + 60 (top) => 256 */}
      {/* //left bar */}
      <rect x="58" y="48" rx="10" ry="10" width="8" height="156" />
      {/* bottom bar*/}
      <rect x="58" y="196px" rx="10" ry="10" width="267" height="8" />
      {/* top bar*/}
      <rect x="58" y="48" rx="10" ry="10" width="267" height="7" />
      {/* right bar*/}
      <rect x="317" y="48" rx="10" ry="10" width="8" height="156" />

      {/* first card content */}
      {/* address */}
      <rect x="78" y="68" rx="5" ry="3" width="200" height="26" />
      {/* address */}
      <rect x="78" y="128" rx="5" ry="3" width="200" height="26" />
      <rect x="78" y="160" rx="5" ry="3" width="200" height="26" />

      {/* second card x -> 38 + 20 + 256 y-> 156 + 60 (top) => 256/}
      {/* //left bar */}
      <rect x="339" y="48" rx="10" ry="10" width="8" height="156" />
      {/* bottom bar*/}
      <rect x="339" y="196px" rx="10" ry="10" width="267" height="8" />
      {/* top bar*/}
      <rect x="339" y="48" rx="10" ry="10" width="267" height="7" />
      {/* right bar*/}
      <rect x="598" y="48" rx="10" ry="10" width="8" height="156" />
      {/* second card content */}
      {/* address */}
      <rect x="359" y="68" rx="5" ry="3" width="200" height="26" />
      {/* address */}
      <rect x="359" y="128" rx="5" ry="3" width="200" height="26" />
      <rect x="359" y="160" rx="5" ry="3" width="200" height="26" />

      {/* third Card  x-> 319+ 20 + 256 = 595*/}
      {/* //left bar */}
      <rect x="615" y="48" rx="10" ry="10" width="8" height="156" />
      {/* bottom bar*/}
      <rect x="615" y="196" rx="10" ry="10" width="267" height="8" />
      {/* top bar*/}
      <rect x="615" y="48" rx="10" ry="10" width="267" height="7" />
      {/* right bar*/}
      <rect x="874" y="48" rx="10" ry="10" width="8" height="156" />
      {/* third card content */}
      {/* address */}
      <rect x="635" y="68" rx="5" ry="3" width="200" height="26" />
      {/* address */}
      <rect x="635" y="128" rx="5" ry="3" width="200" height="26" />
      <rect x="635" y="160" rx="5" ry="3" width="200" height="26" />

      {/* Add address button */}
      <rect x="58" y="236" rx="5" ry="3" width="816" height="40" />
    </ContentLoader>
  );
};

export default PaymentMethodSkeleton;
