@use "../mixin.scss";
@use "../variables.scss" as *;

.view-statement {
  position: fixed;
  top: calc(100% - 110px);
  left: 0;
  width: 100%;
  height: 100%;
  transition: 0.5s ease-in-out;
  z-index: 5;

  &.active {
    top: 15%;
    z-index: 102;
  }
}
// Statement Table
.statement-table {
  width: 100%;

  td,
  th {
    // padding: 20px 12px;
    padding: 10px;
    text-align: left;
    font-weight: 500;

    &:last-child {
      width: 0px;
    }

    @include mixin.mq("phone-and-tablet") {
      padding: 6px;
    }
    @include mixin.mq("lap-and-up") {
      padding: 20px 12px;
    }
    @include mixin.mq("lap-to-small-desk") {
      padding: 12px 6px;
    }

    @include mixin.mq("mini-phone") {
      font-size: 14px;
    }
  }
  td {
    font-weight: 700;
    width: 31%;

    &:last-child {
      width: 7%;
    }

    @include mixin.mq("phone") {
      width: auto !important;
    }
  }
}
