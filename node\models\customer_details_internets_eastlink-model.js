const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("customer_details_internets_eastlink", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    creation_order: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_creation_order',
        key: 'id'
      },
      comment: "Foreign mapping key to Id in the 'internets_eastlink_creation_order' table.",
      // onDelete: 'CASCADE'
    },
    tech_appointment: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_tech_appointment',
        key: 'id'
      },
      comment: 'This is a foreign mapping field to the "Id" in the table "internets_eastlink_tech_appointments".',
      // onDelete: 'CASCADE'
    },
    disconnect_order: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_disconnect_order',
        key: 'id'
      },
      comment: 'This is a foreign mapping to an Id in the table "internets_eastlink_disconnect_order".',
      // onDelete: 'CASCADE'
    },
    swap_order: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_swap_order',
        key: 'id'
      },
      comment: 'This is a foreign mapping to an Id in the table "internets_eastlink_swap_order".',
    },
    speed_change_order: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_speed_change_order',
        key: 'id'
      },
      comment: 'This is a foreign mapping to an Id in the table "internets_eastlink_speed_change_order".	',
      // onDelete: 'CASCADE'
    },
    move_order: {
      type: Sequelize.INTEGER(11),
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'internets_eastlink_move_order',
        key: 'id'
      },
      comment: "Foreign mapping key to Id in the 'internet_eastlink_move_order' table.",
      // onDelete: 'CASCADE'
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    plan_name: {
      type: Sequelize.STRING(100),
      comment: "Associated with api_name field of json object in s3."
    },
    plan_price: {
      type: Sequelize.STRING(20),
      comment: "Associated with price field of json object in s3."
    },
    plan_speed: {
      type: Sequelize.STRING(20),
      comment: "Associated with CP_Speed__c field of salesforce Internet__c object."
    },
    add_ons: {
      type: Sequelize.STRING(250),
      allowNull: true,
      comment: "Not specified."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of Internet__c in sf."
    },
    sf_response_status: {
      type: Sequelize.ENUM('pending', 'success', 'failure'),
      defaultValue: 'pending',
      comment: "Initially the value was pending"
    },
    retries: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
      comment: "How many times getting error during update in salesforce,"
    },
    sf_error_log: {
      type: Sequelize.TEXT,
      comment: "Reason for getting error from salesforce."
    },
    live_date: {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: 'Associated with Live_Date__c field of Internet__c in sf.'
    },
    internal_processing_status: {
      type: Sequelize.ENUM('Complete', 'In Progress', 'Hidden', 'Pending', 'Error'),
      defaultValue: 'Pending',
      comment: "Associated with CP_Status_Internal_Processing__c field of Internet__c in sf."
    },
    ship_package_status: {
      type: Sequelize.ENUM('Complete', 'In Progress', 'Hidden', 'Pending', 'Error'),
      defaultValue: 'Pending',
      comment: "Associated with CP_Status_Ship_Package__c field of Internet__c in sf."
    },
    modem_installation_status: {
      type: Sequelize.ENUM('Complete', 'In Progress', 'Hidden', 'Pending', 'Error'),
      defaultValue: 'Pending',
      comment: "Associated with CP_Status_Modem_Installation__c field of Internet__c in sf."
    },
    modem_activation_status: {
      type: Sequelize.ENUM('Complete', 'In Progress', 'Hidden', 'Pending', 'Error'),
      defaultValue: 'Pending',
      comment: "Associated with CP_Status_Modem_Activation__c field of Internet__c in sf."
    },
    disconnected_date: {
      type: Sequelize.DATEONLY,
      comment: 'Associated with Disconnected_Date__c field of Internet__c in sf.'
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'customer_details_internets_eastlink', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}