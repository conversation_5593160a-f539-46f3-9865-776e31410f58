import React from "react";
import { useSelector } from "react-redux";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";
import { formatCurrency } from "../../../utils/helper";
import Button from "../../common/Button";
type AdditionalTVPackageProps = {
  selectedPlan: Array<string>;
  packageId: string;
  additionalPackage: any;
  handlePackageSelect: any;
};
const AdditionalTVPackage: React.FC<AdditionalTVPackageProps> = ({
  selectedPlan,
  additionalPackage,
  handlePackageSelect,
}) => {
  const subscriptionType = useSelector(customerSubscriptionType);
  return (
    <div
      className={`w-[250px] xl:w-[280px] border-2 ${
        selectedPlan.includes(additionalPackage?.api_name) ? "" : ""
      } border-[3px] border-[#F5EFFF] p-3 rounded-20  cursor-pointer duration-300`}
    >
      <div className={`flex gap-3 rounded-10 flex-col justify-between h-full`}>
        <h1 className="font-anton uppercase text-2xl mt-3 lg:text-[26px] leading-tight">
          {
            additionalPackage?.billing_period?.[0]?.[subscriptionType]
              ?.display_name
          }
        </h1>

        {/* Price */}
        <h1 className="font-anton uppercase text-2xl lg:text-[26px] flex items-center">
          {formatCurrency(
            additionalPackage?.billing_period?.[0]?.[subscriptionType]?.price
          )}
          /{subscriptionType == "monthly" ? "month" : "year"}
        </h1>

        {/* featured channels  images */}
        <div className="grid grid-cols-3 gap-2">
          {additionalPackage?.included_channels?.map(
            (channel: any, index: number) => (
              <div key={index}>
                <img
                  className="w-[50px] h-[50px] object-contain"
                  src={channel?.image_url}
                  alt={channel?.name}
                />
              </div>
            )
          )}
        </div>

        <Button
          clickEvent={handlePackageSelect}
          className={`!rounded-30 !transition-all !duration-150 ${
            selectedPlan.includes(additionalPackage?.api_name)
              ? "!font-bold"
              : ""
          }`}
          title={
            selectedPlan.includes(additionalPackage?.api_name) ? "Added" : "Add"
          }
        />
      </div>
    </div>
  );
};

export default AdditionalTVPackage;
