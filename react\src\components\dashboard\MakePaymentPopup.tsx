import React, { MouseEvent, useEffect, useState } from "react";

import AddCardDetails from "./AddCardDetails";
import CardDetail from "./CardDetail";
import Popup from "../common/Popup";
import { addNotification } from "../../store/reducers/toasterReducer";
import { useDispatch } from "react-redux";
import { useGetCardsMutation } from "../../services/api";
import { logout } from "../../store/reducers/authenticationReducer";
import { useNavigate } from "react-router-dom";

interface MakePaymentPopUpProps {
  outstandingData: object;
  closeHandler?: (event: MouseEvent<HTMLSpanElement>) => void;
  refetch?: () => void;
  showNestedPopup: boolean;
  setShowNestedPopup: (show: boolean) => void;
  setCustomIndex: (index: number) => void;
  setCardCustomData: (data: any) => void;
  isMakePayment?: boolean | number;
}

export const MakePaymentPopUp: React.FC<MakePaymentPopUpProps> = ({
  isMakePayment,
  outstandingData,
  closeHandler,
  refetch,
  showNestedPopup,
  setShowNestedPopup,
  setCustomIndex,
  setCardCustomData,
}) => {
  const [isAddCard, setIsAddCard] = useState<boolean>(false);
  const [getCards, getCardLoading] = useGetCardsMutation();
  const [cardsData, setCardsData] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    handleGetCards();
  }, []);

  // Fetch the available cards
  const handleGetCards = async () => {
    try {
      const response = await getCards(isMakePayment).unwrap();
      if (response?.status === 200) {
        const cardDetailsArray = response.data;
        setCardsData(cardDetailsArray);
        setCardCustomData(cardDetailsArray);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  const handleCloseAddCard = () => {
    setIsAddCard(false);
  };

  return (
    <Popup
      title={isAddCard ? "Add card" : "Make payment"}
      width={"840px"}
      height={"501px"}
      closeHandler={isAddCard ? handleCloseAddCard : closeHandler}
    >
      {isAddCard ? (
        <AddCardDetails
          closeHandler={() => setIsAddCard(false)}
          refetch={handleGetCards}
          subscription_id={isMakePayment}
        />
      ) : (
        <CardDetail
          page="dashboard"
          cardsData={cardsData}
          isAddCard={isAddCard}
          setIsAddCard={setIsAddCard}
          cardLoading={getCardLoading?.isLoading}
          refetch={refetch}
          outstandingData={outstandingData}
          closeHandler={closeHandler}
          showNestedPopup={showNestedPopup}
          setShowNestedPopup={setShowNestedPopup}
          setCustomIndex={setCustomIndex}
        />
      )}
    </Popup>
  );
};
