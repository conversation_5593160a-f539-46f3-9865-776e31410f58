import { CloseHandlerProps, LoginFormState } from "../../typings/typing.ts";
import React, { ChangeEvent, FormEvent, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Button from "../common/Button.tsx";
import InputFields from "../forms/InputFields.tsx";
import { addNotification } from "../../store/reducers/toasterReducer.tsx";
import { login } from "../../store/reducers/authenticationReducer.tsx";
import { regEx } from "../../utils/helper.ts";
import { useDispatch } from "react-redux";
import { useLoginMutation } from "../../services/api.ts";

// Function to validate form data
const validateLoginForm = (data: LoginFormState) => {
  const errors: Partial<LoginFormState> = {};
  let isValid = true;

  if (!data?.email) {
    errors.email = "Email is required";
    isValid = false;
  } else if (!regEx.EMAIl.test(data?.email)) {
    errors.email = "Please enter a valid email address";
    isValid = false;
  }
  if (!data?.password) {
    errors.password = "Password is required";
    isValid = false;
  } else if (!regEx.PASSWORD.test(data.password)) {
    errors.password =
      "Password must be between 8 to 24 characters, should contain at least one lowercase letter, one uppercase letter, one digit, and one special character";
    isValid = false;
  }
  return { isValid, errors };
};

const initialState = {
  email: "",
  password: "",
};

const LoginForm: React.FC<CloseHandlerProps> = ({ handleOPenForgotPasswordPopup }) => {
  const [formData, setFormData] = useState<LoginFormState>(initialState);
  const [getLogin, getLoginLoading] = useLoginMutation();
  const [autoLoginTriggered, setAutoLoginTriggered] = useState(false);
  const [formError, setFormError] = useState<Partial<LoginFormState>>({});
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const prefetchEmail = queryParams.get("email");
  const prefetchPassword = queryParams.get("password");
  const prefetchRedirect = queryParams.get("redirect");

  useEffect(() => {
    if (prefetchEmail) {
      setFormData((state) => ({ ...state, email: prefetchEmail }));
    }
    if (prefetchPassword) {
      setFormData((state) => ({ ...state, password: prefetchPassword }));
    }
  }, [prefetchEmail, prefetchPassword]);

  useEffect(() => {
    if (
      !autoLoginTriggered &&
      prefetchEmail &&
      prefetchPassword &&
      prefetchRedirect === "dashboard" &&
      formData.email &&
      formData.password
    ) {
      handleSubmit();
      setAutoLoginTriggered(true);
    }
  }, [
    prefetchEmail,
    prefetchPassword,
    prefetchRedirect,
    formData.email,
    formData.password,
  ]);

  // Function to handle input change
  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setFormData((state) => ({ ...state, [name]: value }));
    setFormError((state) => ({ ...state, [name]: "" }));
  };

  // Function to handle user login
  const handleLoginUser = async () => {
    try {
      const data = {
        email: formData.email,
        password: formData.password,
      };
      const response = await getLogin(data).unwrap();
      if (response.status === 200) {
        localStorage.setItem("email", response?.data?.email);
        if (response?.data?.redirectUrl == "/") {
          localStorage.setItem("access_token", response?.data?.accessToken);

          navigate(response?.data?.redirectUrl);
          dispatch(login());
        } else {
          navigate(response?.data?.redirectUrl, {
            state: { token: response?.data?.accessToken, type: "new" },
          });
        }
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
      }
    } catch (error: any) {
      dispatch(
        addNotification({
          type: "error",
          message: error?.data?.message || "Something went wrong",
        })
      );
    }
  };

  // Function to handle form submission
  const handleSubmit = (
    e?: ChangeEvent<HTMLFormElement> | FormEvent<HTMLFormElement>
  ): void => {
    e && e.preventDefault();
    const { isValid, errors } = validateLoginForm(formData);
    setFormError(errors);

    if (isValid && !getLoginLoading?.isLoading) {
      setFormError({});
      handleLoginUser();
    }
  };

  return (
    <div className="w-[calc(100%-50px))]">
      <div>
        <h3 className="text-5xl md:text-7xl lg:text-8xl font-anton uppercase text-white text-center md:text-left">Portal Login</h3>
        <p className="text-xl mt-2 text-white text-center md:text-left">See and edit your plans</p>
      </div>
      <form className="mt-10" onSubmit={handleSubmit}>
        <div className="flex gap-5 flex-col">
          <InputFields
            placeHolder="Enter Your Email"
            changeEvent={handleChange}
            isErrorMsg={formError.email}
            attributes={{ name: "email", value: formData?.email }}
          />
          <InputFields
            placeHolder="Enter Your Password"
            changeEvent={handleChange}
            isErrorMsg={formError.password}
            type="password"
            attributes={{ name: "password", value: formData?.password }}
          />
            <p className="text-white">
              Forgot password?{" "}
              <span
                onClick={handleOPenForgotPasswordPopup}
                className="underline cursor-pointer text-white"
              >
                Click here
              </span>
            </p>
            <Button title="Login" isLoading={getLoginLoading?.isLoading} />
        </div>
      </form>
    </div>
  );
};

export default LoginForm;
