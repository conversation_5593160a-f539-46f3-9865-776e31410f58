const Joi = require('joi');

const loginValidation = Joi.object().keys({
  email: Joi.string()
    .trim()
    .email({ minDomainSegments: 2 })
    .label("email")
    .min(5) // Minimum length for email
    .max(96) // Maximum length for email
    .required()
    .messages({
      'any.required': 'Email field is required.',
      'string.email': 'Invalid email format.',
      'string.min': 'Email must be at least {#limit} characters long.',
      'string.max': 'Email cannot exceed {#limit} characters.'
    }),
  password: Joi.string()
    .min(3) // Minimum length for username
    .max(30) // Maximum length for username
    .required()
    .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\\-=\\[\\]{}|;:,~.<>?]).{3,30}$'))
    .messages({
      'any.required': 'Password field is required.',
      'string.min': 'Password must be at least {#limit} characters long.',
      'string.max': 'Password cannot exceed {#limit} characters.',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character.'
    })
});

module.exports = { loginValidation };
