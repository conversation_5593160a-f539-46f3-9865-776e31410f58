const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const PlanServices = require("../../services/plan-services");
const CustomerServices = require("../../services/customer-sync-services");
const { getCurrentAtlanticTime, hasValidChanges, sanitizeValue } = require('../../helper/custom-helper');
const customerServices = new CustomerServices();
const INTERVAL = CONFIG.interval;

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class InternetTestSyncServices {

    async getInternetDetails() {
        let internetCount = 0;
        try {
            let query = "SELECT Id, Customer_Details__c, Name, CP_Speed__c, Live_Date__c, Disconnected_Date__c, Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, CP_Status_Internal_Processing__c, CP_Status_Ship_Package__c, CP_Status_Modem_Activation__c, CP_Status_Modem_Installation__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c, LastModifiedDate, CreatedDate, Status__c, (SELECT Id, Name, Record_Type_Name__c, Related_Shipping_Order__c, Requested_Disconnect_Date__c, Expected_Completion_Date__c, Stage__c, Response_Date__c, LastModifiedDate, Speed__c, Requested_Install_Date__c, Requested_Move_Date__c, Install_Date__c, Install_Time__c, Order_Reject_Reason__c, Order_Reject_Reason_Solved__c, Submit_Date__c, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c FROM Orders__r), (SELECT Id, Name,  Install_Date__c, Install_Time__c, LastModifiedDate FROM Tech_Appointments__r) FROM Internet__c";

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const internetDetails = await salesforceConnection.fetchAllRecords(query);
            internetCount = internetDetails?.length || 0;

            if (internetCount) {
                await this.checkAndManageInternet(internetDetails);
            }

            return { execute: true, synctype: "getInternetDetails", status: true, internetCount };
        } catch (error) {
            console.error("Sync service get internet details -> ", error);
            return { execute: true, synctype: "getInternetDetails", status: false, internetCount, error: error?.message || null };
        }
    }

    async checkAndManageInternet(sfInternetDetails) {
        try {

            let sfIds = sfInternetDetails.map(sfInternetDetail => `'${sfInternetDetail.Id}'`).join(",");
            if (!sfIds?.length) return;

            const query = `SELECT 
                            cds.subscription_type as subsType,
                            ccd.sf_record_id as Customer_Details__c,
                            cdie.id, cdie.creation_order, cdie.tech_appointment, cdie.disconnect_order, cdie.speed_change_order, cdie.move_order, cdie.swap_order, cdie.sf_record_id, cdie.plan_name, cdie.plan_speed,
                            ieco.sf_record_id as creationSfId,
                            ita.sf_record_id as techAppSfId,
                            iedo.sf_record_id as discOrderSfId,
                            isco.sf_record_id as speedChangeSfId,
                            iso.sf_record_id AS swapOrderSfId,
                            imo.sf_record_id as moveOrderSfId
                            FROM customer_details_internets_eastlink cdie
                            left join internets_eastlink_creation_order ieco on ieco.id = cdie.creation_order
                            left join internets_eastlink_tech_appointment ita on ita.id = cdie.tech_appointment
                            left join internets_eastlink_disconnect_order iedo on iedo.id = cdie.disconnect_order
                            left join internets_eastlink_speed_change_order isco on isco.id = cdie.speed_change_order
                            left join internets_eastlink_move_order imo on imo.id = cdie.move_order
                            LEFT JOIN internets_eastlink_swap_order iso ON iso.id = cdie.swap_order
                            left join contacts_customer_details ccd on cdie.id = ccd.internet_id
                            left join customer_details_subscriptions cds on ccd.id = cds.customer_details_id 
                            where cdie.sf_record_id in (${sfIds})`;

            const res = await customerServices.executeQuery(query);
            if (!res?.length) return;
            const fetchedDataFromDb = JSON.parse(JSON.stringify(res));
            await this.compareAndModifyDetails(sfInternetDetails, fetchedDataFromDb);
        } catch (error) {
            console.error("SalesforceService checkAndManageInternet -> ", error);
        }
    }

    async compareAndModifyDetails(fetchedData, prevData) {
        try {
            for (const prev of prevData) {
                const sfInternetDetail = fetchedData.find(fetched => fetched.Id === prev.sf_record_id);
                if (!sfInternetDetail) continue;
                const { Name, CP_Speed__c, Live_Date__c, Disconnected_Date__c, CP_Status_Internal_Processing__c, CP_Status_Ship_Package__c, CP_Status_Modem_Activation__c, CP_Status_Modem_Installation__c, LastModifiedDate } = sfInternetDetail;

                const { id, subsType } = prev;

                const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
                const updatedTime = getCurrentAtlanticTime();

                let updateFields = 'sf_name = ?, live_date = ?, internal_processing_status = ?, ship_package_status = ?, modem_installation_status = ?, modem_activation_status = ?, disconnected_date = ?, updatedAt = ?, sf_updatedAt = ?';

                let updateValues = [];

                if (Name) updateValues.push(Name);
                else updateValues.push(null);

                if (Live_Date__c) updateValues.push(getCurrentAtlanticTime(Live_Date__c));
                else updateValues.push(null);

                if (CP_Status_Internal_Processing__c) updateValues.push(CP_Status_Internal_Processing__c);
                else updateValues.push(null);

                if (CP_Status_Ship_Package__c) updateValues.push(CP_Status_Ship_Package__c);
                else updateValues.push(null);

                if (CP_Status_Modem_Installation__c) updateValues.push(CP_Status_Modem_Installation__c);
                else updateValues.push(null);

                if (CP_Status_Modem_Activation__c) updateValues.push(CP_Status_Modem_Activation__c);
                else updateValues.push(null);

                if (Disconnected_Date__c) updateValues.push(getCurrentAtlanticTime(Disconnected_Date__c));
                else updateValues.push(null);

                updateValues.push(updatedTime)
                updateValues.push(formatedDate)

                const planServices = new PlanServices();
                const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                if (internetPlanDetails?.length) {
                    const getPrice = internetPlanDetails.find(internet => internet.speed === CP_Speed__c);
                    if (getPrice?.api_name) {
                        updateFields += ', plan_name = ?';
                        updateValues.push(getPrice.api_name);
                    }

                    updateFields += ', plan_price = ?';
                    if (subsType) {
                        if (getPrice?.billing?.[0]?.[subsType]?.price) {
                            updateValues.push(getPrice.billing[0][subsType].price);
                        } else {
                            updateValues.push(null);
                        }
                    } else {
                        updateValues.push(null);
                    }

                    updateFields += ', plan_speed = ?';
                    updateValues.push(CP_Speed__c);
                }

                updateValues.push(id);

                const updateQuery = `
                UPDATE customer_details_internets_eastlink 
                SET ${updateFields} 
                WHERE id = ?`;

                await customerServices.executeQuery(updateQuery, updateValues);
                await this.checkAndUpdateInternetOrders(sfInternetDetail, prev);
            }
        } catch (error) {
            console.error("SalesforceService compareAndModifyDetails -> ", error);
        }
    }

    async checkAndUpdateInternetOrders(fetchedData, prevData) {
        try {
            const { Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c } = fetchedData;

            const { creationSfId, discOrderSfId, techAppSfId, speedChangeSfId, moveOrderSfId, swapOrderSfId } = prevData;
            if (hasValidChanges(Creation_Order__c, creationSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_creation_order", Creation_Order__c, "creation_order");
            if (hasValidChanges(Latest_Disconnect_Order__c, discOrderSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_disconnect_order", Latest_Disconnect_Order__c, "disconnect_order");
            if (hasValidChanges(Latest_Speed_Change_Order__c, speedChangeSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_speed_change_order", Latest_Speed_Change_Order__c, "speed_change_order");
            if (hasValidChanges(Latest_Move_Order__c, moveOrderSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_move_order", Latest_Move_Order__c, "move_order");
            if (hasValidChanges(Latest_Modem_Swap_Order__c, swapOrderSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_swap_order", Latest_Modem_Swap_Order__c, "swap_order");
            if (hasValidChanges(Latest_Tech_Appointment__c, techAppSfId)) this.findOrderDetails(fetchedData, prevData, "internets_eastlink_tech_appointment", Latest_Tech_Appointment__c, "tech_appointment");
        } catch (error) {
            console.error("SalesforceService compareAndModifyDetails -> ", error);
        }
    }

    async findOrderDetails(fetchedData, internetDetails, taleName, sfOrderId, deleteField) {
        try {
            const { Orders__r, LastModifiedDate, Tech_Appointments__r } = fetchedData;
            let detailsId = null;
            const { id } = internetDetails;

            if (deleteField == "tech_appointment") {
                if (Tech_Appointments__r && Tech_Appointments__r?.records?.length && sfOrderId) {
                    const techApp = Tech_Appointments__r?.records;
                    const findTechApp = techApp.find(teAp => teAp.Id === sfOrderId);
                    if (findTechApp) {
                        detailsId = await this.getTechDetailsId(findTechApp, taleName);
                    }
                }
            } else {
                if (Orders__r && Orders__r?.records?.length && sfOrderId) {
                    const orders = Orders__r?.records;
                    const findOrders = orders.find(order => order.Id === sfOrderId);

                    if (findOrders) {
                        detailsId = await this.getAndUpdateOrderDetails(findOrders, taleName);
                    }
                }
            }

            if (deleteField) {
                const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
                const updatedTime = getCurrentAtlanticTime();

                const updateQuery = `
                    UPDATE customer_details_internets_eastlink 
                    SET ${deleteField} = ?, updatedAt = ?, sf_updatedAt = ? 
                    WHERE id = ?`;

                await customerServices.executeQuery(updateQuery, [detailsId, updatedTime, formatedDate, id]);

                if (internetDetails[deleteField]) {
                    const { dataCount } = await customerServices.checkDataCount("customer_details_internets_eastlink", deleteField, internetDetails[deleteField]);
                    if (!dataCount) await customerServices.deleteQuery(taleName, internetDetails[deleteField]);
                }
            }

        } catch (error) {
            console.error(`Error findOrderDetails -> `, error);
        }
    }

    async getAndUpdateOrderDetails(orderDetails, tableName) {
        if (!orderDetails?.Id) return null;
        try {
            // Check the database for the existing record
            const checkExQuery = `
                    SELECT id 
                    FROM ${tableName} 
                    WHERE sf_record_id = ${sanitizeValue(orderDetails?.Id)}
                `;

            const existRecord = await customerServices.executeQuery(checkExQuery);
            if (existRecord.length > 0) return existRecord[0].id;

            const sfUpdatedAt = getCurrentAtlanticTime(orderDetails?.LastModifiedDate);

            let insertOrderDetails = { sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, sf_name: orderDetails?.Name };

            if (tableName === "internets_eastlink_creation_order") {
                if (orderDetails?.Related_Shipping_Order__c) {

                    let shipping_id = null;
                    // Check if a record with the same sf_record_id and address_type already exists
                    const checkQuery = `SELECT id 
                              FROM creation_order_shipping 
                              WHERE sf_record_id = ${sanitizeValue(orderDetails.Related_Shipping_Order__c)}`;

                    const existingRecord = await customerServices.executeQuery(checkQuery);

                    if (existingRecord.length > 0) shipping_id = existingRecord[0].id;
                    else {
                        const { shippingData } = await salesforceConnection.getShippingDetails(orderDetails.Related_Shipping_Order__c);

                        if (shippingData) {
                            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updated_At, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingData;

                            const modified_date = getCurrentAtlanticTime(sf_updated_At);
                            const formatedDate = getCurrentAtlanticTime(CreatedDate);
                            const updatedTime = getCurrentAtlanticTime();

                            const query = `INSERT IGNORE INTO creation_order_shipping 
                                    (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                                    VALUES (
                                    ${sanitizeValue(sf_record_id)}, 
                                    ${sanitizeValue(sf_name)}, 
                                    ${sanitizeValue(full_mailing_address)}, 
                                    ${sanitizeValue(ship_date)}, 
                                    ${sanitizeValue(ship_drop_off_date)}, 
                                    ${sanitizeValue(tracking_url)}, 
                                    ${sanitizeValue(courier)}, 
                                    ${sanitizeValue(modified_date)}, 
                                    ${sanitizeValue(package_deliverted_at)}, 
                                    '${formatedDate}', 
                                    '${updatedTime}'
                                    )`;

                            const insertStatus = await customerServices.executeQuery(query);
                            if (insertStatus) shipping_id = insertStatus?.insertId || null;
                        }
                    }
                    if (shipping_id) insertOrderDetails.shipping_id = shipping_id;
                }
                if (orderDetails?.Record_Type_Name__c) insertOrderDetails.record_type = orderDetails.Record_Type_Name__c;
                if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
            }

            if (tableName === "internets_eastlink_disconnect_order" && orderDetails?.Requested_Disconnect_Date__c) insertOrderDetails.requested_disconnect_date = orderDetails?.Requested_Disconnect_Date__c;

            if (tableName === "internets_eastlink_swap_order" && orderDetails?.Stage__c) {
                let value = orderDetails?.Stage__c;
                if (value) {
                    insertOrderDetails.stage = value.replace(/['"`]/g, '');
                }
            }

            if (tableName === "internets_eastlink_speed_change_order" && orderDetails?.Expected_Completion_Date__c) {
                insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;
                if (orderDetails?.Speed__c) insertOrderDetails.speed = orderDetails?.Speed__c;
                let value = orderDetails?.Stage__c;
                if (value) {
                    insertOrderDetails.stage = value.replace(/['"`]/g, '');
                }
                if (orderDetails?.Response_Date__c) {
                    insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);
                }
            }

            if (tableName === "internets_eastlink_move_order") {
                if (orderDetails?.Requested_Move_Date__c) insertOrderDetails.requested_move_date = orderDetails?.Requested_Move_Date__c;
                if (orderDetails?.Requested_Install_Date__c) insertOrderDetails.requested_install_date = orderDetails?.Requested_Install_Date__c;
                if (orderDetails?.Install_Date__c) insertOrderDetails.install_date = orderDetails?.Install_Date__c;
                if (orderDetails?.Submit_Date__c) insertOrderDetails.submit_date = getCurrentAtlanticTime(orderDetails?.Submit_Date__c);
                if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
                if (orderDetails?.Install_Time__c) insertOrderDetails.install_time = orderDetails?.Install_Time__c;
                if (orderDetails?.Order_Reject_Reason__c) insertOrderDetails.reject_reason = orderDetails?.Order_Reject_Reason__c;
                if (orderDetails?.Order_Reject_Reason_Solved__c) insertOrderDetails.reject_reason_solved = orderDetails?.Order_Reject_Reason_Solved__c;
                if (orderDetails?.Service_Suite_Unit__c) insertOrderDetails.unit = orderDetails?.Service_Suite_Unit__c;
                if (orderDetails?.Service_Street_Number__c) insertOrderDetails.street_number = orderDetails?.Service_Street_Number__c;
                if (orderDetails?.Service_Street_Name__c) insertOrderDetails.street_name = orderDetails?.Service_Street_Name__c;
                if (orderDetails?.Service_City_Town__c) insertOrderDetails.city = orderDetails?.Service_City_Town__c;
                if (orderDetails?.Service_Province__c) insertOrderDetails.province = orderDetails?.Service_Province__c;
                if (orderDetails?.Service_Postal_Code__c) insertOrderDetails.postal_code = orderDetails?.Service_Postal_Code__c;
            }
            return await customerServices.insertRecord(tableName, insertOrderDetails, orderDetails.CreatedDate);
        } catch (error) {
            console.error("Sync Service getAndUpdateOrderDetails -> ", error);
        }
    }

    async getTechDetailsId(techData, tableName) {
        try {
            const { Id: techId, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = techData;
            if (!techId) return null;

            // Check if a record with the same sf_record_id and address_type already exists
            const checkQuery = `SELECT id 
                        FROM ${tableName} 
                        WHERE sf_record_id = ${sanitizeValue(techId)}`;

            const existingRecord = await customerServices.executeQuery(checkQuery);

            if (existingRecord.length > 0) return existingRecord[0].id;

            const formatedDate = getCurrentAtlanticTime(CreatedDate);
            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            const query = `INSERT IGNORE INTO ${tableName} 
                    (sf_record_id, sf_name, install_date, install_time, sf_updatedAt, createdAt, updatedAt)
                    VALUES ('${techId}', '${sf_name}', ${sanitizeValue(install_date)}, ${sanitizeValue(install_time)}, '${modifiedDate}', '${formatedDate}', '${updatedTime}')`;

            const insertStatus = await customerServices.executeQuery(query);
            return insertStatus?.insertId;

        } catch (error) {
            console.error("Sync Service getTechDetailsId -> ", error);
        }
    }
}

module.exports = InternetTestSyncServices;