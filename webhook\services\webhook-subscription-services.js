const { getCurrentAtlanticTime, billindTypeCbSubs } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const PlanServices = require("../services/plan-services");
const CONFIG = require('../config');
const SalesforceClient = require('../clients/salesforce-client');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);
const moment = require('moment');
const ChargebeeClient = require('../clients/chargebee-client');
const CustomerService = require("./webhook-customer-services");
const InvoicesSyncService = require("../services/chargebee/invoice-sync-services");

class SubscriptionWebhookServices {
    async getSubscriptionDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get subscription details -> ", error);
        }
    }

    async checkAndManageDetails(subsDetails, _id, _index) {
        try {
            if (!Array.isArray(subsDetails)) subsDetails = [subsDetails];
            for (const subsDetail of subsDetails) {
                const { Id: sf_record_id } = subsDetail;
                const { dataExist: subsExist, dataId: subscriptionData } = await this.checkSubExist(sf_record_id);

                let type;
                if (subsExist) {
                    type = "UPDATE";
                    await this.updateSubsDetails(subsDetail, subscriptionData);
                } else {
                    type = "CREATE";
                    await this.createSubsDetails(subsDetail);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async createSubsDetails(subsDetail) {
        try {

            const { Id: sf_record_id, chargebeeapps__CB_Subscription_Id__c, LastModifiedDate: sf_updatedAt, Customer_Details__c } = subsDetail;

            if (!Customer_Details__c) return;

            const { customerExist, customerId: customerDetailData } = await this.checkCustomerExist(Customer_Details__c);

            if (!customerExist) return;

            if (chargebeeapps__CB_Subscription_Id__c) {

                const chargebeeClient = new ChargebeeClient();
                let subscriptionDetail = await chargebeeClient.getSubscriptions(chargebeeapps__CB_Subscription_Id__c);
                if (!subscriptionDetail.length) return;
                subscriptionDetail = subscriptionDetail[0];
                const customer_details_id = customerDetailData.id;

                const insertCustomerSubsDetails = await this.insertCustomerSubscriptionFromChargebee(subscriptionDetail, customer_details_id, sf_record_id, sf_updatedAt);

                if (insertCustomerSubsDetails) {
                    const query = `SELECT subscription_type FROM customer_details_subscriptions WHERE id = '${insertCustomerSubsDetails}'`;
                    let res = await this.executeQuery(query);
                    res = res?.[0];
                    const { subscription_type } = res;
                    const { internet_id, tv_id, phone_id } = customerDetailData;
                    // Plan 
                    const planServices = new PlanServices();
                    const updatedTime = getCurrentAtlanticTime();

                    if (internet_id) {

                        const query = `SELECT id, plan_speed FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
                        let res = await this.executeQuery(query);
                        let internetDetails = res?.[0];

                        if (internetDetails?.plan_speed) {
                            const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                            if (internetPlanDetails?.length) {
                                let updateObj = {};
                                const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
                                if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
                                if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                                    updateObj.plan_price = getPrice.billing[0][subscription_type].price;
                                }

                                // Build the update query dynamically based on the available fields
                                let updateFields = [];
                                let queryValues = [];

                                if (updateObj.plan_name) {
                                    updateFields.push('plan_name = ?');
                                    queryValues.push(updateObj.plan_name);
                                }

                                if (updateObj.plan_price) {
                                    updateFields.push('plan_price = ?');
                                    queryValues.push(updateObj.plan_price);
                                }

                                updateFields.push('updatedAt = ?');
                                queryValues.push(updatedTime);

                                // Add the id at the end of queryValues for the WHERE clause
                                queryValues.push(internetDetails.id);

                                const updateQuery = `
                                        UPDATE customer_details_internets_eastlink 
                                        SET ${updateFields.join(', ')} 
                                        WHERE id = ?`;

                                await this.executeQuery(updateQuery, queryValues);

                            }
                        }
                    }

                    if (tv_id) {

                        const query = `SELECT id, plan_name, single_channels, iptv_products, extra_packages FROM customer_details_tvs WHERE id = '${tv_id}'`;
                        let res = await this.executeQuery(query);
                        let tvDetails = res?.[0];

                        if (tvDetails) {
                            const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

                            if (plan_name) {

                                const payload = {
                                    plan_name: plan_name,
                                    extra_packages: JSON.parse(extra_packages),
                                    single_channels: JSON.parse(single_channels),
                                    iptv_products: JSON.parse(iptv_products)
                                }

                                const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(payload, subscription_type);

                                const updateQuery = `
                                    UPDATE customer_details_tvs 
                                    SET total_service_cost = ${totalAmount}, updatedAt = '${updatedTime}'
                                    WHERE id = ${id}`;

                                await this.executeQuery(updateQuery);
                            }
                        }
                    }

                    if (phone_id) {

                        const query = `SELECT id, sf_record_id, api_name FROM customer_details_phones WHERE id = '${phone_id}'`;
                        let res = await this.executeQuery(query);
                        let phoneDetails = res?.[0];

                        if (phoneDetails) {
                            const { id, api_name, sf_record_id } = phoneDetails;
                            if (api_name) {
                                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
                                if (phonePlanDetails?.length) {
                                    let updateObj = {};
                                    const getPrice = phonePlanDetails.find(phone => phone.api_name === api_name);
                                    if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                                        updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                                    }
                                    // Build the update query dynamically based on the available fields
                                    let updateFields = [];
                                    let queryValues = [];

                                    if (updateObj.plan_price) {
                                        updateFields.push('plan_price = ?');
                                        queryValues.push(updateObj.plan_price);
                                    }

                                    updateFields.push('updatedAt = ?');
                                    queryValues.push(updatedTime);

                                    // Add the id at the end of queryValues for the WHERE clause
                                    queryValues.push(id);

                                    const updateQuery = `
                                            UPDATE customer_details_phones 
                                            SET ${updateFields.join(', ')} 
                                            WHERE id = ?`;

                                    await this.executeQuery(updateQuery, queryValues);
                                }
                            }
                        }
                    }

                    const invoicesSyncService = new InvoicesSyncService();
                    await invoicesSyncService.getInvoicesList(chargebeeapps__CB_Subscription_Id__c);
                    await this.updateCustomerInvoice(sf_record_id);
                }
            }
        } catch (error) {
            console.error("SalesforceService createSubsDetails -> ", error);
        }
    }

    async insertCustomerSubscriptionFromChargebee(subscriptionData, customer_details_id, sfSubId, sf_updatedAt) {
        try {

            const { subscription: { id: cb_subscription_id, billing_period_unit, plan_amount: amount, status, next_billing_at, created_at, activated_at: activated_on, total_dues } } = subscriptionData;

            const subscription_type = billindTypeCbSubs(billing_period_unit);
            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);

            const customerSubsDetails = {
                customer_details_id,
                cb_subscription_id,
                balance_due: total_dues ? total_dues / 100 : 0.00,
                amount: amount ? amount / 100 : null,
                activated_on: activated_on ? moment.unix(activated_on).format('YYYY-MM-DD HH:mm:ss') : null,
                next_billing_at: next_billing_at ? moment.unix(next_billing_at).format('YYYY-MM-DD HH:mm:ss') : null,
                status: status,
                subscription_type,
                sf_updatedAt: modifiedDate
            };

            if (sfSubId) customerSubsDetails.sf_record_id = sfSubId;
            const customerService = new CustomerService();
            const createdAt = created_at ? moment.unix(created_at).format('YYYY-MM-DD HH:mm:ss') : null;

            return await customerService.insertRecord("customer_details_subscriptions", customerSubsDetails, createdAt);

        } catch (error) {
            console.error(`AuthService insertCustomerSubscriptionFromChargebee ->`, error);
            throw error;
        }
    }

    async updateSubsDetails(subsDetail, subscriptionData) {
        try {
            const updatedTime = getCurrentAtlanticTime();

            const { Id: sf_record_id, chargebeeapps__CB_Subscription_Id__c: cb_subscription_id, chargebeeapps__Subcription_Activated_At__c: activated_on, chargebeeapps__Next_billing__c: next_billing_at, chargebeeapps__Subscription_status__c: status, LastModifiedDate: sf_updatedAt, chargebeeapps__Subscription_Created_At__c: createdAt } = subsDetail;

            let query;
            let queryParams = [];

            const chargebeeClient = new ChargebeeClient();
            let cbSubscriptionDetail = await chargebeeClient.getSubscriptions(cb_subscription_id);

            if (cbSubscriptionDetail?.length) cbSubscriptionDetail = cbSubscriptionDetail?.[0];

            const { subscription: { billing_period_unit, total_dues, plan_amount } } = cbSubscriptionDetail;

            const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
            const created_at = getCurrentAtlanticTime(createdAt);

            const balance_due = total_dues ? total_dues / 100 : 0.00;
            const amount = plan_amount ? plan_amount / 100 : 0.00;

            query = `UPDATE customer_details_subscriptions SET 
                                sf_record_id = ?, 
                                cb_subscription_id = ?, 
                                amount = ?, 
                                balance_due = ?, 
                                status = ?, 
                                sf_updatedAt = ?, 
                                createdAt = ?, 
                                updatedAt = ?,
                                activated_on = ?, 
                                next_billing_at = ?`;

            queryParams.push(sf_record_id, cb_subscription_id, amount, balance_due, status, modifiedDate, created_at, updatedTime);

            if (activated_on) queryParams.push(getCurrentAtlanticTime(activated_on));
            else queryParams.push(null);

            if (next_billing_at) queryParams.push(getCurrentAtlanticTime(next_billing_at));
            else queryParams.push(null);

            let subscription_type;
            if (billing_period_unit) {
                subscription_type = billindTypeCbSubs(billing_period_unit);
                query += ', subscription_type = ?';
                queryParams.push(subscription_type);
            }

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await this.executeQuery(query, queryParams);

            if (subscription_type != subscriptionData.subscription_type) {
                // Update subscription type in tv, internet and phone
                await this.updateSubscriptionType(subscriptionData.customer_details_id, subscription_type);
            }

        } catch (error) {
            console.error("SalesforceService updateSubsDetails -> ", error);
        }
    }

    async checkSubExist(sf_record_id) {
        try {

            const query = `SELECT id, subscription_type, customer_details_id FROM customer_details_subscriptions WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check subs Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async checkCustomerExist(sf_record_id) {
        try {
            const query = `SELECT 
                            ccd.*, 
                            cs.sf_record_id as subsSfId,
                            cs.subscription_type as subscription_type,
                            sa.sf_record_id as serviceSfId, 
                            ma.sf_record_id as mailingSfId, 
                            cdi.sf_record_id as internetSfId, 
                            cdt.sf_record_id as tvSfId,
                            cdp.sf_record_id as phoneSfId
                            FROM contacts_customer_details ccd
                            left join customer_details_addresses sa on sa.id = ccd.service_address_id
                            left join customer_details_addresses ma on ma.id = ccd.mailing_address_id
                            left join customer_details_internets_eastlink cdi on cdi.id = ccd.internet_id
                            left join customer_details_phones cdp on cdp.id = ccd.phone_id
                            left join customer_details_tvs cdt on cdt.id = ccd.tv_id
                            left join customer_details_subscriptions cs on cs.customer_details_id = ccd.id
                            where ccd.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                customerExist: res.length > 0,
                customerId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Customer Exist -> ", error);
            return {
                customerExist: 0,
                customerId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async updateSubscriptionType(customer_details_id, subscription_type) {
        try {
            const query = `SELECT internet_id, tv_id, phone_id FROM contacts_customer_details where id = '${customer_details_id}'`;
            const res = await this.executeQuery(query);
            if (res?.length) {
                const customerDetailData = res[0];
                const { internet_id, tv_id, phone_id } = customerDetailData;
                if (internet_id) this.updateInternetDetails(internet_id, subscription_type);
                if (tv_id) this.updateTvDetails(tv_id, subscription_type);
                if (phone_id) this.updatePhoneDetails(phone_id, subscription_type);
            }
        } catch (error) {
            console.error("SalesforceService updateSubscriptionType -> ", error);
        }
    }

    async updateInternetDetails(internet_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_speed FROM customer_details_internets_eastlink WHERE id = '${internet_id}'`;
            let res = await this.executeQuery(query);
            let internetDetails = res?.[0];

            if (internetDetails?.plan_speed) {
                const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
                if (internetPlanDetails?.length) {
                    let updateObj = {};
                    const getPrice = internetPlanDetails.find(internet => internet.speed === internetDetails.plan_speed);
                    if (getPrice?.api_name) updateObj.plan_name = getPrice.api_name;
                    if (getPrice?.billing?.[0]?.[subscription_type]?.price) {
                        updateObj.plan_price = getPrice.billing[0][subscription_type].price;
                    }

                    // Build the update query dynamically based on the available fields
                    let updateFields = [];
                    let queryValues = [];

                    if (updateObj.plan_name) {
                        updateFields.push('plan_name = ?');
                        queryValues.push(updateObj.plan_name);
                    }

                    if (updateObj.plan_price) {
                        updateFields.push('plan_price = ?');
                        queryValues.push(updateObj.plan_price);
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    updateFields.push('updatedAt = ?');
                    queryValues.push(updatedTime);

                    // Add the id at the end of queryValues for the WHERE clause
                    queryValues.push(internetDetails.id);

                    const updateQuery = `
                            UPDATE customer_details_internets_eastlink 
                            SET ${updateFields.join(', ')} 
                            WHERE id = ?`;

                    await this.executeQuery(updateQuery, queryValues);
                }
            }
        } catch (error) {
            console.error("SalesforceService updateInternetDetails -> ", error);
        }
    }

    async updateTvDetails(tv_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, plan_name, single_channels, iptv_products, extra_packages FROM customer_details_tvs WHERE id = '${tv_id}'`;
            let res = await this.executeQuery(query);
            let tvDetails = res?.[0];

            if (tvDetails) {
                const { id, plan_name, single_channels, iptv_products, extra_packages } = tvDetails;

                if (plan_name) {

                    const payload = {
                        plan_name: plan_name,
                        extra_packages: JSON.parse(extra_packages),
                        single_channels: JSON.parse(single_channels),
                        iptv_products: JSON.parse(iptv_products)
                    }

                    const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(payload, subscription_type);
                    const updatedTime = getCurrentAtlanticTime();

                    const updateQuery = `
                        UPDATE customer_details_tvs 
                        SET total_service_cost = ?, updatedAt = ?
                        WHERE id = ?`;
                    await this.executeQuery(updateQuery, [totalAmount, updatedTime, id]);
                }
            }
        } catch (error) {
            console.error("SalesforceService updateTvDetails -> ", error);
        }
    }

    async updatePhoneDetails(phone_id, subscription_type) {
        try {
            const planServices = new PlanServices();
            const query = `SELECT id, sf_record_id FROM customer_details_phones WHERE id = '${phone_id}'`;
            let res = await this.executeQuery(query);
            let phoneDetails = res?.[0];

            if (phoneDetails) {
                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
                const phoneRes = await salesforceConnection.getPhoneSingleValue(phoneDetails.sf_record_id);
                const { status: phStatus, data: pfData } = phoneRes;
                if (phStatus) {
                    const _PHONE_PLAN = pfData?.Calling_Plan__c;

                    let updateObj = {};
                    const getPrice = phonePlanDetails.find(phone => phone.api_name === _PHONE_PLAN);
                    if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                        updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;
                    }

                    // Build the update query dynamically based on the available fields
                    let updateFields = [];
                    let queryValues = [];

                    if (updateObj.plan_price) {
                        updateFields.push('plan_price = ?');
                        queryValues.push(updateObj.plan_price);
                    }

                    const updatedTime = getCurrentAtlanticTime();

                    updateFields.push('updatedAt = ?');
                    queryValues.push(updatedTime);

                    // Add the id at the end of queryValues for the WHERE clause
                    queryValues.push(phoneDetails.id);

                    const updateQuery = `
                    UPDATE customer_details_phones 
                    SET ${updateFields.join(', ')} 
                    WHERE id = ?`;

                    await this.executeQuery(updateQuery, queryValues);
                }
            }
        } catch (error) {
            console.error("SalesforceService updatePhoneDetails -> ", error);
        }
    }

    async updateCustomerInvoice(sfSubscriptionId) {
        try {

            const { returnStatus, invoiceData: invoiceDetails } = await salesforceConnection.getInvoiceDetails(sfSubscriptionId);
            if (!returnStatus) return;
            for (const invoiceData of invoiceDetails) {
                const { Id: sf_record_id, Expected_Payment_Date_Time__c: expected_payment_date, LastModifiedDate, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id } = invoiceData;

                let query;
                let queryParams = [];

                const modifiedDate = getCurrentAtlanticTime(LastModifiedDate);
                const updatedTime = getCurrentAtlanticTime();

                query = `UPDATE subscriptions_invoices SET 
                                    sf_record_id = ?, 
                                    sf_updatedAt = ?, 
                                    updatedAt = ?, 
                                    expected_payment_date = ?`;

                queryParams.push(sf_record_id, modifiedDate, updatedTime);

                if (expected_payment_date) queryParams.push(getCurrentAtlanticTime(expected_payment_date));
                else queryParams.push(null);

                query += ` WHERE cb_invoice_id = ? and cb_subscription_id = ?`;
                queryParams.push(cb_invoice_id, cb_subscription_id);

                await this.executeQuery(query, queryParams);
            }
        } catch (error) {
            console.error(`AuthService updateCustomerInvoice ->`, error);
            throw error;
        }
    }
}

module.exports = SubscriptionWebhookServices;
