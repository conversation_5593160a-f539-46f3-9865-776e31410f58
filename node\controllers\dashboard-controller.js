const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const DashboardServices = require("../services/dashboard-services");
const dashboardServices = new DashboardServices();

class DashboardController {
    /**
    * Fetches user details based on request.
    * @param {Object} req - Express request object.
    * @param {Object} res - Express response object.
    * @param {Function} next - Express next function.
    */
    async userDetails(req, res, next) {
        try {
            const result = await dashboardServices.userDetails(req.userDetails, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Dashboard Controller dashboard user details-> `, error);
            next(error);
        }
    }

    /**
    * Fetches location details based on request.
    * @param {Object} req - Express request object.
    * @param {Object} res - Express response object.
    * @param {Function} next - Express next function.
    */
    async locationDetails(req, res, next) {
        try {
            const result = await dashboardServices.locationDetails(req.userDetails, req.query, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Dashboard Controller dashboard location details->`, error);
            next(error);
        }
    }
}

// Export an instance of the DashboardController class
module.exports = new DashboardController();