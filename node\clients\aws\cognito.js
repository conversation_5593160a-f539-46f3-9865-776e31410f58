const { CognitoIdentityProviderClient, AdminCreate<PERSON>ser<PERSON>ommand, AdminUpdateUserAttributesCommand, InitiateAuthCommand, ChangePasswordCommand, RespondToAuthChallengeCommand, ForgotPasswordCommand, ConfirmForgotPasswordCommand, GlobalSignOutCommand, AdminDeleteUserCommand, AdminGetUserCommand } = require('@aws-sdk/client-cognito-identity-provider');
const CONFIG = require("../../config");
const CustomError = require('../../utils/errors/CustomError.js');
const { RESPONSE_CODES } = require('../../utils/ResponseCodes.js');
const { generateSecretHashValues } = require("../../helpers/privacyAlgorithms");

class CognitoConnection {
    /**
    * Constructor initializes the Cognito client with required configuration.
    */
    constructor() {
        const { userPoolId, clientId, region, accessKeyId, secretAccessKey, clientSecret } = CONFIG.aws;
        this.userPool = userPoolId;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.client = new CognitoIdentityProviderClient({
            region: region,
            credentials: {
                accessKeyId: accessKeyId,
                secretAccessKey: secretAccessKey
            }
        });
    }

    /**
    * Register a new user in the Cognito user pool.
    * @param {string} email - The user's email address.
    * @param {string} password - The user's temporary password.
    * @param {string} type - Type of action (e.g., "resend").
    * @returns {Promise<Object>} - The result of the AdminCreateUserCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async register(email, password, type) {
        const params = {
            UserPoolId: this.userPool,
            Username: email,
            TemporaryPassword: password,
            UserAttributes: [
                { Name: 'email', Value: email },
            ],
            DesiredDeliveryMediums: ['EMAIL']
        };

        // Resend the invitation message to a user that already exists and reset the expiration limit on the user's account
        if (type == "resend") params.MessageAction = "RESEND";

        try {
            return await this.client.send(new AdminCreateUserCommand(params));
        } catch (error) {
            console.error(`Error creating cognito user: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, `AWS Cognito Error: ${error.message}` || error);
        }
    }

    /**
    * Authenticate a user and initiate an auth session.
    * @param {string} email - The user's email address.
    * @param {string} password - The user's password.
    * @returns {Promise<Object>} - The result of the InitiateAuthCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */
    async login(email, password) {
        const secretHash = generateSecretHashValues(email, this.clientId, this.clientSecret);
        const params = {
            AuthFlow: 'USER_PASSWORD_AUTH',
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password,
                SECRET_HASH: secretHash
            },
            ClientId: this.clientId
        };
        try {
            return await this.client.send(new InitiateAuthCommand(params));
        } catch (error) {
            console.error(`Error cognito login: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * This function will be called to change password for first time only when we got ChallengeName: 'NEW_PASSWORD_REQUIRED' as response
    * Respond to the new password required challenge during authentication.
    * @param {Object} body - Contains email, password, and session.
    * @returns {Promise<Object>} - The result of the RespondToAuthChallengeCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async newPasswordChallenge(body) {
        const { email, password, session } = body;
        const secretHash = generateSecretHashValues(email, this.clientId, this.clientSecret);
        const params = {
            ChallengeName: 'NEW_PASSWORD_REQUIRED',
            ClientId: this.clientId,
            ChallengeResponses: {
                USERNAME: email,
                NEW_PASSWORD: password,
                SECRET_HASH: secretHash
            },
            Session: session
        };

        try {
            return await this.client.send(new RespondToAuthChallengeCommand(params));
        } catch (error) {
            console.error(`Error responding to new password challenge: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Change the password for an authenticated user.
    * @param {Object} body - Contains old_password, password, and token.
    * @returns {Promise<Object>} - The result of the ChangePasswordCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async changePassword(body) {
        const { old_password: PreviousPassword, password: ProposedPassword, token: AccessToken } = body;
        const params = { PreviousPassword, ProposedPassword, AccessToken };
        try {
            return await this.client.send(new ChangePasswordCommand(params));
        } catch (error) {
            console.error(`Error change password: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Verify a user's email address.
    * @param {string} email - The user's email address.
    * @returns {Promise<Object>} - The result of the AdminUpdateUserAttributesCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async verifyEmail(email) {
        const params = {
            UserPoolId: this.userPool,
            Username: email,
            UserAttributes: [
                {
                    Name: 'email_verified',
                    Value: 'true'
                }
            ]
        };
        try {
            return await this.client.send(new AdminUpdateUserAttributesCommand(params));
        } catch (error) {
            console.error(`Error verify email: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Initiate the forgot password flow.
    * @param {string} email - The user's email address.
    * @returns {Promise<Object>} - The result of the ForgotPasswordCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async forgotPassword(email) {
        const secretHash = generateSecretHashValues(email, this.clientId, this.clientSecret);
        const params = {
            ClientId: this.clientId,
            Username: email,
            SecretHash: secretHash
        };

        try {
            return await this.client.send(new ForgotPasswordCommand(params));
        } catch (error) {
            console.error(`Error forgot password: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Confirm the forgot password flow by setting a new password.
    * @param {Object} body - Contains email, password, and confirmation_code.
    * @returns {Promise<Object>} - The result of the ConfirmForgotPasswordCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async forgotPasswordConfirmation(body) {
        const { email, password, confirmation_code } = body;
        const secretHash = generateSecretHashValues(email, this.clientId, this.clientSecret);
        const params = {
            ClientId: this.clientId,
            Username: email,
            Password: password,
            ConfirmationCode: confirmation_code.toString(),
            SecretHash: secretHash,
        };
        try {
            return await this.client.send(new ConfirmForgotPasswordCommand(params));
        } catch (error) {
            console.error(`Error forgot password confirmation: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Sign out a user from all devices.
    * @param {string} AccessToken - The access token of the user.
    * @returns {Promise<Object>} - The result of the GlobalSignOutCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async logout(AccessToken) {
        const params = { AccessToken };
        try {
            return await this.client.send(new GlobalSignOutCommand(params));
        } catch (error) {
            console.error(`Error logout: ->`, error);
            return true;
            // throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Delete a user from the Cognito user pool.
    * @param {string} email - The user's email address.
    * @returns {Promise<Object>} - The result of the AdminDeleteUserCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async deleteUser(email) {
        const params = {
            UserPoolId: this.userPool,
            Username: email
        };
        try {
            return await this.client.send(new AdminDeleteUserCommand(params));
        } catch (error) {
            console.error(`Error delete user: ->`, error);
            throw new CustomError(error?.$metadata?.httpStatusCode || RESPONSE_CODES.BAD_REQUEST, error.message || error);
        }
    }

    /**
    * Get a user's details.
    * @param {string} email - The user's email address.
    * @returns {Promise<Object>} - The result of the AdminUpdateUserAttributesCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async getUserDetail(email) {
        const params = {
            UserPoolId: this.userPool,
            Username: email
        };
        try {
            return await this.client.send(new AdminGetUserCommand(params));
        } catch (error) {
            throw error;
        }
    }
}

module.exports = CognitoConnection;

