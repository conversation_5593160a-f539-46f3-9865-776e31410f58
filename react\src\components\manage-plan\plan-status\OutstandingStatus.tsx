import { WifiIcon } from "../../../assets/Icons";
import StatusCard from "../StatusCard";
import MoveOrderStatus from "./MoveOrderStatus";

interface OutstandingStatusProps {
  isMoveOrder?: any;
}

const OutstandingStatus: React.FC<OutstandingStatusProps> = ({
  isMoveOrder,
}) => {
  return (
    <div>
      <StatusCard
        Icon={<WifiIcon />}
        label="Past due invoice"
        status="Outstanding"
        isMoveOrder={isMoveOrder}
      />
      {isMoveOrder && <MoveOrderStatus isMoveOrder={isMoveOrder} />}
    </div>
  );
};

export default OutstandingStatus;
