import React, { useEffect } from "react";
import { InvalidIcon } from "../../assets/Icons";
import { AddressFieldProps } from "../../typings/typing";
import { useDispatch } from "react-redux";
import { addNotification } from "../../store/reducers/toasterReducer";

const AddressFields: React.FC<AddressFieldProps> = (props) => {
  const {
    type = "text",
    title,
    placeHolder,
    isFocus = false,
    isReadOnly = false,
    isDisabled = false,
    isErrorMsg = null,
    setLocationData,
    inputRef,
    formData,
  } = props;

  const dispatch = useDispatch();
  const GOOGLE_API_KEY = "AIzaSyD16ec79HDYdK_YR8DG3qE1q7OvFhcvdRw";

  const allowedProvince = ["NS", "NL", "PE"];

  useEffect(() => {
    // Avoid adding the script multiple times
    const existingScript = document.querySelector(
      'script[src^="https://maps.googleapis.com/maps/api/js"]'
    );

    if (!existingScript) {
      const script = document.createElement("script");
      script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_API_KEY}&libraries=maps,places&v=beta`;
      script.async = true;
      script.defer = true;
      script.onload = () => {
        setupAutocomplete();
      };
      document.body.appendChild(script);

      return () => {
        // Don't remove the script globally if used elsewhere
        // Instead, just clean up listeners if needed
      };
    } else {
      // If already loaded, just run the autocomplete setup
      if ((window as any).google?.maps?.places) {
        setupAutocomplete();
      } else {
        // Wait for script to load if it's there but not yet initialized
        existingScript.addEventListener("load", setupAutocomplete);
      }
    }

    function setupAutocomplete() {
      if (!inputRef?.current) return;
      const autocomplete = new (window as any).google.maps.places.Autocomplete(
        inputRef?.current,
        {
          types: ["address"],
          componentRestrictions: { country: "ca" },
        }
      );

      autocomplete.addListener("place_changed", () => {
        const place = autocomplete.getPlace();

        const getLong = (type: string) =>
          place.address_components?.find((c: any) => c.types.includes(type))
            ?.long_name || "";

        const getShort = (type: string) =>
          place.address_components?.find((c: any) => c.types.includes(type))
            ?.short_name || "";

        const provinceShort = getShort("administrative_area_level_1");

        // Exclude appending if province is not allowed
        if (!allowedProvince?.includes(provinceShort)) {
          dispatch(
            addNotification({
              type: "error",
              message: `Addresses in province code ${provinceShort} are currently not serviceable.`,
            })
          );

          if (inputRef?.current) {
            inputRef.current.value = ""; // clear input
          }

          setLocationData((prev: any) => ({
            ...prev,
            streetNumber: "",
            streetAddress: "",
            apartment: "",
            streetName: "",
            city: "",
            pinCode: "",
            province: "",
          }));

          return;
        }

        setLocationData((prev: any) => ({
          ...prev,
          streetNumber: getLong("street_number"),
          streetAddress:
            getLong("street_number") + " " + getShort("route")?.endsWith(" Ct")
              ? getShort("route").replace(/ Ct$/, " Crt")
              : getShort("route"),
          apartment: getLong("subpremise")?.toUpperCase(),
          streetName: getShort("route")?.endsWith(" Ct")
            ? getShort("route").replace(/ Ct$/, " Crt")
            : getShort("route"),
          city: getLong("locality"),
          pinCode: getLong("postal_code"),
          province: provinceShort,
        }));
      });
    }
  }, []);

  return (
    <>
      <div
        className={`input-wrapper ${
          isErrorMsg ? "error" : ""
        } flex flex-col gap-2`}
      >
        {title && (
          <div>
            <label
              className={`${
                isReadOnly ? "input-label-read-only" : "input-label"
              } font-medium text-base`}
            >
              {title}
            </label>
          </div>
        )}


        <div>
          <input
            ref={inputRef}
            type={type}
            className={
              isDisabled
                ? "input-default !border-none !cursor-not-allowed"
                : `${
                    isReadOnly ? "!bg-white !shadow-none !border-none" : ""
                  } input-default`
            }
            placeholder={placeHolder}
            autoFocus={isFocus}
            readOnly={isReadOnly}
            disabled={isDisabled}
            defaultValue={formData?.streetName && `${formData?.streetNumber||""} ${formData?.streetAddress ||""}`}
          ></input>
        </div>
        {isErrorMsg != null && (
          <p className="error-text flex gap-2 items-center pt-1.5 text-sm">
            <span className="icon">
              <InvalidIcon />
            </span>
            {isErrorMsg}
          </p>
        )}
      </div>
    </>
  );
};

export default AddressFields;