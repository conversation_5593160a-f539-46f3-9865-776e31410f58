const ContactServices = require("./contact-sync-services");
const CustomerServices = require("./customer-sync-services");
const CognitoConnection = require("../clients/cognito-client");
const awsCognitoConnection = new CognitoConnection();

class DeleteContactService {
    async deleteContact(email) {
        try {
            console.time("deleteContact"); // Start measuring execution time
            await this.checkAndDeleteDetails(email);
            console.timeEnd("deleteContact"); // End measuring execution time
        } catch (error) {
            console.error("Delete contact -> ", error);
        }
    }

    async checkAndDeleteDetails(email) {
        try {
            const customerServices = new CustomerServices();
            const query = `SELECT id FROM contacts WHERE email = '${email}'`;
            const res = await customerServices.executeQuery(query);
            if (res.length) {
                const contactId = res[0]?.id;
                const response = await awsCognitoConnection.deleteUser(email);
                if (response && response?.$metadata?.httpStatusCode == 200) {
                    const contactServices = new ContactServices();
                    await contactServices.deleteOtherContactDetails(contactId);
                }
            }
        } catch (error) {
            console.error("SalesforceService getAllShippingOrderDetails -> ", error);
            return { status: false, data: [] };
        }
    }
}

module.exports = DeleteContactService;
