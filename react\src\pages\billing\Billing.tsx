import Statements from "../../components/billing/Statements";
import PaymentMethod from "./PaymentMethod";
// import { BestDeal } from "../../components/dashboard/BestDeal";
// import BestDealSkeleton from "../../components/dashboard/skeletons/BestDealSkeleton";

interface BillingProps {
  planData: any;
}

const Billing: React.FC<BillingProps> = ({ planData }) => {
  
  // const isBestDealLoading = false;
  return (
    <div className="billing max-w-full">
      <div className="flex max-lg:flex-col gap-5">
        <div className="flex flex-col gap-2.5 2xl:max-w-[calc(100%)] lg:max-w-[calc(100%)] w-full">
          <PaymentMethod planData={planData} />
          <div className={"2xl:pt-10 pt-6"}>
            <Statements planData={planData} />
          </div>
        </div>
        {/* <div className="2xl:w-[320px] w-[280px] max-lg:hidden relative z-[100]"> */}
        {/* <div className="sticky top-5"> */}
        {/* {isBestDealLoading ? <BestDealSkeleton /> : <BestDeal />} */}
        {/* </div> */}
        {/* </div> */}
      </div>
    </div>
  );
};

export default Billing;
