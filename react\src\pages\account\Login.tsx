import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Button from "../../components/common/Button.tsx";
import ForgotPassword from "./ForgotPassword.tsx";
import LoginForm from "../../components/account/LoginForm.tsx";
import Popup from "../../components/common/Popup.tsx";
import ResetPassword from "./ResetPassword.tsx";
import { ResetSuccessIcon } from "../../assets/Icons.tsx";
import { useSelector } from "react-redux";

const Login: React.FC = () => {
  const isLoggedIn = useSelector(
    (state: any) => state.authenticationReducer.isLoggedIn
  );

  const [emailSentPopup, setEmailSentPopup] = useState<boolean>(false);
  const navigate = useNavigate();
  const { hash } = useLocation();
  const [contentType, setContentType] = useState<"login" | "forgot-password">(
    "login"
  );

  // Redirect to home if already logged in
  useEffect(() => {
    if (isLoggedIn) {
      navigate("/");
    }
  }, [isLoggedIn]);

  // Toggle forgot password popup
  const handleOPenForgotPasswordPopup = () => {
    setContentType("forgot-password");
  };

  const handleCloseForgotPasswordPopup = () => {
    setContentType("login");
  };

  // Navigate to reset password
  const handleResetPasswordPopup = () => {
    navigate(window.location.pathname, { replace: true });
  };

  return (
    <div className="min-h-[100svh] flex flex-col p-2 md:p-10 md:pb-5 md:pt-2 box-border">
      {/* Purple cow logo */}
      <div className="flex justify-center md:justify-start">
        <img
          src={
            "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/purple_cow_logo.png"
          }
          alt="logo"
          className="w-32"
        />
      </div>
      <div className="h-1 lg:h-5"></div>

      {/* Purple Container */}
      <div className="flex-1 flex items-center bg-medium_purple rounded-2xl p-5 w-full mx-auto">
        <div className="flex gap-5 rounded-30 lg:w-1/2 w-full md:w-max">
          <div className="lg:basis-1/2 2xl:py-16 py-7 2xl:gap-[80px] lg:gap-[60px] px-5 md:mx-0 flex-1 flex flex-col">
            <div className="w-full md:max-w-[80%] sm:max-w-[90%]  m-auto max-lg:mt-10">
              {contentType == "login" && (
                <LoginForm
                  handleOPenForgotPasswordPopup={handleOPenForgotPasswordPopup}
                />
              )}

              {contentType == "forgot-password" && (
                <ForgotPassword closeHandler={handleCloseForgotPasswordPopup} />
              )}
            </div>
          </div>
        </div>

        <div className="gap-5 rounded-30 hidden md:flex flex-col justify-center lg:w-1/2">
          <div className="flex justify-center">
            <img
              className="w-24 transition duration-300 hover:animate-peace-wiggle"
              src={
                "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/purple_cow_peace.png"
              }
              alt="Purple cow peace"
            />
          </div>

          <div className="flex justify-end">
            <img
              className="w-48 mr-10 hover:-rotate-12 transition-all duration-150"
              src={
                "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/dancing_cow.gif"
              }
              alt="Dancing Cow"
            />
          </div>

          <div className="flex justify-start ml-20 ">
            <img
              className="w-20 mx-5 transition-all duration-300 hover:brightness-110 hover:scale-110"
              src={
                "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/purple_cow_lightning.png"
              }
              alt="Purple Cow Lightning"
            />
          </div>
        </div>
      </div>

      {emailSentPopup && (
        <Popup
          title="Email sent"
          closeHandler={() => setEmailSentPopup(!emailSentPopup)}
        >
          <p>
            Check <EMAIL> for a password reset link. This can
            take up to 5 minutes to arrive.
          </p>
        </Popup>
      )}
      {hash === "#reset-password" && (
        <Popup
          title="Create new password"
          closeHandler={handleResetPasswordPopup}
          width="600px"
        >
          <ResetPassword
            handleOPenForgotPasswordPopup={handleResetPasswordPopup}
          />
        </Popup>
      )}
      {hash === "#reset-password-successful" && (
        <Popup title="">
          <div className="flex flex-col items-center gap-30">
            <ResetSuccessIcon />
            <p>You set a new password successfully.</p>
            <Button title="Okay" />
          </div>
        </Popup>
      )}
    </div>
  );
};

export default Login;
