import React from "react";
import ContentLoader from "react-content-loader";

const CardDetailSkeleton: React.FC = (props) => {
  return (
    <div style={{ width: '100%', maxWidth: 682, margin: '0 auto', padding: '0 16px' }}>
      <ContentLoader
        speed={2}
        width="100%"
        height={316} // Fixed height for the loader
        viewBox="0 0 682 316" // Maintain original dimensions
        backgroundColor="#f5f5f5"
        foregroundColor="#dbdbdb"
        preserveAspectRatio="xMidYMid meet"
        {...props}
      >
        <rect x="0" y="0" rx="5" ry="5" width="100%" height="100%" />
      </ContentLoader>
    </div>
  );
};

export default CardDetailSkeleton;
