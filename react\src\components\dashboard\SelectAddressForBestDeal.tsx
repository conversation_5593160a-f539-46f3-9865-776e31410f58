import React from "react";
import BestDealPopup from "./BestDealPopup";
import OfferLocationSelectCard from "./OfferLocationSelectCard";

type SelectAddressForBestDealProps = {
  locations: Array<{
    address: string;
  }>;
  closeHandler: () => void;
  addPlanHandler: (locationId: string) => void;
};

const SelectAddressForBestDeal: React.FC<SelectAddressForBestDealProps> = ({
  locations,
  closeHandler,
  addPlanHandler,
}) => {
  return (
    <BestDealPopup closeHandler={closeHandler} width="620px">
      <div className="flex flex-col gap-5 max-xl:pb-5 pb-30">
        <div className="p-1.5 bg-primary-gradient text-white w-fit rounded-[6px]">
          <p className="text-white text-sm text-[10px] font-bold leading-none">
            Limited Time Offer
          </p>
        </div>
        <div>
          <p className="text-base">
            Which account would you like to add TV to?
          </p>
        </div>
        {locations.map((location, index) => (
          <React.Fragment key={index}>
            <OfferLocationSelectCard
              location={location}
              nextHandler={addPlanHandler}
            />
          </React.Fragment>
        ))}
      </div>
    </BestDealPopup>
  );
};

export default SelectAddressForBestDeal;
