const { getCurrentAtlanticTime, hasValidChanges } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const PlanServices = require("../services/plan-services");
const CustomerWebhookServices = require("./webhook-customer-services");
const CONFIG = require('../config');
const SalesforceClient = require('../clients/salesforce-client');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class InternetWebhookServices {
    async getInternetDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get internet details -> ", error);
        }
    }

    async checkAndManageDetails(sfInternetDetails, _id, _index) {
        try {
            if (!Array.isArray(sfInternetDetails)) sfInternetDetails = [sfInternetDetails];
            for (const sfInternetDetail of sfInternetDetails) {
                const { internetExist, internetId } = await this.checkInternetExist(sfInternetDetail);
                let type;
                if (internetExist) {
                    type = "UPDATE";
                    this.updateInternetDetails(sfInternetDetail, internetId);
                } else {
                    type = "CREATE";
                    this.createInternetDetails(sfInternetDetail);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateInternetDetails(sfInternetDetail, internetDetails) {
        try {
            const { Id: sf_record_id, Name, CP_Speed__c, Live_Date__c, Disconnected_Date__c, Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, CP_Status_Internal_Processing__c, CP_Status_Ship_Package__c, CP_Status_Modem_Activation__c, CP_Status_Modem_Installation__c, LastModifiedDate, Latest_Move_Order__c, Latest_Modem_Swap_Order__c } = sfInternetDetail;

            const { id, creationSfId, discOrderSfId, techAppSfId, speedChangeSfId, moveOrderSfId, subsType, swapOrderSfId } = internetDetails;

            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            let updateFields = 'live_date = ?, internal_processing_status = ?, ship_package_status = ?, modem_installation_status = ?, modem_activation_status = ?, disconnected_date = ?, updatedAt = ?, sf_updatedAt = ?';

            let updateValues = [];

            if (Live_Date__c) updateValues.push(getCurrentAtlanticTime(Live_Date__c));
            else updateValues.push(null);

            if (CP_Status_Internal_Processing__c) updateValues.push(CP_Status_Internal_Processing__c);
            else updateValues.push(null);

            if (CP_Status_Ship_Package__c) updateValues.push(CP_Status_Ship_Package__c);
            else updateValues.push(null);

            if (CP_Status_Modem_Installation__c) updateValues.push(CP_Status_Modem_Installation__c);
            else updateValues.push(null);

            if (CP_Status_Modem_Activation__c) updateValues.push(CP_Status_Modem_Activation__c);
            else updateValues.push(null);

            if (Disconnected_Date__c) updateValues.push(getCurrentAtlanticTime(Disconnected_Date__c));
            else updateValues.push(null);

            updateValues.push(updatedTime)
            updateValues.push(formatedDate)

            const planServices = new PlanServices();
            const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
            if (internetPlanDetails?.length) {
                const getPrice = internetPlanDetails.find(internet => internet.speed === CP_Speed__c);
                if (getPrice?.api_name) {
                    updateFields += ', plan_name = ?';
                    updateValues.push(getPrice.api_name);
                }

                updateFields += ', plan_price = ?';
                if (subsType) {
                    if (getPrice?.billing?.[0]?.[subsType]?.price) {
                        updateValues.push(getPrice.billing[0][subsType].price);
                    } else {
                        updateValues.push(null);
                    }
                } else {
                    updateValues.push(null);
                }

                updateFields += ', plan_speed = ?';
                updateValues.push(CP_Speed__c);
            }

            updateValues.push(id);

            const updateQuery = `
                UPDATE customer_details_internets_eastlink 
                SET ${updateFields} 
                WHERE id = ?`;

            await this.executeQuery(updateQuery, updateValues);

            if (hasValidChanges(Creation_Order__c, creationSfId)) this.updateDetails(sfInternetDetail, internetDetails, "Creation");
            if (hasValidChanges(Latest_Tech_Appointment__c, techAppSfId)) this.updateDetails(sfInternetDetail, internetDetails, "Tech");
            if (hasValidChanges(Latest_Disconnect_Order__c, discOrderSfId)) this.updateDetails(sfInternetDetail, internetDetails, "Disconnect");
            if (hasValidChanges(Latest_Speed_Change_Order__c, speedChangeSfId)) this.updateDetails(sfInternetDetail, internetDetails, "SpeedChange");
            if (hasValidChanges(Latest_Move_Order__c, moveOrderSfId)) this.updateDetails(sfInternetDetail, internetDetails, "MoveOrder");
            if (hasValidChanges(Latest_Modem_Swap_Order__c, swapOrderSfId)) this.updateDetails(sfInternetDetail, internetDetails, "SwapOrder");
        } catch (error) {
            console.error("Error updateInternetDetails -> ", error);
        }
    }

    async checkInternetExist(sfInternetDetail) {
        try {
            const { Id: sf_record_id } = sfInternetDetail;
            const query = `SELECT 
                            cds.subscription_type as subsType,
                            cdie.*, 
                            ieco.sf_record_id as creationSfId,
                            ita.sf_record_id as techAppSfId,
                            iedo.sf_record_id as discOrderSfId,
                            isco.sf_record_id as speedChangeSfId,
                            iso.sf_record_id AS swapOrderSfId,
                            imo.sf_record_id as moveOrderSfId
                            FROM customer_details_internets_eastlink cdie
                            left join internets_eastlink_creation_order ieco on ieco.id = cdie.creation_order
                            left join internets_eastlink_tech_appointment ita on ita.id = cdie.tech_appointment
                            left join internets_eastlink_disconnect_order iedo on iedo.id = cdie.disconnect_order
                            left join internets_eastlink_speed_change_order isco on isco.id = cdie.speed_change_order
                            left join internets_eastlink_move_order imo on imo.id = cdie.move_order
                            left join internets_eastlink_swap_order iso ON iso.id = cdie.swap_order
                            left join contacts_customer_details ccd on cdie.id = ccd.internet_id
                            left join customer_details_subscriptions cds on ccd.id = cds.customer_details_id 
                            where cdie.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                internetExist: res.length > 0,
                internetId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Internet Exist -> ", error);
            return {
                internetExist: 0,
                internetId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async updateDetails(sfInternetDetail, internetDetails, type) {
        try {
            const customerServices = new CustomerWebhookServices();
            const { LastModifiedDate } = sfInternetDetail;
            const { id } = internetDetails;
            let detailField, getDetailsId, table, deleteField;

            switch (type) {
                case 'Creation':
                    detailField = 'Creation_Order__c';
                    getDetailsId = customerServices.getOrderDetailsId.bind(customerServices);
                    table = 'internets_eastlink_creation_order';
                    deleteField = 'creation_order';
                    break;
                case 'Tech':
                    detailField = 'Latest_Tech_Appointment__c';
                    getDetailsId = customerServices.getTechDetailsId.bind(customerServices);
                    table = 'internets_eastlink_tech_appointment';
                    deleteField = 'tech_appointment';
                    break;
                case 'Disconnect':
                    detailField = 'Latest_Disconnect_Order__c';
                    getDetailsId = customerServices.getOrderDetailsId.bind(customerServices);
                    table = 'internets_eastlink_disconnect_order';
                    deleteField = 'disconnect_order';
                    break;
                case 'SpeedChange':
                    detailField = 'Latest_Speed_Change_Order__c';
                    getDetailsId = customerServices.getOrderDetailsId.bind(customerServices);
                    table = 'internets_eastlink_speed_change_order';
                    deleteField = 'speed_change_order';
                    break;
                case 'MoveOrder':
                    detailField = 'Latest_Move_Order__c';
                    getDetailsId = customerServices.getOrderDetailsId.bind(customerServices);
                    table = 'internets_eastlink_move_order';
                    deleteField = 'move_order';
                    break;
                case 'SwapOrder':
                    detailField = 'Latest_Modem_Swap_Order__c';
                    getDetailsId = customerServices.getOrderDetailsId.bind(customerServices);
                    table = 'internets_eastlink_swap_order';
                    deleteField = 'swap_order';
                    break;
                default:
                    return;
            }

            const detailId = sfInternetDetail[detailField];
            let detailsId = null;
            if (detailId != "") {
                detailsId = await getDetailsId(detailId, table);
            }

            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
                UPDATE customer_details_internets_eastlink 
                SET ${deleteField} = ?, updatedAt = ?, sf_updatedAt = ? 
                WHERE id = ?`;

            await this.executeQuery(updateQuery, [detailsId, updatedTime, formatedDate, id]);

            if (internetDetails[deleteField]) {
                const { dataCount } = await customerServices.checkDataCount("customer_details_internets_eastlink", deleteField, internetDetails[deleteField]);
                if (!dataCount) await customerServices.deleteQuery(table, internetDetails[deleteField]);
            }
        } catch (error) {
            console.error(`Error checkAndUpdate${type}Details -> `, error);
        }
    }

    async createInternetDetails(serviceDetails) {
        try {
            if (!serviceDetails.CP_Speed__c) return;
            const planServices = new PlanServices();
            const sfUpdatedAt = getCurrentAtlanticTime(serviceDetails.LastModifiedDate);
            const insertData = { sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, sf_name: serviceDetails.Name };

            insertData.plan_speed = serviceDetails.CP_Speed__c;
            if (serviceDetails?.Live_Date__c) insertData.live_date = serviceDetails.Live_Date__c;
            if (serviceDetails?.Disconnected_Date__c) insertData.disconnected_date = serviceDetails.Disconnected_Date__c;

            // Status
            insertData.internal_processing_status = serviceDetails.CP_Status_Internal_Processing__c;
            insertData.ship_package_status = serviceDetails.CP_Status_Ship_Package__c;
            insertData.modem_installation_status = serviceDetails.CP_Status_Modem_Installation__c;
            insertData.modem_activation_status = serviceDetails.CP_Status_Modem_Activation__c;

            const customerServices = new CustomerWebhookServices();

            const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order, swap_order } = await customerServices.getAllInternetOrderDetails(serviceDetails);

            if (creation_order) insertData.creation_order = creation_order;
            if (tech_appointment) insertData.tech_appointment = tech_appointment;
            if (disconnect_order) insertData.disconnect_order = disconnect_order;
            if (speed_change_order) insertData.speed_change_order = speed_change_order;
            if (move_order) insertData.move_order = move_order;
            if (swap_order) insertData.swap_order = swap_order;

            const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
            if (internetPlanDetails?.length) {
                const getPrice = internetPlanDetails.find(internet => internet.speed === serviceDetails.CP_Speed__c);
                if (getPrice?.api_name) insertData.plan_name = getPrice.api_name;
            }
            await customerServices.insertRecord("customer_details_internets_eastlink", insertData, serviceDetails.CreatedDate);
        } catch (error) {
            console.error(`Error createInternetDetails -> `, error);
        }
    }
}

module.exports = InternetWebhookServices;
