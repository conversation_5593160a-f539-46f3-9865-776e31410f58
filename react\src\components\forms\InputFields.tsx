import React, { useState, ChangeEvent } from "react";
import { InputFieldProps } from "../../typings/typing";
import { EyeCloseIcon, EyeIcon, InvalidIcon } from "../../assets/Icons";

const InputFields: React.FC<InputFieldProps> = (props) => {
  const [visible, setVisible] = useState<boolean>(false);
  const {
    type = "text",
    title,
    placeHolder,
    changeEvent,
    isFocus = false,
    isReadOnly = false,
    isDisabled = false,
    isErrorMsg = null,
    attributes,
    valid,
    className,
  } = props;

  // Function to handle input change event
  const checkEvent = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.type === "number") {
      const numericValue = parseFloat(e.target.value);
      if (numericValue < 0) {
        // Optionally provide feedback to the user about invalid input.
        return false;
      }
    }
    if (changeEvent) {
      changeEvent(e);
    }
  };

  return (
    <>
      <div
        className={`input-wrapper ${
          isErrorMsg || valid ? "error" : ""
        } flex flex-col gap-2`}
      >
        {title && (
          <div>
            <label
              className={` font-normal ${
                isReadOnly ? "input-label-read-only" : "input-label"
              } font-medium text-base`}
            >
              {title}
            </label>
          </div>
        )}

        {type === "password" ? (
          <div className="input-default-password relative">
            <input
              onChange={checkEvent}
              type={visible ? "text" : type}
              className={`input-default password ${className}`}
              placeholder={placeHolder}
              autoFocus={isFocus}
              readOnly={isReadOnly}
              disabled={isDisabled}
              {...attributes}
            ></input>
            <span
              className="eye-icon absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer z-[5]"
              onClick={() => setVisible((prev) => !prev)}
            >
              {visible ? <EyeCloseIcon /> : <EyeIcon />}
              {/* <Icons img={visible ? "passwordEye-slash-icon" : "passwordEye-icon"} /> */}
            </span>
          </div>
        ) : (
          <div>
            <input
              onChange={checkEvent}
              type={type}
              className={
                isDisabled
                  ? "input-default !border-none !cursor-not-allowed"
                  : `${
                      isReadOnly ? "!bg-white !shadow-none !border-none" : ""
                    } input-default ${className}`
              }
              placeholder={placeHolder}
              autoFocus={isFocus}
              readOnly={isReadOnly}
              disabled={isDisabled}
              {...attributes}
            ></input>
          </div>
        )}
        {isErrorMsg && isErrorMsg != null && (
          <p className="error-text flex gap-2 items-center pt-1.5 text-sm">
            <span className="icon">
              <InvalidIcon />
            </span>
            {isErrorMsg}
          </p>
        )}
      </div>
    </>
  );
};

export default InputFields;
