@use "./mixin.scss";
@use "./variables.scss";

.day {
  text-align: center !important;
  padding: 8px;
  border-radius: 7px !important;
  font-size: 16px !important;
  max-height: 36px !important;
  outline: none !important;
  max-width: 36px !important;
}
.day :hover {
  outline: none !important;
}

.today {
  color: red;
}
.other {
  color: #a9a9a9;
}
.range {
  // background-color: white !important;
  // transition: 0.3s;
  // box-shadow: 0px 4px 100px 0px #00000040;

  &:hover {
    box-shadow: 0px 4px 16px 0px #d356e340;
  }
}
.current {
  color: #a9a9a9;
}
abbr:where([title]) {
  text-decoration: none !important;
}
.selected {
  background-color: #e8d8ff !important;
}
.react-calendar__month-view__weekdays {
  margin: 0 auto;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.react-calendar__navigation {
  text-align: center;
  display: flex;
  align-items: center !important;
  justify-content: center !important;
  margin-bottom: 30px;
  padding: 0px 10px;
}
.react-calendar__navigation__label {
  margin-left: 10px;
  margin-right: 10px;
  text-align: center;
  font-weight: 500;

  @include mixin.mq("tablet-up") {
    flex-grow: unset !important;
  }
}
.react-calendar__month-view__days {
  row-gap: 20px;
  column-gap: calc((100% - 252px) / 7);
  margin-top: 30px;
  align-items: center;
  justify-content: center;
  @include mixin.mq("phone-and-tablet") {
    row-gap: 10px;
    margin-top: 10px;
  }
  @include mixin.mq("mid-lap") {
    row-gap: 10px;
    margin-top: 10px;
  }
  @include mixin.mq("lap-and-up") {
    row-gap: 20px;
  }
}
.react-calendar {
  background: #fbf8ff;
  border-radius: 20px;
  padding: 30px 0px !important;
  width: 100% !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  @include mixin.mq("phone-and-tablet") {
    padding: 0 10px;
  }
  @include mixin.mq("mid-lap") {
    padding: 15px 0px !important;
  }
  @include mixin.mq("lap-and-up") {
    padding: 30px !important;
  }
}
