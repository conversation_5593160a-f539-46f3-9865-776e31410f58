interface MoveOrder {
  stage: string;
  street_number: string;
  street_name: string;
  city: string;
  province: string;
  postal_code: string;
  unit?: string; // Optional property if it may not exist
  requested_install_date: string; // Or Date, depending on what format you use
  install_date: string; // Or Date
  install_time: string; // Or a more specific type if necessary
}

interface MoveOrderStatusProps {
  isMoveOrder: MoveOrder;
}

const MoveOrderStatus: React.FC<MoveOrderStatusProps> = ({ isMoveOrder }) => {
  if (!isMoveOrder) return null;

  const {
    stage,
    street_number,
    street_name,
    city,
    province,
    postal_code,
    unit,
    requested_install_date,
  } = isMoveOrder;

  const formattedAddress = unit
    ? `${unit}-${street_number}, ${street_name}, ${city}, ${province}, ${postal_code}`
    : `${street_number}, ${street_name}, ${city}, ${province}, ${postal_code}`;

  //  Move order case 1
  if (stage === "Eligible For Submission" || stage === "Pre Response") {
    return (
      <div className="mt-4 text-center">
        We are processing your request to move your services to&nbsp;
        {formattedAddress}
        &nbsp;on&nbsp;
        {requested_install_date}. We'll let you know once this is confirmed.
        <div>No action is required on your end.</div>
      </div>
    );
  }

  return null;
};

export default MoveOrderStatus;
