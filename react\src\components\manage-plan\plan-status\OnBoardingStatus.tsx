import React from "react";
import { HomeIcon } from "../../../assets/Icons";
import StatusCard from "../StatusCard";
import useMediaQuery from "../../../hooks/MediaQueryHook";

interface Status {
  internal_processing_status?: string;
  ship_package_status?: string;
  modem_activation_status?: string;
  modem_installation_status?: string;
}

interface OnBoardingStatusProps {
  status?: Status;
  isMoveOrder?:any
}

const OnBoardingStatus: React.FC<OnBoardingStatusProps> = ({ status }) => {
  const isDesktop = useMediaQuery("(min-width:1025px)");
  return (
    <>
      <StatusCard
        Icon={<HomeIcon width={isDesktop ? "100%" : 20} />}
        label="Internal processing"
        status={status?.internal_processing_status}
      />
      <StatusCard
        Icon={"🚚"}
        label="Ship package"
        status={status?.ship_package_status}
      />
      <StatusCard
        Icon={"🤝"}
        label="Modem activation"
        status={status?.modem_activation_status}
      />
      <StatusCard
        Icon={"🔌"}
        label="Modem installation"
        status={status?.modem_installation_status}
      />
    </>
  );
};

export default OnBoardingStatus;
