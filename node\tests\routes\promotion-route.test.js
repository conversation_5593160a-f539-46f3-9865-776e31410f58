const request = require('supertest');
const express = require('express');
const promotionRoute = require('../../routes/promotion-route');

// Mock the promotion controller
jest.mock('../../controllers/promotion-controller', () => ({
  fetchPromotionsList: jest.fn((req, res) => res.status(200).json({ 
    message: 'Promotions list retrieved successfully', 
    data: {
      promotions: [
        {
          id: 'promo_1',
          name: 'New Customer Special',
          description: 'Get 50% off your first 3 months',
          discount: 50,
          discountType: 'percentage',
          validUntil: '2024-12-31',
          eligibleServices: ['internet', 'tv'],
          isActive: true
        },
        {
          id: 'promo_2',
          name: 'Bundle Savings',
          description: 'Save $20/month with Internet + TV bundle',
          discount: 20,
          discountType: 'fixed',
          validUntil: '2024-06-30',
          eligibleServices: ['internet', 'tv'],
          isActive: true
        }
      ]
    }
  })),
  fetchEligibleLocation: jest.fn((req, res) => res.status(200).json({ 
    message: 'Eligible locations retrieved successfully',
    data: {
      locations: [
        {
          id: 'loc_1',
          address: '123 Main St, Halifax, NS',
          postalCode: 'B3H 1A1',
          serviceAvailability: {
            internet: true,
            tv: true,
            phone: true
          },
          eligiblePromotions: ['promo_1', 'promo_2']
        },
        {
          id: 'loc_2',
          address: '456 Oak Ave, Sydney, NS',
          postalCode: 'B1P 2C3',
          serviceAvailability: {
            internet: true,
            tv: false,
            phone: true
          },
          eligiblePromotions: ['promo_1']
        }
      ]
    }
  })),
  claimOffer: jest.fn((req, res) => res.status(201).json({ 
    message: 'Offer claimed successfully',
    data: {
      claimId: 'claim_12345',
      promotionId: 'promo_1',
      customerId: '12345',
      claimedAt: '2024-01-18T10:30:00Z',
      validUntil: '2024-04-18T10:30:00Z',
      status: 'active'
    }
  }))
}));

describe('Promotion Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    promotionRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/promotions/list', () => {
    it('should fetch promotions list successfully', async () => {
      const response = await request(app)
        .get('/api/v1/promotions/list')
        .expect(200);

      expect(response.body.message).toBe('Promotions list retrieved successfully');
      expect(response.body.data).toHaveProperty('promotions');
      expect(Array.isArray(response.body.data.promotions)).toBe(true);
      expect(response.body.data.promotions.length).toBeGreaterThan(0);
      
      // Verify promotion object structure
      const firstPromotion = response.body.data.promotions[0];
      expect(firstPromotion).toHaveProperty('id');
      expect(firstPromotion).toHaveProperty('name');
      expect(firstPromotion).toHaveProperty('description');
      expect(firstPromotion).toHaveProperty('discount');
      expect(firstPromotion).toHaveProperty('discountType');
      expect(firstPromotion).toHaveProperty('validUntil');
      expect(firstPromotion).toHaveProperty('eligibleServices');
      expect(firstPromotion).toHaveProperty('isActive');
      
      // Verify data types
      expect(typeof firstPromotion.id).toBe('string');
      expect(typeof firstPromotion.name).toBe('string');
      expect(typeof firstPromotion.description).toBe('string');
      expect(typeof firstPromotion.discount).toBe('number');
      expect(typeof firstPromotion.discountType).toBe('string');
      expect(typeof firstPromotion.isActive).toBe('boolean');
      expect(Array.isArray(firstPromotion.eligibleServices)).toBe(true);
    });

    it('should handle promotions list request with query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/promotions/list')
        .query({ activeOnly: 'true', serviceType: 'internet' })
        .expect(200);

      expect(response.body.message).toBe('Promotions list retrieved successfully');
    });

    it('should handle promotions list request with authentication', async () => {
      const response = await request(app)
        .get('/api/v1/promotions/list')
        .set('Authorization', 'Bearer fake-jwt-token')
        .expect(200);

      expect(response.body.message).toBe('Promotions list retrieved successfully');
    });
  });

  describe('POST /api/v1/promotions/locations', () => {
    it('should fetch eligible locations successfully', async () => {
      const locationData = {
        postalCode: 'B3H 1A1',
        serviceTypes: ['internet', 'tv'],
        promotionIds: ['promo_1', 'promo_2']
      };

      const response = await request(app)
        .post('/api/v1/promotions/locations')
        .send(locationData)
        .expect(200);

      expect(response.body.message).toBe('Eligible locations retrieved successfully');
      expect(response.body.data).toHaveProperty('locations');
      expect(Array.isArray(response.body.data.locations)).toBe(true);
      
      // Verify location object structure
      const firstLocation = response.body.data.locations[0];
      expect(firstLocation).toHaveProperty('id');
      expect(firstLocation).toHaveProperty('address');
      expect(firstLocation).toHaveProperty('postalCode');
      expect(firstLocation).toHaveProperty('serviceAvailability');
      expect(firstLocation).toHaveProperty('eligiblePromotions');
      
      // Verify serviceAvailability structure
      expect(firstLocation.serviceAvailability).toHaveProperty('internet');
      expect(firstLocation.serviceAvailability).toHaveProperty('tv');
      expect(firstLocation.serviceAvailability).toHaveProperty('phone');
      
      // Verify data types
      expect(typeof firstLocation.serviceAvailability.internet).toBe('boolean');
      expect(typeof firstLocation.serviceAvailability.tv).toBe('boolean');
      expect(typeof firstLocation.serviceAvailability.phone).toBe('boolean');
      expect(Array.isArray(firstLocation.eligiblePromotions)).toBe(true);
    });

    it('should handle eligible locations request with minimal data', async () => {
      const minimalData = {
        postalCode: 'B1P 2C3'
      };

      const response = await request(app)
        .post('/api/v1/promotions/locations')
        .send(minimalData)
        .expect(200);

      expect(response.body.message).toBe('Eligible locations retrieved successfully');
    });

    it('should handle eligible locations request with invalid postal code', async () => {
      const invalidData = {
        postalCode: 'INVALID',
        serviceTypes: ['internet']
      };

      await request(app)
        .post('/api/v1/promotions/locations')
        .send(invalidData)
        .expect(200); // Mocked to return success
    });
  });

  describe('POST /api/v1/promotions/claim', () => {
    it('should claim offer successfully', async () => {
      const claimData = {
        promotionId: 'promo_1',
        customerId: '12345',
        locationId: 'loc_1',
        serviceTypes: ['internet', 'tv']
      };

      const response = await request(app)
        .post('/api/v1/promotions/claim')
        .send(claimData)
        .expect(201);

      expect(response.body.message).toBe('Offer claimed successfully');
      expect(response.body.data).toHaveProperty('claimId');
      expect(response.body.data).toHaveProperty('promotionId');
      expect(response.body.data).toHaveProperty('customerId');
      expect(response.body.data).toHaveProperty('claimedAt');
      expect(response.body.data).toHaveProperty('validUntil');
      expect(response.body.data).toHaveProperty('status');
      
      // Verify data values
      expect(response.body.data.promotionId).toBe(claimData.promotionId);
      expect(response.body.data.customerId).toBe(claimData.customerId);
      expect(response.body.data.status).toBe('active');
    });

    it('should handle claim offer with different promotion', async () => {
      const claimData = {
        promotionId: 'promo_2',
        customerId: '67890',
        locationId: 'loc_2',
        serviceTypes: ['internet']
      };

      const response = await request(app)
        .post('/api/v1/promotions/claim')
        .send(claimData)
        .expect(201);

      expect(response.body.message).toBe('Offer claimed successfully');
    });

    it('should handle claim offer with missing required fields', async () => {
      const incompleteData = {
        promotionId: 'promo_1'
        // Missing customerId and other required fields
      };

      await request(app)
        .post('/api/v1/promotions/claim')
        .send(incompleteData)
        .expect(201); // Mocked to return success
    });

    it('should handle claim offer with authentication headers', async () => {
      const claimData = {
        promotionId: 'promo_1',
        customerId: '12345',
        locationId: 'loc_1',
        serviceTypes: ['internet', 'tv']
      };

      const response = await request(app)
        .post('/api/v1/promotions/claim')
        .send(claimData)
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Customer-ID', '12345')
        .expect(201);

      expect(response.body.message).toBe('Offer claimed successfully');
    });
  });
});
