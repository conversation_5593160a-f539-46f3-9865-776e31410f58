import React from "react";
import Button from "../../common/Button";
import CustomCalendar from "../../common/CustomCalendar";
// import CustomDropdown from "../../common/CustomDropdown";
type Props = {
  setServiceDateSelectorDate: (value: Date) => void;
  closeHandler: () => void;
  handleSubmit: () => void;
  serviceChangeMessage: string;
  selectedDate: Date | null | undefined;
  description: string;
  showDropDown?: boolean;
  updateShippingAddressDropDown?: string;
  setUpdateShippingAddressDropDown?: (text: string) => void;
  disableWeekEnds?: boolean;
  disableHolidays?: boolean;
  disableNext3WorkingDays?: boolean;
};
// const options = [
//   { id: 1, label: "None", value: "" },
//   { id: 2, label: "Update Right Away", value: "Update Right Away" },
//   {
//     id: 3,
//     label: "Update on Requested Move Date",
//     value: "Update on Requested Move Date",
//   },
//   { id: 4, label: "Do Not Change", value: "Do Not Change" },
// ];

const ServiceDateSelector: React.FC<Props> = ({
  selectedDate,
  description,
  serviceChangeMessage,
  setServiceDateSelectorDate,
  handleSubmit,
  closeHandler,
  // showDropDown,
  // updateShippingAddressDropDown,
  // setUpdateShippingAddressDropDown,
  disableWeekEnds,
  disableHolidays,
  disableNext3WorkingDays,
}) => {
  // const [changeMailAddress, setChangeMailAddress] = useState(true);

  return (
    <div className="flex gap-5 flex-col">
      {
        <div>
          <p>{description}</p>
        </div>
      }
      <div className="flex justify-center">
        <p className="uppercase lg:text-base text-sm font-medium">
          {serviceChangeMessage}
        </p>
      </div>
      <div>
        <CustomCalendar
          selectedDate={selectedDate}
          setDate={setServiceDateSelectorDate}
          minDate={new Date()}
          maxDate={new Date(new Date().setMonth(new Date().getMonth() + 2))}
          disableWeekEnds={disableWeekEnds}
          disableHolidays={disableHolidays}
          disableNext3WorkingDays={disableNext3WorkingDays}
        />
      </div>
      {/* <div className="flex gap-2.5 relative">
        <input
          id="for-mail-address"
          type="checkbox"
          name="changeMailAddress"
          onChange={() => setChangeMailAddress(!changeMailAddress)}
          className="input-radio"
          checked={changeMailAddress}
        />
        <label htmlFor="for-mail-address" className="input-radio-label">
          I like my mailing address to update on the same date as well.
        </label>
      </div> */}
      {/* {showDropDown && (
        <div className="w-full">
          <CustomDropdown
            placeholder="Shipping address"
            name="province"
            value={updateShippingAddressDropDown}
            onChange={(e) => setUpdateShippingAddressDropDown(e.target.value)}
            options={options}
            defaultValue={
              updateShippingAddressDropDown ? updateShippingAddressDropDown : ""
            }
          />
        </div>
      )} */}
      <div className="flex gap-2.5 lg:flex-row flex-col">
        <div className="basis-full">
          <Button
            title="Go back"
            btnType="transparent"
            type="button"
            clickEvent={closeHandler}
            
          />
        </div>
        <div className="basis-full">
          <Button
            title="Next"
            clickEvent={handleSubmit}
            className="disabled:opacity-50"
            attributes={{
              disabled: !selectedDate,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ServiceDateSelector;
