const mysql = require('mysql');
const CONFIG = require("./config");
const { getCurrentAtlanticTime } = require('./helper/custom-helper');

let pool;
let retryCount = 0;
const maxRetries = 5;

function createPool() {
    pool = mysql.createPool({
        host: CONFIG.database.host,
        user: CONFIG.database.user,
        database: CONFIG.database.db,
        password: CONFIG.database.password,
        port: CONFIG.database.port,
        connectionLimit: 10 // You can adjust the connection limit as needed
    });

    pool.on('connection', (connection) => {
        const conObj = {
            "log.level": "INFO",
            message: "Connection established with the database",
            timestamp: getCurrentAtlanticTime()
        }
        // console.log(conObj);
        retryCount = 0; // Reset retry count on successful connection
    });

    pool.on('error', (err) => {
        console.error('MySQL pool error:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
            handleReconnect(); // Reconnect on connection loss
        } else {
            throw err;
        }
    });

    pool.on('close', function (err) {
        console.error(new Date(), 'MySQL close', err);
    });

}

function handleReconnect() {
    if (retryCount >= maxRetries) {
        console.error(`Reached maximum retry limit of ${maxRetries}. Connection failed.`);
        return;
    }

    retryCount++;
    console.log(`Retrying to connect (${retryCount}/${maxRetries})...`);
    setTimeout(() => {
        createPool();
    }, 2000); // Retry connection after 2 seconds
}

createPool();

const con = new Promise((resolve, reject) => {
    pool.query(`SELECT id FROM contacts`, (err, res) => {
        if (err) reject(err);
        else resolve(res);
    });
});

module.exports = pool;
