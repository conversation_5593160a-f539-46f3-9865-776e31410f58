const express = require("express");
const cors = require('cors');
const CONFIG = require("./config");
const { exceptionHandling } = require('./helper/exceptionHandling');
const { getCurrentAtlanticTime } = require("./helper/custom-helper");
require("./db")
// Define server port
const PORT = CONFIG.NODE_ENV == "local" ? CONFIG.PORT : 4000;
const ALLOWED_IP = CONFIG.ALLOWED_IP;

const app = express();
require("./cron/cron")()

// Middleware to parse the request body as JSON
app.use(express.json());

// Middleware to parse the request body as URL-encoded data
app.use(express.urlencoded({ extended: true, limit: 'mb' }));

// Middleware to enable Cross-Origin Resource Sharing (CORS)
app.use(cors());

// Middleware to define a route for the root URL
app.get('/', (req, res) => {
    res.status(200).send({ message: `SYNC PROCESS RUNNING ON PORT ${PORT}` });
});

// Load the routes
require("./routes/sync-route")(app);

app.use(exceptionHandling);

// Start server
app.listen(PORT, () => {
    const conObj = {
        "log.level": "INFO",
        message: `APP LISTENING ON PORT ${PORT}`,
        timestamp: getCurrentAtlanticTime()
    }
    console.log(conObj);
});