const Joi = require('joi');

const customerAddressValidation = Joi.object().keys({
    customer_details_id: Joi.number()
        .required()
        .messages({
            'any.required': 'Customer details id field is required.'
        }),
    address_id: Joi.number()
        .required()
        .messages({
            'any.required': 'Address id field is required.'
        }),
    street_number: Joi.string()
        .trim()
        .required()
        .min(1)
        .max(50)
        .messages({
            'any.required': 'Street number field is required.',
            'string.min': 'Street number must be at least {#limit} characters long.',
            'string.max': 'Street number cannot exceed {#limit} characters.'
        }),
    street_name: Joi.string()
        .trim()
        .required()
        .min(1)
        .max(50)
        .messages({
            'any.required': 'Street name field is required.',
            'string.min': 'Street name must be at least {#limit} characters long.',
            'string.max': 'Street name cannot exceed {#limit} characters.'
        }),
    // suit_unit: Joi.string()
    //     .trim()
    //     .required()
    //     .min(1)
    //     .max(50)
    //     .messages({
    //         'any.required': 'Appartment, suite or unit number field is required.',
    //         'string.min': 'Appartment, suite or unit number must be at least {#limit} characters long.',
    //         'string.max': 'Appartment, suite or unit number cannot exceed {#limit} characters.'
    //     }),
    town: Joi.string()
        .trim()
        .required()
        .min(1)
        .max(50)
        .messages({
            'any.required': 'City field is required.',
            'string.min': 'City must be at least {#limit} characters long.',
            'string.max': 'City cannot exceed {#limit} characters.'
        }),
    province: Joi.string()
        .trim()
        .required()
        .min(1)
        .max(50)
        .messages({
            'any.required': 'Province field is required.',
            'string.min': 'Province must be at least {#limit} characters long.',
            'string.max': 'Province cannot exceed {#limit} characters.'
        }),
    postal_code: Joi.string()
        .trim()
        .min(5)
        .max(8)
        .required()
        .messages({
            'string.min': 'Postal code must be at least {#limit} characters long',
            'string.max': 'Postal code cannot be longer than {#limit} characters',
            'any.required': 'Postal code is required'
        })
});

module.exports = { customerAddressValidation };
