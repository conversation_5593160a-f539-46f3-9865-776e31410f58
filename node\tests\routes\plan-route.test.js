const request = require('supertest');
const express = require('express');
const planRoute = require('../../routes/plan-route');

// Mock the plan controller
jest.mock('../../controllers/plan-controller', () => ({
  managePlan: jest.fn((req, res) => res.status(200).json({ 
    message: 'Plan management data retrieved successfully', 
    data: {
      currentPlans: {
        internet: { id: 'internet_100', name: 'Internet 100', price: 69.99 },
        tv: { id: 'basic_tv', name: 'Basic TV', price: 29.99 },
        phone: { id: 'basic_phone', name: 'Basic Phone', price: 19.99 }
      },
      availableUpgrades: [
        { id: 'internet_300', name: 'Internet 300', price: 89.99 },
        { id: 'premium_tv', name: 'Premium TV', price: 49.99 }
      ]
    }
  })),
  updateAddress: jest.fn((req, res) => res.status(200).json({ 
    message: 'Address updated successfully',
    data: {
      addressId: 'addr_12345',
      serviceAddress: '123 Updated St, Halifax, NS',
      effectiveDate: '2024-02-01'
    }
  })),
  getAddress: jest.fn((req, res) => res.status(200).json({ 
    message: 'Address retrieved successfully',
    data: {
      serviceAddress: '123 Main St, Halifax, NS B3H 1A1',
      mailingAddress: '456 Mailing Ave, Halifax, NS B3H 2B2',
      billingAddress: '789 Billing Blvd, Halifax, NS B3H 3C3'
    }
  })),
  getPlanDetails: jest.fn((req, res) => res.status(200).json({ 
    message: 'Plan details retrieved successfully',
    data: {
      planId: 'internet_100',
      planName: 'Internet 100',
      price: 69.99,
      features: ['100 Mbps Download', '10 Mbps Upload', 'Unlimited Data'],
      addons: [
        { id: 'addon_1', name: 'Static IP', price: 9.99 },
        { id: 'addon_2', name: 'Enhanced Security', price: 4.99 }
      ]
    }
  }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  customerAddressValidation: {},
  planDetails: {}
}));

describe('Plan Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    planRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/plan/manage/:custDetailsId', () => {
    it('should retrieve plan management data successfully', async () => {
      const custDetailsId = '12345';

      const response = await request(app)
        .get(`/api/v1/plan/manage/${custDetailsId}`)
        .expect(200);

      expect(response.body.message).toBe('Plan management data retrieved successfully');
      expect(response.body.data).toHaveProperty('currentPlans');
      expect(response.body.data).toHaveProperty('availableUpgrades');
      expect(response.body.data.currentPlans).toHaveProperty('internet');
      expect(response.body.data.currentPlans).toHaveProperty('tv');
      expect(response.body.data.currentPlans).toHaveProperty('phone');
    });

    it('should handle plan management request with different customer ID', async () => {
      const custDetailsId = '67890';

      const response = await request(app)
        .get(`/api/v1/plan/manage/${custDetailsId}`)
        .expect(200);

      expect(response.body.message).toBe('Plan management data retrieved successfully');
    });

    it('should handle plan management request with invalid customer ID', async () => {
      const custDetailsId = 'invalid-id';

      await request(app)
        .get(`/api/v1/plan/manage/${custDetailsId}`)
        .expect(200); // Mocked to return success
    });
  });

  describe('PUT /api/v1/plan/update-address', () => {
    it('should update address successfully', async () => {
      const addressData = {
        serviceAddress: {
          street: '123 Updated St',
          city: 'Halifax',
          province: 'NS',
          postalCode: 'B3H 1A1'
        },
        mailingAddress: {
          street: '456 Updated Ave',
          city: 'Halifax',
          province: 'NS',
          postalCode: 'B3H 2B2'
        },
        customerId: '12345'
      };

      const response = await request(app)
        .put('/api/v1/plan/update-address')
        .send(addressData)
        .expect(200);

      expect(response.body.message).toBe('Address updated successfully');
      expect(response.body.data).toHaveProperty('addressId');
      expect(response.body.data).toHaveProperty('serviceAddress');
      expect(response.body.data).toHaveProperty('effectiveDate');
    });

    it('should handle address update with partial data', async () => {
      const partialAddressData = {
        serviceAddress: {
          street: '789 Partial St',
          city: 'Sydney',
          province: 'NS'
        },
        customerId: '12345'
      };

      const response = await request(app)
        .put('/api/v1/plan/update-address')
        .send(partialAddressData)
        .expect(200);

      expect(response.body.message).toBe('Address updated successfully');
    });

    it('should handle address update with invalid data', async () => {
      const invalidData = {
        customerId: '12345'
        // Missing address data
      };

      await request(app)
        .put('/api/v1/plan/update-address')
        .send(invalidData)
        .expect(200); // Mocked to return success
    });
  });

  describe('GET /api/v1/plan/details', () => {
    it('should retrieve plan details successfully', async () => {
      const response = await request(app)
        .get('/api/v1/plan/details')
        .query({ planId: 'internet_100', customerId: '12345' })
        .expect(200);

      expect(response.body.message).toBe('Plan details retrieved successfully');
      expect(response.body.data).toHaveProperty('planId');
      expect(response.body.data).toHaveProperty('planName');
      expect(response.body.data).toHaveProperty('price');
      expect(response.body.data).toHaveProperty('features');
      expect(response.body.data).toHaveProperty('addons');
      expect(Array.isArray(response.body.data.features)).toBe(true);
      expect(Array.isArray(response.body.data.addons)).toBe(true);
    });

    it('should handle plan details request with different plan ID', async () => {
      const response = await request(app)
        .get('/api/v1/plan/details')
        .query({ planId: 'premium_tv', customerId: '67890' })
        .expect(200);

      expect(response.body.message).toBe('Plan details retrieved successfully');
    });

    it('should handle plan details request without query parameters', async () => {
      await request(app)
        .get('/api/v1/plan/details')
        .expect(200); // Mocked to return success
    });

    it('should handle plan details request with invalid plan ID', async () => {
      const response = await request(app)
        .get('/api/v1/plan/details')
        .query({ planId: 'invalid-plan', customerId: '12345' })
        .expect(200);

      expect(response.body.message).toBe('Plan details retrieved successfully');
    });
  });
});
