const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("customer_details_phone", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    plan_name: {
      type: Sequelize.STRING(100),
      comment: "Associated with level from sf."
    },
    api_name: {
      type: Sequelize.STRING(100),
      comment: "Associated with api_name field of json object in s3."
    },
    plan_price: {
      type: Sequelize.STRING(20),
      comment: "Associated with price field of json object in s3."
    },
    add_ons: {
      type: Sequelize.STRING(250),
      allowNull: true,
      comment: "Not specified."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of Phone__c in sf."
    },
    sf_response_type: {
      type: Sequelize.ENUM('create', 'cancel'),
      defaultValue: null,
      comment: "Initially the value was null, and this column is to track the error type during creation of Phone order."
    },
    sf_response_status: {
      type: Sequelize.ENUM('pending', 'success', 'failure'),
      defaultValue: 'pending',
      comment: "Initially the value was pending"
    },
    retries: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
      comment: "How many times getting error during update in salesforce,"
    },
    sf_error_log: {
      type: Sequelize.TEXT,
      comment: "Reason for getting error from salesforce."
    },
    sf_phone_type: {
      type: Sequelize.TEXT,
      comment: "Store phone number in case of error from salesforce."
    },
    account_status: {
      type: Sequelize.ENUM('ACTIVE', 'DISABLED', 'SUSPENDED', 'DELETED'),
      defaultValue: null,
      comment: "Associated with Account_Status__c field of Phone__c in sf."
    },
    requested_cancellation_date: {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: "Associated with Requested_Cancellation_Date__c field of Phone__order__c in sf."
    },  
    service_start_date: {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: "Associated with Service_Start_Date__c field of Phone__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}