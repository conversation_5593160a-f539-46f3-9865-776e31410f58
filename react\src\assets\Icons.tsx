import React from "react";
import { IconProps } from "../typings/typing";

export const HomeMenuIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10.833 15.833h5V8.315L10 3.778 4.167 8.315v7.518h5v-5h1.666zm6.667.833c0 .46-.373.834-.833.834H3.333a.833.833 0 0 1-.833-.834V7.907c0-.257.119-.5.322-.657l6.666-5.186a.83.83 0 0 1 1.024 0l6.666 5.186c.203.157.322.4.322.657z" />
  </svg>
);

export const BillingMenuIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M15.004 5.833h2.5c.46 0 .834.373.834.834v10c0 .46-.373.833-.834.833h-15a.833.833 0 0 1-.833-.833V3.333c0-.46.373-.833.833-.833h12.5zM3.338 7.5v8.333H16.67V7.5zm0-3.333v1.666h10V4.167zm9.166 6.666h2.5V12.5h-2.5z" />
  </svg>
);

export const ReferralsMenuIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 15 15"
    fill="none"
    {...props}
  >
    <path
      d="M5.33337 7.60029C5.33337 8.11363 5.73337 8.53363 6.22004 8.53363H7.22004C7.64671 8.53363 7.99337 8.16696 7.99337 7.72029C7.99337 7.23363 7.78004 7.06029 7.46671 6.94696L5.86671 6.38696C5.54671 6.27363 5.33337 6.10029 5.33337 5.61363C5.33337 5.16696 5.68004 4.80029 6.10671 4.80029H7.10671C7.60004 4.80696 8.00004 5.22029 8.00004 5.73363"
      stroke={props.color || "#292D32"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66663 8.56689V9.06023"
      stroke={props.color || "#292D32"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66663 4.27344V4.79344"
      stroke={props.color || "#292D32"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66004 11.9868C9.60188 11.9868 11.9867 9.602 11.9867 6.66016C11.9867 3.71833 9.60188 1.3335 6.66004 1.3335C3.7182 1.3335 1.33337 3.71833 1.33337 6.66016C1.33337 9.602 3.7182 11.9868 6.66004 11.9868Z"
      stroke={props.color || "#292D32"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.65332 13.2532C9.25332 14.0998 10.2333 14.6532 11.3533 14.6532C13.1733 14.6532 14.6533 13.1732 14.6533 11.3532C14.6533 10.2465 14.1067 9.2665 13.2733 8.6665"
      stroke={props.color || "#292D32"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SupportMenuIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M18.333 14.169a5 5 0 0 1-3.927 4.882l-.532-1.595a3.34 3.34 0 0 0 2.347-1.623h-2.054c-.92 0-1.667-.746-1.667-1.666v-3.334c0-.92.746-1.666 1.667-1.666h2.448a6.668 6.668 0 0 0-13.23 0h2.448c.92 0 1.667.746 1.667 1.666v3.334c0 .92-.746 1.666-1.667 1.666h-2.5c-.92 0-1.666-.746-1.666-1.666V10a8.333 8.333 0 0 1 16.666 0zm-1.666-.002v-3.334h-2.5v3.334zM3.333 10.833v3.334h2.5v-3.334z" />
  </svg>
);

export const TermsMenuIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="m2.5 6.667 5.003-5h9.162c.461 0 .835.38.835.826v15.014c0 .456-.37.826-.828.826H3.328a.833.833 0 0 1-.828-.839zm5.833-3.334V7.5H4.167v9.167h11.666V3.333z" />
  </svg>
);

export const WifiIcon: React.FC<IconProps> = (props) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="#D958DA"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M1.15 11.661A29.88 29.88 0 0 1 20 5c7.14 0 13.7 2.495 18.85 6.661l-2.094 2.593A26.56 26.56 0 0 0 20 8.334a26.56 26.56 0 0 0-16.756 5.92zm5.235 6.483A21.58 21.58 0 0 1 20 13.334c5.157 0 9.894 1.801 13.614 4.81l-2.094 2.593A18.26 18.26 0 0 0 20 16.667a18.26 18.26 0 0 0-11.52 4.07zm5.237 6.483A13.28 13.28 0 0 1 20 21.667c3.174 0 6.089 1.108 8.378 2.96l-2.094 2.593A9.96 9.96 0 0 0 20 25c-2.38 0-4.567.832-6.284 2.22zm5.236 6.483A4.98 4.98 0 0 1 20 30a4.98 4.98 0 0 1 3.142 1.11L20 35z" />
  </svg>
);

export const HomeWifiIcon: React.FC<IconProps> = (props) => (
  <svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="#D958DA"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10 31.667h20V15.262l-10-9.09-10 9.09zM31.667 35H8.333c-.92 0-1.666-.746-1.666-1.667v-15h-5L18.879 2.686a1.667 1.667 0 0 1 2.242 0l17.212 15.647h-5v15c0 .92-.746 1.667-1.666 1.667M13.333 16.667C19.777 16.667 25 21.89 25 28.333h-3.333A8.333 8.333 0 0 0 13.333 20zm0 6.666a5 5 0 0 1 5 5h-5z" />
  </svg>
);

export const CloseIcon: React.FC<IconProps> = (props) => (
  <svg
    width={24}
    height={25}
    viewBox="0 0 24 25"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="m12 11.087 4.95-4.95 1.414 1.414-4.95 4.95 4.95 4.95-1.415 1.414-4.95-4.95-4.949 4.95-1.414-1.415 4.95-4.95-4.95-4.95L7.05 6.138z" />
  </svg>
);

export const EditIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="m13.107 7.98-1.179-1.178-7.761 7.761v1.179h1.178zm1.178-1.178 1.179-1.179-1.179-1.178-1.178 1.178zm-8.25 10.606H2.5v-3.535L13.696 2.677a.833.833 0 0 1 1.178 0l2.357 2.357a.833.833 0 0 1 0 1.178z" />
  </svg>
);

export const MenuIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={18}
    viewBox="0 0 20 18"
    fill="#2C212C"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 1a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1m0 8a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1m20 8a1 1 0 0 0-1-1H1a1 1 0 1 0 0 2h18a1 1 0 0 0 1-1"
    />
  </svg>
);

export const InvalidIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={15}
    height={15}
    viewBox="0 0 15 15"
    fill="red"
    {...props}
  >
    <path d="M7.5 11.413a0.554 0.554 0 1 0 0 -1.109 0.554 0.554 0 0 0 0 1.108m0 -1.991a0.416 0.416 0 0 1 -0.417 -0.417v-5a0.416 0.416 0 1 1 0.834 0v5a0.416 0.416 0 0 1 -0.416 0.416" />
    <path d="M7.5 14.166a6.667 6.667 0 1 1 0 -13.334 6.667 6.667 0 0 1 0 13.334m0 -12.5a5.834 5.834 0 1 0 0 11.667 5.834 5.834 0 0 0 0 -11.667" />
  </svg>
);

export const DownCaretIcon: React.FC<IconProps> = (props) => (
  <svg
    width={11}
    height={8}
    viewBox="0 0 11 8"
    fill="#2C212C"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.305 4.884 1.18.759.002 1.938 5.305 7.24l5.303-5.303L9.43.759z" />
  </svg>
);

export const RightCaretIcon: React.FC<IconProps> = (props) => (
  <svg
    width={8}
    height={12}
    viewBox="0 0 8 12"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M4.976 6 .851 1.877 2.03.697l5.303 5.304-5.303 5.303-1.18-1.179z" />
  </svg>
);

export const LeftCaretIcon: React.FC<IconProps> = (props) => (
  <svg
    width={8}
    height={14}
    viewBox="0 0 8 14"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="m2.94 7 4.95-4.95L6.474.636.11 7l6.364 6.364 1.414-1.414z" />
  </svg>
);
export const LocationIcon: React.FC<IconProps> = ({ color }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M10.0004 11.1917C11.4363 11.1917 12.6004 10.0276 12.6004 8.5917C12.6004 7.15576 11.4363 5.9917 10.0004 5.9917C8.56445 5.9917 7.40039 7.15576 7.40039 8.5917C7.40039 10.0276 8.56445 11.1917 10.0004 11.1917Z"
      stroke={color || "#292D32"}
      strokeWidth="1.5"
    />
    <path
      d="M3.01675 7.07508C4.65842 -0.141583 15.3501 -0.13325 16.9834 7.08342C17.9418 11.3167 15.3084 14.9001 13.0001 17.1168C11.3251 18.7334 8.67508 18.7334 6.99175 17.1168C4.69175 14.9001 2.05842 11.3084 3.01675 7.07508Z"
      stroke={color || "#292D32"}
      strokeWidth="1.5"
    />
  </svg>
);

export const AddIcon: React.FC<IconProps> = (props) => (
  <svg
    width={17}
    height={18}
    viewBox="0 0 17 18"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M7.667 8.167V4.833h1.666v3.334h3.333v1.666H9.333v3.334H7.667V9.833H4.333V8.167zm.833 9.166A8.333 8.333 0 1 1 8.5.667a8.333 8.333 0 0 1 0 16.666m0-1.666a6.667 6.667 0 1 0 0-13.334 6.667 6.667 0 0 0 0 13.334" />
  </svg>
);

export const NextLabel: React.FC<IconProps> = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
  >
    <path
      d="M7.92578 16.5999L13.3591 11.1666C14.0008 10.5249 14.0008 9.4749 13.3591 8.83324L7.92578 3.3999"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PrevLabel: React.FC<IconProps> = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
  >
    <path
      d="M13.0005 16.5999L7.56719 11.1666C6.92552 10.5249 6.92552 9.4749 7.56719 8.83324L13.0005 3.3999"
      stroke="#292D32"
      strokeWidth="1.5"
      strokeMiterlimit="10"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CopyIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M12 10.175V13.325C12 15.95 10.95 17 8.325 17H5.175C2.55 17 1.5 15.95 1.5 13.325V10.175C1.5 7.55 2.55 6.5 5.175 6.5H8.325C10.95 6.5 12 7.55 12 10.175Z"
        stroke="#111111"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 5.675V8.825C16.5 11.45 15.45 12.5 12.825 12.5H12V10.175C12 7.55 10.95 6.5 8.325 6.5H6V5.675C6 3.05 7.05 2 9.675 2H12.825C15.45 2 16.5 3.05 16.5 5.675Z"
        stroke="#111111"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </svg>
);

export const ShareIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 19"
    fill="none"
    {...props}
  >
    <path
      d="M5.55001 5.24012L11.9175 3.11762C14.775 2.16512 16.3275 3.72512 15.3825 6.58262L13.26 12.9501C11.835 17.2326 9.49501 17.2326 8.07001 12.9501L7.44001 11.0601L5.55001 10.4301C1.26751 9.00512 1.26751 6.67262 5.55001 5.24012Z"
      stroke="#292D32"
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.58252 10.7375L10.2675 8.04504"
      stroke="#292D32"
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LineIcon: React.FC<IconProps> = (props) => (
  <svg
    width={53}
    height={2}
    viewBox="0 0 53 2"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path fill="#D9D9D9" d="M0 0h53v2H0z" />
  </svg>
);
export const DownloadIcon: React.FC<IconProps> = (props) => (
  <svg
    width={18}
    height={16}
    viewBox="0 0 18 16"
    fill="currentColor"
    {...props}
  >
    <path d="M9.833 6.333H14l-5 5-5-5h4.166V.5h1.667zm-7.5 7.5h13.334V8h1.666v6.667c0 .46-.373.833-.833.833h-15a.833.833 0 0 1-.833-.833V8h1.666z" />
  </svg>
);
export const GreenTick: React.FC<IconProps> = (props) => (
  <svg
    width={21}
    height={20}
    viewBox="0 0 21 20"
    fill="#24CC20"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10.416 20c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10m-.997-6 7.07-7.071-1.414-1.414-5.656 5.657L6.59 8.343 5.176 9.757z" />
  </svg>
);

export const PurpleCircleIcon: React.FC<IconProps> = (props) => (
  <svg
    width={21}
    height={20}
    viewBox="0 0 21 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={10.61} cy={10} r={10} fill="#C136C2" />
  </svg>
);

export const CancelIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M10 18.333a8.333 8.333 0 1 1 0-16.666 8.333 8.333 0 0 1 0 16.666m0-1.666a6.667 6.667 0 1 0 0-13.334 6.667 6.667 0 0 0 0 13.334m0-7.846 2.356-2.357 1.179 1.179L11.178 10l2.357 2.357-1.179 1.179L10 11.179l-2.357 2.357-1.178-1.179L8.82 10 6.464 7.643l1.178-1.179z" />
  </svg>
);
export const TrashIcon: React.FC<IconProps> = (props) => (
  <svg
    width={15}
    height={16}
    viewBox="0 0 15 16"
    fill="currentColor"
    {...props}
  >
    <path d="M11.25 3.055H15v1.5h-1.5v9.75a.75.75 0 0 1-.75.75H2.25a.75.75 0 0 1-.75-.75v-9.75H0v-1.5h3.75V.805a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 .75.75zm.75 1.5H3v9h9zm-6.75 2.25h1.5v4.5h-1.5zm3 0h1.5v4.5h-1.5zm-3-5.25v1.5h4.5v-1.5z" />
  </svg>
);

export const EyeIcon: React.FC<IconProps> = (props) => (
  <svg width={20} height={20} viewBox="0 0 20 20" {...props}>
    <path d="M10 2.5a9.17 9.17 0 0 1 9.015 7.5A9.169 9.169 0 0 1 .984 10C1.768 5.733 5.506 2.5 10 2.5m0 13.333c3.53 0 6.55-2.456 7.314-5.833a7.504 7.504 0 0 0-14.629 0A7.504 7.504 0 0 0 10 15.833m0-2.083a3.75 3.75 0 1 1 0-7.5 3.75 3.75 0 0 1 0 7.5m0-1.667a2.083 2.083 0 1 0 0-4.166 2.083 2.083 0 0 0 0 4.166" />
  </svg>
);

export const EyeCloseIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 6.4 6.4"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M5.859 4.02a.3.3 0 1 1-.52.3l-.407-.704a3.2 3.2 0 0 1-.734.337l.13.736a.3.3 0 1 1-.591.104l-.126-.716a3.5 3.5 0 0 1-.824 0l-.126.716a.3.3 0 0 1-.591-.104l.13-.736a3.2 3.2 0 0 1-.733-.337l-.41.709a.3.3 0 0 1-.52-.3l.446-.773a4 4 0 0 1-.417-.441.3.3 0 0 1 .467-.377C1.426 2.92 2.121 3.5 3.2 3.5s1.774-.58 2.167-1.067a.3.3 0 1 1 .467.377 4 4 0 0 1-.418.442Z" />
  </svg>
);

export const SelectedCardIcon: React.FC<IconProps> = (props) => (
  <svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={22}
      width={22}
      height={22}
      rx={6}
      transform="rotate(90 22 0)"
      fill="#F3F3F3"
    />
    <path d="m5.411 12.993 1.226-1.58 5.05 3.916-1.226 1.58z" fill="#2C212C" />
    <path d="m10.46 16.909-1.58-1.226 6.129-7.902 1.58 1.225z" fill="#2C212C" />
  </svg>
);
export const UnSelectedCardIcon: React.FC<IconProps> = (props) => (
  <svg
    width={22}
    height={23}
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={22}
      y={0.5}
      width={22}
      height={22}
      rx={6}
      transform="rotate(90 22 .5)"
      fill="#F3F3F3"
    />
  </svg>
);

export const UploadIcon: React.FC<IconProps> = (props) => (
  <svg
    width={19}
    height={17}
    viewBox="0 0 19 17"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M.334 11.083c0-1.94 1.02-3.642 2.554-4.599a6.668 6.668 0 0 1 13.226 0 5.417 5.417 0 0 1-2.446 10l-8.334.016c-2.797-.229-5-2.565-5-5.417m13.207 3.74a3.75 3.75 0 0 0 1.69-6.925l-.67-.419-.1-.785a5.001 5.001 0 0 0-9.92 0l-.1.785-.671.42a3.75 3.75 0 0 0 1.69 6.924l.145.01h7.791zm-3.207-4.99v3.334H8.667V9.833h-2.5l3.334-4.166 3.333 4.166z" />
  </svg>
);

export const ResetSuccessIcon: React.FC<IconProps> = (props) => (
  <svg
    width={119}
    height={118}
    viewBox="0 0 119 118"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={59.5} cy={59} r={59} fill="#24CC20" />
    <path d="m39.5 58 1.414-1.414 14.142 14.142-1.414 1.414z" fill="#fff" />
    <path
      d="m76.27 46.686 1.414 1.414-24.042 24.042-1.414-1.414z"
      fill="#fff"
    />
  </svg>
);
export const CheckIcon: React.FC<IconProps> = (props) => (
  <svg
    width={17}
    height={18}
    viewBox="0 0 17 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.333 17.333a8.333 8.333 0 1 1 0-16.667 8.333 8.333 0 0 1 0 16.667m-.83-5 5.892-5.892-1.179-1.179-4.714 4.714L5.145 7.62 3.967 8.798z"
      fill="#24CC20"
    />
  </svg>
);

export const ErrorIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 17 17"
    xmlSpace="preserve"
    width={17}
    height={17}
    {...props}
  >
    <path
      style={{
        fill: "#d75a4a",
      }}
      d="M17 8.5A8.5 8.5 0 0 1 8.5 17 8.5 8.5 0 0 1 0 8.5a8.5 8.5 0 0 1 17 0"
    />
    <path
      style={{
        fill: "none",
        stroke: "#fff",
        strokeWidth: 2,
        strokeLinecap: "round",
        strokeMiterlimit: 10,
      }}
      d="M5.44 11.56 8.5 8.5l3.06-3.06m-6.12 0L8.5 8.5l3.06 3.06"
    />
  </svg>
);

export const HomeIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M8 12V10"
      stroke="#AA7DE6"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.71343 1.87998L2.09343 5.57998C1.57343 5.99331 1.2401 6.86665 1.35343 7.51998L2.2401 12.8266C2.4001 13.7733 3.30676 14.54 4.26676 14.54H11.7334C12.6868 14.54 13.6001 13.7666 13.7601 12.8266L14.6468 7.51998C14.7534 6.86665 14.4201 5.99331 13.9068 5.57998L9.28676 1.88665C8.57343 1.31331 7.42009 1.31331 6.71343 1.87998Z"
      stroke="#AA7DE6"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SignOutIcon: React.FC<IconProps> = (props) => (
  <svg
    width={19}
    height={18}
    viewBox="0 0 19 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9 17.333A8.333 8.333 0 1 1 15.667 4h-2.258a6.667 6.667 0 1 0 0 10h2.259A8.32 8.32 0 0 1 9 17.334m5.833-5v-2.5H8.167V8.167h6.666v-2.5L19 9z"
      fill="#2C212C"
    />
  </svg>
);

export const SuccessIcon: React.FC<IconProps> = (props) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 0.48 0.48"
    xmlns="http://www.w3.org/2000/svg"
    fill="#24cc20"
    {...props}
  >
    <path
      fillRule="evenodd"
      d="M.24.04c.11 0 .2.09.2.2s-.09.2-.2.2-.2-.09-.2-.2.09-.2.2-.2m0 .04a.16.16 0 1 0 0 .32.16.16 0 0 0 0-.32m.066.086L.2.272.174.246a.02.02 0 1 0-.028.028l.04.04a.02.02 0 0 0 .028 0l.12-.12A.02.02 0 1 0 .306.166"
    />
  </svg>
);

export const FailureIcon: React.FC<IconProps> = (props) => (
  <svg
    width={800}
    height={800}
    viewBox="0 0 512 512"
    xmlns="http://www.w3.org/2000/svg"
    fill="#cc2b20"
    {...props}
  >
    <path
      d="M256 64c106.039 0 192 85.961 192 192s-85.961 192-192 192S64 362.039 64 256 149.961 64 256 64m81.018 80.824-81.006 81.006-80.855-80.855-30.17 30.17L225.842 256l-80.855 80.855 30.17 30.17 80.855-80.855 81.006 81.006 30.17-30.17L286.182 256l81.006-81.006z"
      fillRule="evenodd"
    />
  </svg>
);

export const WarningIcon: React.FC<IconProps> = (props) => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 0.48 0.48"
    baseProfile="tiny"
    fill="#FF8C22"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M.423.308.305.111Q.281.071.24.07C.199.069.19.085.175.111L.057.308a.08.08 0 0 0-.005.077Q.072.419.12.42h.24Q.407.419.428.385C.449.351.439.335.423.308M.24.351A.03.03 0 0 1 .209.32C.209.303.223.289.24.289S.271.303.271.32A.03.03 0 0 1 .24.351M.273.203.245.272Q.243.275.24.275.236.275.235.272L.207.203.204.19C.204.171.22.155.239.155a.035.035 0 0 1 .033.048" />
  </svg>
);

export const BackIcon: React.FC<IconProps> = (props) => (
  <svg
    fill="#2C212C"
    width={18}
    height={18}
    viewBox="0 0 23.04 23.04"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="m5.016 13.053 6.78 7.392a1.534 1.534 0 0 1-2.339 1.985L.44 12.599a1.5 1.5 0 0 1-.31-.45 1.5 1.5 0 0 1-.134-.663v-.001a1.5 1.5 0 0 1 .163-.656l.031-.058a1.5 1.5 0 0 1 .227-.308L9.674.475a1.533 1.533 0 1 1 2.249 2.085L5.042 9.985h16.435a1.534 1.534 0 0 1 0 3.068z" />
  </svg>
);

export const RestartIcon: React.FC<IconProps> = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 0.4 0.4"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M.319.2a.113.113 0 0 1-.215.046L.069.26A.15.15 0 0 0 .356.2.15.15 0 0 0 .088.108V.063H.05v.1l.019.019h.088V.144H.109A.113.113 0 0 1 .319.2"
    />
  </svg>
);

export const TotalReferralsIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
  >
    <path
      d="M32.5 59.9219C19.6198 59.9219 9.14062 49.4427 9.14062 36.5625C9.14062 23.6823 19.6198 13.2031 32.5 13.2031C45.3802 13.2031 55.8594 23.6823 55.8594 36.5625C55.8594 49.4427 45.3802 59.9219 32.5 59.9219ZM32.5 15.2344C20.7391 15.2344 11.1719 24.8016 11.1719 36.5625C11.1719 48.3234 20.7391 57.8906 32.5 57.8906C44.2609 57.8906 53.8281 48.3234 53.8281 36.5625C53.8281 24.8016 44.2609 15.2344 32.5 15.2344Z"
      fill="#DC9600"
    />
    <path
      d="M16.25 60.9375C22.981 60.9375 28.4375 55.481 28.4375 48.75C28.4375 42.019 22.981 36.5625 16.25 36.5625C9.51903 36.5625 4.0625 42.019 4.0625 48.75C4.0625 55.481 9.51903 60.9375 16.25 60.9375Z"
      fill="#C80A50"
    />
    <path
      d="M24.375 55.8594C24.375 54.1765 23.011 52.8125 21.3281 52.8125H11.1719C9.48898 52.8125 8.125 54.1765 8.125 55.8594V57.8327C10.2812 59.7624 13.128 60.9375 16.25 60.9375C19.372 60.9375 22.2188 59.7634 24.375 57.8327V55.8594Z"
      fill="#FAB400"
    />
    <path
      d="M12.1875 52.8125C12.1875 55.056 14.0065 56.875 16.25 56.875C18.4935 56.875 20.3125 55.056 20.3125 52.8125H12.1875Z"
      fill="#00A0C8"
    />
    <path
      d="M16.25 54.8438C15.1277 54.8438 14.2188 53.9348 14.2188 52.8125V49.7656H18.2812V52.8125C18.2812 53.9348 17.3723 54.8438 16.25 54.8438Z"
      fill="#D2AA82"
    />
    <path
      d="M17.2656 50.7812H15.2344C13.5515 50.7812 12.1875 49.4173 12.1875 47.7344V44.6875C12.1875 43.5652 13.0965 42.6562 14.2188 42.6562H18.2812C19.4035 42.6562 20.3125 43.5652 20.3125 44.6875V47.7344C20.3125 49.4173 18.9485 50.7812 17.2656 50.7812Z"
      fill="#F0C8A0"
    />
    <path
      d="M18.2812 42.6562H14.2188C13.0965 42.6562 12.1875 43.5652 12.1875 44.6875V42.6562C12.1875 41.534 13.0965 40.625 14.2188 40.625H18.2812C19.4035 40.625 20.3125 41.534 20.3125 42.6562V44.6875C20.3125 43.5652 19.4035 42.6562 18.2812 42.6562Z"
      fill="#0A5078"
    />
    <path
      d="M48.75 60.9375C55.481 60.9375 60.9375 55.481 60.9375 48.75C60.9375 42.019 55.481 36.5625 48.75 36.5625C42.019 36.5625 36.5625 42.019 36.5625 48.75C36.5625 55.481 42.019 60.9375 48.75 60.9375Z"
      fill="#0A5078"
    />
    <path
      d="M56.875 55.8594C56.875 54.1765 55.511 52.8125 53.8281 52.8125H43.6719C41.989 52.8125 40.625 54.1765 40.625 55.8594V57.8327C42.7812 59.7624 45.628 60.9375 48.75 60.9375C51.872 60.9375 54.7188 59.7634 56.875 57.8327V55.8594Z"
      fill="#00A0C8"
    />
    <path
      d="M44.6875 52.8125C44.6875 55.056 46.5065 56.875 48.75 56.875C50.9935 56.875 52.8125 55.056 52.8125 52.8125H44.6875Z"
      fill="#0A5078"
    />
    <path
      d="M48.75 54.8438C47.6277 54.8438 46.7188 53.9348 46.7188 52.8125V49.7656H50.7812V52.8125C50.7812 53.9348 49.8723 54.8438 48.75 54.8438Z"
      fill="#D2AA82"
    />
    <path
      d="M49.7656 50.7812H47.7344C46.0515 50.7812 44.6875 49.4173 44.6875 47.7344V44.6875C44.6875 43.5652 45.5965 42.6562 46.7188 42.6562H50.7812C51.9035 42.6562 52.8125 43.5652 52.8125 44.6875V47.7344C52.8125 49.4173 51.4485 50.7812 49.7656 50.7812Z"
      fill="#F0C8A0"
    />
    <path
      d="M50.7812 42.6562H46.7188C45.5965 42.6562 44.6875 43.5652 44.6875 44.6875V42.6562C44.6875 41.534 45.5965 40.625 46.7188 40.625H50.7812C51.9035 40.625 52.8125 41.534 52.8125 42.6562V44.6875C52.8125 43.5652 51.9035 42.6562 50.7812 42.6562Z"
      fill="#DC9600"
    />
    <path
      d="M32.5 28.4375C39.231 28.4375 44.6875 22.981 44.6875 16.25C44.6875 9.51903 39.231 4.0625 32.5 4.0625C25.769 4.0625 20.3125 9.51903 20.3125 16.25C20.3125 22.981 25.769 28.4375 32.5 28.4375Z"
      fill="#00A0C8"
    />
    <path
      d="M33.5156 6.09375H31.4844C28.6802 6.09375 26.4062 8.36773 26.4062 11.1719V21.3281C26.4062 21.8888 26.8613 22.3438 27.4219 22.3438H37.5781C38.1388 22.3438 38.5938 21.8888 38.5938 21.3281V11.1719C38.5938 8.36773 36.3198 6.09375 33.5156 6.09375Z"
      fill="#C80A50"
    />
    <path
      d="M36.5625 20.3125H28.4375C26.194 20.3125 24.375 22.1315 24.375 24.375V25.3327C26.5312 27.2624 29.378 28.4375 32.5 28.4375C35.622 28.4375 38.4688 27.2634 40.625 25.3327V24.375C40.625 22.1315 38.806 20.3125 36.5625 20.3125Z"
      fill="#C80A50"
    />
    <path
      d="M28.4375 20.3125C28.4375 22.556 30.2565 24.375 32.5 24.375C34.7435 24.375 36.5625 22.556 36.5625 20.3125H28.4375Z"
      fill="#00A0C8"
    />
    <path
      d="M32.5 22.3438C31.3777 22.3438 30.4688 21.4348 30.4688 20.3125V17.2656H34.5312V20.3125C34.5312 21.4348 33.6223 22.3438 32.5 22.3438Z"
      fill="#D2AA82"
    />
    <path
      d="M33.5156 18.2812H31.4844C29.8015 18.2812 28.4375 16.9173 28.4375 15.2344V12.1875C28.4375 11.0652 29.3465 10.1562 30.4688 10.1562H31.4844C31.4844 11.2785 32.3934 12.1875 33.5156 12.1875H36.5625V15.2344C36.5625 16.9173 35.1985 18.2812 33.5156 18.2812Z"
      fill="#F0C8A0"
    />
    <path
      d="M36.5625 48.75C36.5625 48.8566 36.5635 48.9633 36.5666 49.0699C41.2984 47.3952 44.6875 42.8827 44.6875 37.5781C44.6875 37.4715 44.6865 37.3648 44.6834 37.2582C39.9516 38.933 36.5625 43.4454 36.5625 48.75Z"
      fill="#00325A"
    />
    <path
      d="M20.3166 37.2582C20.3135 37.3648 20.3125 37.4715 20.3125 37.5781C20.3125 42.8827 23.7016 47.3952 28.4334 49.0699C28.4365 48.9633 28.4375 48.8566 28.4375 48.75C28.4375 43.4454 25.0484 38.933 20.3166 37.2582Z"
      fill="#A00028"
    />
    <path
      d="M32.5 48.75C39.231 48.75 44.6875 43.2935 44.6875 36.5625C44.6875 29.8315 39.231 24.375 32.5 24.375C25.769 24.375 20.3125 29.8315 20.3125 36.5625C20.3125 43.2935 25.769 48.75 32.5 48.75Z"
      fill="#FAB400"
    />
    <path
      d="M33.5156 35.5469H31.4844C30.3641 35.5469 29.4531 34.6359 29.4531 33.5156C29.4531 32.3954 30.3641 31.4844 31.4844 31.4844H33.5156C34.6359 31.4844 35.5469 32.3954 35.5469 33.5156C35.5469 34.0763 36.0019 34.5312 36.5625 34.5312C37.1231 34.5312 37.5781 34.0763 37.5781 33.5156C37.5781 31.2752 35.7561 29.4531 33.5156 29.4531V28.4375C33.5156 27.8769 33.0606 27.4219 32.5 27.4219C31.9394 27.4219 31.4844 27.8769 31.4844 28.4375V29.4531C29.2439 29.4531 27.4219 31.2752 27.4219 33.5156C27.4219 35.7561 29.2439 37.5781 31.4844 37.5781H33.5156C34.6359 37.5781 35.5469 38.4891 35.5469 39.6094C35.5469 40.7296 34.6359 41.6406 33.5156 41.6406H31.4844C30.3641 41.6406 29.4531 40.7296 29.4531 39.6094C29.4531 39.0487 28.9981 38.5938 28.4375 38.5938C27.8769 38.5938 27.4219 39.0487 27.4219 39.6094C27.4219 41.8498 29.2439 43.6719 31.4844 43.6719V44.6875C31.4844 45.2481 31.9394 45.7031 32.5 45.7031C33.0606 45.7031 33.5156 45.2481 33.5156 44.6875V43.6719C35.7561 43.6719 37.5781 41.8498 37.5781 39.6094C37.5781 37.3689 35.7561 35.5469 33.5156 35.5469Z"
      fill="#BE7800"
    />
  </svg>
);

export const TotalPaidReferralsIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="65"
    height="65"
    viewBox="0 0 65 65"
    fill="none"
  >
    <path
      d="M54.8438 24.375L48.7501 32.5H52.8126C52.8126 43.7003 43.7004 52.8125 32.5001 52.8125C23.7921 52.8125 16.3597 47.2977 13.4774 39.583L10.6753 43.3195C14.6626 51.347 22.9278 56.875 32.5001 56.875C45.9622 56.875 56.8751 45.9621 56.8751 32.5H60.9376L54.8438 24.375Z"
      fill="#3CC8B4"
    />
    <path
      d="M55.8594 25.7288L54.8438 24.375L48.75 32.5H52.8125C52.8125 43.3997 44.1756 52.2976 33.3887 52.7678C33.7685 52.7891 34.1463 52.8125 34.5312 52.8125C45.7316 52.8125 54.8438 43.7003 54.8438 32.5H50.7812L55.8594 25.7288Z"
      fill="#1EAA96"
    />
    <path
      d="M12.7056 43.3194L14.2209 41.2994C13.9507 40.7398 13.6958 40.17 13.4764 39.583L10.6743 43.3194C14.6627 51.3469 22.9278 56.875 32.5001 56.875C32.9003 56.875 33.2974 56.8638 33.6924 56.8445C24.4797 56.5317 16.5761 51.1113 12.7056 43.3194Z"
      fill="#1EAA96"
    />
    <path
      d="M32.5 8.125C19.0379 8.125 8.125 19.0379 8.125 32.5H4.0625L10.1562 40.625L16.25 32.5H12.1875C12.1875 21.2997 21.2997 12.1875 32.5 12.1875C41.208 12.1875 48.6403 17.7023 51.5227 25.417L54.3248 21.6805C50.3374 13.653 42.0723 8.125 32.5 8.125Z"
      fill="#3CC8B4"
    />
    <path
      d="M33.5156 8.15039C33.1784 8.13719 32.8402 8.125 32.5 8.125C19.0379 8.125 8.125 19.0379 8.125 32.5H4.0625L10.1562 40.625L11.1719 39.2712L6.09375 32.5H10.1562C10.1562 19.3781 20.5248 8.68359 33.5156 8.15039Z"
      fill="#1EAA96"
    />
    <path
      d="M36.5625 22.3438H28.4375L24.1526 25.2007C23.0222 25.9533 22.3438 27.2218 22.3438 28.5807V44.6875C22.3438 46.931 24.1627 48.75 26.4062 48.75H38.5938C40.8373 48.75 42.6562 46.931 42.6562 44.6875V28.5807C42.6562 27.2228 41.9778 25.9543 40.8474 25.2007L36.5625 22.3438Z"
      fill="#FAB400"
    />
    <path
      d="M24.375 44.6875V28.5807C24.375 27.2228 25.0534 25.9543 26.1838 25.2007L30.4688 22.3438H28.4375L24.1526 25.2007C23.0222 25.9533 22.3438 27.2218 22.3438 28.5807V44.6875C22.3438 46.931 24.1627 48.75 26.4062 48.75H28.4375C26.194 48.75 24.375 46.931 24.375 44.6875Z"
      fill="#DC9600"
    />
    <path
      d="M36.5625 22.3438H28.4375C27.8769 22.3438 27.4219 21.8887 27.4219 21.3281C27.4219 20.7675 27.8769 20.3125 28.4375 20.3125H36.5625C37.1231 20.3125 37.5781 20.7675 37.5781 21.3281C37.5781 21.8887 37.1231 22.3438 36.5625 22.3438Z"
      fill="#505050"
    />
    <path
      d="M30.4688 21.3281C30.4688 20.7675 30.9237 20.3125 31.4844 20.3125H28.4375C27.8769 20.3125 27.4219 20.7675 27.4219 21.3281C27.4219 21.8887 27.8769 22.3438 28.4375 22.3438H31.4844C30.9237 22.3438 30.4688 21.8887 30.4688 21.3281Z"
      fill="#3C3C3C"
    />
    <path
      d="M30.4687 20.3125L28.1399 17.9837C27.5001 17.3438 27.953 16.25 28.8579 16.25H36.142C37.0469 16.25 37.4999 17.3438 36.8601 17.9837L34.5312 20.3125H30.4687Z"
      fill="#FAB400"
    />
    <path
      d="M30.292 16.25H28.8579C27.953 16.25 27.5001 17.3438 28.1399 17.9837L30.4687 20.3125H31.9028L29.574 17.9837C28.9341 17.3438 29.3881 16.25 30.292 16.25Z"
      fill="#DC9600"
    />
    <path
      d="M35.4789 32.693V30.5652H33.4934V28.4375H31.5068V30.5652H29.5212V36.9495H33.4934V39.0772H29.5212V41.2049H31.5068V43.3337H33.4934V41.2049H35.4789V34.8217H31.5068V32.693H35.4789Z"
      fill="#BE7800"
    />
    <path
      d="M35.0391 26.4062H34.0234V25.217C34.0234 24.8097 33.865 24.4278 33.5776 24.1394L32.5 23.0618L31.4224 24.1394C31.135 24.4278 30.9766 24.8097 30.9766 25.217V26.4062H29.9609V25.217C29.9609 24.5385 30.225 23.9007 30.7044 23.4213L32.5 21.6257L34.2956 23.4213C34.776 23.9007 35.0391 24.5385 35.0391 25.217V26.4062Z"
      fill="#505050"
    />
  </svg>
);

export const TotalEarnedReferralsIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="66"
    height="66"
    viewBox="0 0 66 66"
    fill="none"
  >
    <path
      d="M13.5859 20.4187H52.4898C55.9445 20.4187 58.7546 23.2289 58.7546 26.6836V55.739C58.7546 59.1937 55.9445 62.0039 52.4898 62.0039H13.5859C10.1312 62.0039 7.32104 59.1937 7.32104 55.739V26.6836C7.32104 23.216 10.1183 20.4187 13.5859 20.4187Z"
      fill="#F9BE40"
    />
    <path
      d="M6.12305 20.4187H59.9543C60.7664 20.4187 61.4367 21.0762 61.4367 21.9012V28.5656C61.4367 29.3777 60.7793 30.048 59.9543 30.048H6.12305C5.31094 30.048 4.64062 29.3906 4.64062 28.5656V21.9012C4.64062 21.0762 5.29805 20.4187 6.12305 20.4187Z"
      fill="#EE6145"
    />
    <path
      d="M41.8167 7.96639C40.4245 9.38436 37.5499 13.0711 36.2866 14.7469C35.8354 16.6676 35.2553 20.4961 36.596 20.4703C38.2717 20.4445 49.7573 18.7043 52.7479 17.2863C55.7385 15.8683 55.0553 14.3473 54.5139 12.839C53.9725 11.3308 51.9229 7.6828 50.6081 5.72342C49.2932 3.76405 48.1331 3.90585 46.9858 4.12499C45.8514 4.35702 43.544 6.20038 41.8167 7.96639Z"
      fill="#F6764F"
    />
    <path
      d="M47.2567 4.06054C43.9696 4.26679 38.5169 11.4727 36.2095 15.0562L36.6993 16.2809C43.57 12.5168 46.2642 10.0418 47.2696 9.16523C48.2493 8.28867 51.356 3.81562 47.2567 4.06054Z"
      fill="#CA442B"
    />
    <path
      d="M50.0675 18.1887C47.5409 18.8719 41.044 20.0063 36.8804 20.4703L36.3647 20.2125C39.639 18.6785 48.3788 15.2625 49.178 15.0305C49.9772 14.7984 54.4503 12.9551 54.8757 14.5406C55.314 16.1391 53.2128 17.3379 50.0675 18.1887Z"
      fill="#CA442B"
    />
    <path
      d="M24.3115 7.96639C25.7037 9.38436 28.5783 13.0711 29.8416 14.7469C30.2928 16.6676 30.8728 20.4961 29.5322 20.4703C27.8564 20.4445 16.3709 18.7043 13.3803 17.2863C10.3896 15.8683 11.0728 14.3473 11.6142 12.839C12.1557 11.3308 14.2053 7.6828 15.5201 5.72342C16.835 3.76405 17.9951 3.90585 19.1424 4.12499C20.2639 4.35702 22.5713 6.20038 24.3115 7.96639Z"
      fill="#F6764F"
    />
    <path
      d="M18.8719 4.06054C22.159 4.26679 27.6118 11.4727 29.9192 15.0562L29.4293 16.2809C22.5586 12.5168 19.8645 10.0418 18.859 9.16523C17.8793 8.28867 14.7727 3.81562 18.8719 4.06054Z"
      fill="#CA442B"
    />
    <path
      d="M16.0489 18.1887C18.5754 18.8719 25.0723 20.0063 29.236 20.4703L29.7516 20.2125C26.4774 18.6785 17.7376 15.2625 16.9383 15.0305C16.1391 14.7984 11.6661 12.9551 11.2407 14.5406C10.8153 16.1391 12.9165 17.3379 16.0489 18.1887Z"
      fill="#CA442B"
    />
    <path
      d="M28.9128 15.6879C28.9128 14.8113 29.6218 14.0895 30.5113 14.0895H35.5773C36.4539 14.0895 37.1757 14.7984 37.1757 15.6879V30.0481H28.9128V15.6879Z"
      fill="#F77A54"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M35.5773 14.0895H33.0378V30.0352H37.1628V15.6879C37.1628 14.8113 36.4539 14.0895 35.5773 14.0895Z"
      fill="#F36D44"
    />
    <path
      d="M33.039 55.3394C38.7486 55.3394 43.3772 50.7108 43.3772 45.0012C43.3772 39.2915 38.7486 34.6629 33.039 34.6629C27.3293 34.6629 22.7007 39.2915 22.7007 45.0012C22.7007 50.7108 27.3293 55.3394 33.039 55.3394Z"
      fill="#F39826"
    />
    <path
      d="M33.0384 53.4188C28.3978 53.4188 24.6079 49.6418 24.6079 44.9883C24.6079 40.3477 28.3849 36.5578 33.0384 36.5578C37.6919 36.5578 41.4688 40.3348 41.4688 44.9883C41.4688 49.6418 37.679 53.4188 33.0384 53.4188ZM33.0384 37.4344C28.8618 37.4344 25.4716 40.8246 25.4716 45.0012C25.4716 49.1778 28.8618 52.568 33.0384 52.568C37.2149 52.568 40.6052 49.1778 40.6052 45.0012C40.6052 40.8246 37.2149 37.4344 33.0384 37.4344Z"
      fill="#CE611E"
    />
    <path
      d="M36.673 47.2441C36.673 47.7598 36.5441 48.1851 36.2992 48.5332C36.0671 48.8683 35.6546 49.2808 35.0617 49.7449C34.5976 50.1058 34.0691 50.325 33.4503 50.4023C33.4503 50.9308 33.4503 51.2531 33.4632 51.3562C33.4632 51.4723 33.4246 51.5625 33.3343 51.6141C33.2699 51.6785 33.1667 51.7043 33.0507 51.7043C32.896 51.7043 32.7542 51.6527 32.6253 51.5496C32.5093 51.4594 32.4449 51.3176 32.4449 51.15C32.4449 50.9566 32.432 50.7246 32.4062 50.4668C32.1484 50.4539 31.9035 50.4023 31.6843 50.325C31.4652 50.2476 31.2718 50.1703 31.1171 50.093C30.9753 50.0285 30.8851 49.9769 30.8335 49.9512C30.4082 49.6547 30.0859 49.4226 29.8796 49.2422C29.6734 49.0488 29.4929 48.8426 29.364 48.6234C29.2351 48.4043 29.1707 48.1336 29.1707 47.8371C29.1707 47.5406 29.2351 47.3473 29.364 47.2441C29.4929 47.1281 29.725 47.0637 30.073 47.0637C30.2277 47.0637 30.3695 47.1023 30.5113 47.1926C30.6531 47.2699 30.7562 47.373 30.8335 47.4891C31.2203 48.1851 31.723 48.6105 32.3289 48.7781C32.3289 48.7137 32.3417 48.3141 32.3675 47.5922C32.3933 46.8703 32.4062 46.1484 32.4191 45.4266C31.7488 45.1945 31.2718 45.0012 30.9882 44.8594C29.841 44.2922 29.2738 43.4801 29.2738 42.3973C29.2738 41.9332 29.4285 41.4691 29.7507 40.9922C30.073 40.5152 30.4339 40.1672 30.8593 39.9738C31.0269 39.8965 31.2589 39.7933 31.5683 39.6902C31.8777 39.5871 32.1742 39.4969 32.4449 39.4195C32.4449 39.0973 32.4449 38.8523 32.432 38.6976C32.432 38.4914 32.4835 38.3367 32.5867 38.2594C32.6898 38.1691 32.7929 38.1176 32.9089 38.1176C33.1796 38.1176 33.3472 38.2078 33.3988 38.4012C33.4632 38.5945 33.5019 38.9168 33.489 39.368C33.8628 39.4066 34.2882 39.5484 34.7394 39.7934C35.2679 40.1027 35.6546 40.3734 35.8867 40.6055C36.1316 40.8246 36.2605 41.1211 36.2605 41.4691C36.2605 41.6625 36.1832 41.8301 36.0156 41.959C35.848 42.0879 35.5773 42.1523 35.2035 42.1523C35.0359 42.1523 34.8296 41.9848 34.5718 41.6625C34.3398 41.4047 34.1593 41.25 34.0304 41.1855C33.9789 41.1726 33.9015 41.1469 33.7984 41.1211C33.6953 41.0695 33.5921 41.0437 33.4761 41.018C33.4761 42.0879 33.4632 42.9773 33.4374 43.6734V43.9312C34.0691 44.2148 34.5976 44.4855 35.0101 44.7433C35.5773 45.0914 35.9898 45.4523 36.2476 45.8004C36.5441 46.1226 36.673 46.6125 36.673 47.2441ZM31.1042 42.2941C31.1042 42.5262 31.2203 42.7582 31.4394 42.9644C31.6585 43.1578 31.9164 43.3125 32.2128 43.4285L32.4835 43.5187V40.9793C32.0968 41.0566 31.7617 41.2113 31.491 41.4433C31.2332 41.6625 31.1042 41.9461 31.1042 42.2941ZM33.4503 48.7394C33.8757 48.6234 34.2109 48.4172 34.4687 48.1465C34.7265 47.8629 34.8554 47.5793 34.8554 47.2828C34.8554 46.7672 34.3914 46.2902 33.4632 45.8777C33.4503 46.4707 33.4503 47.4246 33.4503 48.7394Z"
      fill="#FDE58F"
    />
  </svg>
);
