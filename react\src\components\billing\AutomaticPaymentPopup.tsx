import AddCardDetails from "../dashboard/AddCardDetails";
import Popup from "../common/Popup";
import React from "react";

interface AutomaticPaymentPopupProps {
  refetch: () => void;
  closeHandler: () => void;
  subscription_id:any
}

export const AutomaticPaymentPopup: React.FC<AutomaticPaymentPopupProps> = ({
  refetch,
  closeHandler,
  subscription_id,
}) => {
  return (
    <Popup
      title="Add card"
      width="840px"
      height="700px"
      closeHandler={closeHandler}
    >
      <AddCardDetails subscription_id={subscription_id} closeHandler={closeHandler} refetch={refetch} />
    </Popup>
  );
};
