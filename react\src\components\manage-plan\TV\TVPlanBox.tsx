import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { AddIcon, BackIcon, EditIcon } from "../../../assets/Icons";
import {
  useCancelServiceMutation,
  useGetPlanDetailsMutation,
  useTelevisionUpdateMutation,
  useTelevisionUpdateProratedMutation,
} from "../../../services/api";
import {
  toggleShowTelevisionConfirmationPopup,
  toggleShowTelevisionPopup,
} from "../../../store/reducers/televisionReducer";
import {
  plan,
  showTelevisionConfirmationPopup,
  showTelevisionPopup,
} from "../../../store/selectors/televisionSelectors";

import useMediaQuery from "../../../hooks/MediaQueryHook";
import { logout } from "../../../store/reducers/authenticationReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import { selectedTVPlanProps } from "../../../typings/typing";
import { formatCurrency } from "../../../utils/helper";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import Popup from "../../common/Popup";
import ServiceBox from "../ServiceBox";
import TVPlan from "./TVPlan";

interface TVPlanBoxProps {
  refetch: () => void;
  subscriptionID?: number;
  billingDate?: Date;
  stage?: string;
}

const TVPlanBox: React.FC<TVPlanBoxProps> = ({
  refetch,
  subscriptionID,
  billingDate,
  stage,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const currentPlan = useSelector(plan);
  const showPopup = useSelector(showTelevisionPopup);
  const showConfirmationPopup = useSelector(showTelevisionConfirmationPopup);
  const [showViewAll, setShowViewAll] = useState({ status: false, data: "" });
  const [tvList, setTvList] = useState<any>({});
  const [getPlans, plansLoading] = useGetPlanDetailsMutation();
  const [televisionEstimate, televisionEstimateLoading] =
    useTelevisionUpdateProratedMutation();
  const [televisionUpdate, televisionUpdateLoading] =
    useTelevisionUpdateMutation();
  const [proratedData, setProratedData] = useState({});
  const [cancelTvService, cancelTvServiceLoading] = useCancelServiceMutation();
  const [isEditable, setIsEditable] = useState<boolean>(false);
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const [isCancel, setIsCancel] = useState<boolean>(false);

  // Initial state for the TV plan
  const initialTVplanState = {
    additionalChannels: currentPlan?.single_channels,
    basePlan: currentPlan?.plan_name,
    additionalPlan: currentPlan?.extra_packages,
    DVR: currentPlan?.iptv_products,
    isPlanRemoved: currentPlan?.isPlanRemoved
      ? currentPlan?.isPlanRemoved
      : false,
    isPlanUpdated: currentPlan?.isPlanUpdated
      ? currentPlan?.isPlanUpdated
      : true,
    modificationDate: currentPlan?.requested_cancellation_date,
  };
  const [tvPlan, setTVPlan] = useState<selectedTVPlanProps>(initialTVplanState);

  // useEffect hook to update the TV plan state whenever the current plan changes
  useEffect(() => {
    setTVPlan((prevPlan) => ({
      ...prevPlan,
      additionalChannels: currentPlan?.single_channels
        ? JSON.parse(currentPlan?.single_channels)
        : [],
      basePlan: currentPlan?.plan_name ? currentPlan?.plan_name : "",
      additionalPlan: currentPlan?.extra_packages
        ? JSON.parse(currentPlan?.extra_packages)
        : [],
      DVR: currentPlan?.iptv_products
        ? JSON.parse(currentPlan?.iptv_products)
        : [],
    }));
  }, [currentPlan]);

  // Media query to determine if the viewport is desktop size
  const isDesktop = useMediaQuery("(min-width:1300px");

  // Handler for the "View All" functionality
  const handleViewAll = (event: React.MouseEvent<HTMLElement>, channel: []) => {
    event.stopPropagation();
    dispatch(toggleShowTelevisionPopup());
    setShowViewAll((prev: any) => ({
      ...prev,
      status: !prev.status,
      data: channel,
    }));
  };

  // Handler for selecting a base package
  const handleBasePackageSelect = (plan: any) => {
    const allTvChannels = tvList?.planDetails.filter(
      (list: any) => list.api_name === plan?.api_name
    )?.[0]?.optional_single_channels;
    const selectedChannel = tvPlan?.additionalChannels
      ? tvPlan?.additionalChannels
      : JSON.parse(currentPlan?.single_channels);

    const availableChannels = allTvChannels.filter((channel) =>
      selectedChannel.includes(channel.api_name)
    );
    const newAdditionalChannel =
      availableChannels?.length > 0
        ? availableChannels.map((channel) => channel.api_name)
        : tvPlan?.additionalChannels;

    if (currentPlan?.plan_name === plan?.api_name) {
      if (tvPlan?.isPlanRemoved) {
        setTVPlan((prevPlan) => ({
          ...prevPlan,
          additionalChannels: JSON.parse(currentPlan?.single_channels),
          basePlan: currentPlan?.plan_name,
          additionalPlan: JSON.parse(currentPlan?.extra_packages),
          DVR: JSON.parse(currentPlan?.iptv_products),
          isPlanRemoved: !tvPlan?.isPlanRemoved,
          isPlanUpdated: true,
        }));
      } else {
        if (tvPlan?.isPlanUpdated) {
          setTVPlan((prevPlan) => ({
            ...prevPlan,
            basePlan: [],
            isPlanUpdated: false,
            isPlanRemoved: !tvPlan?.isPlanRemoved,
          }));
        } else {
          setTVPlan((prevPlan) => ({
            ...prevPlan,
            basePlan: currentPlan?.plan_name,
            additionalChannels: newAdditionalChannel,
            isPlanUpdated: true,
          }));
        }
      }
    } else {
      setTVPlan((prevPlan) => ({
        ...prevPlan,
        basePlan: plan?.api_name,
        isPlanUpdated: currentPlan?.basePlan ? true : false,
        additionalChannels: newAdditionalChannel,
        isPlanRemoved: false,
      }));
    }
  };

  // Handler for selecting an additional package
  const handleAdditionalPackageSelect = (plan: any) => {
    const apiName = plan?.api_name;
    setTVPlan(() => {
      const additionalPlan = tvPlan.additionalPlan;
      const isPlanIncluded = additionalPlan.includes(apiName);
      const updatedAdditionalPlan = isPlanIncluded
        ? additionalPlan.filter((p) => p !== apiName)
        : [...additionalPlan, apiName];
      return {
        ...tvPlan,
        additionalPlan: updatedAdditionalPlan,
      };
    });
  };

  // Handler for selecting a single channel
  const handleChannelSelect = (plan: any) => {
    const apiName = plan?.api_name;
    setTVPlan((prevPlan) => {
      const additionalChannels = prevPlan.additionalChannels;
      const isPlanIncluded = additionalChannels.includes(apiName);
      const updatedAdditionalChannels = isPlanIncluded
        ? additionalChannels.filter((p) => p !== apiName)
        : [...additionalChannels, apiName];

      return {
        ...prevPlan,
        additionalChannels: updatedAdditionalChannels,
      };
    });
  };
  // Handler for selecting a DVR option
  const handleDVRSelect = (plan: any) => {
    const apiName = plan?.api_name;
    setTVPlan(() => {
      const dVRChannel = tvPlan.DVR;
      const isPlanIncluded = dVRChannel.includes(apiName);
      const updatedDVR = isPlanIncluded
        ? dVRChannel.filter((p) => p !== apiName)
        : [...dVRChannel, apiName];

      return {
        ...tvPlan,
        DVR: updatedDVR,
      };
    });
  };

  // Handler for submitting the TV plan
  const handleTVPlanSubmit = async () => {
    if (!televisionEstimateLoading?.isLoading) {
      try {
        const data = {
          customer_details_id: parseInt(id as string),
          customer_subscription_id: subscriptionID,
          plan_name: tvPlan?.basePlan,
          extra_packages: tvPlan?.additionalPlan,
          single_channels: tvPlan?.additionalChannels,
          iptv_products: tvPlan?.DVR,
        };
        const response = await televisionEstimate(data).unwrap();
        if (response.status === 200) {
          setProratedData({
            prorated_amount: response?.data?.prorated_amount,
          });
          dispatch(toggleShowTelevisionPopup());
          dispatch(toggleShowTelevisionConfirmationPopup());
        }
      } catch (error: any) {
        if (error?.data?.error?.length > 0) {
          dispatch(
            addNotification({
              type: "error",
              message: error?.data?.error?.[0]?.message,
            })
          );
        } else {
          dispatch(
            addNotification({ type: "error", message: error?.data?.message })
          );
        }
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Handler for confirmation the TV plan
  const handleTVPlanConfirm = async () => {
    if (!televisionUpdateLoading?.isLoading) {
      try {
        const data = {
          customer_details_id: parseInt(id as string),
          customer_subscription_id: subscriptionID,
          plan_name: tvPlan?.basePlan,
          extra_packages: tvPlan?.additionalPlan,
          single_channels: tvPlan?.additionalChannels,
          iptv_products: tvPlan?.DVR,
        };
        const response = await televisionUpdate(data).unwrap();
        if (response.status === 200) {
          dispatch(toggleShowTelevisionConfirmationPopup());
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
          refetch();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
    // if (!tvPlan.basePlan) {
    //   setTVPlan((plan) => ({
    //     ...plan,
    //     isPlanRemoved: true,
    //   }));
    // } else {
    //   dispatch(setPlan(tvPlan));
    // }
  };

  // Handler for confirmation the TV plan
  const handleTVPlanCancelConfirm = async () => {
    if (!cancelTvServiceLoading?.isLoading) {
      try {
        const data = {
          cancel_type: "tv",
          customer_details_id: parseInt(id as string),
          customer_subscription_id: subscriptionID,
        };
        const response = await cancelTvService(data).unwrap();
        if (response.status === 200) {
          setTVPlan((prev) => ({
            ...prev,
            isPlanRemoved: true,
          }));
          dispatch(toggleShowTelevisionConfirmationPopup());
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
          refetch();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Handler for cancelling the TV plan selection
  const handleTvPlanCancel = () => {
    dispatch(toggleShowTelevisionPopup());
    setTVPlan((prevPlan) => ({
      ...prevPlan,
      additionalChannels: currentPlan?.single_channels
        ? JSON.parse(currentPlan?.single_channels)
        : [],
      basePlan: currentPlan?.plan_name ? currentPlan?.plan_name : "",
      additionalPlan: currentPlan?.extra_packages
        ? JSON.parse(currentPlan?.extra_packages)
        : [],
      DVR: currentPlan?.iptv_products
        ? JSON.parse(currentPlan?.iptv_products)
        : [],
      isPlanRemoved: false,
    }));
  };

  // Handler to fetch the list of available TV plans from the API
  const handleGetTelevision = async () => {
    try {
      const query = {
        type: "tv",
      };
      const response = await getPlans(query).unwrap();
      if (response.status === 200) {
        setTvList(response?.data);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  //  Handler to show the TV plans popup and fetch the plans
  const handleShowTelevisions = () => {
    dispatch(toggleShowTelevisionPopup());
    handleGetTelevision();
  };

  const handleTVCancelService = () => {
    dispatch(toggleShowTelevisionPopup());
    dispatch(toggleShowTelevisionConfirmationPopup());
  };

  // Side effects to handle add, edit & cancel TV
  useEffect(() => {
    if (subscriptionID) {
      if (
        stage === "Onboarding" ||
        stage === "Online" ||
        stage === "Outstanding" ||
        stage === "Payment arrangements" ||
        stage === "Offboarding"
      ) {
        if (currentPlan?.state_text === "Complete") {
          setIsEditable(true);
        } else if (currentPlan === null) {
          setIsCreate(true);
        }
      }

      if (
        stage === "Onboarding" ||
        stage === "Online" ||
        stage === "Outstanding" ||
        stage === "Payment arrangements"
      ) {
        if (currentPlan?.state_text === "Complete") {
          setIsCancel(true);
        }
      }
    }
  }, [subscriptionID, stage, currentPlan?.state_text]);

  return (
    <ServiceBox border={currentPlan?.plan_name ? false : true}>
      <div className={`flex flex-col gap-5 2xl:gap-10 basis-full`}>
        <div className={`flex flex-col 2xl:gap-5 gap-2.5`}>
          <div className="flex justify-between rounded-10 items-center">
            <div>
              <p className="text-xl 2xl:text-[26px] font-medium">Television</p>
            </div>
            {/* <div
              className={`border border-black 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey ${
                currentPlan == null ||
                (subscriptionID &&
                  currentPlan?.state_text === "Complete" &&
                  currentPlan?.account_status === "ACTIVE")
                  ? ""
                  : "opacity-50 pointer-events-none"
              }`}
              onClick={handleShowTelevisions}
            > */}

            <div
              className={`border border-[#D7B9FF] 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 bg-white hover:bg-purple-50 ${
                isEditable || isCreate
                  ? ""
                  : "opacity-50 custom-cursor-not-allowed"
              }`}
              onClick={
                isEditable || isCreate ? handleShowTelevisions : () => {}
              }
            >
              {currentPlan?.plan_name ? (
                <EditIcon width={20} height={20} />
              ) : (
                <AddIcon width={20} height={20} />
              )}
            </div>
          </div>
          {currentPlan != null && (
            <div className="">
              <span className="text-base font-medium">
                {formatCurrency(
                  currentPlan?.total_service_cost
                    ? currentPlan?.total_service_cost
                    : 0,
                  true
                )}
              </span>
              -
              <span className="text-base font-medium">
                {currentPlan?.packages?.length > 0 &&
                  currentPlan?.packages.slice(0, 4).map((item, index) => {
                    return (
                      item +
                      (index < currentPlan.packages.slice(0, 4).length - 1
                        ? ", "
                        : "")
                    );
                  })}
              </span>
            </div>
          )}
        </div>
        {currentPlan?.state_text === "In Progress" && (
          <div className="bg-white border border-[#FBC400] flex flex-col 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
            <div className="flex gap-2.5 items-center">
              <div className="bg-warning-light  rounded-[6px] p-2 gap-2.5">
                <p className=" text-warning">Processing</p>
              </div>
            </div>
            <div>
              <p className="text-base font-bold">{`Your TV plan is being updated.`}</p>
            </div>
          </div>
        )}
      </div>
      {showViewAll?.status && (
        <Popup
          title=""
          width="840px"
          height="720px"
          closeHandler={handleViewAll}
        >
          <div className="flex flex-col gap-5">
            <div className="flex gap-5 items-center">
              <span className="cursor-pointer" onClick={handleViewAll}>
                <BackIcon />
              </span>
              <p className="text-[26px] font-bold">{showViewAll?.data?.name}</p>
            </div>
            <div className="flex justify-center gap-y-5 gap-x-5 flex-wrap p-5 items-center max-md:gap-x-[calc((100%-(200px))/4)] max-lg:gap-x-[calc((100%-(180px))/9)] rounded-20 border-2 border-[#EBDBFF]">
              {showViewAll?.data?.included_channels.map(
                (item: any, index: number) => (
                  <div
                    key={index}
                    className={`select-none w-[65px] cursor-pointer h-[45px] sm:w-[85px] sm:h-[47px] lg:w-[100px] lg:h-[60px] relative bg-white p-1.5 rounded-10 z-10 shadow shadow-purple-200 duration-300`}
                  >
                    <img
                      src={item?.image_url}
                      alt={item?.name}
                      className="h-full w-full object-contain object-center"
                      title={item?.name}
                    />
                  </div>
                )
              )}
            </div>
          </div>
        </Popup>
      )}
      {/* Tv Popup */}
      {showPopup && (
        <Popup
          title="TV"
          closeHandler={() => {
            if (!televisionEstimateLoading?.isLoading) {
              handleTvPlanCancel();
            }
          }}
          width={isDesktop ? "974px" : "760px"}
          height="1200px"
        >
          <TVPlan
            closeHandler={handleTvPlanCancel}
            tvList={tvList}
            isLoading={plansLoading?.isLoading}
            tvPlan={tvPlan}
            isCancel={isCancel}
            handleChannelSelect={handleChannelSelect}
            handleBasePackageSelect={handleBasePackageSelect}
            handleAdditionalPackageSelect={handleAdditionalPackageSelect}
            handleDVRSelect={handleDVRSelect}
            handleTVPlanSubmit={handleTVPlanSubmit}
            handleViewAll={handleViewAll}
            btnLoading={televisionEstimateLoading?.isLoading}
            handleTVCancelService={handleTVCancelService}
          />
        </Popup>
      )}
      {showConfirmationPopup && (
        <ConfirmationMessagePopup
          title={
            !tvPlan?.basePlan
              ? "Confirm adding TV"
              : tvPlan.isPlanRemoved
              ? "Confirm TV cancelation"
              : "Confirm TV plan change"
          }
          message={
            !tvPlan?.basePlan
              ? `Your adjustments to your TV plan should be updated within a couple minutes. Your card on file will be charged a prorated amount of ${formatCurrency(
                  proratedData?.prorated_amount
                    ? proratedData?.prorated_amount
                    : 0
                )}.<br/> We will text you instructions on how to download the TV app and your username and password.`
              : tvPlan.isPlanRemoved
              ? `Your TV will remain live until the end of your billing cycle on ${billingDate}`
              : `Your adjustments to your TV plan should be updated within a couple minutes. Your card on file will be charged a prorated amount of ${formatCurrency(
                  proratedData?.prorated_amount
                    ? proratedData?.prorated_amount
                    : 0
                )}`
          }
          handleSubmit={
            tvPlan.isPlanRemoved
              ? handleTVPlanCancelConfirm
              : handleTVPlanConfirm
          }
          closeHandler={() => {
            dispatch(toggleShowTelevisionConfirmationPopup());
            dispatch(toggleShowTelevisionPopup());
          }}
          isLoading={
            tvPlan.isPlanRemoved
              ? cancelTvServiceLoading?.isLoading
              : televisionUpdateLoading?.isLoading
          }
          btnText="Confirm"
        />
      )}
    </ServiceBox>
  );
};

export default TVPlanBox;
