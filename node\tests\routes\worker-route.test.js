const request = require('supertest');
const express = require('express');
const workerRoute = require('../../routes/worker-route');

// Mock the auth controller
jest.mock('../../controllers/auth-controller', () => ({
  userSignupProcess: jest.fn((req, res) => res.status(200).json({ 
    message: 'User signup process completed successfully', 
    data: {
      processId: 'process_12345',
      customerId: '12345',
      email: '<EMAIL>',
      status: 'completed',
      processedAt: '2024-01-18T10:30:00Z',
      steps: [
        {
          step: 'validate_user_data',
          status: 'completed',
          completedAt: '2024-01-18T10:30:01Z',
          duration: 150
        },
        {
          step: 'create_cognito_user',
          status: 'completed',
          completedAt: '2024-01-18T10:30:03Z',
          duration: 2000
        },
        {
          step: 'create_database_record',
          status: 'completed',
          completedAt: '2024-01-18T10:30:05Z',
          duration: 1500
        },
        {
          step: 'send_welcome_email',
          status: 'completed',
          completedAt: '2024-01-18T10:30:07Z',
          duration: 800
        },
        {
          step: 'setup_initial_subscription',
          status: 'completed',
          completedAt: '2024-01-18T10:30:10Z',
          duration: 3000
        }
      ],
      totalDuration: 7450,
      createdResources: {
        cognitoUserId: 'cognito_user_12345',
        databaseRecordId: 'db_record_12345',
        subscriptionId: 'sub_12345',
        welcomeEmailId: 'email_12345'
      },
      notifications: {
        welcomeEmailSent: true,
        smsVerificationSent: false,
        accountSetupComplete: true
      }
    }
  }))
}));

describe('Worker Routes', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    workerRoute(app);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /worker/register-process', () => {
    it('should process user signup successfully', async () => {
      const signupData = {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'SecurePassword123!',
        phone: '+**********',
        address: {
          street: '123 Main St',
          city: 'Halifax',
          province: 'NS',
          postalCode: 'B3H 1A1'
        },
        selectedPlan: {
          planId: 'internet_100',
          planName: 'Internet 100',
          price: 69.99
        },
        paymentMethod: {
          cardToken: 'card_token_12345',
          cardLast4: '4242',
          cardBrand: 'visa'
        },
        referralCode: 'FRIEND123',
        marketingConsent: true,
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupData)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
      expect(response.body.data).toHaveProperty('processId');
      expect(response.body.data).toHaveProperty('customerId');
      expect(response.body.data).toHaveProperty('email');
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('processedAt');
      expect(response.body.data).toHaveProperty('steps');
      expect(response.body.data).toHaveProperty('totalDuration');
      expect(response.body.data).toHaveProperty('createdResources');
      expect(response.body.data).toHaveProperty('notifications');
      
      // Verify steps array structure
      expect(Array.isArray(response.body.data.steps)).toBe(true);
      expect(response.body.data.steps.length).toBeGreaterThan(0);
      
      const firstStep = response.body.data.steps[0];
      expect(firstStep).toHaveProperty('step');
      expect(firstStep).toHaveProperty('status');
      expect(firstStep).toHaveProperty('completedAt');
      expect(firstStep).toHaveProperty('duration');
      
      // Verify createdResources structure
      expect(response.body.data.createdResources).toHaveProperty('cognitoUserId');
      expect(response.body.data.createdResources).toHaveProperty('databaseRecordId');
      expect(response.body.data.createdResources).toHaveProperty('subscriptionId');
      expect(response.body.data.createdResources).toHaveProperty('welcomeEmailId');
      
      // Verify notifications structure
      expect(response.body.data.notifications).toHaveProperty('welcomeEmailSent');
      expect(response.body.data.notifications).toHaveProperty('smsVerificationSent');
      expect(response.body.data.notifications).toHaveProperty('accountSetupComplete');
      
      // Verify data types
      expect(typeof response.body.data.processId).toBe('string');
      expect(typeof response.body.data.customerId).toBe('string');
      expect(typeof response.body.data.email).toBe('string');
      expect(typeof response.body.data.status).toBe('string');
      expect(typeof response.body.data.processedAt).toBe('string');
      expect(typeof response.body.data.totalDuration).toBe('number');
      expect(typeof firstStep.step).toBe('string');
      expect(typeof firstStep.status).toBe('string');
      expect(typeof firstStep.completedAt).toBe('string');
      expect(typeof firstStep.duration).toBe('number');
      expect(typeof response.body.data.notifications.welcomeEmailSent).toBe('boolean');
      expect(typeof response.body.data.notifications.smsVerificationSent).toBe('boolean');
      expect(typeof response.body.data.notifications.accountSetupComplete).toBe('boolean');
    });

    it('should handle signup process with minimal required data', async () => {
      const minimalSignupData = {
        email: '<EMAIL>',
        firstName: 'Min',
        lastName: 'User',
        password: 'Password123!',
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(minimalSignupData)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
      expect(response.body.data).toHaveProperty('processId');
      expect(response.body.data).toHaveProperty('customerId');
    });

    it('should handle signup process with different plan selection', async () => {
      const signupDataWithBundle = {
        email: '<EMAIL>',
        firstName: 'Bundle',
        lastName: 'User',
        password: 'BundlePass123!',
        selectedPlan: {
          planId: 'internet_tv_bundle',
          planName: 'Internet + TV Bundle',
          price: 119.99
        },
        paymentMethod: {
          cardToken: 'card_token_67890',
          cardLast4: '1234',
          cardBrand: 'mastercard'
        },
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupDataWithBundle)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
    });

    it('should handle signup process with referral code', async () => {
      const signupDataWithReferral = {
        email: '<EMAIL>',
        firstName: 'Referred',
        lastName: 'User',
        password: 'ReferredPass123!',
        referralCode: 'PURPLECOW123',
        selectedPlan: {
          planId: 'internet_300',
          planName: 'Internet 300',
          price: 89.99
        },
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupDataWithReferral)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
    });

    it('should handle signup process with marketing consent', async () => {
      const signupDataWithConsent = {
        email: '<EMAIL>',
        firstName: 'Marketing',
        lastName: 'User',
        password: 'MarketingPass123!',
        marketingConsent: true,
        newsletterSubscription: true,
        smsNotifications: false,
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupDataWithConsent)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
    });

    it('should handle signup process with complete address information', async () => {
      const signupDataWithAddress = {
        email: '<EMAIL>',
        firstName: 'Address',
        lastName: 'User',
        password: 'AddressPass123!',
        address: {
          street: '456 Oak Avenue',
          unit: 'Apt 2B',
          city: 'Sydney',
          province: 'Nova Scotia',
          postalCode: 'B1P 2C3',
          country: 'Canada'
        },
        mailingAddress: {
          street: '789 Pine Street',
          city: 'Halifax',
          province: 'Nova Scotia',
          postalCode: 'B3H 3C3',
          country: 'Canada'
        },
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupDataWithAddress)
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
    });

    it('should handle signup process with invalid data gracefully', async () => {
      const invalidSignupData = {
        email: 'invalid-email',
        firstName: '',
        password: '123', // Too short
        termsAccepted: false
      };

      await request(app)
        .post('/worker/register-process')
        .send(invalidSignupData)
        .expect(200); // Mocked to return success
    });

    it('should handle signup process with authentication headers', async () => {
      const signupData = {
        email: '<EMAIL>',
        firstName: 'Auth',
        lastName: 'User',
        password: 'AuthPass123!',
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupData)
        .set('Authorization', 'Bearer worker-token')
        .set('X-Worker-ID', 'worker-123')
        .set('X-Process-ID', 'process-456')
        .expect(200);

      expect(response.body.message).toBe('User signup process completed successfully');
    });

    it('should verify all signup process steps are completed', async () => {
      const signupData = {
        email: '<EMAIL>',
        firstName: 'Steps',
        lastName: 'User',
        password: 'StepsPass123!',
        termsAccepted: true
      };

      const response = await request(app)
        .post('/worker/register-process')
        .send(signupData)
        .expect(200);

      const steps = response.body.data.steps;
      const expectedSteps = [
        'validate_user_data',
        'create_cognito_user',
        'create_database_record',
        'send_welcome_email',
        'setup_initial_subscription'
      ];

      expectedSteps.forEach(expectedStep => {
        const step = steps.find(s => s.step === expectedStep);
        expect(step).toBeDefined();
        expect(step.status).toBe('completed');
        expect(step.completedAt).toBeDefined();
        expect(typeof step.duration).toBe('number');
      });
    });
  });
});
