import { ChangeEvent, useState } from "react";
import { ContactInfoDataState } from "../../typings/typing";
import { regEx } from "../../utils/helper";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { EditIcon } from "../../assets/Icons";
import profileImg from "../../assets/images/userProfile.png";
import { useUpdateCustomerMutation } from "../../services/api";
import { logout } from "../../store/reducers/authenticationReducer";
import { addNotification } from "../../store/reducers/toasterReducer";
import Button from "../common/Button";
import InputFields from "../forms/InputFields";

interface AppHeaderProps {
  closeHandler: () => void;
  data: ContactInfoDataState;
  refreshData: () => void;
}

// Function to validate contact information data
const validateFormData = (contactInfoData: ContactInfoDataState) => {
  const errors: Partial<ContactInfoDataState> = {};
  let isValid = true;

  if (contactInfoData?.cell_phone?.length < 8) {
    isValid = false;
    errors.cell_phone = "Enter valid phone number";
  }

  //! Don't add validaiton because its in readonly field
  // else if (!regEx.PHONE.test(contactInfoData?.cell_phone)) {
  //   isValid = false;
  //   errors.cell_phone = "Invalid phone number";
  // }

  if (contactInfoData?.additional_name) {
    if (contactInfoData.additional_name.length < 4) {
      isValid = false;
      errors.additional_name =
        "Additional name must be at least 4 characters long";
    } else if (contactInfoData.additional_name.length > 50) {
      isValid = false;
      errors.additional_name =
        "Additional name must be at most 50 characters long";
    }
  }

  if (contactInfoData?.company_name) {
    if (contactInfoData.company_name.length < 4) {
      isValid = false;
      errors.company_name = "Company name must be at least 4 characters long";
    } else if (contactInfoData.company_name.length > 50) {
      isValid = false;
      errors.company_name = "Company name must be at most 50 characters long";
    }
  }

  if (contactInfoData?.secondary_phone?.length) {
    if (!regEx.PHONE.test(contactInfoData?.secondary_phone)) {
      isValid = false;
      errors.secondary_phone = "Invalid phone number";
    }
  }
  if (contactInfoData?.cell_phone === contactInfoData?.secondary_phone) {
    isValid = false;
    errors.secondary_phone =
      "Secondary number must not be the same as cell phone.";
  }
  return { isValid, errors };
};

const ContactInfoPopup: React.FC<AppHeaderProps> = ({
  data,
  closeHandler,
  refreshData,
}) => {
  const [contactInfoData, setContactInfoData] =
    useState<ContactInfoDataState>(data);
  const [contactInfoDataError, setContactInfoDataError] = useState<
    Partial<ContactInfoDataState>
  >({});
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageError, setImageError] = useState<string | null>(null);
  const [updateCustomer, updateCustomerLoading] = useUpdateCustomerMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Function to handle saving contact information data
  const handleSaveContactInfoData = async () => {
    try {
      const formData = new FormData();
      formData.append(
        "secondary_phone",
        contactInfoData?.secondary_phone?.trim() || ""
      );

      formData.append(
        "additional_name",
        contactInfoData?.additional_name?.trim() || ""
      );

      formData.append(
        "company_name",
        contactInfoData?.company_name?.trim() || ""
      );

      if (imageFile) formData.append("image_url", imageFile);

      const response = await updateCustomer(formData).unwrap();
      if (response.status === 200) {
        closeHandler();
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
        refreshData();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle input change
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    if (name == "cell_phone" && value && !/^\d*$/.test(value)) {
      return;
    }
    if (name == "secondary_phone" && value && !/^\d*$/.test(value)) {
      return;
    }
    setContactInfoData((state) => ({ ...state, [name]: value }));
    setContactInfoDataError((state) => ({ ...state, [name]: "" }));
  };

  // Function to handle form submission
  const handleSubmit = (e: ChangeEvent<HTMLFormElement>): void => {
    e.preventDefault();
    const { isValid, errors } = validateFormData(contactInfoData);
    setContactInfoDataError(errors);
    if (isValid && !updateCustomerLoading?.isLoading && !imageError) {
      setContactInfoDataError({});
      handleSaveContactInfoData();
    }
  };

  // Function to handle click event on edit icon
  const handleEditIconClick = () => {
    document.getElementById("file-input")?.click();
  };

  // Function to handle image change
  const handleImageChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (file) {
      const validTypes = ["image/png", "image/jpeg", "image/jpg"];
      if (!validTypes.includes(file.type)) {
        setImageError(
          "Error: Invalid file format. Please upload an image file in one of the following formats: PNG, JPG, or JPEG."
        );
      } else if (file.size > 2 * 1024 * 1024) {
        setImageError("Image size must be less than 2 MB");
      } else {
        setImageFile(file);
        setImageError(null);
        setContactInfoData((state: any) => ({
          ...state,
          image_url: URL.createObjectURL(file),
        }));
      }
    }
  };

  return (
    <div>
      <div className="form-container">
        <form onSubmit={handleSubmit}>
          <div className="flex flex-col gap-[19px]">
            <div className="flex flex-col xl:gap-5 gap-3">
              <div className="bg-[#F5EFFF] flex gap-2.5 p-2.5 items-center w-fit rounded-2xl">
                <img
                  src={contactInfoData?.image_url || profileImg}
                  alt="profile picture"
                  width={40}
                  height={40}
                  className="bg-primary w-10 h-10 object-cover object-center rounded-full p-0.5"
                />
                <p className="text-base font-medium">Update Picture</p>
                <div className="cursor-pointer" onClick={handleEditIconClick}>
                  <EditIcon />
                  <input
                    id="file-input"
                    type="file"
                    accept=".jpg, .jpeg, .png"
                    hidden
                    onChange={handleImageChange}
                  />
                </div>
              </div>
              {imageError && (
                <p className="text-red-500 text-sm">{imageError}</p>
              )}
              <div className="flex flex-col md:flex-row md:flex-grow gap-2.5">
                <div className="flex md:flex-grow gap-2.5 flex-col">
                  <InputFields
                    isReadOnly={true}
                    title="First name"
                    changeEvent={handleInputChange}
                    placeHolder={"Johnson"}
                    isErrorMsg={contactInfoDataError.first_name}
                    attributes={{
                      name: "first_name",
                      value: contactInfoData?.first_name || "",
                    }}
                  />
                </div>
                <div className="flex md:flex-grow gap-2.5 flex-col">
                  <InputFields
                    isReadOnly={true}
                    title="Last name"
                    changeEvent={handleInputChange}
                    placeHolder={"Doe"}
                    isErrorMsg={contactInfoDataError.last_name}
                    attributes={{
                      name: "last_name",
                      value: contactInfoData?.last_name,
                    }}
                  />
                </div>
              </div>
              <div className="md:flex-grow">
                <InputFields
                  isReadOnly={true}
                  title="Email"
                  changeEvent={handleInputChange}
                  placeHolder={"<EMAIL>"}
                  isErrorMsg={contactInfoDataError.email}
                  className="bg-white shadow-none border-none"
                  attributes={{
                    name: "email",
                    value: contactInfoData?.email || "",
                  }}
                />
              </div>
              <div className="md:flex-grow">
                <InputFields
                  title="Cell phone"
                  isReadOnly={true}
                  type="tel"
                  // changeEvent={handleInputChange}
                  placeHolder={"(*************"}
                  // isErrorMsg={contactInfoDataError.cell_phone}
                  attributes={{
                    name: "cell_phone",
                    maxLength: 10,
                    value: contactInfoData?.cell_phone || "",
                  }}
                />
              </div>
              <div className="md:flex-grow">
                <InputFields
                  title="Secondary number"
                  type="tel"
                  changeEvent={handleInputChange}
                  placeHolder={"Secondary number"}
                  isErrorMsg={contactInfoDataError.secondary_phone}
                  attributes={{
                    name: "secondary_phone",
                    maxLength: 10,
                    value: contactInfoData?.secondary_phone || "",
                  }}
                  className="!bg-[#F5EFFF]"
                />
              </div>
              <div className="md:flex-grow">
                <InputFields
                  title="Additional name on account"
                  changeEvent={handleInputChange}
                  placeHolder={"Sarah Smith"}
                  isErrorMsg={contactInfoDataError.additional_name}
                  attributes={{
                    name: "additional_name",
                    value: contactInfoData?.additional_name || "",
                  }}
                  className="!bg-[#F5EFFF]"
                />
              </div>
              <div className="md:flex-grow">
                <InputFields
                  title="Business name"
                  changeEvent={handleInputChange}
                  placeHolder={"Business name"}
                  isErrorMsg={contactInfoDataError.company_name}
                  attributes={{
                    name: "company_name",
                    value: contactInfoData?.company_name || "",
                  }}
                  className="!bg-[#F5EFFF]"
                />
              </div>
            </div>
            <div className="flex flex-col gap-2.5 md:gap-5 md:flex-row">
              <Button
                title="Cancel"
                btnType="transparent"
                type="button"
                clickEvent={closeHandler}
                
              />
              <Button
                title="Update"
                type="submit"
                isLoading={updateCustomerLoading?.isLoading}
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactInfoPopup;
