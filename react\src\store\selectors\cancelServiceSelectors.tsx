import { rootState } from "../reducers/rootReducer";

export const cancelDate = (store: rootState) =>
  store.cancelServiceReducer.cancelDate;

export const deleteOrder = (store: rootState) =>
  store.cancelServiceReducer.deleteOrder;

export const feedback = (store: rootState) =>
  store.cancelServiceReducer.feedback;

export const showSelectCancelServiceDatePopup = (store: rootState) =>
  store.cancelServiceReducer.showSelectCancelServiceDatePopup;

export const showFeedbackPopup = (store: rootState) =>
  store.cancelServiceReducer.showFeedbackPopup;

export const showOfferPopup = (store: rootState) =>
  store.cancelServiceReducer.showOfferPopup;

export const showConfirmPopup = (store: rootState) =>
  store.cancelServiceReducer.showConfirmPopup;

export const showDeleteOrderPopup = (store: rootState) =>
  store.cancelServiceReducer.showDeleteOrderPopup;
