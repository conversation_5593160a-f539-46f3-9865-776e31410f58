const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const { getCurrentAtlanticTime, hasValidChanges, sanitizeValue } = require('../../helper/custom-helper');
const CustomerServices = require("../../services/customer-sync-services");
const customerServices = new CustomerServices();
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const INTERVAL = CONFIG.interval;

// Helper function to map incoming record_type to allowed enum values
function mapRecordType(input) {
    if (!input) return 'None';
    const mapping = {
        'Add Order': 'Add Order',
        'Move Order': 'Move Order',
        'Disconnect Order': 'Disconnect Order',
        'Speed Order': 'Speed Order',
        'Name Change Order': 'Name Change Order',
        'Vacation Mode': 'Vacation Mode Order',
        'Vacation Mode Order': 'Vacation Mode Order',
        'None': 'None',
    };
    // Try direct match first
    if (mapping[input]) return mapping[input];
    // Try case-insensitive match
    const key = Object.keys(mapping).find(k => k.toLowerCase() === input.toLowerCase());
    if (key) return mapping[key];
    return 'None';
}

class OrderTestServices {
    async getOrderDetails() {
        let orderCount = 0;
        try {
            let query = `SELECT Id, Name, Record_Type_Name__c, Related_Shipping_Order__c, Requested_Disconnect_Date__c, Expected_Completion_Date__c, Stage__c, Response_Date__c, LastModifiedDate, Speed__c, Requested_Install_Date__c, Requested_Move_Date__c, Install_Date__c, Install_Time__c, Order_Reject_Reason__c, Order_Reject_Reason_Solved__c, Submit_Date__c, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c, (SELECT Id, Name, Full_Mailing_Address__c, Ship_Date__c, Ship_Drop_Off_Date__c, Tracking_URL__c, Courier__c, Package_Delivered__c, CreatedDate, LastModifiedDate FROM Shipping__r) FROM Order__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const orderDetails = await salesforceConnection.fetchAllRecords(query);
            orderCount = orderDetails?.length || 0;

            if (orderCount) {
                await this.checkAndManageDetails(orderDetails);
            }

            return { execute: true, synctype: "getOrderDetails", status: true, orderCount };
        } catch (error) {
            console.error("Sync service get Speed Change Detailss -> ", error);
            return { execute: true, synctype: "getOrderDetails", status: false, orderCount, error: error?.message || null };
        }
    }

    async checkAndManageDetails(orderDetails) {
        try {
            for (const orderDetail of orderDetails) {
                await this.checkTableToUpdate(orderDetail);
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async checkTableToUpdate(orderDetail) {
        try {
            const { Record_Type_Name__c: Name } = orderDetail;
            if (!Name) return;
            switch (Name) {
                case "Speed Change Order":
                    this.checkSpeedChangeOrder(orderDetail);
                    break;
                case "Disconnect Order":
                    this.checkDisconnectOrder(orderDetail);
                    break;
                case "Move Order":
                    this.checkMoveOrder(orderDetail);
                    break;
                case "Modem Swap Order":
                    this.checkSwapOrder(orderDetail);
                    break;
                default:
                    this.checkAddOrder(orderDetail, Name);
                    break;
            }
        } catch (error) {
            console.error("SalesforceService checkTableToUpdate -> ", error);
        }
    }

    async checkSpeedChangeOrder(orderDetail) {
        try {
            const { Id: sf_record_id, Expected_Completion_Date__c: expected_completion_date, LastModifiedDate: sf_updatedAt, Stage__c: stage, Response_Date__c: response_date, Speed__c: speed, Name: sf_name } = orderDetail;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_speed_change_order SET 
                sf_name = ?,
                sf_updatedAt = ?, 
                updatedAt = ?,
                stage = ?,
                speed = ?,
                response_date = ?,
                expected_completion_date = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            if (stage) queryParams.push(stage.replace(/['"`]/g, ''));
            else queryParams.push(null);

            if (speed) queryParams.push(speed);
            else queryParams.push(null);

            if (response_date) queryParams.push(getCurrentAtlanticTime(response_date));
            else queryParams.push(null);

            if (expected_completion_date) queryParams.push(expected_completion_date);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService checkSpeedChangeOrder -> ", error);
        }
    }

    async checkDisconnectOrder(orderDetail) {
        try {
            const { Id: sf_record_id, Requested_Disconnect_Date__c: requested_disconnect_date, LastModifiedDate: sf_updatedAt, Name: sf_name } = orderDetail;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_disconnect_order SET 
                sf_name = ?,
                sf_updatedAt = ?, 
                updatedAt = ?,
                requested_disconnect_date = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            if (requested_disconnect_date) queryParams.push(requested_disconnect_date);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService checkDisconnectOrder -> ", error);
        }
    }

    async checkMoveOrder(orderDetail) {
        try {
            const { Id: sf_record_id, Requested_Install_Date__c, LastModifiedDate: sf_updatedAt, Install_Date__c, Install_Time__c, Order_Reject_Reason__c, Order_Reject_Reason_Solved__c, Submit_Date__c, Stage__c, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c, Name: sf_name, Requested_Move_Date__c } = orderDetail;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_move_order SET 
                sf_name = ?,
                sf_updatedAt = ?, 
                updatedAt = NOW(),
                requested_install_date = ?,
                requested_move_date = ?,
                install_date = ?,
                submit_date = ?,
                stage = ?,
                install_time = ?,
                reject_reason = ?,
                reject_reason_solved = ?,
                unit = ?,
                street_number = ?,
                street_name = ?,
                city = ?,
                province = ?,
                postal_code = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            if (Requested_Install_Date__c) queryParams.push(Requested_Install_Date__c);
            else queryParams.push(null);

            if (Requested_Move_Date__c) queryParams.push(Requested_Move_Date__c);
            else queryParams.push(null);

            if (Install_Date__c) queryParams.push(Install_Date__c);
            else queryParams.push(null);

            if (Submit_Date__c) queryParams.push(getCurrentAtlanticTime(Submit_Date__c));
            else queryParams.push(null);

            if (Stage__c) queryParams.push(Stage__c);
            else queryParams.push(null);

            if (Install_Time__c) queryParams.push(Install_Time__c);
            else queryParams.push(null);

            if (Order_Reject_Reason__c) queryParams.push(Order_Reject_Reason__c);
            else queryParams.push(null);

            if (Order_Reject_Reason_Solved__c) queryParams.push(Order_Reject_Reason_Solved__c);
            else queryParams.push(null);

            if (Service_Suite_Unit__c) queryParams.push(Service_Suite_Unit__c);
            else queryParams.push(null);

            if (Service_Street_Number__c) queryParams.push(Service_Street_Number__c);
            else queryParams.push(null);

            if (Service_Street_Name__c) queryParams.push(Service_Street_Name__c);
            else queryParams.push(null);

            if (Service_City_Town__c) queryParams.push(Service_City_Town__c);
            else queryParams.push(null);

            if (Service_Province__c) queryParams.push(Service_Province__c);
            else queryParams.push(null);

            if (Service_Postal_Code__c) queryParams.push(Service_Postal_Code__c);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService checkMoveOrder -> ", error);
        }
    }

    async checkAddOrder(orderDetail, record_type) {
        try {
            const { Id: sf_record_id, LastModifiedDate: sf_updatedAt, Name: sf_name, Install_Date__c: install_date } = orderDetail;

            const { dataExist, orderData } = await this.checkCreationOrderExist(sf_record_id);
            if (!dataExist) return;

            const { creationSfId, shippingSfId } = orderData;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_creation_order SET 
                sf_name = ?, 
                install_date = ?,
                sf_updatedAt = ?, 
                updatedAt = ?,
                record_type = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            if (install_date) queryParams.push(install_date);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            // Use the helper to map to allowed enum value
            queryParams.push(mapRecordType(record_type));

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(creationSfId);

            await customerServices.executeQuery(query, queryParams);
            this.orderDetail(orderDetail, orderData);
        } catch (error) {
            console.error("SalesforceService checkAddOrder -> ", error);
        }
    }

    async checkCreationOrderExist(sf_record_id) {
        try {
            const query = `SELECT ieco.id as creationId, ieco.sf_record_id as creationSfId, cos.id as shippingId, cos.sf_record_id as shippingSfId  
            FROM internets_eastlink_creation_order ieco
            left join creation_order_shipping cos on cos.id = ieco.shipping_id 
            where ieco.sf_record_id = '${sf_record_id}'`;

            const res = await customerServices.executeQuery(query);
            return {
                dataExist: res.length > 0,
                orderData: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check checkCreationOrderExist -> ", error);
            return {
                dataExist: 0,
                orderData: null
            };
        }
    }

    async orderDetail(orderDetail, orderData) {
        try {
            const { shippingSfId } = orderData
            const { Related_Shipping_Order__c, Shipping__r } = orderDetail;
            let shippingDetails;
            if (Shipping__r && Shipping__r?.records?.length && Related_Shipping_Order__c) {
                const shippingRec = Shipping__r?.records;
                shippingDetails = shippingRec.find(sR => sR.Id === Related_Shipping_Order__c);
            }
            if (hasValidChanges(Related_Shipping_Order__c, shippingSfId)) this.updateDetails(orderData, shippingDetails);
        } catch (error) {
            console.error("Check orderDetail -> ", error);
        }
    }

    async updateDetails(orderData, shippingDetails) {
        try {
            const { creationId, shippingId: previousShippingId } = orderData;
            const updatedTime = getCurrentAtlanticTime();

            const updateQuery = `
                        UPDATE internets_eastlink_creation_order 
                        SET shipping_id = ?, updatedAt = ?
                        WHERE id = ?`;

            const shipping_id = await this.getAndInsertShippingDetails(shippingDetails);

            await customerServices.executeQuery(updateQuery, [shipping_id, updatedTime, creationId]);

            if (previousShippingId) {
                const { dataCount } = await customerServices.checkDataCount("internets_eastlink_creation_order", "shipping_id", previousShippingId);
                if (!dataCount) await customerServices.deleteQuery("creation_order_shipping", previousShippingId);
            }

        } catch (error) {
            console.error(`Error checkAndUpdateDetails -> `, error);
        }
    }

    async getAndInsertShippingDetails(shippingDetails) {
        try {
            let shipping_id = null;
            if (!shippingDetails) return null;

            const checkQuery = `SELECT id 
                        FROM creation_order_shipping 
                        WHERE sf_record_id = ${sanitizeValue(shippingDetails?.Id)}`;

            const existingRecord = await customerServices.executeQuery(checkQuery);
            if (existingRecord.length > 0) shipping_id = existingRecord[0].id;
            else {
                const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = shippingDetails;

                const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
                const createdAt = getCurrentAtlanticTime(CreatedDate);
                const updatedTime = getCurrentAtlanticTime();

                const query = `INSERT IGNORE INTO creation_order_shipping 
                                        (sf_record_id, sf_name, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt, package_deliverted_at, createdAt, updatedAt)
                                        VALUES (
                                        ${sanitizeValue(sf_record_id)}, 
                                        ${sanitizeValue(sf_name)}, 
                                        ${sanitizeValue(full_mailing_address)}, 
                                        ${sanitizeValue(ship_date)}, 
                                        ${sanitizeValue(ship_drop_off_date)}, 
                                        ${sanitizeValue(tracking_url)}, 
                                        ${sanitizeValue(courier)}, 
                                        '${formatedDate}', 
                                        ${sanitizeValue(package_deliverted_at)}, 
                                        '${createdAt}', 
                                        '${updatedTime}'
                                        )`;

                const insertStatus = await customerServices.executeQuery(query);
                if (insertStatus) shipping_id = insertStatus?.insertId || null
            }
            return shipping_id;
        } catch (error) {
            console.error(`Error getAndInsertShippingDetails -> `, error);
        }
    }

    async getTechAppDetails() {
        let techCount = 0;
        try {
            let query = `SELECT Id, Name, Install_Date__c, Install_Time__c, LastModifiedDate FROM Tech_Appointment__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const techAppDetails = await salesforceConnection.fetchAllRecords(query);
            techCount = techAppDetails?.length || 0;

            if (techCount) {
                await this.updateTechDetails(techAppDetails);
            }

            return { execute: true, synctype: "getOgetTechAppDetailsrderDetails", status: true, techCount };
        } catch (error) {
            console.error("Sync service get Speed Change Detailss -> ", error);
            return { execute: true, synctype: "getOgetTechAppDetailsrderDetails", status: false, techCount, error: error?.message || null };
        }
    }

    async updateTechDetails(techAppDetails) {
        try {
            for (const techAppDetail of techAppDetails) {
                const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, Name: sf_name } = techAppDetail;
                let queryParams = [];

                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const updatedTime = getCurrentAtlanticTime();

                let query = `UPDATE internets_eastlink_tech_appointment SET 
                sf_name = ?, 
                sf_updatedAt = ?, 
                updatedAt = ?,
                install_date = ?,
                install_time = ?`;

                if (sf_name) queryParams.push(sf_name);
                else queryParams.push(null);

                queryParams.push(modifiedDate, updatedTime);

                if (install_date) queryParams.push(install_date);
                else queryParams.push(null);

                if (install_time) queryParams.push(install_time);
                else queryParams.push(null);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);

                await customerServices.executeQuery(query, queryParams);
            }
        } catch (error) {
            console.error("SalesforceService updateTechDetails -> ", error);
        }
    }

    async checkSwapOrder(orderDetail) {
        try {
            const { Id: sf_record_id, Stage__c, LastModifiedDate: sf_updatedAt, Name: sf_name } = orderDetail;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_swap_order SET 
                sf_name = ?,
                sf_updatedAt = ?, 
                updatedAt = ?,
                stage = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            let value = Stage__c;
            let stage = null;
            if (value) stage = value.replace(/['"`]/g, '');
            queryParams.push(stage);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService checkSwapOrder -> ", error);
        }
    }

    async getCreationOrderDetails() {
        try {
            const query = `SELECT id, sf_record_id FROM internets_eastlink_creation_order`;

            const creationOrderDetails = await customerServices.executeQuery(query);

            if (!creationOrderDetails?.length) return;
            let creationOrderSfRecId = [];
            for (const creationOrderDetail of creationOrderDetails) creationOrderSfRecId.push(creationOrderDetail.sf_record_id);
            if (!creationOrderSfRecId?.length) return;

            // Split salesforceRecId into chunks of 500 elements
            const chunkSize = 500;
            const chunks = [];
            for (let i = 0; i < creationOrderSfRecId.length; i += chunkSize) {
                chunks.push(creationOrderSfRecId.slice(i, i + chunkSize));
            }

            // Loop through chunks and query Salesforce in chunks of 500
            for (const chunk of chunks) {
                await this.getOrderDetailsFromSalesforce(chunk);
            }
        } catch (error) {
            console.error("SalesforceService getCreationOrderDetails -> ", error);
        }
    }

    async getOrderDetailsFromSalesforce(orderDetails) {
        try {
            let idsFormatted = orderDetails.map(id => `'${id}'`).join(",");
            const query = `SELECT Id, Name, LastModifiedDate, Install_Date__c FROM Order__c WHERE Id IN (${idsFormatted})`;

            const creationOrderDetails = await salesforceConnection.getBulkDetails(query);
            if (!creationOrderDetails?.length) return;

            for (const creationOrderDetail of creationOrderDetails) {
                await this.updateOrderDetails(creationOrderDetail);
            }
        } catch (error) {
            console.error("SalesforceService getOrderDetailsFromSalesforce -> ", error);
        }
    }

    async updateOrderDetails(creationOrderDetail) {
        try {
            const { Id: sf_record_id, LastModifiedDate: sf_updatedAt, Name: sf_name, Install_Date__c: install_date } = creationOrderDetail;

            const formatedDate = getCurrentAtlanticTime(sf_updatedAt);
            const updatedTime = getCurrentAtlanticTime();

            let queryParams = [];
            let query = `UPDATE internets_eastlink_creation_order SET 
                sf_name = ?, 
                install_date = ?,
                sf_updatedAt = ?, 
                updatedAt = ?`;

            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            if (install_date) queryParams.push(install_date);
            else queryParams.push(null);

            queryParams.push(formatedDate, updatedTime);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(sf_record_id);

            await customerServices.executeQuery(query, queryParams);
        } catch (error) {
            console.error("SalesforceService updateOrderDetails -> ", error);
        }
    }
}

module.exports = OrderTestServices;
