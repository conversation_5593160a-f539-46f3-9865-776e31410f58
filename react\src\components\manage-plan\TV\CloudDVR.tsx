import React from "react";
import { formatCurrency } from "../../../utils/helper";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";
import { useSelector } from "react-redux";
import Button from "../../common/Button";

type DVRProps = {
  selectedDVR: any;
  DVR: any;
  handleDVRSelect: (dvrData: any) => void;
};

const CloudDVR: React.FC<DVRProps> = ({
  selectedDVR,
  DVR,
  handleDVRSelect,
}) => {
  const subscriptionType = useSelector(customerSubscriptionType);

  return (
    <div
      className={`w-[250px] xl:w-[280px] border-[3px] border-[#F5EFFF] p-3 rounded-20  cursor-pointer duration-300`}
    >
      <div className={`flex gap-3 rounded-10 flex-col justify-between h-full`}>
        <h1 className="font-anton uppercase text-2xl mt-3 lg:text-[26px] leading-tight">
          {DVR?.billing_period?.[0]?.[subscriptionType]?.display_name}
        </h1>

        {/* Price */}
        <h1 className="font-anton uppercase text-2xl lg:text-[26px] flex items-center">
          {formatCurrency(DVR?.billing_period?.[0]?.[subscriptionType]?.price)}{" "}
          <span className="text-sm lowercase ml-2">
            /{subscriptionType === "monthly" ? "month" : "year"}
          </span>
        </h1>
        <p className="my-4">{DVR?.description}</p>
        <Button
          clickEvent={() => handleDVRSelect(DVR)}
          className={`!rounded-30 !transition-all !duration-150 ${
            selectedDVR.includes(DVR?.api_name) ? "!font-bold" : ""
          }`}
          title={selectedDVR.includes(DVR?.api_name) ? "Added" : "Add"}
        />
      </div>
    </div>
  );
};

export default CloudDVR;
