import React from "react";
import { Link, useLocation } from "react-router-dom";
import { HomeMenuIcon, MenuIcon, ReferralsMenuIcon } from "../../assets/Icons";
// import { BestDeal } from "../dashboard/BestDeal";

interface MenuProps {
  isOpen: boolean;
  menuHandler: () => void;
}

const SideBarMenu: React.FC<MenuProps> = ({ isOpen, menuHandler }) => {
  const { pathname } = useLocation();

  return (
    <div
      className={`SideBarMenu lg:relative overflow-hidden h-screen group xl:w-[210px] lg:w-[190px] transition-all duration-700 bg-white pt-[16px] max-lg:absolute z-[99] max-lg:overflow-y-auto ${
        isOpen
          ? "menu-open rounded-tr-[1rem] rounded-br-[1rem] w-[80%] sm:w-[50%] md:w-[40%] max-w-[90%] translate-x-0 left-0 top-0 "
          : "w-0 max-lg:-translate-x-full"
      }`}
    >
      <div className="2xl:pt-[22px] lg:pt-[12px] flex items-center justify-between min-h-[65px]">
        <img
          src={
            "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/dancing_cow.gif"
          }
          alt="purple cow logo"
          className={`lg:opacity-100 ${
            isOpen ? "opacity-100 scale-100" : "opacity-0"
          } transition-all duration-700 scale-100 h-[65px]`}
        />
        <div
          className="lg:hidden w-10 h-10 flex items-center justify-center bg-[#EFEEEF] rounded-10 cursor-pointer absolute right-4 top-[30px]"
          onClick={menuHandler}
        >
          <div
            className={`inline-block MenuButton transition-all duration-700 lg:opacity-0 lg:pt-[20px] lg:pl-[16px]`}
          >
            <MenuIcon />
          </div>
        </div>
      </div>
      <div className="h-auto flex flex-col gap-5">
        <div
          className={`menuBar space-y-5 mt-[50px] px-[20px] lg:opacity-100 ${
            isOpen ? "opacity-100" : "opacity-0"
          } transition-all duration-700`}
        >
          <div className="nav-item-group p-2 rounded-[8px]">
            <Link
              to="/"
              className={`menu-item homeIcon flex items-center gap-2.5 transition-all text-color_A9A9A9 hover:text-primary ${
                (pathname === "/" ||
                  pathname === "/home" ||
                  pathname.includes("/manage-plan")) &&
                "!text-[#333333] font-medium active"
              }`}
            >
              <span className="w-5">
                <HomeMenuIcon
                  color={
                    pathname === "/" ||
                    pathname === "/home" ||
                    pathname.includes("/manage-plan")
                      ? "#AA7DE6"
                      : ""
                  }
                />
              </span>
              <label
                className={`menu-label max-w-[calc(100%-30px)] transition cursor-pointer`}
              >
                Dashboard
              </label>
            </Link>
          </div>

          <div className="nav-item-group p-2 rounded-[8px]">
            <Link
              to="/referrals"
              className={`menu-item homeIcon flex items-center gap-2.5 transition-all text-color_A9A9A9 hover:text-primary ${
                pathname === "/referrals" &&
                "!text-[#333333] font-medium active"
              }`}
            >
              <span className="w-5">
                <ReferralsMenuIcon
                  color={pathname === "/referrals" ? "#AA7DE6" : ""}
                />
              </span>
              <label
                className={`menu-label max-w-[calc(100%-30px)] transition  cursor-pointer`}
              >
                Referrals
              </label>
            </Link>
          </div>
        </div>
        <div className="flex flex-col gap-10 flex-1 lg:justify-center justify-evenly mt-10">
          <div
            className={`lg:hidden px-5 max-w-[288px] transition-all box-content ${
              isOpen ? "opacity-100" : "opacity-0"
            }`}
          >
            {/* <BestDeal /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideBarMenu;
