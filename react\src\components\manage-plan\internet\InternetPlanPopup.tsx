import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useGetPlanDetailsMutation } from "../../../services/api";
import { logout } from "../../../store/reducers/authenticationReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import Button from "../../common/Button";
import InternetPlanDetailCard from "./InternetPlanDetailCard";
import InternetPlanCardSkeleton from "./skeletons/InternetPlanCardSkeleton";

type InternetPlanPopupProps = {
  selectedPlan: string;
  closeHandler: () => void;
  handleSubmit: (data: object) => void;
  isLoading?: boolean;
  currentPlan?: object;
};
const InternetPlanPopup: React.FC<InternetPlanPopupProps> = ({
  selectedPlan,
  closeHandler,
  handleSubmit,
  isLoading,
  currentPlan,
}) => {
  const [selected, setSelected] = useState<string>();
  const [getPlans, plansLoading] = useGetPlanDetailsMutation();
  const [internetList, setInternetList] = useState({});
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    setSelected((prev) => ({ ...prev, speed: selectedPlan }));
  }, [selectedPlan]);

  useEffect(() => {
    handleGetInternet();
  }, []);

  // Function to fetch internet list
  const handleGetInternet = async () => {
    try {
      const query = {
        type: "internet",
      };
      const response = await getPlans(query).unwrap();
      if (response.status === 200) {
        setInternetList(response?.data);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle plan selection
  const handlePlanSelect = (plan: object) => {
    if (selected?.speed === plan?.speed) {
      setSelected("");
    } else {
      setSelected(plan);
    }
  };

  const currentSpeed =
    currentPlan?.internetElSpeedChangeOrder?.stage ===
      "Eligible For Submission" &&
    currentPlan?.internetElSpeedChangeOrder?.speed
      ? currentPlan?.internetElSpeedChangeOrder?.speed
      : currentPlan?.plan_speed;

  return (
    <div className="flex gap-5 flex-col">
      <div>
        <p>
          Please choose the internet plans that most suits your needs. After
          confirming it does take about two business days to update and once
          complete we will send you a confirmation text.
        </p>
      </div>
      <div className={`flex flex-wrap justify-center gap-3 xl:gap-4 my-4`}>
        {plansLoading?.isLoading
          ? [1, 2, 3].map((index) => <InternetPlanCardSkeleton key={index} />)
          : internetList?.planDetails
              ?.filter((plan: any) => plan?.provider === "Eastlink")
              ?.map((item: any) => (
                <InternetPlanDetailCard
                  key={item?.id}
                  selected={selected}
                  defaultList={item}
                  handlePlanSelect={() => {
                    if (!isLoading) handlePlanSelect(item);
                  }}
                />
              ))}
      </div>

      <div className="flex gap-2.5 max-lg:flex-col">
        <div className="basis-full">
          <Button
            title="Go back"
            btnType="transparent"
            attributes={{
              disabled: isLoading,
            }}
            className="disabled:opacity-50"
            type="button"
            clickEvent={closeHandler}
          />
        </div>
        <div className="basis-full">
          <Button
            title="Next"
            attributes={{
              disabled:
                !selected ||
                plansLoading?.isLoading ||
                currentSpeed === selected?.speed,
            }}
            className="disabled:opacity-50"
            isLoading={isLoading}
            clickEvent={() => handleSubmit(selected)}
          />
        </div>
      </div>
    </div>
  );
};

export default InternetPlanPopup;
