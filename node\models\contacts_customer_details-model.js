const { allow } = require('joi');
const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("contacts_customer_details", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    contact_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'contacts',
        key: 'id'
      },
      allowNull: false,
      comment: "Foreign key mapping to the primary key 'id' in the 'contacts' table, with cascading delete functionality.",
      onDelete: 'CASCADE'
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      allowNull: false,
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "Found in Salesforce on the Customer_Details__c under Name."
    },
    service_address_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_addresses',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_addresses' table."
    },
    mailing_address_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_addresses',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_addresses' table."
    },
    internet_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_internets_eastlink',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_internets_eastlink' table."
    },
    tv_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_tvs',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_tv' table."
    },
    phone_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_phones',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_phone' table."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the Last Modified By field in Salesforce."
    },
    eligible_promotions: {
      type: Sequelize.STRING(100),
      allowNull: true,
      defaultValue: "[]",
      comment: "This is an array of all the promotions that a customer could be eligible for."
    },
    stage: {
      type: Sequelize.ENUM('Onboarding', 'Online', 'Offboarding', 'Inactive', 'Outstanding', 'Payment arrangements', 'Hidden'),
      allowNull: false,
      defaultValue: 'Inactive',
      comment: "Found in Salesforce on the Customer_Details__c under CP_Stage__c"
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      indexes: [
        // {
        //   name: "CustomerServiceMailAddressUniqueIndex",
        //   unique: true,
        //   fields: ['contact_id', 'service_address_id', 'mailing_address_id']
        // },
        {
          name: "SalesforceRecordIndex",
          unique: true,
          fields: ['sf_record_id']
        },
      ]
    });
}