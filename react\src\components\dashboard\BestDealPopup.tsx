import React, { useEffect, useRef, useState } from "react";
import { CloseIcon } from "../../assets/Icons.tsx";
import { BestDealPopupProps } from "../../typings/typing.ts";

const BestDealPopup: React.FC<BestDealPopupProps> = ({
  children,
  width = "840px",
  height = "750px",
  closeBtn = true,
  closeHandler,
}) => {
  // State variables
  const [screenHeight, setScreenHeight] = useState<number>(window.innerHeight);
  const [actualHeight, setActualHeight] = useState<number | null>(null);
  const popupBody = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const updateHeight = () => {
      if (popupBody.current) {
        const calculatedHeight = popupBody.current.clientHeight + 60;
        setActualHeight(calculatedHeight);
        setScreenHeight(window.innerHeight);
      }
    };

    updateHeight(); // Initial update
    window.addEventListener("resize", updateHeight); // Update on resize

    return () => window.removeEventListener("resize", updateHeight);
  }, [actualHeight]);

  useEffect(() => {
    const mutationObserver = new MutationObserver(() => {
      if (popupBody.current) {
        const calculatedHeight = popupBody.current.clientHeight + 60;
        setActualHeight(calculatedHeight);
      }
    });

    if (popupBody.current) {
      mutationObserver.observe(popupBody.current, {
        childList: true,
        subtree: true,
      });
    }

    return () => mutationObserver.disconnect();
  }, []);

  useEffect(() => {
    // Set actual height after a short delay
    const timeoutId = setTimeout(() => {
      if (popupBody.current) {
        const calculatedHeight = popupBody.current.clientHeight + 60;
        setActualHeight(calculatedHeight);
      }
    }, 100); // Adjust the delay (100ms in this case) as needed

    return () => clearTimeout(timeoutId);
  }, [children]);

  return (
    <div className="fixed w-full h-dvh left-0 top-0 z-[103] best-popup-open scrollbarNone overflow-auto">
      <span className="best-popup-backdrop fixed w-dvw h-dvh left-0 top-0 bg-popupBg z-[9]"></span>
      <div
        className={`absolute left-1/2 bg-primary text-white -translate-x-1/2 w-full z-[12] max-lg:w-[85%] xl:rounded-30 rounded-[20px] lg:py-[30px] lg:px-5 p-5 ${
          actualHeight && screenHeight < actualHeight
            ? "top-[10%]"
            : "top-1/2 -translate-y-1/2"
        }`}
        style={{ maxWidth: width }}
        ref={popupBody}
      >
        {/* Close button */}
        {closeBtn && (
          <div
            onClick={closeHandler}
            className="cursor-pointer w-[50px] h-[50px] rounded-full bg-primary flex items-center justify-center shadow-cardShadow25 absolute -top-5 -right-5 z-[13]"
          >
            <CloseIcon fill="white" />
          </div>
        )}
        {/* Popup content */}
        <div className="relative">{children}</div>
      </div>
    </div>
  );
};

export default BestDealPopup;
