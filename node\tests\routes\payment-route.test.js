const request = require('supertest');
const express = require('express');
const paymentRoute = require('../../routes/payment-route');

// Mock the payment controller
jest.mock('../../controllers/payment-controller', () => ({
  fetchOutstanding: jest.fn((req, res) => res.status(200).json({ 
    message: 'Outstanding payments fetched successfully', 
    data: {
      totalOutstanding: 179.98,
      invoices: [
        { id: 1, amount: 89.99, dueDate: '2024-01-15', status: 'overdue' },
        { id: 2, amount: 89.99, dueDate: '2024-02-15', status: 'pending' }
      ]
    }
  })),
  makePayment: jest.fn((req, res) => res.status(200).json({ 
    message: 'Payment processed successfully',
    data: {
      transactionId: 'TXN-12345',
      amount: 89.99,
      status: 'completed'
    }
  })),
  makePaymentArrangements: jest.fn((req, res) => res.status(201).json({ 
    message: 'Payment arrangement created successfully',
    data: {
      arrangementId: 'ARR-12345',
      installments: 3,
      nextPaymentDate: '2024-02-01'
    }
  }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  outstandingDetailValidation: {},
  paymentArrangementValidation: {}
}));

describe('Payment Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    paymentRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/payment/outstanding', () => {
    it('should fetch outstanding payments successfully', async () => {
      const response = await request(app)
        .get('/api/v1/payment/outstanding')
        .query({ customerId: '12345' })
        .expect(200);

      expect(response.body.message).toBe('Outstanding payments fetched successfully');
      expect(response.body.data).toHaveProperty('totalOutstanding');
      expect(response.body.data).toHaveProperty('invoices');
      expect(Array.isArray(response.body.data.invoices)).toBe(true);
    });

    it('should handle outstanding payments request with different customer ID', async () => {
      const response = await request(app)
        .get('/api/v1/payment/outstanding')
        .query({ customerId: '67890' })
        .expect(200);

      expect(response.body.message).toBe('Outstanding payments fetched successfully');
    });

    it('should handle outstanding payments request without customer ID', async () => {
      await request(app)
        .get('/api/v1/payment/outstanding')
        .expect(200); // Mocked to return success
    });
  });

  describe('POST /api/v1/payment/', () => {
    it('should process payment successfully', async () => {
      const paymentData = {
        amount: 89.99,
        cardId: 'card_12345',
        invoiceIds: [1, 2],
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/payment/')
        .send(paymentData)
        .expect(200);

      expect(response.body.message).toBe('Payment processed successfully');
      expect(response.body.data).toHaveProperty('transactionId');
      expect(response.body.data).toHaveProperty('amount');
      expect(response.body.data).toHaveProperty('status');
    });

    it('should handle payment with partial amount', async () => {
      const paymentData = {
        amount: 50.00,
        cardId: 'card_12345',
        invoiceIds: [1],
        customerId: '12345'
      };

      const response = await request(app)
        .post('/api/v1/payment/')
        .send(paymentData)
        .expect(200);

      expect(response.body.message).toBe('Payment processed successfully');
    });

    it('should handle payment with missing required fields', async () => {
      const incompleteData = {
        amount: 89.99
        // Missing cardId and other required fields
      };

      await request(app)
        .post('/api/v1/payment/')
        .send(incompleteData)
        .expect(200); // Mocked to return success
    });
  });

  describe('POST /api/v1/payment/arrangement', () => {
    it('should create payment arrangement successfully', async () => {
      const arrangementData = {
        totalAmount: 179.98,
        installments: 3,
        startDate: '2024-02-01',
        customerId: '12345',
        invoiceIds: [1, 2]
      };

      const response = await request(app)
        .post('/api/v1/payment/arrangement')
        .send(arrangementData)
        .expect(201);

      expect(response.body.message).toBe('Payment arrangement created successfully');
      expect(response.body.data).toHaveProperty('arrangementId');
      expect(response.body.data).toHaveProperty('installments');
      expect(response.body.data).toHaveProperty('nextPaymentDate');
    });

    it('should handle payment arrangement with different installment count', async () => {
      const arrangementData = {
        totalAmount: 269.97,
        installments: 6,
        startDate: '2024-02-15',
        customerId: '67890',
        invoiceIds: [3, 4, 5]
      };

      const response = await request(app)
        .post('/api/v1/payment/arrangement')
        .send(arrangementData)
        .expect(201);

      expect(response.body.message).toBe('Payment arrangement created successfully');
    });

    it('should handle payment arrangement with invalid data', async () => {
      const invalidData = {
        totalAmount: 179.98
        // Missing required fields
      };

      await request(app)
        .post('/api/v1/payment/arrangement')
        .send(invalidData)
        .expect(201); // Mocked to return success
    });
  });
});
