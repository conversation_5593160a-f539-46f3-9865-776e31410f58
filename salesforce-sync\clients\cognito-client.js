const { CognitoIdentityProviderClient, AdminDeleteUserCommand } = require('@aws-sdk/client-cognito-identity-provider');
const CONFIG = require("../config");

class CognitoConnection {
    /**
    * Constructor initializes the Cognito client with required configuration.
    */
    constructor() {
        const { userPoolId, clientId, region, accessKeyId, secretAccessKey, clientSecret } = CONFIG.aws;
        this.userPool = userPoolId;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.client = new CognitoIdentityProviderClient({
            region: region,
            credentials: {
                accessKeyId: accessKeyId,
                secretAccessKey: secretAccessKey
            }
        });
    }

    /**
    * Delete a user from the Cognito user pool.
    * @param {string} email - The user's email address.
    * @returns {Promise<Object>} - The result of the AdminDeleteUserCommand.
    * @throws {CustomError} - Custom error with HTTP status code.
    */

    async deleteUser(email) {
        const params = {
            UserPoolId: this.userPool,
            Username: email
        };
        try {
            return await this.client.send(new AdminDeleteUserCommand(params));
        } catch (error) {
            console.error(`Error delete user: ->`, error);
            throw new Error(error?.$metadata?.httpStatusCode || 400, error.message || error);
        }
    }
}

module.exports = CognitoConnection;

