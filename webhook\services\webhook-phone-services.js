const { getCurrentAtlanticTime, sanitizeValue } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const PlanServices = require("../services/plan-services");
const CustomerService = require("./webhook-customer-services");
const CONFIG = require('../config');
const SalesforceClient = require('../clients/salesforce-client');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class PhoneWebhookServices {
    async getPhoneDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get phone details -> ", error);
        }
    }

    async checkAndManageDetails(sfServiceDetails, _id, _index) {
        try {
            if (!Array.isArray(sfServiceDetails)) sfServiceDetails = [sfServiceDetails];
            for (const sfServiceDetail of sfServiceDetails) {
                const { serviceExist, serviceId } = await this.checkServiceExist(sfServiceDetail);

                let type;
                if (serviceExist) {
                    type = "UPDATE";
                    await this.updatePhoneDetails(sfServiceDetail, serviceId);
                } else {
                    type = "CREATE";
                    await this.createPhoneDetails(sfServiceDetail, serviceId);
                }
                if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updatePhoneDetails(sfServiceDetail, existingDet) {
        try {
            const { id: existingId, sf_record_id: phoneId, subsType: subscription_type } = existingDet;
            const { Account_Status__c: account_status, LastModifiedDate, Calling_Plan__c, Service_Start_Date__c, Phone_Number_To_Port__c, CreatedDate, Requested_Cancellation_Date__c } = sfServiceDetail;
            const updatedTime = getCurrentAtlanticTime();

            if (account_status == "Deleted") {
                const updateQuery = `
                            UPDATE contacts_customer_details 
                            SET phone_id = ?, updatedAt = ?
                            WHERE phone_id = ?`;

                await this.executeQuery(updateQuery, [null, updatedTime, existingId]);
                const customerService = new CustomerService();

                const { dataCount } = await customerService.checkDataCount("contacts_customer_details", "phone_id", existingId);
                if (dataCount == 1) await customerService.deleteQuery("customer_details_phones", existingId);

                return;
            }

            const modifiedDate = getCurrentAtlanticTime(LastModifiedDate);
            const createdAt = getCurrentAtlanticTime(CreatedDate);

            const planServices = new PlanServices();
            const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);

            if (phonePlanDetails?.length) {

                let updateFields = 'updatedAt = ?, sf_updatedAt = ?, createdAt = ?';
                let updateValues = [updatedTime, modifiedDate, createdAt];

                const phoneCallRes = await salesforceConnection.getPhoneApiValue(phoneId);
                const { status: phStatus, data: phData } = phoneCallRes;

                if (phStatus) {
                    updateFields += ', plan_name = ?';
                    updateValues.push(phData?.Calling_Plan__c);
                }

                if (Requested_Cancellation_Date__c) {
                    const requested_cancellation_date = getCurrentAtlanticTime(Requested_Cancellation_Date__c);
                    updateFields += ', requested_cancellation_date = ?';
                    updateValues.push(requested_cancellation_date);
                }

                if (Service_Start_Date__c) {
                    const service_start_date = getCurrentAtlanticTime(Service_Start_Date__c);
                    updateFields += ', service_start_date = ?';
                    updateValues.push(service_start_date);
                }

                // Check the port type of home phone
                let sf_phone_type = { sf_phone_type: "new", phone_number: "" };
                if (Phone_Number_To_Port__c) {
                    sf_phone_type.phone_number = Phone_Number_To_Port__c;
                    sf_phone_type.sf_phone_type = "existing";
                }
                updateFields += ', sf_phone_type = ?';
                updateValues.push(JSON.stringify(sf_phone_type));

                if (Calling_Plan__c) {
                    updateFields += ', api_name = ?';
                    updateValues.push(Calling_Plan__c);
                }

                const getPrice = phonePlanDetails.find(phone => phone.api_name === Calling_Plan__c);
                updateFields += ', plan_price = ?';

                if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                    updateValues.push(getPrice.billing_period[0][subscription_type].price);
                } else {
                    updateValues.push(null);
                }

                // Handle account_status
                if (account_status) {
                    updateFields += ", account_status = ?";
                    updateValues.push(account_status);
                } else {
                    updateFields += ", account_status = NULL";
                }

                updateValues.push(phoneId);

                const updateQuery = `
                UPDATE customer_details_phones 
                SET ${updateFields} 
                WHERE sf_record_id = ?`;

                await this.executeQuery(updateQuery, updateValues);
            }
        } catch (error) {
            console.error("Error updatePhoneDetails -> ", error);
        }
    }

    async createPhoneDetails(serviceDetails) {
        try {
            const planServices = new PlanServices();
            const sfUpdatedAt = getCurrentAtlanticTime(serviceDetails.LastModifiedDate);
            const insertData = { sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, sf_name: serviceDetails.Name };
            if (serviceDetails.Account_Status__c == "Deleted") return;
            const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);
            if (phonePlanDetails?.length) {
                const getPrice = phonePlanDetails.find(phone => phone.api_name === serviceDetails.Calling_Plan__c);
                if (getPrice) {
                    if (getPrice?.api_name) {
                        const phoneRes = await salesforceConnection.getPhoneApiValue(serviceDetails.Id);
                        const { status: phStatus, data: pfData } = phoneRes;
                        if (phStatus) {
                            insertData.plan_name = pfData?.Calling_Plan__c;
                        }
                    }
                }
            }
            insertData.api_name = serviceDetails.Calling_Plan__c;
            insertData.account_status = serviceDetails.Account_Status__c;
            if (serviceDetails?.Service_Start_Date__c) {
                insertData.service_start_date = getCurrentAtlanticTime(serviceDetails.Service_Start_Date__c);
            }
            if (serviceDetails?.Requested_Cancellation_Date__c) {
                insertData.requested_cancellation_date = getCurrentAtlanticTime(serviceDetails.Requested_Cancellation_Date__c);
            }

            // Check the port type of home phone
            let sf_phone_type = { sf_phone_type: "new", phone_number: "" };
            if (serviceDetails?.Phone_Number_To_Port__c) {
                sf_phone_type.phone_number = serviceDetails.Phone_Number_To_Port__c;
                sf_phone_type.sf_phone_type = "existing";
            }
            insertData.sf_phone_type = JSON.stringify(sf_phone_type);
            const customerService = new CustomerService();
            await customerService.insertRecord("customer_details_phones", insertData, serviceDetails.CreatedDate);
        } catch (error) {
            console.error("SalesforceService createPhoneDetails -> ", error);
        }
    }

    async checkServiceExist(sfServiceDetail) {
        try {
            const { Id: sf_record_id } = sfServiceDetail;

            const query = `SELECT cd.id, cd.sf_record_id, cds.subscription_type as subsType
                            FROM customer_details_phones cd 
                            left join contacts_customer_details ccd on cd.id = ccd.phone_id 
                            left join customer_details_subscriptions cds on ccd.id = cds.customer_details_id 
                            WHERE cd.sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);

            return {
                serviceExist: res.length > 0,
                serviceId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Service Exist -> ", error);
            return {
                serviceExist: 0,
                serviceId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = PhoneWebhookServices;
