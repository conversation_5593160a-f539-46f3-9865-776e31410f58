const Sequelize = require('sequelize');
module.exports = (sequelize) => {
    return sequelize.define("duplicate_customers", {
        id: {
            type: Sequelize.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
        },
        sf_record_id: {
            type: Sequelize.STRING(18),
            allowNull: false,
            comment: "Salesforce record ID"
        },
        cb_customer_id: {
            type: Sequelize.STRING(30),
            comment: "Chargebee customer ID"
        },
        first_name: {
            type: Sequelize.STRING(50),
            allowNull: true
        },
        last_name: {
            type: Sequelize.STRING(50),
            allowNull: true
        },
        email: {
            type: Sequelize.STRING(96),
            allowNull: false
        },
        createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was created in the database."
        },
        updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was last updated in the database."
        }
    },
        {
            collate: 'utf8mb4_unicode_ci',
            timestamps: true
        });
}