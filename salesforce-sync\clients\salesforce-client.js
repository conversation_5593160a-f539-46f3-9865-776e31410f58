const jsforce = require('jsforce');
const { once } = require('events');
const moment = require('moment');
const INTERVAL = "interval";

class SalesforceConnection {

    constructor(baseurl, username, password, token) {
        this.baseurl = baseurl;
        this.username = username;
        this.password = password;
        this.token = token;
        this.conn = new jsforce.Connection({ loginUrl: this.baseurl });
        this.login();
    }

    async login() {
        try {
            await this.conn.login(this.username, this.password + this.token);
        } catch (error) {
            console.error("Salesforce login failed --> ", error);
        }
    }

    async getBulkDetails(soql, retryCount = 2, delayMs = 10000) {
        try {
            const options = {
                timeout: 300000
            };
            const query = await this.conn.bulk2.query(soql, options)
            const records = [];
            query.on('record', record => records.push(record));
            await once(query, 'end');
            return records;
        } catch (error) {
            console.error("Error fetching bulk details from Salesforce -> ", error);
            if (retryCount > 0) {
                console.log(`Retrying in ${delayMs / 1000} seconds... (${retryCount} retries left)`);
                await new Promise(resolve => setTimeout(resolve, delayMs));
                return this.getBulkDetails(soql, retryCount - 1, delayMs); // Recursive call with decremented retries
            }
        }
    }

    //fetch the all record and return in single array
    async fetchAllRecords(soql) {
        try {
            let allRecords = [];
            let result = await this.conn.query(soql);
            allRecords = allRecords.concat(result?.records);    
            while (!result.done) {
                result = await this.conn.queryMore(result.nextRecordsUrl);
                allRecords = allRecords.concat(result.records);
            }
            return allRecords;
        } catch (error) {
            console.error("Error fetching details from Salesforce -> ", error);
        }
    }
    

    async getYesterdayData() {
        return { $gte: jsforce.Date.YESTERDAY };
    }

    //this function use for addredd and  contact details fetch 
    async getAddressDetailsById(Id, apiName, selectedObj, type) {
        const returnResponse = { returnStatus: false, addresstData: null };
        try {

            let queryCondition = { Id };

            if (type === INTERVAL) {
                // queryCondition = {
                //     ...queryCondition, // Keep the existing Id condition
                //     LastModifiedDate: { $gte: jsforce.Date.YESTERDAY }
                // };
                queryCondition.LastModifiedDate = await this.getYesterdayData();
            }

            const addressDetails = await this.conn.sobject(apiName).findOne(queryCondition, selectedObj);

            if (addressDetails) {
                returnResponse.returnStatus = true;
                returnResponse.addresstData = addressDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching address details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getInternetTvPhoneDetailsByServiceId(serviceId, apiName, selectedObj, type) {
        const returnResponse = { returnStatus: false, internetTvPhoneData: null };
        try {
            let queryCondition = { Id: serviceId };

            if (type === INTERVAL) {
                // queryCondition = {
                //     ...queryCondition, // Keep the existing Id condition
                //     LastModifiedDate: { $gte: jsforce.Date.YESTERDAY }
                // };
                queryCondition.LastModifiedDate = await this.getYesterdayData();
            }
            const internetTvPhoneDetails = await this.conn.sobject(apiName).findOne(queryCondition, selectedObj);

            if (internetTvPhoneDetails) {
                returnResponse.returnStatus = true;
                returnResponse.internetTvPhoneData = internetTvPhoneDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching internet tv phone details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getOrderDetails(Id, selectedObj, type) {
        const returnResponse = { returnStatus: false, orderDetails: null };
        try {
            let queryCondition = { Id };

            if (type === INTERVAL) {
                // queryCondition = {
                //     ...queryCondition, // Keep the existing Id condition
                //     LastModifiedDate: { $gte: jsforce.Date.YESTERDAY }
                // };
                queryCondition.LastModifiedDate = await this.getYesterdayData();
            }

            const orderDetails = await this.conn.sobject('Order__c').findOne(queryCondition, selectedObj);
            if (orderDetails) {
                returnResponse.returnStatus = true;
                returnResponse.orderDetails = orderDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching customer order details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getShippingDetails(Id, type) {
        const returnResponse = { returnStatus: false, shippingData: null };
        try {

            let queryCondition = { Id };

            if (type === INTERVAL) {
                // queryCondition = {
                //     ...queryCondition, // Keep the existing Id condition
                //     LastModifiedDate: { $gte: jsforce.Date.YESTERDAY }
                // };
                queryCondition.LastModifiedDate = await this.getYesterdayData();
            }

            const shippingDetails = await this.conn.sobject('Shipping__c').findOne(queryCondition, { Id: 1, Full_Mailing_Address__c: 1, Ship_Date__c: 1, Ship_Drop_Off_Date__c: 1, Tracking_URL__c: 1, Courier__c: 1, LastModifiedDate: 1, Package_Delivered__c: 1, CreatedDate: 1, Name: 1 });
            if (shippingDetails) {
                returnResponse.returnStatus = true;
                returnResponse.shippingData = shippingDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching customer shipping details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getTechAppDetails(Id, type) {
        const returnResponse = { returnStatus: false, techData: null };
        try {
            let queryCondition = { Id };

            if (type === INTERVAL) {
                queryCondition.LastModifiedDate = await this.getYesterdayData();
            }

            const techDetails = await this.conn.sobject('Tech_Appointment__c').findOne(queryCondition, { Id: 1, LastModifiedDate: 1, Install_Date__c: 1, Install_Time__c: 1, CreatedDate: 1, Name: 1 });
            if (techDetails) {
                returnResponse.returnStatus = true;
                returnResponse.techData = techDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching customer tech app details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getDetailsByFieldAndApi(searchField, apiName, selectedObj, type) {
        const returnResponse = { returnStatus: false, salesforceData: null };
        try {
            if (type === INTERVAL) {
                searchField.LastModifiedDate = await this.getYesterdayData();
            }
            const salesforceDetails = await this.conn.sobject(apiName).find(searchField, selectedObj);
            if (Array.isArray(salesforceDetails) && salesforceDetails.length) {
                returnResponse.returnStatus = true;
                returnResponse.salesforceData = salesforceDetails
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error fetching details from Salesforce ->`, error);
            throw error;
        }
    }

    async getSubscriptionDetails(Id) {
        const returnResponse = { returnStatus: false, subscriptionData: null };
        try {
            const subscriptionDetails = await this.conn.sobject('chargebeeapps__CB_Subscription__c').findOne({ Id }, { Id: 1, chargebeeapps__CB_Subscription_Id__c: 1, chargebeeapps__Plan_Amount__c: 1, chargebeeapps__Subcription_Activated_At__c: 1, chargebeeapps__Next_billing__c: 1, chargebeeapps__Subscription_status__c: 1, LastModifiedDate: 1, CreatedDate: 1 });
            if (subscriptionDetails) {
                returnResponse.returnStatus = true;
                returnResponse.subscriptionData = subscriptionDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching customer subscription details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getInvoiceDetails(chargebeeapps__SubscriptionId__c, type) {
        const returnResponse = { returnStatus: false, invoiceData: null };
        try {

            let searchField = { chargebeeapps__SubscriptionId__c }
            if (type === INTERVAL) searchField.LastModifiedDate = await this.getYesterdayData();

            const invoiceDetails = await this.conn.sobject('chargebeeapps__CB_Invoice__c').find(searchField, { Id: 1, chargebeeapps__CB_Invoice_Id__c: 1, chargebeeapps__Due_Amount__c: 1, chargebeeapps__Amount__c: 1, Expected_Payment_Date_Time__c: 1, chargebeeapps__Status__c: 1, chargebeeapps__Subscription_CB_Id__c: 1, LastModifiedDate: 1, chargebeeapps__Invoice_Date__c: 1 });
            if (Array.isArray(invoiceDetails) && invoiceDetails.length) {
                returnResponse.returnStatus = true;
                returnResponse.invoiceData = invoiceDetails
            }
            return returnResponse;
        } catch (error) {
            console.error("Error fetching customer subscription details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getRecordDetails(Id) {
        const returnResponse = { returnStatus: false, recordData: null };
        try {

            const recDetails = await this.conn.sobject('RecordType').findOne({ Id }, { Name: 1 });
            if (recDetails) {
                returnResponse.returnStatus = true;
                returnResponse.recordData = recDetails
            }

            return returnResponse;
        } catch (error) {
            console.error("Error fetching record details from Salesforce -> ", error);
            return returnResponse;
        }
    }

    async getPhoneApiValue(Id) {
        try {
            let query = `Select Id, ToLabel(Calling_plan__c) From Phone__c WHERE Id = '${Id}' LIMIT 1`;
            let data = await this.conn.query(query);
            data = data?.records?.[0];
            return { status: true, data: data ? data : null };
        } catch (error) {
            console.error(`Error fetching phone api value from Salesforce ->`, error);
            throw error;
        }
    }

    async getPhoneSingleValue(Id) {
        try {
            let query = `Select Id, Calling_plan__c From Phone__c WHERE Id = '${Id}' LIMIT 1`;
            let data = await this.conn.query(query);
            data = data?.records?.[0];
            return { status: true, data: data ? data : null };
        } catch (error) {
            console.error(`Error fetching phone api value from Salesforce ->`, error);
            throw error;
        }
    }

    async getDeletedData(RecordType) {
        try {
            const returnResponse = { deleteStatus: false, deletedData: null };
            const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
            const tomorrow = moment().add(1, 'days').format('YYYY-MM-DD');
            const recDetails = await this.conn.sobject(RecordType).deleted(yesterday, tomorrow);
            if (recDetails) {
                returnResponse.deleteStatus = true;
                returnResponse.deletedData = recDetails?.deletedRecords;
            }
            return returnResponse;
        } catch (error) {
            console.error(`Error get Deleted Data from Salesforce ->`, error);
            throw error;
        }
    }
}

// Export the SalesforceConnection class
module.exports = SalesforceConnection;

