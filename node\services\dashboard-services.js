const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();
const {
  calculateMembershipDuration,
  getPagingData,
  parsePagination,
  rearrangeLocations,
  getCurrentAtlanticTime,
} = require("../helpers/privacyAlgorithms");
const db = require("../models");
const { Sequelize, Op } = require("sequelize");
const PAYEMENT_STATUS = ["PAYMENT_DUE", "NOT_PAID"];
const moment = require("moment");

class DashboardServices {
  // Method to retrieve user details (filtered)
  async userDetails(userDetails, elasticLogObj) {
    const { _id, _index } = await elasticSearch.insertDocument(
      "fetch_user_details_logs",
      { ...elasticLogObj, request: { userDetails } }
    );
    try {
      let data = {};
      const { createdAt, full_name, id , first_name} = userDetails;
      const member_for = calculateMembershipDuration(createdAt);

      let payment_arrangement = false;

      const total_outstanding = await this.calculateOutstandingAmount(id);
      if (total_outstanding > 0) {
        payment_arrangement = await this.fetchOutstanding(id);
      }
      data = { full_name,first_name, member_for, total_outstanding, payment_arrangement };
      const result = { status: true, data };
      if (_id && _index)
        await elasticSearch.updateDocument(_index, _id, {
          "log.level": "INFO",
          response: result,
        });
      return result;
    } catch (error) {
      if (_id && _index)
        await elasticSearch.updateDocument(_index, _id, {
          "log.level": "ERROR",
          response: { error: error.message },
        });
      console.error(`Customer service get details -> `, error);
      throw error;
    }
  }

  // Method to calculate outstanding amount
  async calculateOutstandingAmount(contact_id) {
    try {
      let totalOutstanding = await db.CustomerDetails.findAll({
        include: {
          model: db.CustomerSubscriptions,
          as: "customerSubscription",
          attributes: [],
          required: true,
        },
        where: { contact_id },
        attributes: [
          [
            Sequelize.fn(
              "SUM",
              Sequelize.col("customerSubscription.balance_due")
            ),
            "total_outstanding",
          ],
        ],
        raw: true,
      });
      return totalOutstanding[0]?.total_outstanding || 0.0;
    } catch (error) {
      console.error(`Customer service calculate outstanding amount -> `, error);
      throw error;
    }
  }

  // Method to retrieve location details (filtered)
  async locationDetails(userDetails, query, elasticLogObj) {
    const { _id, _index } = await elasticSearch.insertDocument(
      "fetch_location_details_logs",
      { ...elasticLogObj, request: { userDetails, query } }
    );

    try {
      const data = {};
      const { id } = userDetails;
      let findOptions = {
        include: [
          {
            model: db.CustomerAddresses,
            as: "serviceAddress",
            attributes: [],
            required: false,
          },
          {
            model: db.CustomerInternet,
            as: "customerInternet",
            attributes: ['live_date'],
            include: [{
              model: db.InternetElMoveOrder,
              as: 'internetElMoveOrder',
              attributes: ['id', 'stage', 'sf_record_id'],
              required: false
            },
            {
              model: db.InternetElCreationOrder,
              as: 'internetElCreationOrder',
              attributes: ['record_type', 'sf_record_id'],
              required: false
            }],
            required: false,
          }
        ],
        attributes: [
          "id",
          "stage",
          [
            Sequelize.literal("`serviceAddress`.`full_address`"),
            "full_address",
          ],
          [Sequelize.literal("`serviceAddress`.`status`"), "status"],
        ],
        where: { contact_id: id },
        order: [["id", "DESC"]],
        raw: true,
      };

      let limit;
      let page;
      let offset;

      if (query && query?.page && query?.limit) {
        ({ page, limit, offset } = parsePagination(query));
        findOptions.limit = limit;
        findOptions.offset = offset;
      }
      let { count, rows } = await db.CustomerDetails.findAndCountAll(
        findOptions
      );
      const locations = await this.getStatus(rows);
      data.locations = rearrangeLocations(locations);
      if (limit) data.pageData = getPagingData(count, limit, page);

      const result = { status: true, data };
      if (_id && _index)
        await elasticSearch.updateDocument(_index, _id, {
          "log.level": "INFO",
          response: result,
        });
      return result;
    } catch (error) {
      if (_id && _index)
        await elasticSearch.updateDocument(_index, _id, {
          "log.level": "ERROR",
          response: { error: error.message },
        });
      console.error(`Customer service get details -> `, error);
      throw error;
    }
  }

  async getStatus(customerDetails) {
    const currentDate = new Date(getCurrentAtlanticTime(null, "sfUpdate"));
    currentDate.setHours(0, 0, 0, 0);

    const custDetailsArray = await Promise.all(
      customerDetails.map(async (customerDetail) => {
        const moveOrderExistId = customerDetail['customerInternet.internetElMoveOrder.id'];
        const originalStage = customerDetail?.stage;
        if (originalStage === "Online") {
          if (moveOrderExistId) {
            const moveOrderstage = customerDetail['customerInternet.internetElMoveOrder.stage'];
            if (moveOrderstage == "Eligible For Submission" || moveOrderstage == "Pre Response") customerDetail.stage = "Processing move request";
          } else {
            const subscriptionInvoices = await db.SubscriptionInvoice.findAll({
              where: {
                status: PAYEMENT_STATUS,
                customer_details_id: customerDetail.id,
              },
              raw: true,
            });

            let hasPaymentArrangement = false;
            if (subscriptionInvoices.length) {
              let paymentArrangementCount = 0;
              for (const invoice of subscriptionInvoices) {
                const { status, expected_payment_date } = invoice;
                const paymentDate = new Date(expected_payment_date); // Convert to Date object
                paymentDate.setHours(0, 0, 0, 0);
                if (
                  (status === "PAYMENT_DUE" || status === "NOT_PAID") &&
                  expected_payment_date !== null
                ) {
                  if (paymentDate > currentDate) {
                    customerDetail.stage = "Payment arrangements";
                    hasPaymentArrangement = true;
                    paymentArrangementCount++;
                  }
                }
              }

              if (paymentArrangementCount != subscriptionInvoices.length) customerDetail.stage = "Outstanding";

              // If no payment arrangement found, set to "Outstanding"
              if (!hasPaymentArrangement) {
                customerDetail.stage = "Outstanding";
              }
            }
          }
        }
        return customerDetail;
      })
    );
    return custDetailsArray;
  }

  async fetchOutstanding(contact_id) {
    try {
      // Fetch the customer details IDs
      const resultData = await db.CustomerDetails.findAll({
        attributes: ["id"],
        where: { contact_id },
      });

      const CustDetailsArray = resultData.map((data) => data.id);

      let whereClause = {
        status: ["PAYMENT_DUE", "NOT_PAID"],
        customer_details_id: {
          [Op.in]: CustDetailsArray,
        },
      };

      whereClause.createdAt = {
        [Op.gte]: moment().subtract(28, "days").toDate(),
      };
      whereClause.sf_record_id = {
        [Op.and]: [
          { [Op.not]: null },
          { [Op.ne]: "" }
        ]
      };

      const data = await db.SubscriptionInvoice.count({
        include: [
          {
            model: db.CustomerDetails,
            as: "customerDetail",
            attributes: [],
          },
        ],
        attributes: [],
        where: whereClause,
        raw: true,
      });

      return data ? true : false;
    } catch (error) {
      console.error(`PaymentServices fetch Outstanding -> `, error);
      throw error;
    }
  }
}

module.exports = DashboardServices;
