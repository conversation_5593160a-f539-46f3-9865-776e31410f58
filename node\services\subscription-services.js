const db = require('../models/index.js');
const CONFIG = require('../config');
const moment = require('moment');
const ChargebeeClient = require("../clients/chargebee/chargebee.js");
const { RESPONSES, RESPONSE_CODES, RESPONSE_MESSAGES } = require('../utils/ResponseCodes.js');
const PlanServices = require("../services/plan-services");
const ChargebeeService = require("./chargebee-services")
const CustomError = require("../utils/errors/CustomError.js");
const SalesforceService = require("./salesforce-services");
const callChargebee = new ChargebeeClient();
const planServices = new PlanServices();
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const { getCurrentAtlanticTime, checkSpeedChange, checkTVChange } = require('../helpers/privacyAlgorithms.js');
const elasticSearch = new ElasticSearch();

class SubscriptionServices {

  // Method to get renewal estimate
  async getRenewalEstimate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("fetch_estimate_renewal_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, renewal_date } = payload;
      if (!renewal_date || !customer_subscription_id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

      const customerSubscription = await db.CustomerSubscriptions.findByPk(customer_subscription_id);
      if (!customerSubscription) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const customerId = customerSubscription?.customer_details_id;
      const validContact = await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });
      if (!validContact) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const isMonthly = customerSubscription.subscription_type === "monthly";
      const currentNextBillingDate = moment(customerSubscription.next_billing_at);
      const lastDateOfRenewal = isMonthly ? moment(customerSubscription.next_billing_at).add(1, 'month') : moment(customerSubscription.next_billing_at).add(1, 'year');

      const renewalMoment = moment(renewal_date);
      if (!renewalMoment.isBetween(currentNextBillingDate, lastDateOfRenewal, null, '[]')) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'The provided date is invalid. Please select a valid date');

      // Convert renewal date to Unix timestamp
      const unixRenewalDate = moment(renewal_date).unix();
      const info = await callChargebee.estimateTermEnd(customerSubscription?.cb_subscription_id, unixRenewalDate);
      if (info) {
        const data = { prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0 };
        returnStatus.status = true;
        returnStatus.data = data;
      }
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices getRenewalEstimate -> `, error);
      throw error;
    }
  }

  // Method to update renewal billing date
  async renewalBillingDate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("update_renewal_date_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, renewal_date } = payload;
      const customerSubscription = await db.CustomerSubscriptions.findByPk(customer_subscription_id);
      if (!customerSubscription) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const customerId = customerSubscription?.customer_details_id;
      const validContact = await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });
      if (!validContact) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const isMonthly = customerSubscription.subscription_type === "monthly";
      const currentNextBillingDate = moment(customerSubscription.next_billing_at);
      const lastDateOfRenewal = isMonthly ? moment(customerSubscription.next_billing_at).add(1, 'month') : moment(customerSubscription.next_billing_at).add(1, 'year');

      const renewalMoment = moment(renewal_date);
      if (!renewalMoment.isBetween(currentNextBillingDate, lastDateOfRenewal, null, '[]')) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'The provided date is invalid. Please select a valid date');

      const unixRenewalDate = moment(renewal_date).unix();
      const info = await callChargebee.changeTermEnd(customerSubscription?.cb_subscription_id, unixRenewalDate);
      if (info?.next_billing_at) {
        // Format next billing date
        const next_billing_at = moment.unix(info?.next_billing_at).format('YYYY-MM-DD HH:mm:ss');
        // Update CustomerSubscription with next billing date
        await customerSubscription.update({ next_billing_at });

        returnStatus.status = true;
        returnStatus.data = info;

        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices renewalBillingDate -> `, error);
      throw error;
    }
  }

  // Retrieves details of a specific internet plan based on plan_id
  async getInternetPhonePlanDetails(plan_id, planType, subscriptionType) {
    try {
      if (planType !== 'internet' && planType !== 'phone') throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
      const url = planType === "internet" ? CONFIG.internet.plans : CONFIG.phone.plans;
      const planDetails = await planServices.getPlanDetailsFromJSON(url);
      let plan, billingDetails;
      if (planDetails?.length) {
        plan = planDetails.find(plan => plan.api_name === plan_id);
        if (subscriptionType === "monthly") {
          if (plan) billingDetails = planType === "internet" ? plan?.billing?.[0].monthly : plan?.billing_period?.[0].monthly;
        } else {
          if (plan) billingDetails = planType === "internet" ? plan?.billing?.[0].yearly : plan?.billing_period?.[0].yearly;
        }
      }
      let response = {
        api_name: billingDetails?.api_name,
        price: billingDetails?.price || null
      };

      if (planType === "internet" && plan?.speed) response.speed = plan.speed;
      return response;
    } catch (error) {
      console.error(`SubscriptionServices getInternetPhonePlanDetails -> `, error);
      throw error;
    }
  }

  // Retrieves estimated cost for updating the internet subscription plan
  async getEstimateForInternetUpdate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("fetch_estimate_internet_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, plan_id } = payload;

      const customerSubscription = await db.CustomerSubscriptions.findByPk(customer_subscription_id);
      if (!customerSubscription) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const customerId = customerSubscription?.customer_details_id;
      const validContact = await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });
      if (!validContact) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      // Retrieve internet plan details based on plan_id
      const getPlanDetails = await this.getInternetPhonePlanDetails(plan_id, "internet", customerSubscription?.subscription_type);
      if (!getPlanDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const reqPayload = {
        subscription: {
          id: customerSubscription?.cb_subscription_id,
          plan_id: getPlanDetails.api_name,
        },
        prorate: true,
        invoice_immediately: true
      };

      const info = await callChargebee.getEstimateUpdateSubscription(reqPayload);
      if (info) {
        const data = {
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        }
        returnStatus.status = true;
        returnStatus.data = data;
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices getSubscriptionUpdateEstimate -> `, error);
      throw error;
    }
  }

  // Updates the internet subscription with the specified plan
  async updateInternet(reqPayload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("update_internet_logs", { ...elasticLogObj, request: reqPayload, type: "UPDATE" });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, plan_id } = reqPayload;

      let customerSubscription = await db.CustomerSubscriptions.findOne({
        include: {
          model: db.CustomerDetails,
          as: 'customerDetails',
          include: {
            model: db.CustomerInternet,
            as: 'customerInternet',
            attributes: ["sf_record_id", "retries", "sf_response_status", "speed_change_order", "plan_speed", "plan_name"],
            required: false,
            include: {
              model: db.InternetElSpeedChangeOrder,
              as: 'internetElSpeedChangeOrder',
              attributes: ['stage', 'response_date', 'sf_record_id', 'speed'],
              required: false
            },
          },
          attributes: ["internet_id", "stage"],
          required: false
        },
        attributes: ["customer_details_id", "cb_subscription_id", "subscription_type", "id"],
        where: { id: customer_subscription_id }
      });
      customerSubscription = JSON.parse(JSON.stringify(customerSubscription));

      const customerId = customerSubscription?.customer_details_id;
      const validContact = await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });
      if (!validContact) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      if (customerSubscription?.customerDetails?.customerInternet?.sf_response_status === "failure" || customerSubscription?.customerDetails?.customerInternet?.retries > 3) {
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
      }
      const sfInternetId = customerSubscription?.customerDetails?.customerInternet?.sf_record_id;
      if (!customerSubscription || !sfInternetId) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const stage = customerSubscription?.customerDetails?.stage;
      if (stage != "Online") throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);

      const latestSpeedChangeOrder = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder;
      const latestSpeedChangeOrderStage = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.stage;
      const latestSpeedChangeOrderResDate = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.response_date;
      const latestSpeedChangeOrdersfRecId = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.sf_record_id;
      const currentSpeedChangeOrder = customerSubscription?.customerDetails?.customerInternet?.speed_change_order;

      let updateLogic;

      if (!latestSpeedChangeOrder) updateLogic = "Create";
      else if (latestSpeedChangeOrder && latestSpeedChangeOrderStage == "Eligible For Submission" && latestSpeedChangeOrdersfRecId) updateLogic = "Update";
      else if (latestSpeedChangeOrder && latestSpeedChangeOrderResDate) updateLogic = "Create";

      if (!updateLogic) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);

      // Retrieve internet plan details based on plan_id
      const getPlanDetails = await this.getInternetPhonePlanDetails(plan_id, "internet", customerSubscription?.subscription_type);
      if (!getPlanDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const payload = {
        plan_id: getPlanDetails.api_name,
        prorate: true,
        invoice_immediately: true
      };

      const info = await callChargebee.updateSubscription(customerSubscription.cb_subscription_id, payload);

      if (info) {
        // const internetObj = { plan_name: plan_id, plan_price: getPlanDetails?.price, plan_speed: getPlanDetails?.speed };
        const chargebeeServiceClient = new ChargebeeService();
        await chargebeeServiceClient.getInvoicesList(customerSubscription);
        const internetObj = {};

        const previousSpeed = customerSubscription?.customerDetails?.customerInternet?.plan_speed;
        const requestedSpeed = getPlanDetails?.speed;
        const plan_change_type = checkSpeedChange(previousSpeed, requestedSpeed);

        const nextBillingDate = moment.unix(info.next_billing_at).format('YYYY-MM-DD HH:mm:ss');
        const amount = info.plan_amount ? info.plan_amount / 100 : 0;

        await db.CustomerSubscriptions.update({ next_billing_at: nextBillingDate, amount }, { where: { id: customer_subscription_id } });
        let equalSpeed = false;
        // Add internet id in SF
        const salesforceServiceClient = new SalesforceService();
        try {
          let updateStatus;
          let orderId;

          if (updateLogic === "Create") {
            ({ returnStatus: updateStatus, orderId } = await salesforceServiceClient.createNewSpeedOrderinSf(sfInternetId, requestedSpeed));
            if (updateStatus && orderId) {
              const speedChangeOrderId = await this.updateSpeedChangeOrder(orderId);
              if (speedChangeOrderId) internetObj.speed_change_order = speedChangeOrderId;
            }
          } else {
            if (previousSpeed == requestedSpeed) {
              equalSpeed = true;
              ({ returnStatus: updateStatus } = await salesforceServiceClient.deleteSpeedChangeOrder(latestSpeedChangeOrdersfRecId));
              if (updateStatus) internetObj.speed_change_order = null;
            } else {
              ({ returnStatus: updateStatus } = await salesforceServiceClient.updateOrderSpeedinSf(latestSpeedChangeOrdersfRecId, requestedSpeed));
            }
            await db.InternetElSpeedChangeOrder.update({ speed: requestedSpeed }, { where: { sf_record_id: latestSpeedChangeOrdersfRecId } });
          }

          if (updateStatus) {
            internetObj.sf_response_status = "success";
            returnStatus.message = RESPONSE_MESSAGES.SUCCESS;
          }
          returnStatus.status = true;
          if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus, request: { ...reqPayload, previousSpeed, requestedSpeed, plan_change_type } });
        } catch (error) {
          internetObj.sf_error_log = error?.message;
          returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;
          if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message }, request: { ...reqPayload, previousSpeed, requestedSpeed, plan_change_type } });
          await this.callForRollBack(customerSubscription);
        } finally {
          await db.CustomerInternet.update(internetObj, { where: { id: customerSubscription.customerDetails.internet_id } });
          returnStatus.status = true;
          if ((updateLogic === "Create" && currentSpeedChangeOrder && internetObj?.speed_change_order && currentSpeedChangeOrder !== internetObj?.speed_change_order) || (updateLogic === "Update" && equalSpeed)) await db.InternetElSpeedChangeOrder.destroy({ where: { id: currentSpeedChangeOrder } });
          if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
        }
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices updateInternet -> `, error);
      throw error;
    }
  }

  // Retrieves cost estimate for updating television subscription
  async getEstimateForTelevisionUpdate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("fetch_estimate_tv_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id } = payload;

      const customerSubscriptionDetails = await this.getCustomerSubsDetails(customer_subscription_id, "tv");
      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const contactId = customerSubscriptionDetails?.customerDetails?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      // Retrieve addon details for television plan
      const responseObj = await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);
      if (!responseObj) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
      let addonsArray = responseObj?.addons;

      // Include phone plan addon if customer has associated phone
      const phoneId = customerSubscriptionDetails?.customerDetails?.phone_id;
      if (phoneId) {
        const phonePlanAPIName = customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name;
        const account_status = customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;
        if (account_status === 'ACTIVE') {
          const getPlanDetails = await this.getInternetPhonePlanDetails(phonePlanAPIName, "phone", customerSubscriptionDetails?.subscription_type);
          addonsArray.push({ id: getPlanDetails.api_name });
        }
      }

      const reqPayload = {
        subscription: { id: customerSubscriptionDetails?.cb_subscription_id },
        prorate: true,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };

      // Call Chargebee API to get estimate for subscription update
      const info = await callChargebee.getEstimateUpdateSubscription(reqPayload);
      if (info) {
        const data = {
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        }
        returnStatus.status = true;
        returnStatus.data = data;
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices getEstimateForTelevisionUpdate -> `, error);
      throw error;
    }
  }

  // Updates television subscription details
  async updateTelevision(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("update_tv_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };
      const { customer_subscription_id, plan_name, extra_packages, single_channels, iptv_products } = payload;
      const customerSubscriptionDetails = await this.getCustomerSubsDetails(customer_subscription_id, "tv");
      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
      if (customerSubscriptionDetails?.customerDetails?.customerTv?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries > 3) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);

      const contactId = customerSubscriptionDetails?.customerDetails?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      // Retrieve addon details for television plan
      const responseObj = await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);
      if (!responseObj) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
      let addonsArray = responseObj?.addons;

      // Include phone plan addon if customer has associated phone
      const phoneId = customerSubscriptionDetails?.customerDetails?.phone_id;
      if (phoneId) {
        const phonePlanAPIName = customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name;
        const account_status = customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;
        if (account_status === 'ACTIVE') {
          const getPlanDetails = await this.getInternetPhonePlanDetails(phonePlanAPIName, "phone", customerSubscriptionDetails?.subscription_type);
          addonsArray.push({ id: getPlanDetails.api_name });
        }
      }

      const reqPayload = {
        prorate: true,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };

      // Call Chargebee API to update subscription
      const info = await callChargebee.updateSubscription(customerSubscriptionDetails?.cb_subscription_id, reqPayload);

      if (_id && _index) {
        const tvId = customerSubscriptionDetails?.customerDetails?.tv_id;
        const type = tvId ? "UPDATE" : "CREATE";
        let request = { requestedPlan: payload }
        let currentPackage;
        if (tvId) {
          const previousPlan = {
            plan_name: customerSubscriptionDetails?.customerDetails?.customerTv?.plan_name,
            extra_packages: customerSubscriptionDetails?.customerDetails?.customerTv?.extra_packages,
            single_channels: customerSubscriptionDetails?.customerDetails?.customerTv?.single_channels,
            iptv_products: customerSubscriptionDetails?.customerDetails?.customerTv?.iptv_products
          };
          request.previousPlan = previousPlan;
          currentPackage = customerSubscriptionDetails?.customerDetails?.customerTv?.plan_name;
        }
        const plan_change_type = checkTVChange(currentPackage, plan_name);
        await elasticSearch.updateDocument(_index, _id, { request, type, plan_change_type });
      }
      ;
      if (info) {
        const chargebeeServiceClient = new ChargebeeService();
        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);
        const tvObj = {
          plan_name,
          extra_packages: JSON.stringify(extra_packages),
          single_channels: JSON.stringify(single_channels),
          iptv_products: JSON.stringify(iptv_products),
          total_service_cost: responseObj.totalAmount,
        };

        const message = await this.saveCustomerTv(tvObj, customerSubscriptionDetails, payload);

        const nextBillingDate = moment.unix(info?.next_billing_at).format('YYYY-MM-DD HH:mm:ss');
        await db.CustomerSubscriptions.update({ next_billing_at: nextBillingDate }, { where: { id: customer_subscription_id } });
        returnStatus.status = true;
        returnStatus.message = message;
        if (_id && _index) {
          await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
        }
        if (message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices updateTelevision -> `, error);
      throw error;
    }
  }

  async saveCustomerTv(tvObj, customerSubscriptionDetails, payload) {
    const tvId = customerSubscriptionDetails?.customerDetails?.tv_id;
    const customerDetailsId = customerSubscriptionDetails?.customer_details_id;
    let message = RESPONSE_MESSAGES.SUCCESS;
    if (tvId) {
      payload.orderType = "update";
      payload.customerTvId = customerSubscriptionDetails?.customerDetails?.customerTv?.sf_record_id;
    } else {
      payload.orderType = "create";
      payload.customerSfId = customerSubscriptionDetails?.customerDetails?.sf_record_id;
    }
    try {
      const orderId = await this.manageTvOrderinSF(payload);
      tvObj.sf_response_status = orderId ? "success" : "failure";
      if (!tvId && orderId) tvObj.sf_record_id = orderId;
      tvObj.account_status = 'ACTIVE';
      tvObj.state_text = 'In Progress';
      if (tvId) {
        await db.CustomerTv.update(tvObj, { where: { id: tvId } });
      } else {
        const customerTv = await db.CustomerTv.create(tvObj);
        await db.CustomerDetails.update({ tv_id: customerTv.id }, { where: { id: customerDetailsId } });
      }
    } catch (error) {
      const account_status = customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;
      const tvRollBackObj = {
        cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
        type: "tv",
        serviceDetails: {
          customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
        },
        subscription_type: customerSubscriptionDetails?.subscription_type
      }
      if (account_status == 'ACTIVE') tvRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;
      this.rollbackSubscription(tvRollBackObj);
      console.log("saveCustomerTv---> ", error?.message);
      message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;
    }
    return message;
  }

  // Retrieves addons details for television plan based on payload
  async getAddonsTelevisionPlanDetails(payload, subscriptionType) {
    try {
      const { plan_name, extra_packages, single_channels, iptv_products } = payload;

      // Retrieve television plan details from plan service
      const result = await planServices.getPlanDetails({ type: 'tv' });
      const planData = result?.data?.planDetails;
      const plan = planData.find(p => p.api_name === plan_name);

      if (!plan) throw new Error(`Plan with name ${plan_name} not found`);

      const isMonthly = subscriptionType === "monthly";
      let addons = [];
      let totalAmount = 0;

      // Process plan details and calculate total amount
      if (plan.api_name === plan_name) {
        const planBillingPeriod = isMonthly ? plan.billing_period[0].monthly : plan.billing_period[0].yearly
        totalAmount += planBillingPeriod.price;
        addons.push({ id: planBillingPeriod.api_name });
      }

      // Process extra packages and add to addons list
      extra_packages.forEach(extra => {
        const extraPackage = plan.optional_extra_packages.find(pkg => pkg.api_name === extra);
        if (extraPackage) {
          const extraPackagebillingPeriod = isMonthly ? extraPackage.billing_period[0].monthly : extraPackage.billing_period[0].yearly
          totalAmount += extraPackagebillingPeriod.price;
          addons.push({ id: extraPackagebillingPeriod.api_name });
        }
      });

      // Process IPTV products and add to addons list
      iptv_products.forEach(iptv => {
        const iptvProduct = plan.optional_iptv_products.find(prod => prod.api_name === iptv);
        if (iptvProduct) {
          const iptvbillingPeriod = isMonthly ? iptvProduct.billing_period[0].monthly : iptvProduct.billing_period[0].yearly
          totalAmount += iptvbillingPeriod.price;
          addons.push({ id: iptvbillingPeriod.api_name });
        }
      });

      // Process single channels and add to addons list
      const totalSingleChannels = single_channels.length;
      if (totalSingleChannels > 0) {
        const X_PICK_5 = Math.floor(totalSingleChannels / 5);
        const X_SNGL_CHANNEL = totalSingleChannels % 5;

        const singleChannels = isMonthly ? CONFIG.chargebee.phonePlanDetails.monthlySingleChannels : CONFIG.chargebee.phonePlanDetails.yearlySingleChannels
        if (X_PICK_5 > 0) {
          totalAmount += X_PICK_5 * singleChannels.pick5ChannelPrice;
          addons.push({ id: singleChannels.pick5ChannelName, quantity: X_PICK_5 });
        }
        if (X_SNGL_CHANNEL > 0) {
          totalAmount += X_SNGL_CHANNEL * singleChannels.pick1ChannelPrice;
          addons.push({ id: singleChannels.pick1ChannelName, quantity: X_SNGL_CHANNEL });
        }
      }

      return { addons, totalAmount };
    } catch (error) {
      console.error(`SubscriptionServices getAddonsTelevisionPlanDetails -> `, error);
      throw error;
    }
  }

  // Retrieves cost estimate for updating phone subscription
  async getEstimateForupdatePhone(payload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("fetch_estimate_phone_logs", { ...elasticLogObj, request: payload });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, phone_plan_id } = payload;
      const customerSubscriptionDetails = await this.getCustomerSubsDetails(customer_subscription_id, "phone");
      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const contactId = customerSubscriptionDetails?.customerDetails?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const getPlanDetails = await this.getInternetPhonePlanDetails(phone_plan_id, "phone", customerSubscriptionDetails?.subscription_type);
      if (!getPlanDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const reqPayload = {
        subscription: {
          id: customerSubscriptionDetails?.cb_subscription_id,
        },
        prorate: true,
        invoice_immediately: true,
        addons: [
          {
            id: getPlanDetails.api_name
          }
        ]
      };

      const info = await callChargebee.getEstimateUpdateSubscription(reqPayload);
      if (info) {
        const data = {
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        };
        returnStatus.status = true;
        returnStatus.data = data;
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      }
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices getEstimateForupdatePhone -> `, error);
      throw error;
    }
  }

  // Updates phone subscription details
  async updatePhone(reqPayload, elasticLogObj, contact_id) {
    const { _id, _index } = await elasticSearch.insertDocument("update_phone_logs", { ...elasticLogObj, request: reqPayload, type: "CREATE", plan_change_type: "UPGRADE" });
    try {
      const returnStatus = { status: false };

      const { customer_subscription_id, phone_plan_id, phone_number_type, phone_number } = reqPayload;

      const customerSubscriptionDetails = await this.getCustomerSubsDetails(customer_subscription_id, "phone");
      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);


      const contactId = customerSubscriptionDetails?.customerDetails?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const getPlanDetails = await this.getInternetPhonePlanDetails(phone_plan_id, "phone", customerSubscriptionDetails?.subscription_type);
      if (!getPlanDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const payload = {
        prorate: true,
        invoice_immediately: true,
        addons: [
          {
            id: getPlanDetails?.api_name,
          }
        ]
      };

      const info = await callChargebee.updateSubscription(customerSubscriptionDetails?.cb_subscription_id, payload);

      if (info) {
        const chargebeeServiceClient = new ChargebeeService();
        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);
        const customerPhoneObj = {
          plan_price: getPlanDetails?.price,
          api_name: phone_plan_id,
        };
        // if (info?.status === 'active') customerPhoneObj.account_status = 'ACTIVE';

        const sf_phone_type = { sf_phone_type: phone_number_type };
        sf_phone_type.phone_number = (phone_number_type === "existing" && phone_number) ? phone_number : "";
        customerPhoneObj.sf_phone_type = JSON.stringify(sf_phone_type);

        const salesforceServiceClient = new SalesforceService();

        if (customerSubscriptionDetails?.customerDetails?.phone_id && customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status != "DELETED") {
          await db.CustomerPhone.update(customerPhoneObj, { where: { id: customerSubscriptionDetails?.customerDetails?.phone_id } });
          returnStatus.status = true;
          returnStatus.message = RESPONSE_MESSAGES.SUCCESS;
        } else {
          const customerSfId = customerSubscriptionDetails?.customerDetails?.sf_record_id;
          const customerPhone = await db.CustomerPhone.create(customerPhoneObj);
          const updateObject = {};
          try {
            const { returnStatus: updateStatus, phoneId } = await salesforceServiceClient.createNewPhoneOrderinSf(phone_number_type, phone_number, customerSfId);
            if (updateStatus && phoneId) {
              const phoneRes = await salesforceServiceClient.getPhoneApiValue(phoneId);
              const { status: phStatus, data: pfData } = phoneRes;
              if (phStatus) {
                updateObject.plan_name = pfData?.Calling_Plan__c;
              }

              updateObject.sf_record_id = phoneId;
              updateObject.sf_response_status = "success";
              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;
              await db.CustomerPhone.update(updateObject, { where: { id: customerPhone.id } });
              await db.CustomerDetails.update({ phone_id: customerPhone.id }, { where: { id: customerSubscriptionDetails.customer_details_id } });
            }
          } catch (error) {
            const phoneRollbackObj = {
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "phone",
              serviceDetails: {
                customerPhone: customerSubscriptionDetails?.customerDetails?.customerPhone,
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
            this.rollbackSubscription(phoneRollbackObj);
            console.log("sf phone update ->", error?.message);
            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;
          } finally {
            returnStatus.status = true;
          }
        }
      }
      if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices updatePhone -> `, error);
      throw error;
    }
  }

  async cancelAddonsSubscription(reqPayload, elasticLogObj, contact_id) {
    const { customer_subscription_id, cancel_type } = reqPayload;
    const elasticIndices = cancel_type == "tv" ? "update_tv_logs" : "update_phone_logs";
    const { _id, _index } = await elasticSearch.insertDocument(elasticIndices, { ...elasticLogObj, request: reqPayload, type: "CANCEL", plan_change_type: "DOWNGRADE" });
    try {
      const returnStatus = { status: false };

      const customerSubscriptionDetails = await this.getCustomerSubsDetails(customer_subscription_id, "cancel");
      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const contactId = customerSubscriptionDetails?.customerDetails?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      if (cancel_type === "tv" && (customerSubscriptionDetails?.customerDetails?.customerTv?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries > 3))
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
      if (cancel_type === "phone" && (customerSubscriptionDetails?.customerDetails?.customerPhone?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries > 3))
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);

      let addonsArray = [];
      let prorateValue = false;

      if (customerSubscriptionDetails?.customerDetails?.tv_id && cancel_type === "phone") {
        prorateValue = true;
        const account_status = customerSubscriptionDetails?.customerDetails?.customerTv?.account_status;
        if (account_status === 'ACTIVE') {
          const { plan_name, extra_packages, single_channels, iptv_products } = customerSubscriptionDetails?.customerDetails?.customerTv;
          const payload = {
            plan_name: plan_name,
            extra_packages: JSON.parse(extra_packages),
            single_channels: JSON.parse(single_channels),
            iptv_products: JSON.parse(iptv_products)
          };

          const responseObj = await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);
          if (!responseObj) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
          addonsArray = responseObj.addons;
        }
      } else if (customerSubscriptionDetails?.customerDetails?.phone_id && cancel_type === "tv") {
        const account_status = customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;
        if (account_status === 'ACTIVE') {
          const getPlanDetails = await this.getInternetPhonePlanDetails(customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name, "phone", customerSubscriptionDetails?.subscription_type);
          addonsArray = [{ id: getPlanDetails.api_name }];
        }
      }

      const subscriptionPayload = {
        prorate: prorateValue,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };

      const info = await callChargebee.updateSubscription(customerSubscriptionDetails.cb_subscription_id, subscriptionPayload);
      if (info) {
        const chargebeeServiceClient = new ChargebeeService();
        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);
        const requested_cancellation_date = info?.next_billing_at ? moment.unix(info?.next_billing_at).format('YYYY-MM-DD') : null;
        if (cancel_type === "phone") {
          const sfPhoneId = customerSubscriptionDetails?.customerDetails?.customerPhone?.sf_record_id;
          const updateObject = { requested_cancellation_date };
          try {
            const orderId = await this.managePhoneOrderinSF(sfPhoneId, info?.next_billing_at);
            if (orderId) {
              updateObject.sf_response_status = "success";
              // updateObject.account_status = "DELETED";
              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;
            }
          } catch (error) {
            const account_status = customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;
            const phRollBackObj = {
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "cancel",
              serviceDetails: {
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
            if (account_status == 'ACTIVE') phRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;
            this.rollbackSubscription(phRollBackObj);
            console.error("cancel tv ->", error)
            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;
          } finally {
            returnStatus.status = true;
            await db.CustomerPhone.update(updateObject, { where: { id: customerSubscriptionDetails?.customerDetails?.phone_id } });
          }
        } else if (cancel_type === "tv") {
          const cancelObj = {
            orderType: "cancel",
            customerTvId: customerSubscriptionDetails?.customerDetails?.customerTv?.sf_record_id,
            submitTime: info?.next_billing_at
          };
          try {
            const orderId = await this.manageTvOrderinSF(cancelObj);
            if (orderId) {
              await db.CustomerTv.update({ account_status: 'REMOVED', requested_cancellation_date, state_text: 'In Progress' }, { where: { id: customerSubscriptionDetails?.customerDetails?.tv_id } });
              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;
            }
          } catch (error) {
            const tvRollBackObj = {
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "cancel",
              serviceDetails: {
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
            if (account_status == 'ACTIVE') tvRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;
            this.rollbackSubscription(tvRollBackObj);
            console.error("cancel TV ->", error, customerSubscriptionDetails?.customerDetails?.tv_id);
            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;
          } finally {
            returnStatus.status = true;
          }
        }
      }
      if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
      return returnStatus;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`SubscriptionServices cancelPhone -> `, error);
      throw error;
    }
  }

  async getCustomerSubsDetails(customer_subscription_id, type) {
    try {

      let phoneAttributeArray = ["api_name", "plan_name", "retries", "sf_response_status", "sf_record_id", "account_status"];
      let tvAttributeArray = ["sf_record_id", "retries", "sf_response_status", "plan_name", 'extra_packages', "single_channels", 'iptv_products', "account_status"]

      let includeObj = {
        model: db.CustomerDetails,
        as: 'customerDetails',
        attributes: ["internet_id", "phone_id", "tv_id", "sf_record_id", "contact_id"],
        required: false,
        include: [{
          model: db.CustomerPhone,
          as: 'customerPhone',
          attributes: phoneAttributeArray,
          required: false
        },
        {
          model: db.CustomerTv,
          as: 'customerTv',
          attributes: tvAttributeArray,
          required: false
        }]
      };

      let customerSubscription = await db.CustomerSubscriptions.findOne({
        include: includeObj,
        attributes: ["customer_details_id", "cb_subscription_id", "subscription_type", "id"],
        where: { id: customer_subscription_id }
      });
      return JSON.parse(JSON.stringify(customerSubscription));
    } catch (error) {
      throw error;
    }
  }

  async manageTvOrderinSF(payload) {
    const { orderType, customerSfId, customerTvId } = payload;
    const salesforceServiceClient = new SalesforceService();
    try {
      if (orderType == "create") {
        if (!customerSfId) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        const { returnStatus: updateStatus, orderId } = await salesforceServiceClient.createOrUpdateTVObject(customerSfId, payload, "create");
        if (updateStatus && orderId) return orderId;
      } else if (orderType == "update" || orderType == "cancel") {
        if (!customerTvId) new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        const { returnStatus: updateStatus } = await salesforceServiceClient.createOrUpdateTVObject(customerSfId, payload, orderType, customerTvId);
        if (updateStatus) return customerTvId;
      }
      return false;
    } catch (error) {
      throw error;
    }
  }

  async managePhoneOrderinSF(sfPhoneId, submitTime) {
    const salesforceServiceClient = new SalesforceService();
    try {
      if (!sfPhoneId) new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
      const { returnStatus: retrnStatus, orderId } = await salesforceServiceClient.createPhoneCancelObject(sfPhoneId, submitTime);
      if (retrnStatus) return orderId;
      return false;
    } catch (error) {
      throw error;
    }
  }

  async updateSpeedChangeOrder(orderId) {

    try {
      const salesforceServiceClient = new SalesforceService();
      const orderDetails = await salesforceServiceClient.getOrderDetails(orderId, "InternetElSpeedChangeOrder");
      if (!orderDetails) return null;

      const sfUpdatedAt = getCurrentAtlanticTime(orderDetails?.LastModifiedDate, "sfUpdate");
      const createdAt = getCurrentAtlanticTime(orderDetails.CreatedDate, "sfUpdate");

      let insertOrderDetails = { sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, createdAt, sf_name: orderDetails?.Name };

      if (orderDetails?.Expected_Completion_Date__c) insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;
      if (orderDetails?.Speed__c) insertOrderDetails.speed = orderDetails?.Speed__c;
      if (orderDetails?.Stage__c) insertOrderDetails.stage = orderDetails?.Stage__c;
      if (orderDetails?.Response_Date__c) insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);

      const orderInsert = await db.InternetElSpeedChangeOrder.create(insertOrderDetails);
      return orderInsert?.id;
    } catch (error) {
      const salesforceServiceClient = new SalesforceService();
      await salesforceServiceClient.deleteSpeedChangeOrder(orderId);
      console.error("Subscription Service updateSpeedChangeOrder -> ", error);
      throw error;
    }
  }

  async rollbackSubscription(payload) {
    try {
      const { cb_subscription_id, type, serviceDetails, subscription_type } = payload;
      if (!serviceDetails || !type || !subscription_type) return; // Early return for missing subscription

      const updateChargebeeSubscription = async (payload) => {
        await callChargebee.updateSubscription(cb_subscription_id, payload);
      };

      if (type === 'internet') {
        const { plan_name } = serviceDetails;
        if (!plan_name) return;
        const internetPlanDetails = await this.getInternetPhonePlanDetails(plan_name, type, subscription_type);
        if (!internetPlanDetails) return; // Early return for missing plan details

        const internetPayload = { plan_id: internetPlanDetails.api_name, prorate: true, invoice_immediately: true };
        await updateChargebeeSubscription(internetPayload);

      } else if (type === 'tv' || type === 'cancel') {
        const { customerTv, customerPhone } = serviceDetails;

        let addons = [];
        if (customerTv) {
          const { plan_name, extra_packages, single_channels, iptv_products } = customerTv;
          const tvPayload = {
            plan_name,
            extra_packages: JSON.parse(extra_packages),
            single_channels: JSON.parse(single_channels),
            iptv_products: JSON.parse(iptv_products)
          };
          const { addons: tvAddOn } = await this.getAddonsTelevisionPlanDetails(tvPayload, subscription_type);
          addons.push(tvAddOn);
        }

        if (customerPhone) {
          const phonePlanDetails = await this.getInternetPhonePlanDetails(customerPhone.api_name, "phone", subscription_type);
          if (phonePlanDetails) addons.push({ id: phonePlanDetails.api_name });
        }

        const reqPayload = { prorate: true, replace_addon_list: true, invoice_immediately: true, addons };
        await updateChargebeeSubscription(reqPayload);
      } else if (type === 'phone') {
        const { customerPhone } = serviceDetails;
        if (customerPhone) {
          const { api_name } = customerPhone;
          if (!api_name) return;
          const phonePlanDetails = await this.getInternetPhonePlanDetails(api_name, type, subscription_type);
          if (!phonePlanDetails) return; // Early return for missing plan details

          const phonePayload = { prorate: true, invoice_immediately: true, addons: [{ id: api_name }] };
          await updateChargebeeSubscription(phonePayload);
        } else {
          const { customerTv } = serviceDetails;
          if (customerTv) {
            const { customerTv } = serviceDetails;
            const { plan_name, extra_packages, single_channels, iptv_products } = customerTv;
            const tvPayload = {
              plan_name,
              extra_packages: JSON.parse(extra_packages),
              single_channels: JSON.parse(single_channels),
              iptv_products: JSON.parse(iptv_products)
            };

            const { addons } = await this.getAddonsTelevisionPlanDetails(tvPayload, subscription_type);
            const reqPayload = { prorate: true, replace_addon_list: true, invoice_immediately: true, addons };
            await updateChargebeeSubscription(reqPayload);
          }
        }
      }
    } catch (error) {
      console.error("Error during rollbackSubscription:", error);
    }
  }

  async callForRollBack(customerSubscription) {
    try {
      const cb_subscription_id = customerSubscription.cb_subscription_id;
      const subscription_type = customerSubscription.subscription_type;
      let plan_name = customerSubscription?.customerDetails?.customerInternet?.plan_name;
      const latestSpeedChangeOrderStage = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.stage;
      const latestSpeedChangeOrderSpeed = customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.speed;

      if (latestSpeedChangeOrderStage == "Eligible For Submission" && latestSpeedChangeOrderSpeed) {
        const internetPlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);
        if (internetPlanDetails?.length) {
          const getPrice = internetPlanDetails.find(internet => internet.speed === latestSpeedChangeOrderSpeed);
          if (getPrice?.api_name) plan_name = getPrice.api_name;
        }
      }
      const internetRollbackObj = {
        cb_subscription_id,
        type: "internet",
        serviceDetails: { plan_name },
        subscription_type
      }
      this.rollbackSubscription(internetRollbackObj);

    } catch (error) {
      console.error("callForRollBack -> ", error)
    }
  }
}

module.exports = SubscriptionServices;
