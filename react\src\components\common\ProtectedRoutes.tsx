import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Navigate, Route, Routes } from "react-router-dom";
import { rootState } from "../../store/reducers/rootReducer";
import Layout from "./Layout";

export const ProtectedRoutes: React.FC = () => {
  const [delayPassed, setDelayPassed] = useState(false);
  const isLoggedIn = useSelector(
    (state: rootState) => state.authenticationReducer.isLoggedIn
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      setDelayPassed(true);
    }, 100);

    // Clean up the timer if the component unmounts
    return () => clearTimeout(timer);
  }, []);

  if (!delayPassed) {
    return null;
  }

  return isLoggedIn ? (
    <Routes>
      <Route path="/*" element={<Layout />} />
    </Routes>
  ) : (
    <>
      <Navigate to="/login" />
    </>
  );
};
