import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AddIcon, EditIcon } from "../../../assets/Icons";
import useMediaQuery from "../../../hooks/MediaQueryHook";
import {
  useRenewalDateMutation,
  useRenewalProratedMutation,
} from "../../../services/api";
import {
  toggleShowNextPaymentDateConfirmationPopup,
  toggleShowNextPaymentDatePopup,
} from "../../../store/reducers/nextPaymentDateReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import {
  nextPaymentDate,
  showNextPaymentDateConfirmationPopup,
  showNextPaymentDatePopup,
} from "../../../store/selectors/nextPaymentDateSelectors";
import { formatCurrency, formatDate } from "../../../utils/helper";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import Popup from "../../common/Popup";
import ServiceBox from "../ServiceBox";
import ChangeRenewalDatePopup from "./ChangeRenewalDatePopup";
import { logout } from "../../../store/reducers/authenticationReducer";
import { useNavigate } from "react-router-dom";

interface RenewalDateBoxProps {
  refetch: () => void;
}
const RenewalDateBox: React.FC<RenewalDateBoxProps> = ({ refetch }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const paymentDate = useSelector(nextPaymentDate);
  const showPopup = useSelector(showNextPaymentDatePopup);
  const showConfirmationPopup = useSelector(
    showNextPaymentDateConfirmationPopup
  );

  // Media query
  const isDesktop = useMediaQuery("screen and (min-width: 1300px)");
  const [renewalDate, renewalDateLoading] = useRenewalDateMutation();
  const [renewalEstimate, renewalEstimateLoading] =
    useRenewalProratedMutation();
  const [newPaymentDate, setNewPaymentDate] = useState<Date | null>(null);
  const [proratedAmount, setProratedAmount] = useState<number | null>(null);

  // Function to handle next payment date submission
  const handleNextPaymentDateSubmit = async () => {
    if (!renewalEstimateLoading?.isLoading) {
      try {
        const paymentDateWithTime = new Date(newPaymentDate);
        const currentDateTime = new Date();
        paymentDateWithTime.setHours(currentDateTime.getHours());
        paymentDateWithTime.setMinutes(currentDateTime.getMinutes());
        paymentDateWithTime.setSeconds(currentDateTime.getSeconds());
        const data = {
          customer_subscription_id: paymentDate?.id,
          renewal_date: paymentDateWithTime,
        };
        const response = await renewalEstimate(data).unwrap();

        if (response.status === 200) {
          setProratedAmount(response?.data?.prorated_amount);
          dispatch(toggleShowNextPaymentDatePopup());
          dispatch(toggleShowNextPaymentDateConfirmationPopup());
        }
      } catch (error: any) {
        if (error?.data?.error?.length > 0) {
          dispatch(
            addNotification({
              type: "error",
              message: error?.data?.error?.[0]?.message,
            })
          );
        } else {
          dispatch(
            addNotification({ type: "error", message: error?.data?.message })
          );
        }
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to handle confirmation submission
  const handleConfirmationSubmit = async () => {
    if (!renewalDateLoading?.isLoading) {
      try {
        const paymentDateWithTime = new Date(newPaymentDate);
        const currentDateTime = new Date();
        paymentDateWithTime.setHours(currentDateTime.getHours());
        paymentDateWithTime.setMinutes(currentDateTime.getMinutes());
        paymentDateWithTime.setSeconds(currentDateTime.getSeconds());
        const data = {
          customer_subscription_id: paymentDate?.id,
          renewal_date: paymentDateWithTime,
        };
        const response = await renewalDate(data).unwrap();

        if (response.status === 200) {
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
          dispatch(toggleShowNextPaymentDateConfirmationPopup(false));
          // dispatch(setNextPaymentDate(newPaymentDate));
          refetch();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to handle confirmation close
  const handleConfirmationClose = () => {
    dispatch(toggleShowNextPaymentDateConfirmationPopup());
    dispatch(toggleShowNextPaymentDatePopup());
  };

  return (
    <ServiceBox
      isEdit={paymentDate ? true : false}
      attributes={{
        onClick: () => dispatch(toggleShowNextPaymentDatePopup()),
      }}
    >
      {paymentDate ? (
        <div className="flex 2xl:gap-5 gap-2.5 flex-col">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-xl 2xl:text-[26px] font-medium">Renewal Date</p>
            </div>
            <div
              className="border border-[#D7B9FF] bg-white 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-purple-50"
              onClick={() => dispatch(toggleShowNextPaymentDatePopup())}
            >
              {paymentDate ? (
                <EditIcon width={20} height={20} />
              ) : (
                <AddIcon width={20} height={20} />
              )}
            </div>
          </div>
          <div>
            <p className="text-base font-medium uppercase">
              {paymentDate?.next_billing_at}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex rounded-20 justify-between items-center">
          <div className="items-center">
            <p className="2xl:text-[26px] text-xl font-bold">Renewal Date</p>
          </div>
          <div
            className="border border-black 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey"
            onClick={() => dispatch(toggleShowNextPaymentDatePopup())}
          >
            {paymentDate ? (
              <EditIcon width={20} height={20} />
            ) : (
              <AddIcon width={20} height={20} />
            )}
          </div>
        </div>
      )}
      {/* Next Payment Date Popup */}
      {showPopup && (
        <Popup
          title="Change renewal date"
          width={isDesktop ? "720px" : "600px"}
          height="1280px"
          closeHandler={() => {
            if (!renewalEstimateLoading?.isLoading) {
              dispatch(toggleShowNextPaymentDatePopup());
              setNewPaymentDate(null);
            }
          }}
        >
          <ChangeRenewalDatePopup
            nextPaymentDate={newPaymentDate}
            currentPaymentDate={paymentDate}
            updateNextPaymentDate={setNewPaymentDate}
            closeHandler={() => {
              dispatch(toggleShowNextPaymentDatePopup());
              setNewPaymentDate(null);
            }}
            handleSubmit={handleNextPaymentDateSubmit}
            isLoading={renewalEstimateLoading?.isLoading}
          />
        </Popup>
      )}
      {showConfirmationPopup && (
        <ConfirmationMessagePopup
          title={"Confirm Renewal Date Change"}
          message={`You have edited the renewal date in your plan to ${formatDate(
            newPaymentDate
          )}. These changes should take effect right away and your card on file will be charged the prorated amount of ${formatCurrency(
            proratedAmount ? proratedAmount : 0,
            true
          )}`}
          handleSubmit={handleConfirmationSubmit}
          closeHandler={handleConfirmationClose}
          isLoading={renewalDateLoading?.isLoading}
        />
      )}
    </ServiceBox>
  );
};

export default RenewalDateBox;
