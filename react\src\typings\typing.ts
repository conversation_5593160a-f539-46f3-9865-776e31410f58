import {
  ButtonHTMLAttributes,
  ChangeEvent,
  Mouse<PERSON>vent,
  ReactN<PERSON>,
  SVGProps,
} from "react";

export interface AuthState {
  isLoggedIn: boolean;
}

export enum AuthActionTypes {
  LOGIN = "LOGIN",
  LOGOUT = "LOGOUT",
}

interface LoginAction {
  type: AuthActionTypes.LOGIN;
  [key: string]: any;
}

interface LogoutAction {
  type: AuthActionTypes.LOGOUT;
  [key: string]: any;
}

export type AuthAction = LoginAction | LogoutAction;

// Icon Props
export type IconProps = SVGProps<SVGSVGElement>;

// Button Props
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  title: string;
  className?: string;
  isLoading?: boolean;
  btnType?: string;
  clickEvent?: (action?: any) => void;
  type?: "submit" | "reset" | "button" | undefined;
  attributes?: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

// Input Field Props
export interface InputFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: string;
  title?: string;
  placeHolder?: string;
  className?: string;
  changeEvent?: (event: ChangeEvent<HTMLInputElement>) => void;
  isFocus?: boolean;
  isReadOnly?: boolean;
  isDisabled?: boolean;
  isErrorMsg?: string | null;
  valid?: boolean;
  attributes?: React.InputHTMLAttributes<HTMLInputElement>;
  disableManualEntryForaddress?: boolean;
}
export interface AddressFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  type?: string;
  title?: string;
  placeHolder?: string;
  className?: string;
  changeEvent?: (event: ChangeEvent<HTMLInputElement>) => void;
  isFocus?: boolean;
  isReadOnly?: boolean;
  isDisabled?: boolean;
  isErrorMsg?: string | null;
  attributes?: React.InputHTMLAttributes<HTMLInputElement>;
  setLocationData: (locationData: any) => void;
  inputRef: any;
  formData: any;
}

export interface PopupProps {
  title: string;
  children: ReactNode;
  width?: string;
  height?: string;
  closeBtn?: boolean;
  closeHandler?: (event: MouseEvent<HTMLSpanElement>) => void;
}
export interface BestDealPopupProps {
  children: ReactNode;
  width?: string;
  height?: string;
  closeBtn?: boolean;
  closeHandler?: (event: MouseEvent<HTMLSpanElement>) => void;
}
export interface CloseHandlerProps {
  handleOPenForgotPasswordPopup: (event?: MouseEvent<HTMLSpanElement>) => void;
}

// Login Props
export interface LoginFormState {
  email: string;
  password: string;
}

export interface LocationDataState {
  streetNumber: string;
  streetAddress: string;
  streetName: string;
  apartment: string;
  city: string;
  province: string;
  postal_code: string;
  pinCode: string;
  service_change_date?: Date;
  id: any;
  address: string;
  full_address: string;
}
export interface LocationDataProps {
  title?: string;
  description?: string;
  formData: LocationDataState;
  currAddress?: object;
  setFormData: (value: LocationDataState) => void;
  handleSubmit: (value: void) => void;
  closeHandler?: (event: MouseEvent<HTMLSpanElement>) => void;
  isLoading?: boolean;
  disableManualEntryForaddress?: boolean;
  resetAddressClick?: () => void;
  handleNextFibreAddress?: () => void;
}
export interface ContactInfoDataState {
  first_name: string;
  last_name: string;
  email: string;
  cell_phone: string;
  secondary_phone: string;
  additional_name: string;
  company_name: string;
  image_url: null;
}

export interface AddCardDetailsProps {
  refetch: () => void;
  closeHandler: (event?: MouseEvent<HTMLSpanElement>) => void;
  subscription_id: any;
}

export interface CardDetailState {
  userName: string;
  cardNumber: string;
  cvv: string;
  expiryDate: string;
  pinCode: string;
  postalCode: string;
  primaryCard: boolean;
}

export interface CustomCalendarProps {
  selectedDate?: Date | null;
  timeRangeInDays?: number;
  coolingTime?: number;
  setDate: (value: Date) => void;
  maxDate?: Date | null;
  minDate?: Date | null;
  isLoading?: boolean; // optional field
  disableWeekEnds?: boolean;
  disableHolidays?: boolean;
  disableNext3WorkingDays?: boolean;
}

export interface PrevDayTileState {
  className: string;
  suffix: string;
}

export interface TotalCardProps {
  cardType: string;
  value: number | string;
  fill?: boolean;
  icon?: ReactNode;
}

export interface ConfirmationMessageProps {
  title: string;
  message: string | Array<string>;
  handleSubmit: () => void;
  closeHandler: () => void;
  isLoading?: boolean;
  btnText?: string;
}

export interface ManagePlanState {
  showServiceAddressPopup: boolean;
  showMailAddressPopup: boolean;
  showInternetPopup: boolean;
  showTVPopup: boolean;
  showNextPaymentDatePopup: boolean;
  showHomePhonePopup: boolean;
  showViewEquipmentPopup: boolean;
  showPauseServicePopup: boolean;
  showResumeServicePopup: boolean;
  showCancelServicePopup: boolean;
}

export interface ConfirmationMessagePopupState {
  addServiceAddress: boolean;
  changeServiceAddress: boolean;
  addMailAddress: boolean;
  changeMailAddress: boolean;
  newInternet: boolean;
  changeInternet: boolean;
  newTVPlan: boolean;
  changeTVPlan: boolean;
  renewalDate: boolean;
  HomePhone: boolean;
  pauseService: boolean;
  resumeService: boolean;
  cancelService: boolean;
}
export interface TVPlanProps {
  tvList: any;
  isLoading: boolean;
  tvPlan: selectedTVPlanProps;
  isCancel?: boolean;
  handleChannelSelect: (value: any) => void;
  handleBasePackageSelect: (value: object) => void;
  handleAdditionalPackageSelect: (value: object) => void;
  handleDVRSelect: () => void;
  handleTVPlanSubmit: () => void;
  closeHandler: () => void;
  handleViewAll: (
    event: React.MouseEvent<HTMLElement>,
    data: Array<string>
  ) => void;
  btnLoading?: boolean;
  handleTVCancelService?: () => void;
}

export interface selectedTVPlanProps {
  additionalChannels: Array<string>;
  basePlan: string;
  additionalPlan: any;
  DVR: Array<string>;
  isPlanRemoved: boolean;
  isPlanUpdated: boolean;
  single_channels: any;
  plan_name: string;
  extra_packages:any;
  iptv_products: Array<string>;
  requested_cancellation_date: Date | null;
}

export interface HomePhonePlanState {
  haveHomePhonePlan: object;
  isNewPhoneNumber: boolean;
  isPlanRemoved: boolean;
  phoneNumber: string;
}

export interface AddHomePhoneProps {
  closeHandler: () => void;
  handleSubmit: () => void;
  homePhonePlan: HomePhonePlanState;
  handleHomePhonePlanChange: (key: string, value: string | boolean) => void;
  handleCancelService: () => void;
  cancelLoading?: boolean;
}

export interface serviceAddressReducerState {
  currentAddress: LocationDataState;
  newAddress: LocationDataState;
  moveDate: Date | null;
  serviceStatus: string;
  showServiceAddressPopup: boolean;
  showFibreAddressPopup: boolean;
  showDeleteOrderPopup: boolean;
  showServiceAddressDateSelectorPopup: boolean;
  showServiceAddressConfirmationPopup: boolean;
}

export interface mailingReducerState {
  currentAddress: LocationDataState;
  showMailingAddressPopup: boolean;
  showMailingAddressConfirmationPopup: boolean;
}

export interface nextPaymentDateReducerState {
  nextPaymentDate: Date | null;
  showNextPaymentDatePopup: boolean;
  showNextPaymentDateConfirmationPopup: boolean;
}
export interface customerSubscriptionReducerState {
  subscriptionType: string;
}

export interface internetReducerState {
  internetPlan: object;
  newInternetPlan: object;
  showInternetPopup: boolean;
  showDeleteOrderPopup: boolean;
  showInternetConfirmationPopup: boolean;
}
export interface televisionReducerState {
  plan: selectedTVPlanProps;
  showDeleteOrderPopup: boolean;
  showTelevisionPopup: boolean;
  showTelevisionConfirmationPopup: boolean;
}
export interface homePhoneReducerState {
  haveHomePhonePlan: any;
  isNewPhoneNumber: boolean;
  phoneNumber: string;
  isPlanRemoved: boolean;
  showHomePhonePopup: boolean;
  showDeleteOrderPopup: boolean;
  showSelectNumberPopup: boolean;
  showHomePhoneChangeConfirmationPopup: boolean;
}
export interface cancelServiceReducerState {
  cancelDate: Date | null;
  deleteOrder: boolean;
  feedback: string;
  showDeleteOrderPopup: boolean;
  showSelectCancelServiceDatePopup: boolean;
  showFeedbackPopup: boolean;
  showOfferPopup: boolean;
  showConfirmPopup: boolean;
}
export interface SelectNumberPopupProps {
  homePhonePlan: HomePhonePlanState;
  handleHomePhonePlanChange: (key: string, value: string | boolean) => void;
  handleSubmit: () => void;
  closeHandler: () => void;
  isLoading?: boolean;
  phoneError?: any;
}

export interface ToastProps {
  id: string;
  message: string;
  type: string;
  closeHandler?: (id: string) => void;
}

export interface LocationSelectCardProps {
  location: any;
  nextHandler: (locationId: string) => void;
}

export interface PaginationData {
  limit: number;
  page: number;
  total: number;
  totalPage: number;
}
export interface Statement {
  createdAt: string | null;
  amount: number;
  id: string;
  status: any;
  expected_payment_date?: Date | null;
  cb_invoice_id: number;
  credit_issue: number | null;
  amount_adjusted: number | null;
}

export interface PaginationProps {
  id?: number;
  currentPage: number;
  totalPageCount: number;
  onChange: (index: number, pageNumber: number) => void;
}
export interface YourPlacesProps {
  places: any;
}
export interface YourBalanceCardProps {
  refreshLocation: () => void;
}

// Interface for referral link props
export interface ReferralLinkProps {
  link: string;
}

export interface ReferralDetails {
  total_referrals: number;
  total_paid_referrals: number;
  total_earned: number;
  referral_link: string;
}

// Interface for referral item
export interface ReferralItem {
  id: string;
  referral_name: string | null;
  createdAt: string | null;
  credit_added_on: string | null;
  referred_amount: number;
}

export interface PaymentStatusInterface {
  expectedDate: Date | string;
  isMoveOrder?: any;
}
