const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const INTERVAL = CONFIG.interval;

const customerServices = new CustomerServices();

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class CustomerTestServices {

    async syncCustomerDetails(contactIds) {
        try {
            let query = `SELECT Id, Contact__c, Service_Address__c, Mailing_Address__c, Latest_TV__c, Latest_Internet__c, Latest_Phone_VOIP__c, Latest_CB_Subscription__c, CB_Subscription_Id__c, CP_Stage__c, Name, CreatedDate, LastModifiedDate, (SELECT Id, Customer_Details__c, Name, CP_Speed__c, Live_Date__c, Disconnected_Date__c, Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, CP_Status_Internal_Processing__c, CP_Status_Ship_Package__c, CP_Status_Modem_Activation__c, CP_Status_Modem_Installation__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c, LastModifiedDate, CreatedDate, Status__c FROM Internet__r), (SELECT Id, Name, Account_Status__c, Calling_Plan__c, Latest_Cancel_Phone_Order__c, Requested_Cancellation_Date__c, Phone_Number_To_Port__c, Service_Start_Date__c, CreatedDate, LastModifiedDate FROM Phones__r), (SELECT Id, Name, Current_Base_Package__c, Current_Extra_Packages__c, State_Text__c, Current_Single_Channels__c, current_iptv_products__c, current_account_status__c, LastModifiedDate, Login_Details_Last_Sent__c, Requested_Cancellation_Date__c, CreatedDate FROM TV__r), (SELECT Id, chargebeeapps__CB_Subscription_Id__c, chargebeeapps__Plan_Amount__c, chargebeeapps__Subcription_Activated_At__c, chargebeeapps__Next_billing__c, chargebeeapps__Subscription_status__c, LastModifiedDate, chargebeeapps__Subscription_Created_At__c, Customer_Details__c, CreatedDate FROM CB_Subscriptions__r), (SELECT Id, Name, Mailing_Suite_Unit__c, Mailing_Street_Number__c, Mailing_Street_Name__c, Mailing_City_Town__c, Mailing_Province__c, Mailing_Postal_Code__c, Full_Mailing_Address__c, Mailing_Country__c, Status__c, LastModifiedDate, CreatedDate FROM Mailing_Addresses__r) FROM Customer_Details__c WHERE Contact__c IN (${contactIds})`;

            if (INTERVAL === "regular") query += " AND LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const customerData = await salesforceConnection.fetchAllRecords(query);
            if (customerData?.length) await customerServices.checkAndManageCustomers(customerData);
            return { execute: true, status: true, contactCount: customerData?.length }
        } catch (error) {
            console.error("Sync test service get customer details -> ", error);
            return { execute: true, status: false, contactIds, error: error?.message || null }
        }
    }
}

module.exports = CustomerTestServices;
