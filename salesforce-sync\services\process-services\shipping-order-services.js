const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const customerServices = new CustomerServices();
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);
const INTERVAL = CONFIG.interval;
const { getCurrentAtlanticTime } = require("../../helper/custom-helper");

class shippingOrderTestServices {
    async getShippingOrderDetails() {
        let shippingCount = 0;
        try {
            let query = `SELECT Id, Name, Full_Mailing_Address__c, Ship_Date__c, Ship_Drop_Off_Date__c, Tracking_URL__c, Courier__c, CreatedDate, LastModifiedDate FROM Shipping__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const shippingDetails = await salesforceConnection.fetchAllRecords(query);
            shippingCount = shippingDetails?.length || 0;

            if (shippingCount) {
                await this.checkAndManageDetails(shippingDetails);
            }

            return { execute: true, synctype: "getShippingOrderDetails", status: true, shippingCount };
        } catch (error) {
            console.error("Sync service get Shipping Order Details -> ", error);
            return { execute: true, synctype: "getShippingOrderDetails", status: false, shippingCount, error: error?.message || null };
        }
    }

    async checkAndManageDetails(sfshippingDetails) {
        try {
            for (const shippingData of sfshippingDetails) {
                const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, Name: sf_name } = shippingData;

                let queryParams = [];
                let query = `UPDATE creation_order_shipping SET 
                                    sf_name = ?,
                                    full_mailing_address = ?, 
                                    courier = ?,
                                    sf_updatedAt = ?, 
                                    updatedAt = ?,
                                    tracking_url = ?,
                                    ship_date = ?,
                                    ship_drop_off_date = ?,
                                    package_deliverted_at = ?`;

                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const updatedTime = getCurrentAtlanticTime();

                if (sf_name) queryParams.push(sf_name);
                else queryParams.push(null);

                if (full_mailing_address) queryParams.push(full_mailing_address);
                else queryParams.push(null);

                if (courier) queryParams.push(courier);
                else queryParams.push(null);

                queryParams.push(modifiedDate, updatedTime);

                if (tracking_url) queryParams.push(tracking_url);
                else queryParams.push(null);

                if (ship_date) queryParams.push(ship_date);
                else queryParams.push(null);

                if (ship_drop_off_date) queryParams.push(ship_drop_off_date);
                else queryParams.push(null);

                if (package_deliverted_at) queryParams.push(package_deliverted_at);
                else queryParams.push(null);

                query += ` WHERE sf_record_id = ?`;
                queryParams.push(sf_record_id);

                await customerServices.executeQuery(query, queryParams);
            }
        } catch (error) {
            console.error("SalesforceService check And Manage shipping Internet -> ", error);
        }
    }
}

module.exports = shippingOrderTestServices;
