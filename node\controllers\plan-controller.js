const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const PlanServices = require("../services/plan-services");
const planServices = new PlanServices();

class PlanController {

    /**
     * Manage plan endpoint handler
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    async managePlan(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { custDetailsId } = req.params;
            const result = await planServices.managePlan(id, custDetailsId, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Plan Controller manage plan ->`, error);
            next(error);
        }
    }

    /**
     * Update address endpoint handler
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    async updateAddress(req, res, next) {
        try {
            const userDetails = req.userDetails;
            const body = req.body;
            const result = await planServices.updateAddress({ userDetails, body }, req.elasticLogObj);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Plan Controller update address ->`, error);
            next(error);
        }
    }

    /**
     * Get plan details endpoint handler
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    async getPlanDetails(req, res, next) {
        try {
            const result = await planServices.getPlanDetails(req.query, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Plan Controller update address ->`, error);
            next(error);
        }
    }
}

// Export an instance of the PlanController class
module.exports = new PlanController();