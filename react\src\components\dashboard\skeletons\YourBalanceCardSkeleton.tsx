import React from "react";
import ContentLoader from "react-content-loader";

const YourBalanceCardSkeleton: React.FC = (props) => (
  <ContentLoader
    width={"100%"}
    height={"215"}
    backgroundColor="#f5f5f5"
    foregroundColor="#dbdbdb"
    {...props}
  >
    {/* //left bar */}
    <rect x="4" y="8" rx="5" ry="3" width="8" height="215" />
    {/* bottom bar*/}
    <rect x="7" y="207" rx="5" ry="3" height="8" width={"100%"} />
    {/* top bar*/}
    <rect x="5" y="8" rx="5" ry="3"  height="7" width={"100%"} />
    {/* right bar*/}
    <rect x="99.50%" y="9" rx="5" ry="3" width="8" height="215" />

    {/* text 1 */}
    <rect x="30" y="40" rx="5" ry="3" width="300" height="19" />

    {/* text 2 */}
    <rect x="30" y="126" rx="5" ry="3" width="138" height="20" />
    {/* text 3 */}
    <rect x="30" y="166" rx="5" ry="3" width="138" height="20" />

    {/* text 4 */}
    <rect x="188" y="126" rx="5" ry="3" width="138" height="19" />
    {/* text 5 */}
    <rect x="188" y="166" rx="5" ry="3" width="138" height="19" />

    {/* Make payment */}
    <rect x="80%" y="38" rx="12" ry="12" width="268" height="60"/>
    {/* Make payment arrangement */}
    <rect x="80%" y="118" rx="12" ry="12" width="268" height="60"/>
  </ContentLoader>
);

export default YourBalanceCardSkeleton;
