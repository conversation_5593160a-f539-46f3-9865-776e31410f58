const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("promotion", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: 'sf_record_id',
      allowNull: false,
      comment: "Salesforce record ID"
    },
    promotion_name: {
      type: Sequelize.STRING(30),
      comment: "Associated with Name field of Promotion__c in sf."
    },
    name: {
      type: Sequelize.STRING(30),
      comment: "Associated with Name__c field of Promotion__c in sf."
    },
    status: {
      type: Sequelize.ENUM('true', 'false'),
      comment: "Associated with Active__c field of Promotion__c in sf."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of Promotion__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}