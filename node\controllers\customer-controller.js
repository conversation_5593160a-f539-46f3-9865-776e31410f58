const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const CustomerServices = require("../services/customer-services");
const customerServices = new CustomerServices();
const { decodeToken } = require("../middleware/userAuth")
class CustomerController {

    /**
     * Reset the customer's password
     * @param {Object} req - The request object
     * @param {Object} res - The response object
     * @param {Function} next - The next middleware function
     */

    async resetPassword(req, res, next) {
        try {
            const authHeader = req.headers['authorization']
            let token = authHeader && authHeader.split(' ')[1];
            token = await decodeToken(token, "aws");

            // Call the resetPassword service with the request body and token
            const result = await customerServices.resetPassword({ ...req.body, token }, req.elasticLogObj);
            const { status, message } = result;

            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Customer Controller Reset Password ->`, error);
            next(error);
        }
    }

    /**
     * Get customer details
     * @param {Object} req - The request object
     * @param {Object} res - The response object
     * @param {Function} next - The next middleware function
     */
    async getCustomerDetails(req, res, next) {
        try {
            // Call the getCustomerDetails service with user details from the request
            const result = await customerServices.getCustomerDetails(req.userDetails, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Customer Controller get Customer Details ->`, error);
            next(error);
        }
    }

    /**
     * Logout the customer
     * @param {Object} req - The request object
     * @param {Object} res - The response object
     * @param {Function} next - The next middleware function
     */
    async logout(req, res, next) {
        try {
            const authHeader = req.headers['authorization']
            let token = authHeader && authHeader.split(' ')[1];
            token = await decodeToken(token, "aws");

            const result = await customerServices.logout(token, req.elasticLogObj);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Customer Controller logout ->`, error);
            next(error);
        }
    }

    /**
     * Update customer details
     * @param {Object} req - The request object
     * @param {Object} res - The response object
     * @param {Function} next - The next middleware function
     */
    async updateCustomerDetails(req, res, next) {
        try {
            const file = req.file;
            const userDetails = req.userDetails;
            const body = req.body;
            const result = await customerServices.updateCustomerDetails({ userDetails, body, file }, req.elasticLogObj);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Customer Controller update details ->`, error);
            next(error);
        }
    }
}

// Export an instance of the CustomerController class
module.exports = new CustomerController();