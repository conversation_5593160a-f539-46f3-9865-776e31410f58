const Joi = require('joi');

const subscriptionRenewalValidation = Joi.object({
    customer_subscription_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer subscription ID is required.',
            'number.base': 'Customer subscription ID must be a number.',
            'number.integer': 'Customer subscription ID must be an integer.'
        }),
    renewal_date: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Renewal date is required.'
        }),
});

module.exports = { subscriptionRenewalValidation };
