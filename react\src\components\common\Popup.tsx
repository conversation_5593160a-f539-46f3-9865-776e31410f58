import React, { useEffect, useRef, useState } from "react";
import { CloseIcon } from "../../assets/Icons.tsx";
import { PopupProps } from "../../typings/typing";

const Popup: React.FC<PopupProps> = ({
  title,
  children,
  width = "840px",
  // height = "501px",
  closeBtn = true,
  closeHandler,
}) => {
  const [screenHeight, setScreenHeight] = useState<number>(window.innerHeight);
  const [actualHeight, setActualHeight] = useState<number | null>(null);
  const popupBody = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const updateHeight = () => {
      if (popupBody.current) {
        const calculatedHeight = popupBody.current.clientHeight + 60;
        setActualHeight(calculatedHeight);
        setScreenHeight(window.innerHeight);
      }
    };

    updateHeight(); // Initial update
    window.addEventListener("resize", updateHeight); // Update on resize

    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  useEffect(() => {
    const mutationObserver = new MutationObserver(() => {
      if (popupBody.current) {
        const calculatedHeight = popupBody.current.clientHeight + 60;
        setActualHeight(calculatedHeight);
      }
    });

    if (popupBody.current) {
      mutationObserver.observe(popupBody.current, {
        childList: true,
        subtree: true,
      });
    }

    return () => mutationObserver.disconnect();
  }, []);

  return (
    <div className="fixed w-full h-full left-0 top-0 z-[103] popup-open scrollbarNone overflow-auto">
      <div
        className={`absolute left-1/2 bg-white -translate-x-1/2 w-full z-[12] max-lg:w-[85%] xl:rounded-30 rounded-20 lg:p-30 p-5 ${
          actualHeight && screenHeight < actualHeight
            ? "top-[10%]"
            : "top-1/2 -translate-y-1/2"
        } `}
        style={{ maxWidth: width }}
        ref={popupBody}
      >
        <div
          className={`bg-lightBg flex justify-between items-center mb-4 xl:pl-2.5`}
        >
          <h4 className="xl:text-[26px] lg:text-xl text-lg leading-1_35 font-medium text-[#333333] w-[95%]">
            {title}
          </h4>
          {closeBtn && (
            <div
              onClick={closeHandler}
              className="cursor-pointer w-[30px] h-[30px] rounded-full border-2 border-black flex items-center justify-center"
            >
              <CloseIcon />
            </div>
          )}
        </div>
        <div className="2xl:pt-6 xl:p-2.5 relative">{children}</div>
      </div>
    </div>
  );
};

export default Popup;
