const request = require('supertest');
const express = require('express');
const referralRoute = require('../../routes/referral-route');

// Mock the referral controller
jest.mock('../../controllers/referral-controller', () => ({
  fetchDetails: jest.fn((req, res) => res.status(200).json({ 
    message: 'Referral details retrieved successfully', 
    data: {
      referralProgram: {
        id: 'ref_program_1',
        name: 'Purple Cow Referral Program',
        description: 'Refer friends and earn rewards',
        isActive: true,
        terms: 'Terms and conditions apply'
      },
      userReferralInfo: {
        customerId: '12345',
        referralCode: 'PURPLECOW12345',
        referralLink: 'https://purplecow.com/refer/PURPLECOW12345',
        totalReferrals: 5,
        successfulReferrals: 3,
        pendingReferrals: 2,
        totalEarnings: 150.00,
        availableBalance: 75.00
      },
      rewardStructure: {
        referrerReward: 50.00,
        refereeReward: 25.00,
        currency: 'CAD',
        payoutMethod: 'account_credit'
      }
    }
  })),
  fetchReferralList: jest.fn((req, res) => res.status(200).json({ 
    message: 'Referral list retrieved successfully',
    data: {
      referrals: [
        {
          id: 'ref_1',
          refereeEmail: '<EMAIL>',
          refereeName: 'John Friend',
          referralDate: '2024-01-01T00:00:00Z',
          status: 'completed',
          rewardAmount: 50.00,
          paidDate: '2024-01-15T00:00:00Z',
          serviceActivated: 'internet'
        },
        {
          id: 'ref_2',
          refereeEmail: '<EMAIL>',
          refereeName: 'Jane Friend',
          referralDate: '2024-01-10T00:00:00Z',
          status: 'pending',
          rewardAmount: 50.00,
          paidDate: null,
          serviceActivated: null
        },
        {
          id: 'ref_3',
          refereeEmail: '<EMAIL>',
          refereeName: 'Bob Friend',
          referralDate: '2024-01-05T00:00:00Z',
          status: 'completed',
          rewardAmount: 50.00,
          paidDate: '2024-01-20T00:00:00Z',
          serviceActivated: 'bundle'
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 3,
        itemsPerPage: 10
      }
    }
  }))
}));

describe('Referral Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    referralRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/referrals/details', () => {
    it('should fetch referral details successfully', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/details')
        .expect(200);

      expect(response.body.message).toBe('Referral details retrieved successfully');
      expect(response.body.data).toHaveProperty('referralProgram');
      expect(response.body.data).toHaveProperty('userReferralInfo');
      expect(response.body.data).toHaveProperty('rewardStructure');
      
      // Verify referralProgram structure
      expect(response.body.data.referralProgram).toHaveProperty('id');
      expect(response.body.data.referralProgram).toHaveProperty('name');
      expect(response.body.data.referralProgram).toHaveProperty('description');
      expect(response.body.data.referralProgram).toHaveProperty('isActive');
      expect(response.body.data.referralProgram).toHaveProperty('terms');
      
      // Verify userReferralInfo structure
      expect(response.body.data.userReferralInfo).toHaveProperty('customerId');
      expect(response.body.data.userReferralInfo).toHaveProperty('referralCode');
      expect(response.body.data.userReferralInfo).toHaveProperty('referralLink');
      expect(response.body.data.userReferralInfo).toHaveProperty('totalReferrals');
      expect(response.body.data.userReferralInfo).toHaveProperty('successfulReferrals');
      expect(response.body.data.userReferralInfo).toHaveProperty('pendingReferrals');
      expect(response.body.data.userReferralInfo).toHaveProperty('totalEarnings');
      expect(response.body.data.userReferralInfo).toHaveProperty('availableBalance');
      
      // Verify rewardStructure structure
      expect(response.body.data.rewardStructure).toHaveProperty('referrerReward');
      expect(response.body.data.rewardStructure).toHaveProperty('refereeReward');
      expect(response.body.data.rewardStructure).toHaveProperty('currency');
      expect(response.body.data.rewardStructure).toHaveProperty('payoutMethod');
      
      // Verify data types
      expect(typeof response.body.data.referralProgram.isActive).toBe('boolean');
      expect(typeof response.body.data.userReferralInfo.totalReferrals).toBe('number');
      expect(typeof response.body.data.userReferralInfo.successfulReferrals).toBe('number');
      expect(typeof response.body.data.userReferralInfo.pendingReferrals).toBe('number');
      expect(typeof response.body.data.userReferralInfo.totalEarnings).toBe('number');
      expect(typeof response.body.data.userReferralInfo.availableBalance).toBe('number');
      expect(typeof response.body.data.rewardStructure.referrerReward).toBe('number');
      expect(typeof response.body.data.rewardStructure.refereeReward).toBe('number');
    });

    it('should handle referral details request with query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/details')
        .query({ customerId: '12345', includeHistory: 'true' })
        .expect(200);

      expect(response.body.message).toBe('Referral details retrieved successfully');
    });

    it('should handle referral details request with authentication headers', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/details')
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Customer-ID', '12345')
        .expect(200);

      expect(response.body.message).toBe('Referral details retrieved successfully');
    });
  });

  describe('GET /api/v1/referrals/list', () => {
    it('should fetch referral list successfully', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/list')
        .expect(200);

      expect(response.body.message).toBe('Referral list retrieved successfully');
      expect(response.body.data).toHaveProperty('referrals');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.referrals)).toBe(true);
      expect(response.body.data.referrals.length).toBeGreaterThan(0);
      
      // Verify referral object structure
      const firstReferral = response.body.data.referrals[0];
      expect(firstReferral).toHaveProperty('id');
      expect(firstReferral).toHaveProperty('refereeEmail');
      expect(firstReferral).toHaveProperty('refereeName');
      expect(firstReferral).toHaveProperty('referralDate');
      expect(firstReferral).toHaveProperty('status');
      expect(firstReferral).toHaveProperty('rewardAmount');
      expect(firstReferral).toHaveProperty('paidDate');
      expect(firstReferral).toHaveProperty('serviceActivated');
      
      // Verify pagination structure
      expect(response.body.data.pagination).toHaveProperty('currentPage');
      expect(response.body.data.pagination).toHaveProperty('totalPages');
      expect(response.body.data.pagination).toHaveProperty('totalItems');
      expect(response.body.data.pagination).toHaveProperty('itemsPerPage');
      
      // Verify data types
      expect(typeof firstReferral.id).toBe('string');
      expect(typeof firstReferral.refereeEmail).toBe('string');
      expect(typeof firstReferral.refereeName).toBe('string');
      expect(typeof firstReferral.referralDate).toBe('string');
      expect(typeof firstReferral.status).toBe('string');
      expect(typeof firstReferral.rewardAmount).toBe('number');
      expect(typeof response.body.data.pagination.currentPage).toBe('number');
      expect(typeof response.body.data.pagination.totalPages).toBe('number');
      expect(typeof response.body.data.pagination.totalItems).toBe('number');
      expect(typeof response.body.data.pagination.itemsPerPage).toBe('number');
    });

    it('should handle referral list request with pagination parameters', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/list')
        .query({ page: '1', limit: '5', status: 'completed' })
        .expect(200);

      expect(response.body.message).toBe('Referral list retrieved successfully');
    });

    it('should handle referral list request with filter parameters', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/list')
        .query({ 
          status: 'pending', 
          dateFrom: '2024-01-01', 
          dateTo: '2024-01-31',
          sortBy: 'referralDate',
          sortOrder: 'desc'
        })
        .expect(200);

      expect(response.body.message).toBe('Referral list retrieved successfully');
    });

    it('should handle referral list request with authentication', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/list')
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Customer-ID', '12345')
        .expect(200);

      expect(response.body.message).toBe('Referral list retrieved successfully');
    });

    it('should handle referral list request with invalid query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/referrals/list')
        .query({ page: 'invalid', limit: 'invalid' })
        .expect(200);

      expect(response.body.message).toBe('Referral list retrieved successfully');
    });
  });
});
