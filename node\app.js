const express = require("express");
const path = require("path");
const CONFIG = require("./config");
const app = express();
const helmet = require("helmet");

// Middleware imports
const { exceptionHandling } = require("./middleware/exceptionHandling");
const cors = require("cors");
const {
  authenticateToken,
  authenticateRegistrationToken,
  authenticateWebhook,
  authenticateRegistrationTokenV1,
  decodeBodyPayload,
} = require("./middleware/userAuth");
const { RESPONSE_CODES, RESPONSES } = require("./utils/ResponseCodes");
const routesConfig = require("./helpers/routesConfig");
const rateLimiter = require("./helpers/rateLimiter");
const ElasticSearch = require("./clients/elastic-search/elastic-search");
const SalesforceClient = require("./clients/salesforce/salesforce-client");

// Flag for ElasticSearch logging
const isElasticSearch = false;

// Allow Cross-Origin requests
app.use(cors());
app.set("trust proxy", 1);

// Set security HTTP headers
app.use(helmet());

// Set Content Security Policy (CSP) headers with API Gateway URL
app.use(
  helmet.contentSecurityPolicy({
    directives: {
      defaultSrc: ["'self'"], // Default sources allowed
      connectSrc: ["*"], // Allow API Gateway domain
      imgSrc: ["'self'", "data:", "blob:", "*"], // Explicitly allow data URLs for images
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Allow inline scripts
        "https://cdn.mouseflow.com", // Allow Mouseflow external script
        "https://maps.googleapis.com", // Allow Google Maps API script
        "https://maps.gstatic.com", // Allow related Google Maps resources
      ],
    },
  })
);

// Middleware for parsing requests
app.use(express.urlencoded({ extended: true }));
app.use(express.json({ limit: "50mb" }));

// Serve static files
app.use(express.static(path.join(__dirname, "assets")));
app.use(express.static(path.join(__dirname, "bundle")));

// Route for root URL
// app.get('/', (req, res) => {
//   res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS });
// });

// Initialize ElasticSearch if enabled
if (isElasticSearch) {
  const elasticSearch = new ElasticSearch();
  elasticSearch.createLogIndices();
}

// Salesforce client initialization
const salesforceConnection = new SalesforceClient(
  CONFIG.salesforce.baseurl,
  CONFIG.salesforce.username,
  CONFIG.salesforce.password,
  CONFIG.salesforce.token
);

// Test Salesforce connection
app.get("/test", async (req, res, next) => {
  try {
    const response = await salesforceConnection.getContact();
    res
      .status(RESPONSE_CODES.SUCCESS)
      .json({ ...RESPONSES.SUCCESS, data: response });
  } catch (error) {
    next(error);
  }
});

// Base path for routes with version
const basePath = routesConfig.basePath;

// Authentication middleware
app.use("/auth/register", authenticateRegistrationTokenV1);
app.use("/auth/map-subscription", authenticateRegistrationTokenV1);
app.use("/auth/delete-contact", authenticateRegistrationTokenV1);
app.use("/auth/cognito/*splat", authenticateRegistrationTokenV1);
app.use("/sync/*splat", authenticateRegistrationToken);
app.use("/webhook/*splat", authenticateWebhook);
app.use(basePath + "/*splat", authenticateToken);
app.use("/retry/*splat", authenticateRegistrationToken);

// Apply rate limiter
app.use(basePath + "/*splat", rateLimiter);

// Decode payload middleware
app.use("/auth/login", decodeBodyPayload);
app.use("/auth/forgot-password", decodeBodyPayload);
app.use("/auth/reset-password", decodeBodyPayload);

// Routes imports
require("./routes/card-route")(app, basePath);
require("./routes/customer-route")(app, basePath);
require("./routes/dashboard-route")(app, basePath);
require("./routes/plan-route")(app, basePath);
require("./routes/billing-route")(app, basePath);
require("./routes/referral-route")(app, basePath);
require("./routes/payment-route")(app, basePath);
require("./routes/subscription-route")(app, basePath);
require("./routes/promotion-route")(app, basePath); //this is not used in the app

require("./routes/auth-route")(app);
require("./routes/worker-route")(app);
require("./routes/retry-route")(app); //this is not used in the app

// Handle unknown routes
app.get("/*splat", (req, res) => {
  res.sendFile(path.join(__dirname, "bundle", "index.html"));
});

// Exception handling middleware
app.use(exceptionHandling);

// Error route
// app.get('/error', (req, res) => {
//   const nonce = 'Fn+2vgnkkOoBS+strwgiCw==';
//   res.set({
//     'Content-Security-Policy': `script-src 'self' 'nonce-${nonce}'`
//   });
//   res.sendFile(path.join(__dirname, 'assets/views/error.html'));
// });

// Catch-all route for non-existing routes
app.use((req, res) => {
  res.redirect("/error?code=404");
});

// Export the app module
module.exports = app;
