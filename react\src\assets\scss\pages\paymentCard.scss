@use "../mixin.scss";
@use "../variables.scss" as *;

// Payment Card
.payment-card {
  position: relative;
  z-index: 2;

  &:before {
    content: "";
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='280' height='180' viewBox='0 0 284 172' fill='none'%3E%3Cg clip-path='url(%23clip0_119_7537)'%3E%3Crect x='0.5' y='0.5' width='283' height='171' rx='12' fill='url(%23paint0_linear_119_7537)'/%3E%3Cpath d='M64.8582 68.6639C72.1526 51.8174 50.4721 54.1279 48.3607 45.5038C45.6094 34.2679 40.7753 25.7194 26.9997 37.446C19.67 43.6855 15.514 42.2705 9.19888 41.3162C-0.300277 39.8814 -5.21733 52.5087 7.52755 58.2185C22.1186 64.7548 13.3358 70.0451 14.1983 76.9215C15.6066 88.1545 33.5881 87.1781 44.2378 84.4569C52.0472 82.4616 61.4935 76.4337 64.8582 68.6639Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M247.863 60.9073C256.484 40.8888 230.859 43.6343 228.364 33.3863C225.112 20.0347 219.398 9.87662 203.116 23.8113C194.453 31.2256 189.541 29.5443 182.077 28.4102C170.849 26.7053 165.038 41.7103 180.101 48.4952C197.347 56.2622 186.966 62.5486 187.986 70.7198C189.65 84.0678 210.903 82.9076 223.491 79.674C232.721 77.303 243.886 70.1401 247.863 60.9073Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M2.08448 134.853C-6.15621 150.338 9.66118 159.799 27.8883 149.504C41.3607 141.895 25.4274 159.713 37.7193 169.2C49.8548 178.566 83.2659 156.677 77.8671 131.624C72.5904 107.139 43.0489 100.945 24.6107 112.127C10.6549 120.591 4.69451 129.947 2.08448 134.853Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M79.3114 33.9494C82.4975 17.3084 98.1542 -1.11419 110.293 -0.484286C122.433 0.145614 114.637 19.0676 129.373 13.9659C138.085 10.9504 152.272 7.02894 155.617 16.7106C158.276 24.4061 153.11 28.0712 145.916 33.2355C141.428 36.4582 140.266 43.0047 141.224 46.9449C142.909 53.8712 143.617 60.9198 135.882 63.6145C126.309 66.949 115.625 53.7354 107.469 50.9316C99.3126 48.1279 96.7639 62.2698 86.3775 59.7752C79.6874 58.1688 76.8916 46.5885 79.3114 33.9494Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M106.705 127.531C127.457 119.778 130.807 120.36 142.707 127.25C157.528 135.832 175.342 101.543 159.879 103.811C132.081 107.888 133.56 80.0147 116.104 89.7276C97.7794 99.9225 93.4853 132.469 106.705 127.531Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M227.182 167.713C218.384 161.64 212.18 158.231 203.873 162.352C187.905 170.274 176.092 165.684 177.794 149.038C179.463 132.701 197.262 115.208 218.105 113.56C240.13 111.82 261.609 122.448 262.794 133.408C264.007 144.619 245.463 141.545 248.891 157.1C251.087 167.072 240.615 176.988 227.182 167.713Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M340.795 101.576C325.211 112.382 321.119 99.5352 313.136 85.4981C303.755 69.004 274.545 72.1732 270.546 95.6715C268.415 108.193 276.799 112.513 276.257 123.858C275.714 135.204 267.939 138.446 271.647 144.849C275.793 152.008 290.9 142.465 304.604 153.304C323.796 168.483 338.298 161.679 350.556 146.82C362.023 132.92 366.151 116.693 361.814 105.887C357.475 95.0799 346.323 97.7427 340.795 101.576Z' fill='white' fill-opacity='0.13'/%3E%3Cpath d='M293.516 22.5536C291.33 36.6584 274.817 19.5298 274.607 32.9667C274.439 43.6994 297.054 67.1218 307.752 56.7609C314.328 50.3907 310.545 40.3883 322.836 40.4434C338.516 40.5139 348.774 23.3074 332.362 22.4993C319.253 21.8535 320.064 6.40215 310.027 4.79251C298.997 3.02345 294.316 17.3905 293.516 22.5536Z' fill='white' fill-opacity='0.13'/%3E%3C/g%3E%3Cdefs%3E%3Cfilter id='filter0_d_119_7537' x='216.7' y='14.7' width='70.6' height='37.6' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3E%3CfeOffset dx='-4' dy='4'/%3E%3CfeGaussianBlur stdDeviation='3.9'/%3E%3CfeComposite in2='hardAlpha' operator='out'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0'/%3E%3CfeBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_119_7537'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow_119_7537' result='shape'/%3E%3C/filter%3E%3ClinearGradient id='paint0_linear_119_7537' x1='0.5' y1='86' x2='283.5' y2='86' gradientUnits='userSpaceOnUse'%3E%3Cstop stop-color='%23A86CF0'/%3E%3Cstop offset='1' stop-color='%238B5CF6'/%3E%3C/linearGradient%3E%3CclipPath id='clip0_119_7537'%3E%3Crect width='283' height='171' fill='white' transform='translate(0.5 0.5)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }

  .close-icon {
    box-shadow: 0px 0px 8.000000953674316px 0px #00000026;
  }
}
