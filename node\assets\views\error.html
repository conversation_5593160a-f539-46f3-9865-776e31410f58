<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <base href="/" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      body {
        margin: 0px;
        padding: 0px;
        background: #eee;
      }

      UL {
        position: absolute;
        top: 45%;
        left: 50%;
        display: flex;
        transform: translate(-50%, -50%);
        align-items: center;
      }

      ul li {
        list-style: none;
        letter-spacing: 15px;
        font-size: 3.4em;
        font-family: fantasy;
        color: #eee;
        animation: ani 10s cubic-bezier(0, 0.21, 1, 0.88) infinite;
      }

      ul li {
        animation-delay: 0.1s;
      }

      @keyframes ani {
        0% {
          color: #000000;
          text-shadow: none;
        }

        50% {
          color: #ffffff00;
          text-shadow: 0px 1px 6px #0000000f, 1px 1px 4px #00000052;
        }

        100% {
          color: #363636;
          text-shadow: none;
        }
      }
    </style>
    <script nonce="Fn+2vgnkkOoBS+strwgiCw==">
      function setError(){
        let parse_url = new URL(window.location).searchParams
        let error_code_div = document.getElementById("error_code")
        if(parse_url.has("code")){
          error_code_div.innerHTML = `${parse_url.get("code")} Sever Error`
        } else {
          error_code_div.innerHTML = `404 Page Not Found`
        }
      }
      window.onload = setError
    </script>
  </head>

  <body>
    <ul>
      <li id="error_code"></li>
      <hr />
      <li>Purple Cow</li>
    </ul>
  </body>
</html>
