const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("internets_eastlink_disconnect_order", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      allowNull: false,
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the LastModifiedDate in Salesforce."
    },
    requested_disconnect_date: {
      type: Sequelize.DATEONLY,
      comment: 'This refers to the Requested_Disconnect_Date__c field in the Order__c object of type "Disconnect Order".'
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'internets_eastlink_disconnect_order', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}