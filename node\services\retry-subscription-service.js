const { Op } = require('sequelize');
const SalesforceClient = require('../clients/salesforce/salesforce-client');
const CONFIG = require('../config');
const db = require('../models/index.js');
const { RESPONSE_CODES } = require('../utils/ResponseCodes.js');
const CustomError = require('../utils/errors/CustomError.js');
const moment = require('moment');
const SalesforceService = require("./salesforce-services");
const salesforceServiceClient = new SalesforceService();

class RetrySubscriptionService {
    async getFailureSubscriptions(req, res, next) {
        try {
            const {type} = req.query;
            if (!type) return;
            if(type === "Internet") this.getFailureInternet();
            if(type === "TV") this.getFailureTv();
            if(type === "Phone") this.getFailurePhone();
        } catch (error) {
        }
    }

    async getFailureInternet() {
        try {
            const attributes = ["id", "sf_record_id", "plan_speed", "retries"];
            const internetDetails = await this.getFailureDetails("CustomerInternet", attributes);
            if (!internetDetails?.length) return;
            for (const internetDetail of internetDetails) {
                const { id, sf_record_id, plan_speed, retries, customerDetails } = internetDetail;
                const updateObj = {};
                try {
                    const { returnStatus: updateStatus } = await salesforceServiceClient.createNewSpeedOrderinSf(sf_record_id, plan_speed);
                    if (updateStatus) {
                        updateObj.sf_response_status = "success";
                        updateObj.retries = 0;
                    }
                } catch (error) {
                    updateObj.sf_error_log = error?.message;
                    updateObj.retries = retries + 1;
                } finally {
                    await db.CustomerInternet.update(updateObj, { where: { id } });
                }
            }
        } catch (error) {
            console.error(error);
        }
    }

    async getFailureTv() {
        try {
            const attributes = ["id", "sf_record_id", "plan_name", "extra_packages", "single_channels", "iptv_products", "retries", "sf_response_type", "requested_cancellation_date"];
            const tvDetails = await this.getFailureDetails("CustomerTv", attributes);
            if (!tvDetails?.length) return;
            for (const tvDetail of tvDetails) {
                const { id, sf_record_id, plan_name, retries, customerDetails, single_channels, iptv_products, sf_response_type, extra_packages, requested_cancellation_date } = tvDetail;
                const updateObj = {};
                try {
                    const payload = {
                        plan_name: plan_name,
                        extra_packages: JSON.parse(extra_packages),
                        single_channels: JSON.parse(single_channels),
                        iptv_products: JSON.parse(iptv_products)
                    };
                    if (sf_response_type === "create") {
                        if (!customerDetails.sf_record_id) return;
                        const { returnStatus: updateStatus, orderId } = await salesforceServiceClient.createOrUpdateTVObject(customerDetails.sf_record_id, payload, "create");
                        if (updateStatus && orderId) {
                            updateObj.sf_record_id = orderId;
                            updateObj.account_status = 'ACTIVE';
                            updateObj.sf_response_status = "success";
                            updateObj.retries = 0;
                        }
                    }

                    if (sf_response_type === "update" || sf_response_type == "cancel") {
                        if (!customerDetails.sf_record_id || !sf_record_id) return;
                        if (sf_response_type == "cancel") {
                            let date = new Date(requested_cancellation_date);
                            payload.submitTime = Math.floor(date.getTime() / 1000);
                        }
                        const { returnStatus: updateStatus } = await salesforceServiceClient.createOrUpdateTVObject(customerDetails.sf_record_id, payload, "update", sf_record_id);
                        if (updateStatus) {
                            updateObj.sf_response_status = "success";
                            updateObj.retries = 0;
                        }
                    }
                } catch (error) {
                    updateObj.sf_error_log = error?.message;
                    updateObj.retries = retries + 1;
                } finally {
                    await db.CustomerTv.update(updateObj, { where: { id } });
                }
            }
        } catch (error) {
            console.error(error);
        }
    }

    async getFailurePhone() {
        try {
            const attributes = ["id", "sf_record_id", "plan_name", "retries", "sf_response_type", "sf_phone_type", "requested_cancellation_date"];
            const phoneDetails = await this.getFailureDetails("CustomerPhone", attributes);
            if (!phoneDetails?.length) return;
            for (const phoneDetail of phoneDetails) {
                const { id, sf_phone_type, retries, customerDetails, sf_response_type, sf_record_id } = phoneDetail;
                const phoneNumberType = JSON.parse(sf_phone_type);
                if (sf_response_type === "create") {
                    const { sf_phone_type: phone_number_type, phone_number } = phoneNumberType;
                    if (phone_number_type == "existing" && !phone_number) continue;
                    const updateObject = {};
                    try {
                        const { returnStatus: updateStatus, phoneId } = await salesforceServiceClient.createNewPhoneOrderinSf(phone_number_type, phone_number, customerDetails.sf_record_id);
                        if (updateStatus && phoneId) {
                            const phoneRes = await salesforceServiceClient.getPhoneApiValue(phoneId);
                            const { status: phStatus, data: pfData } = phoneRes;
                            if (phStatus) {
                                updateObject.plan_name = pfData?.Calling_Plan__c;
                            }
                            updateObject.sf_record_id = phoneId;
                            updateObject.sf_response_status = "success";
                            updateObject.retries = 0;
                        }
                    } catch (error) {
                        updateObject.retries = retries + 1;
                        updateObject.sf_error_log = error?.message;
                    } finally {
                        await db.CustomerPhone.update(updateObject, { where: { id } });
                    }
                }

                if (sf_response_type === "cancel") {
                    const updateObject = {};
                    try {
                        const { requested_cancellation_date } = phoneNumberType;
                        if (!requested_cancellation_date || !sf_record_id) continue;
                        const { returnStatus: retrnStatus, orderId } = await salesforceServiceClient.createPhoneCancelObject(sf_record_id, requested_cancellation_date);
                        if (orderId) {
                            updateObject.sf_response_status = "success";
                            updateObject.account_status = "DELETED";
                            updateObject.retries = 0;
                        }
                    } catch (error) {
                        updateObject.retries = retries + 1;
                        updateObject.sf_error_log = error?.message;
                    } finally {
                        await db.CustomerPhone.update(updateObject, { where: { id } });
                    }
                }
            }
        } catch (error) {
            console.error(error);
        }
    }

    async getFailureDetails(tableName, attributes) {
        try {
            let failureDetails = await db[tableName].findAll({
                include: {
                    model: db.CustomerDetails,
                    as: 'customerDetails',
                    attributes: ["id", "sf_record_id"],
                    required: true
                },
                where: {
                    sf_response_status: "failure",
                    retries: { [Op.lt]: CONFIG.salesforce.retryCount }
                },
                attributes
            });
            failureDetails = JSON.parse(JSON.stringify(failureDetails));
            return failureDetails;
        } catch (error) {
            console.error(error);
        }
    }
}

module.exports = RetrySubscriptionService;
