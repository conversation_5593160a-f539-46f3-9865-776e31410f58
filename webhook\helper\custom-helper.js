const momentTimezone = require('moment-timezone');
const _TIMEZONE = "America/Halifax";
const fs = require('fs');
const xml2js = require('xml2js');
const CONFIG = require('../config');

exports.generateSfAdrsObj = (addressId, apiName) => {
    const prefix = apiName.split('_')[0];
    const selectedObj = {
        Id: 1,
        Name: 1,
        [`${prefix}_Suite_Unit__c`]: 1,
        [`${prefix}_Street_Number__c`]: 1,
        [`${prefix}_Street_Name__c`]: 1,
        [`${prefix}_City_Town__c`]: 1,
        [`${prefix}_Province__c`]: 1,
        [`${prefix}_Postal_Code__c`]: 1,
        [`Full_${prefix}_Address__c`]: 1,
        [`${prefix}_Country__c`]: 1,
        Status__c: 1,
        LastModifiedDate: 1,
        CreatedDate: 1
    };
    return selectedObj;
};

exports.getInternetTvPhoneDetailsObj = (apiName) => {
    const selectedObj = {
        Id: 1,
        Name: 1,
        LastModifiedDate: 1,
        CreatedDate: 1,
        ...(apiName === "Internet__c" && {
            Status__c: 1, Live_Date__c: 1, Disconnected_Date__c: 1, Creation_Order__c: 1, Latest_Tech_Appointment__c: 1, Latest_Disconnect_Order__c: 1, Latest_Speed_Change_Order__c: 1, CP_Speed__c: 1, CP_Status_Internal_Processing__c: 1, CP_Status_Ship_Package__c: 1, CP_Status_Modem_Activation__c: 1, CP_Status_Modem_Installation__c: 1, Latest_Move_Order__c: 1, Latest_Modem_Swap_Order__c: 1
        }),
        ...(apiName === "TV__c" && {
            Current_Base_Package__c: 1,
            Current_Extra_Packages__c: 1,
            Current_Single_Channels__c: 1,
            current_iptv_products__c: 1,
            current_account_status__c: 1,
            Login_Details_Last_Sent__c: 1,
            Requested_Cancellation_Date__c: 1,
            State_Text__c: 1
        }),
        ...(apiName === "Phone__c" && {
            Account_Status__c: 1,
            Calling_Plan__c: 1,
            Service_Start_Date__c: 1,
            Latest_Cancel_Phone_Order__c: 1,
            Requested_Cancellation_Date__c: 1,
            Phone_Number_To_Port__c: 1
        })
    }
    return selectedObj;
}

exports.getOrderDetailsObj = (orderType) => {
    let selectedObj = {
        Id: 1,
        Name: 1,
        LastModifiedDate: 1,
        CreatedDate: 1,
        Record_Type_Name__c: 1,
        ...(orderType === "internets_eastlink_creation_order" && { Related_Shipping_Order__c: 1, Install_Date__c: 1 }),
        ...(orderType === "internets_eastlink_swap_order" && { Stage__c: 1 }),
        ...(orderType === "internets_eastlink_disconnect_order" && { Requested_Disconnect_Date__c: 1 }),
        ...(orderType === "internets_eastlink_speed_change_order" && { Expected_Completion_Date__c: 1, Stage__c: 1, Response_Date__c: 1, Speed__c: 1 }),
        ...(orderType === "internets_eastlink_move_order" && { Requested_Move_Date__c: 1, Requested_Install_Date__c: 1, Install_Date__c: 1, Install_Time__c: 1, Order_Reject_Reason__c: 1, Order_Reject_Reason_Solved__c: 1, Service_Suite_Unit__c: 1, Service_Street_Number__c: 1, Service_Street_Name__c: 1, Service_City_Town__c: 1, Service_Province__c: 1, Service_Postal_Code__c: 1, Stage__c: 1, Submit_Date__c: 1 }),
    };
    return selectedObj;
}

exports.hasValidChanges = (newValue, oldValue) => {
    switch (true) {
        case newValue === oldValue:
            return false;
        case newValue === "" && oldValue === null:
            return false;
        case newValue === "" && oldValue !== null:
            return true;
        case newValue !== "" && oldValue === null:
            return true;
        case newValue !== oldValue:
            return true;
        default:
            return false;
    }
};


exports.billindTypeCbSubs = (type) => {
    let subsType;
    switch (type) {
        case "month":
            subsType = "monthly";
            break;
        case "year":
            subsType = "yearly";
            break;
        default:
            break;
    }
    return subsType;
};

exports.getCurrentAtlanticTime = (date = null, type) => {
    if (date) {
        if (type == "elasticSearch") return momentTimezone(date).tz(_TIMEZONE).format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
        else return momentTimezone(date).tz(_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
    } else {
        const nowUtc = momentTimezone.utc();
        // Convert to Canada time zone
        if (type == "elasticSearch") return nowUtc.tz(_TIMEZONE).format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
        else return nowUtc.tz(_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
    }
};

exports.sanitizeValue = (value) => {
    if (value === null || value === undefined) return 'NULL';

    // Handle non-string values safely
    if (typeof value !== 'string') {
        // If it's a Date object, format it as 'YYYY-MM-DD HH:MM:SS'
        if (value instanceof Date) {
            return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
        }
        // Convert other data types (like numbers) to strings
        value = String(value);
    }

    // Replace single quotes with backticks
    return `'${value.replace(/'/g, '`')}'`;
};

exports.decryptXML = async (data, type) => {
    try {
        if (CONFIG.NODE_ENV === "local") {
            if (!type) return { status: false };
            const response = await readXML(type);
            if (response) data = response;
        }

        const soapEnvelope = data['soapenv:Envelope'];
        const notifications = soapEnvelope['soapenv:Body'].notifications;
        const organizationId = notifications.OrganizationId;
        const sessionId = notifications?.SessionId;
        let notification = notifications.Notification;

        if (!Array.isArray(notification)) {
            notification = [notification];
        }

        const webhookDataArray = [];
        let sfApiName;

        for (const item of notification) {
            const xmlData = item.sObject;
            sfApiName = xmlData.$['xsi:type'];
            sfApiName = sfApiName.replace(/^sf:/, ''); // Remove "sf:" prefix
            const webhookData = removeSfPrefix(xmlData);
            webhookDataArray.push(webhookData);
        }

        return {
            status: true,
            webhookData: webhookDataArray.length === 1 ? webhookDataArray[0] : webhookDataArray,
            sfApiName,
            organizationId,
            sessionId
        };

    } catch (error) {
        console.error("decryptXML error -> ", error);
        console.error("data 1 -> ", JSON.stringify(data));
        return { status: false };
    }
}

function removeSfPrefix(obj) {
    const newObj = {};
    Object.keys(obj).forEach(key => {
        const newKey = key.replace(/^sf:/, '');  // Remove 'sf:' prefix from the key
        const value = obj[key];

        newObj[newKey] = (typeof value === 'object' && value !== null)
            ? removeSfPrefix(value)  // Recursively handle nested objects
            : value;
    });
    return newObj;
}

async function readXML(type) {
    let xmlPath;
    if (type === "customer_details") xmlPath = '../webhook/xml-test-cases/customer-details.xml';
    if (type === "referral") xmlPath = '../webhook/xml-test-cases/referrals.xml';
    if (type === "contact") xmlPath = '../webhook/xml-test-cases/contacts.xml';
    if (type === "shipping") xmlPath = '../webhook/xml-test-cases/creation_order_shipping.xml';
    if (type === "order") xmlPath = '../webhook/xml-test-cases/internets_eastlink_order.xml';
    if (type === "service_address") xmlPath = '../webhook/xml-test-cases/customer_details_service_address.xml';
    if (type === "mailing_address") xmlPath = '../webhook/xml-test-cases/customer_details_mailing_address.xml';
    if (type === "tv") xmlPath = '../webhook/xml-test-cases/customer_details_tvs.xml';
    if (type === "phone") xmlPath = '../webhook/xml-test-cases/customer_details_phones.xml';
    if (type === "subscription") xmlPath = '../webhook/xml-test-cases/customer_details_subscriptions.xml';
    if (type === "subscription_invoice") xmlPath = '../webhook/xml-test-cases/subscriptions_invoices.xml';
    if (type === "internet") xmlPath = '../webhook/xml-test-cases/customer_details_internets_eastlink.xml';

    if (!xmlPath) return { status: false };

    const parser = new xml2js.Parser({
        explicitArray: false, // Prevents wrapping elements in arrays
    });

    const data = await new Promise((resolve, reject) => {
        fs.readFile(xmlPath, (err, detail) => {
            if (err) {
                console.error('Error reading XML file:', err);
                reject(err);
            } else {
                parser.parseString(detail, (err, result) => {
                    if (err) {
                        console.error('Error parsing XML data:', err);
                        reject(err);
                    } else {
                        resolve(result);
                    }
                });
            }
        });
    });
    return data;
}
