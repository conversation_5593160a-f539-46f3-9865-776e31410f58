import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import { LocationDataState } from "../../typings/typing";
import { serviceAddressReducerState } from "../../typings/typing";

const initialState = {
  currentAddress: {
    address: "",
    apartment: "",
    city: "",
    province: "",
    pinCode: "",
  },
  newAddress: {
    address: "",
    apartment: "",
    city: "",
    province: "",
    pinCode: "",
  },
  moveDate: null,
  serviceStatus: "",
  showServiceAddressPopup: false,
  showFibreAddressPopup: false,
  showServiceAddressDateSelectorPopup: false,
  showDeleteOrderPopup: false,
  showServiceAddressConfirmationPopup: false,
} satisfies serviceAddressReducerState as serviceAddressReducerState;

const serviceAddressReducer = createSlice({
  name: "SERVICE_ADDRESS_REDUCER",
  initialState,
  reducers: {
    toggleShowServiceAddressPopup: (state) => {
      state.showServiceAddressPopup = !state.showServiceAddressPopup;
    },
    toggleShowFibreAddressPopup: (state) => {
      state.showFibreAddressPopup = !state.showFibreAddressPopup;
    },
    toggleShowServiceAddressDateSelectorPopup: (state) => {
      state.showServiceAddressDateSelectorPopup =
        !state.showServiceAddressDateSelectorPopup;
    },
    toggleShowServiceAddressConfirmationPopup: (state) => {
      state.showServiceAddressConfirmationPopup =
        !state.showServiceAddressConfirmationPopup;
    },
    toggleShowDeleteServicePopup: (state) => {
      state.showDeleteOrderPopup = !state.showDeleteOrderPopup;
    },
    setMoveDate: (state, action: PayloadAction<Date | null>) => {
      state.moveDate = action.payload;
    },
    setCurrentServiceAddress: (
      state,
      action: PayloadAction<LocationDataState>
    ) => {
      state.currentAddress = action.payload;
    },
    setNewServiceAddress: (state, action: PayloadAction<LocationDataState>) => {
      state.newAddress = action.payload;
    },
    setServiceStatus: (state, action: PayloadAction<string>) => {
      state.serviceStatus = action.payload;
    },
    resetServicePopups: (state) => {
      state.showServiceAddressPopup = false;
      state.showFibreAddressPopup = false;
      state.showServiceAddressDateSelectorPopup = false;
      state.showDeleteOrderPopup = false;
      state.showServiceAddressConfirmationPopup = false;
    },
  },
});

export const {
  toggleShowServiceAddressPopup,
  toggleShowFibreAddressPopup,
  toggleShowServiceAddressDateSelectorPopup,
  toggleShowServiceAddressConfirmationPopup,
  toggleShowDeleteServicePopup,
  setMoveDate,
  setCurrentServiceAddress,
  setNewServiceAddress,
  setServiceStatus,
  resetServicePopups,
} = serviceAddressReducer.actions;

export default serviceAddressReducer.reducer;
