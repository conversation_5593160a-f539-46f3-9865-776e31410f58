import React from "react";
import { useDispatch } from "react-redux";
import { CopyIcon, ShareIcon } from "../../assets/Icons";
import { addNotification } from "../../store/reducers/toasterReducer";
import { ReferralLinkProps } from "../../typings/typing";
import Circle from "../../assets/images/CircleSteps.png";
const ReferralLink: React.FC<ReferralLinkProps> = ({ link }) => {
  const dispatch = useDispatch();

  const referralSteps = [
    {
      title: "Share Your Link",
      description: (
        <>
          Send your personal referral link to friends, family, or anyone you
          know!
        </>
      ),
    },
    {
      title: "They Sign Up & Get $15",
      description: (
        <>
          When someone uses your link to sign up for internet service, they’ll
          instantly get <b>$15 off at signup.</b>
        </>
      ),
    },
    {
      title: "You Earn $25",
      description: (
        <>
          Once their internet goes live,
          <b>you get $25 credited to your account.</b>
        </>
      ),
    },
  ];
  // Function to copy URL to clipboard
  const handleCopyURL = (link: string) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(link)
        .then(() => {
          dispatch(
            addNotification({ type: "success", message: "Link copied" })
          );
        })
        .catch((error) => {
          console.error("Failed to copy URL:", error);
          dispatch(
            addNotification({ type: "error", message: "Unable to copy!" })
          );
        });
    } else {
      console.warn(
        "Clipboard API not supported or navigator.clipboard is undefined"
      );
      // Fallback method if Clipboard API is not available
      fallbackCopyTextToClipboard(link);
    }
  };

  const handleShare = (link: string) => {
    if (navigator?.share) {
      navigator
        ?.share({
          title: "Check this out!",
          text: link,
        })
        .catch((error) => console.error("Error sharing", error));
    } else {
      alert("Sharing is not supported on this browser.");
    }
  };

  const fallbackCopyTextToClipboard = (text: string) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      const successful = document.execCommand("copy");
      if (successful) {
        dispatch(addNotification({ type: "success", message: "Link copied" }));
      } else {
        throw new Error("Fallback copy command was unsuccessful");
      }
    } catch (error) {
      console.error("Failed to copy URL:", error);
      dispatch(addNotification({ type: "error", message: "Unable to copy!" }));
    }
    document.body.removeChild(textArea);
  };

  return (
    <div>
      <div className="bg-medium_purple rounded-30 p-5 md:p-8 gap-5 flex flex-col items-center text-white shadow-purpleGlow">
        <h1 className="font-anton uppercase text-5xl"> Your Referral link</h1>
        <div className="bg-[#bb97eb] p-4 rounded-xl">
          <p className="break-all">{link}</p>
        </div>

        <div className="flex gap-4 ">
          <div
            onClick={() => handleCopyURL(link)}
            className="bg-white text-black p-3 rounded-20 flex gap-2 items-center cursor-pointer shadow-buttonShadow"
          >
            Copy Link <CopyIcon />
          </div>
          <div
            onClick={() => handleShare(link)}
            className="bg-[#FBC400] text-black p-3 rounded-20 flex gap-2 items-center cursor-pointer shadow-buttonShadow"
          >
            Share Link <ShareIcon />
          </div>
        </div>
      </div>

      {/* How it works */}

      <div className="bg-white border p-5 py-10 md:p-8 border-[#AA7DE6] rounded-30 mt-[1.25rem]">
        <h1 className="uppercase font-anton text-5xl text-center">
          How it works
        </h1>

        <div className="flex flex-wrap justify-center gap-8 mt-10">
          {referralSteps?.map((step, index) => (
            <div
              key={index}
              className="w-full sm:w-[45%] md:w-[30%] flex flex-col items-center text-center gap-3"
            >
              <div
                className="w-36 h-36 bg-no-repeat bg-center bg-contain flex items-center justify-center shrink-0"
                style={{ backgroundImage: `url(${Circle})` }}
              >
                <h1 className="font-tt font-bold text-5xl">{index + 1}</h1>
              </div>
              <h1 className="text-xl text-[#111111] font-semibold">
                {step?.title}
              </h1>
              <p className="text-base text-[#111111]">{step?.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReferralLink;
