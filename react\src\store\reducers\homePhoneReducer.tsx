import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import {
  HomePhonePlanState,
  homePhoneReducerState,
} from "../../typings/typing";

const initialState = {
  haveHomePhonePlan: {},
  isNewPhoneNumber: true,
  phoneNumber: "",
  isPlanRemoved: false,
  showHomePhonePopup: false,
  showDeleteOrderPopup: false,
  showSelectNumberPopup: false,
  showHomePhoneChangeConfirmationPopup: false,
} satisfies homePhoneReducerState as homePhoneReducerState;

const homePhoneReducer = createSlice({
  name: "HOME_PHONE_REDUCER",
  initialState,
  reducers: {
    setHaveHomePhonePlan: (state, action: PayloadAction<object>) => {
      state.haveHomePhonePlan = action.payload;
    },
    setIsNewPhoneNumber: (state, action: PayloadAction<boolean>) => {
      state.isNewPhoneNumber = action.payload;
    },
    setPhoneNumber: (state, action: PayloadAction<string>) => {
      state.phoneNumber = action.payload;
    },
    setIsPlanRemoved: (state, action: PayloadAction<boolean>) => {
      state.isPlanRemoved = action.payload;
    },
    setPhonePlan: (state, action: PayloadAction<HomePhonePlanState>) => {
      state.haveHomePhonePlan = action.payload.haveHomePhonePlan;
      state.isNewPhoneNumber = action.payload.isNewPhoneNumber;
      state.phoneNumber = action.payload.phoneNumber;
      state.isPlanRemoved = action.payload.isPlanRemoved;
    },
    toggleShowHomePhonePopup: (state) => {
      state.showHomePhonePopup = !state.showHomePhonePopup;
    },
    toggleShowSelectNumberPopup: (state) => {
      state.showSelectNumberPopup = !state.showSelectNumberPopup;
    },
    toggleShowDeleteOrderPopup: (state) => {
      state.showDeleteOrderPopup = !state.showDeleteOrderPopup;
    },
    toggleShowHomePhoneChangeConfirmationPopup: (state) => {
      state.showHomePhoneChangeConfirmationPopup =
        !state.showHomePhoneChangeConfirmationPopup;
    },
    resetPhonePopups: (state) => {
      state.showHomePhonePopup = false;
      state.showDeleteOrderPopup = false;
      state.showSelectNumberPopup = false;
      state.showHomePhoneChangeConfirmationPopup = false;
    },
  },
});

export const {
  setHaveHomePhonePlan,
  setIsNewPhoneNumber,
  setPhoneNumber,
  setIsPlanRemoved,
  toggleShowHomePhonePopup,
  toggleShowSelectNumberPopup,
  toggleShowDeleteOrderPopup,
  toggleShowHomePhoneChangeConfirmationPopup,
  setPhonePlan,
  resetPhonePopups,
} = homePhoneReducer.actions;

export default homePhoneReducer.reducer;
