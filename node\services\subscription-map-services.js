const db = require('../models');
const ChargebeeService = require("./chargebee-services.js");
const CardService = require("./card-services.js");
const { Op } = require('sequelize');

// Service class for handling user authentication and other user related operations
class SubscriptionMapServices {
    async mapSubscription(contactId) {
        try {
            console.log("<<<<<<<<-------- Card subscription mapping started ------>>>>>>")
            let userDetails = await db.Contacts.findAll({
                where: contactId ? { id: contactId } : undefined,
                include: {
                    model: db.CustomerDetails,
                    attributes: [['id', 'customerDetailsId']],
                    required: true,
                    include: {
                        model: db.CustomerSubscriptions,
                        as: "customerSubscription",
                        attributes: ['id', 'cb_subscription_id'],
                        required: true
                    }
                },
                attributes: [['id', 'contactId'], 'cb_customer_id']
            });

            if (!userDetails?.length) return;
            userDetails = JSON.parse(JSON.stringify(userDetails));

            for (const userDetail of userDetails) await this.getCardDetailsAndUpdate(userDetail);
            console.log("<<<<<<<<-------- Card subscription mapping ended ------>>>>>>")
        } catch (error) {
            console.error(`SubscriptionMapServices mapSubscription -> `, error);
        }
    }

    async getCardDetailsAndUpdate(userDetail) {
        try {
            if (!userDetail?.cb_customer_id) return;
            const contact_id = userDetail.contactId;
            const cb_customer_id = userDetail.cb_customer_id;
            const chargebeeServiceClient = new ChargebeeService();
            const res = await chargebeeServiceClient.getAllCards(contact_id, cb_customer_id);
            if (res?.status && res?.cardIds) {
                const currentCards = res?.cardIds;
                let existingCards = await db.ContactsCardDetails.findAll({ where: { contact_id }, attributes: [['id', 'contactCardId'], 'cb_card_id'] });
                existingCards = existingCards.map(item => item?.cb_card_id);
                const cardNotInChargebee = existingCards.filter(item => !currentCards.includes(item));

                if (cardNotInChargebee?.length) {
                    
                    await db.ContactsCardDetails.destroy({
                        where: {
                            cb_card_id: { [Op.in]: cardNotInChargebee },
                            contact_id
                        }
                    });
                }

                await db.SubscriptionCardMapping.destroy({
                    where: { contact_id }
                });

                if (userDetail?.contacts_customer_details?.length) {
                    const cardService = new CardService();
                    await cardService.getSubscriptionPaymentDetails(userDetail?.contacts_customer_details);
                }
            }
        } catch (error) {
            console.error(`SubscriptionMapServices Get Card Details And Update -> `, error);
        }
    }
}

module.exports = SubscriptionMapServices;
