const db = require('../models/index.js');
const { Sequelize } = require('sequelize');
const ChargebeeClient = require("../clients/chargebee/chargebee");
const { getPagingData, parsePagination } = require("../helpers/privacyAlgorithms");
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require('../utils/ResponseCodes.js');
const CustomError = require('../utils/errors/CustomError.js');
const elasticSearch = new ElasticSearch();

class BillingServices {

    // Method to fetch customer location details
    async fetchLocation(id, elasticLogObj) {
        try {
            const data = await db.CustomerDetails.findAll({
                include: {
                    model: db.CustomerAddresses,
                    as: 'serviceAddress',
                    attributes: [],
                    required: false
                },
                attributes: [
                    "id",
                    [Sequelize.literal('`serviceAddress`.`full_address`'), 'full_address']
                ],
                where: { contact_id: id },
                order: [['id', 'DESC']]
            });

            return { status: true, data };
        } catch (error) {
            console.error(`Billing Services fetch Location ->`, error);
            throw error;
        }
    }

    // Method to fetch customer statements (invoices)
    async fetchStatements(custDetailsId, query, elasticLogObj, contact_id) {
        try {
            const validContact = await db.CustomerDetails.findOne({ where: { contact_id, id: custDetailsId } });
            if (!validContact) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);
            const data = {};
            const { page, limit, offset } = parsePagination(query);
            let { count, rows } = await db.SubscriptionInvoice.findAndCountAll({
                where: { customer_details_id: custDetailsId },
                attributes: ["id", "cb_invoice_id", "amount", 'expected_payment_date', "status", "createdAt", "credit_issue", "amount_adjusted"],
                order: [['createdAt', 'DESC']],
                limit,
                offset,
                raw: true
            });

            data.invoices = rows;
            data.pageData = getPagingData(count, limit, page);

            return { status: true, data };
        } catch (error) {
            console.error(`Billing Services fetch Statements ->`, error);
            throw error;
        }
    }

    // Method to download an invoice using Chargebee client
    async downloadInvoice(invoiceId, elasticLogObj, contact_id) {
        const { _id, _index } = await elasticSearch.insertDocument("download_billing_invoice_logs", { ...elasticLogObj, request: { invoiceId } });

        try {

            let invoiceDetails = await db.SubscriptionInvoice.findOne({
                include: [
                    {
                        model: db.CustomerDetails,
                        as: "customerDetail",
                        attributes: ["contact_id"],
                    }
                ],
                attributes: ["id", "customer_details_id"],
                where: { cb_invoice_id: invoiceId }
            });

            invoiceDetails = JSON.parse(JSON.stringify(invoiceDetails));
            const contactId = invoiceDetails?.customerDetail?.contact_id;
            if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

            const callChargebee = new ChargebeeClient();
            const result = await callChargebee.downloadInvoice(invoiceId);
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: { status: true, data: result } });
            return { status: true, data: result };
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Billing Services download Invoice ->`, error);
            throw error;
        }
    }
}

module.exports = BillingServices;
