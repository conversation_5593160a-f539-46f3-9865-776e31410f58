const Sequelize = require('sequelize');

module.exports = (sequelize) => {
    return sequelize.define("subscription_card_mapping", {
        id: {
            type: Sequelize.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: "Primary key, auto-incrementing identifier."
        },
        contact_id: {
            type: Sequelize.INTEGER(11),
            references: {
                model: 'contacts',
                key: 'id'
            },
            allowNull: false,
            comment: "Foreign key mapping to the primary key 'id' in the 'contacts' table, with cascading delete functionality.",
            onDelete: 'CASCADE'
        },
        customer_subscription_id: {
            type: Sequelize.INTEGER(11),
            references: {
                model: 'customer_details_subscriptions',
                key: 'id'
            },
            allowNull: false,
            comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_subscriptions' table.",
            onDelete: 'CASCADE'
        },
        contact_card_id: {
            type: Sequelize.INTEGER(11),
            references: {
                model: 'contacts_card_details',
                key: 'id'
            },
            allowNull: false,
            comment: "Foreign key mapping to the primary key 'id' in the 'contacts_card_detail' table.",
            onDelete: 'CASCADE'
        },
        is_primary: {
            type: Sequelize.ENUM('0', '1'),
            defaultValue: '0',
            comment: "Associated with chargebee primary card as per subscription level."
        },
        createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was created in the database."
        },
        updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            comment: "The timestamp when the record was last updated in the database."
        }
    },
        {
            collate: 'utf8mb4_unicode_ci',
            timestamps: true,
            indexes: [
                {
                    name: "ContactSubscriptionCardUniqueIndex",
                    unique: true,
                    fields: ['contact_id', 'customer_subscription_id', 'contact_card_id']
                },
            ]
        });
}