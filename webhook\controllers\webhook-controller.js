const { decryptXML } = require("../helper/custom-helper");
const WebhookApiService = require("../services/webhook-api-services");
const webhookApiService = new WebhookApiService();
const CONFIG = require('../config');

class WebhookController {

    async updateWebhook(req, res, next) {
        let verified = false;
        try {
            const { status, webhookData, sfApiName, organizationId, sessionId } = await decryptXML(req.body, "order");
            let first15 = organizationId.substring(0, 15);
            if (first15 == CONFIG.ORGANIZATIONID) verified = true;
            if (status && webhookData && sfApiName && verified) await webhookApiService.checkAndUpdateWebhookData(webhookData, sfApiName, "update", organizationId, sessionId);
        } catch (error) {
            console.error(`WebhookController updateWebhook -> `, error);
        } finally {
            res.set('Content-Type', 'application/xml');
            res.status(200).send(`
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                <soap:Body>
                    <response>
                        <status>${verified}</status>
                    </response>
                </soap:Body>
            </soap:Envelope>
            `);
        }
    }
}

module.exports = new WebhookController();