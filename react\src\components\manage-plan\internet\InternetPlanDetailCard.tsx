import React from "react";
import { useSelector } from "react-redux";
import { GreenTick } from "../../../assets/Icons";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";
import { formatCurrency } from "../../../utils/helper";

type InternetPlanDetailCard = {
  selected: any;
  defaultList: any;
  handlePlanSelect: () => void;
};

const InternetPlanDetailCard: React.FC<InternetPlanDetailCard> = ({
  defaultList,
  selected,
  handlePlanSelect,
}) => {
  const subscriptionType = useSelector(customerSubscriptionType);

  const features = defaultList?.description
    ?.split("✔️")
    .map((item: any) => item.trim())
    .filter((item: any) => item.length > 0);

  const isSelected = selected?.speed === defaultList?.speed;

  return (
    <div
      className={`w-[230px] flex flex-col justify-between border border-[#F5EFFF] rounded-20 shadow transition duration-300 cursor-pointer p-5 ${
        isSelected ? "shadow-[0_2px_12px_0_rgba(170,125,230,0.57)]" : ""
      }`}
      onClick={handlePlanSelect}
    >
      {/* Plan Name */}
      <div>
        <h1 className="font-anton uppercase text-2xl mt-3 lg:text-[26px] leading-tight">
          Internet {defaultList?.name}
        </h1>

        {/* Price */}
        <h1 className="font-anton uppercase text-2xl lg:text-[26px] mt-3 flex items-center">
          {formatCurrency(defaultList?.billing?.[0]?.[subscriptionType]?.price)}
          <span className="text-sm lowercase ml-2">
            /{subscriptionType === "monthly" ? "month" : "year"}
          </span>
        </h1>
      </div>

      {/* Features */}
      <div className="flex flex-col gap-5 mt-5">
        {features?.map((item: any, index: number) => (
          <div
            key={index}
            className="flex gap-2.5 items-center text-sm text-[#111]"
          >
            <GreenTick />
            <p>{item}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InternetPlanDetailCard;
