const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("subscriptions_invoices", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    customer_details_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'contacts_customer_details',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'contacts_customer_details' table, with cascading delete functionality.",
      onDelete: 'CASCADE'
    },
    customer_subscription_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'customer_details_subscriptions',
        key: 'id'
      },
      comment: "Foreign key mapping to the primary key 'id' in the 'customer_details_subscriptions' table.",
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    cb_subscription_id: {
      type: Sequelize.STRING(40),
      allowNull: false,
      comment: "Associated with chargebeeapps__Subscription_CB_Id__c field of chargebeeapps__CB_Invoice__c in sf."
    },
    cb_invoice_id: {
      type: Sequelize.STRING(40),
      allowNull: false,
      comment: 'Associated with chargebeeapps__CB_Invoice_Id__c field of chargebeeapps__CB_Invoice__c in sf.'
    },
    amount: {
      type: Sequelize.FLOAT(16, 2),
      amount: "Associated with chargebeeapps__Amount__c field of chargebeeapps__CB_Invoice__c in sf."
    },
    total_outstanding: {
      type: Sequelize.FLOAT(16, 2),
      comment: 'Associated with chargebeeapps__Due_Amount__c field of chargebeeapps__CB_Invoice__c in sf'
    },
    amount_adjusted: {
      type: Sequelize.FLOAT(16, 2),
      defaultValue: 0,
      comment: 'Associated with amount_adjusted field of invoice in chargebee'
    },
    credit_issue: {
      type: Sequelize.FLOAT(16, 2),
      defaultValue: 0,
      comment: 'Associated with adjustment_credit_notes`s cn_total field of invoice in chargebee'
    },
    expected_payment_date: {
      type: Sequelize.DATE,
      comment: 'Associated with Expected_Payment_Date_Time__c field of chargebeeapps__CB_Invoice__c in sf.'
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of chargebeeapps__CB_Invoice__c in sf."
    },
    status: {
      type: Sequelize.ENUM('NOT_PAID', 'PAID', 'PAYMENT_DUE', 'PENDING', 'POSTED', 'VOIDED', '_UNKNOWN'),
      defaultValue: '_UNKNOWN',
      comment: "Associated with chargebeeapps__Status__c field of chargebeeapps__CB_Invoice__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true,
      indexes: [
        {
          name: "CustomerInvoiceUniqueIndex",
          unique: true,
          fields: ['cb_subscription_id', 'cb_invoice_id']
        }
      ]
    });
}