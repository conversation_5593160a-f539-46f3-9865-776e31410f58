const ContactWebhookServices = require("./webhook-contact-services");
const CustomerWebhookServices = require("./webhook-customer-services");
const ReferralWebhookServices = require("./webhook-referral-services");
const ShippingWebhookServices = require("./webhook-shipping-services");
const OrderWebhookServices = require("./webhook-order-services");
const TechAppWebhookServices = require("./webhook-tech-app-services");
const AddressWebhookServices = require("./webhook-address-services");
const TVWebhookServices = require("./webhook-tv-services");
const PhoneWebhookServices = require("./webhook-phone-services");
const SubscriptionWebhookServices = require("./webhook-subscription-services");
const SubscriptionInvoiceWebhookServices = require("./webhook-subscription-invoice-services");
const InternetWebhookServices = require("./webhook-internet-services");

const contactWebhookServices = new ContactWebhookServices();
const customerWebhookServices = new CustomerWebhookServices();
const referralWebhookServices = new ReferralWebhookServices();
const shippingWebhookServices = new ShippingWebhookServices();
const orderWebhookServices = new OrderWebhookServices();
const techAppWebhookServices = new TechAppWebhookServices();
const addressWebhookServices = new AddressWebhookServices();
const tVWebhookServices = new TVWebhookServices();
const phoneWebhookServices = new PhoneWebhookServices();
const subscriptionWebhookServices = new SubscriptionWebhookServices();
const subscriptionInvoiceWebhookServices = new SubscriptionInvoiceWebhookServices();
const internetWebhookServices = new InternetWebhookServices();

const ElasticSearch = require("../clients/elastic-search");
const { getCurrentAtlanticTime } = require("../helper/custom-helper");
const elasticSearch = new ElasticSearch();

class WebhookApiService {
    async checkAndUpdateWebhookData(webhookData, sfApiName, type, organization_id, sessionId) {
        try {
            const { Id, LastModifiedDate, CreatedDate, Name } = webhookData;

            const conObj = {
                "log.level": "INFO",
                sf_object: sfApiName,
                organization_id,
                request: webhookData,
                timestamp: getCurrentAtlanticTime(null, "elasticSearch")
            }

            if (Id) conObj.sf_object_id = Id;
            if (Name) conObj.sf_name = Name;
            if (sessionId) conObj.session_id = sessionId;
            if (CreatedDate) conObj.sf_createdAt = getCurrentAtlanticTime(CreatedDate, "elasticSearch");
            if (LastModifiedDate) conObj.sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate, "elasticSearch");

            const { _id, _index } = await elasticSearch.insertDocument("webhook_logs", conObj);
            switch (sfApiName) {
                case "Contact":
                    if (type == "update") contactWebhookServices.getContactDetails(webhookData, _id, _index);
                    break;
                case "Customer_Details__c":
                    if (type == "update") customerWebhookServices.getCustomerDetails(webhookData, _id, _index);
                    break;
                case "Service_Address__c":
                    if (type == "update" && sfApiName) addressWebhookServices.getServiceMailingAddressDetails(webhookData, _id, _index, sfApiName);
                    break;
                case "Mailing_Address__c":
                    if (type == "update" && sfApiName) addressWebhookServices.getServiceMailingAddressDetails(webhookData, _id, _index, sfApiName);
                    break;
                case "Internet__c":
                    if (type == "update") internetWebhookServices.getInternetDetails(webhookData, _id, _index);
                    break;
                case "TV__c":
                    if (type == "update") tVWebhookServices.getTVDetails(webhookData, _id, _index);
                    break;
                case "Phone__c":
                    if (type == "update") phoneWebhookServices.getPhoneDetails(webhookData, _id, _index);
                    break;
                case "Order__c":
                    if (type == "update") orderWebhookServices.getOrderDetails(webhookData, _id, _index);
                    break;
                case "Shipping__c":
                    if (type == "update") shippingWebhookServices.getShippingDetails(webhookData, _id, _index);
                    break;
                case "Tech_Appointment__c":
                    if (type == "update") techAppWebhookServices.getTechAppDetails(webhookData, _id, _index);
                    break;
                case "chargebeeapps__CB_Subscription__c":
                    if (type == "update") subscriptionWebhookServices.getSubscriptionDetails(webhookData, _id, _index);
                    break;
                case "chargebeeapps__CB_Invoice__c":
                    if (type == "update") subscriptionInvoiceWebhookServices.getSubscriptionInvoiceDetails(webhookData, _id, _index);
                    break;
                case "Referral__c":
                    if (type == "update") referralWebhookServices.getReferrals(webhookData, _id, _index);
                    break;
                default:
                    break;
            }
        } catch (error) {
            console.error("Check And Update Webhook Data -> ", error);
        }
    }
}

module.exports = WebhookApiService;
