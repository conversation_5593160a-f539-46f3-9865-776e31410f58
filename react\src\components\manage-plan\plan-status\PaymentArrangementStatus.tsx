import { WifiIcon } from "../../../assets/Icons";
import { PaymentStatusInterface } from "../../../typings/typing";
import { formatPatternDate } from "../../../utils/helper";
import StatusCard from "../StatusCard";
import MoveOrderStatus from "./MoveOrderStatus";

const PaymentArrangementStatus: React.FC<PaymentStatusInterface> = ({
  expectedDate,
  isMoveOrder,
}) => {
  return (
    <div>
      <StatusCard
        Icon={<WifiIcon />}
        label={`Arrangements for ${
          expectedDate ? formatPatternDate(expectedDate) : "NA"
        }`}
        status="Payment arrangements"
        isMoveOrder={isMoveOrder}
      />
      {isMoveOrder && <MoveOrderStatus isMoveOrder={isMoveOrder} />}
    </div>
  );
};

export default PaymentArrangementStatus;
