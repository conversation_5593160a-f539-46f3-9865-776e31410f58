const request = require('supertest');
const express = require('express');
const dashboardRoute = require('../../routes/dashboard-route');

// Mock the dashboard controller
jest.mock('../../controllers/dashboard-controller', () => ({
  userDetails: jest.fn((req, res) => res.status(200).json({ 
    message: 'User details retrieved successfully', 
    data: {
      user: {
        id: '12345',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+**********',
        accountStatus: 'active',
        joinDate: '2023-01-15',
        lastLogin: '2024-01-18T10:30:00Z'
      },
      preferences: {
        notifications: true,
        paperlessBilling: true,
        autoPayEnabled: false
      }
    }
  })),
  locationDetails: jest.fn((req, res) => res.status(200).json({ 
    message: 'Location details retrieved successfully',
    data: {
      serviceLocation: {
        address: '123 Main St',
        city: 'Halifax',
        province: 'Nova Scotia',
        postalCode: 'B3H 1A1',
        coordinates: {
          latitude: 44.6488,
          longitude: -63.5752
        }
      },
      serviceArea: {
        region: 'Halifax Regional Municipality',
        zone: 'Central Halifax',
        serviceAvailability: {
          internet: true,
          tv: true,
          phone: true
        }
      },
      technicalInfo: {
        connectionType: 'Fiber',
        maxSpeed: '1000 Mbps',
        infrastructure: 'FTTH'
      }
    }
  }))
}));

describe('Dashboard Routes', () => {
  let app;
  const basePath = '/api/v1';

  beforeEach(() => {
    app = express();
    app.use(express.json());
    dashboardRoute(app, basePath);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/v1/dashboard/user-details', () => {
    it('should retrieve user details successfully', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/user-details')
        .expect(200);

      expect(response.body.message).toBe('User details retrieved successfully');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('preferences');
      
      // Verify user object structure
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user).toHaveProperty('firstName');
      expect(response.body.data.user).toHaveProperty('lastName');
      expect(response.body.data.user).toHaveProperty('email');
      expect(response.body.data.user).toHaveProperty('phone');
      expect(response.body.data.user).toHaveProperty('accountStatus');
      expect(response.body.data.user).toHaveProperty('joinDate');
      expect(response.body.data.user).toHaveProperty('lastLogin');
      
      // Verify preferences object structure
      expect(response.body.data.preferences).toHaveProperty('notifications');
      expect(response.body.data.preferences).toHaveProperty('paperlessBilling');
      expect(response.body.data.preferences).toHaveProperty('autoPayEnabled');
      
      // Verify data types
      expect(typeof response.body.data.user.id).toBe('string');
      expect(typeof response.body.data.user.firstName).toBe('string');
      expect(typeof response.body.data.user.lastName).toBe('string');
      expect(typeof response.body.data.user.email).toBe('string');
      expect(typeof response.body.data.preferences.notifications).toBe('boolean');
      expect(typeof response.body.data.preferences.paperlessBilling).toBe('boolean');
      expect(typeof response.body.data.preferences.autoPayEnabled).toBe('boolean');
    });

    it('should handle user details request with authentication headers', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/user-details')
        .set('Authorization', 'Bearer fake-jwt-token')
        .expect(200);

      expect(response.body.message).toBe('User details retrieved successfully');
    });

    it('should handle user details request with custom headers', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/user-details')
        .set('X-Customer-ID', '12345')
        .set('X-Request-ID', 'req-12345')
        .expect(200);

      expect(response.body.message).toBe('User details retrieved successfully');
    });
  });

  describe('GET /api/v1/dashboard/location-details', () => {
    it('should retrieve location details successfully', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/location-details')
        .expect(200);

      expect(response.body.message).toBe('Location details retrieved successfully');
      expect(response.body.data).toHaveProperty('serviceLocation');
      expect(response.body.data).toHaveProperty('serviceArea');
      expect(response.body.data).toHaveProperty('technicalInfo');
      
      // Verify serviceLocation object structure
      expect(response.body.data.serviceLocation).toHaveProperty('address');
      expect(response.body.data.serviceLocation).toHaveProperty('city');
      expect(response.body.data.serviceLocation).toHaveProperty('province');
      expect(response.body.data.serviceLocation).toHaveProperty('postalCode');
      expect(response.body.data.serviceLocation).toHaveProperty('coordinates');
      expect(response.body.data.serviceLocation.coordinates).toHaveProperty('latitude');
      expect(response.body.data.serviceLocation.coordinates).toHaveProperty('longitude');
      
      // Verify serviceArea object structure
      expect(response.body.data.serviceArea).toHaveProperty('region');
      expect(response.body.data.serviceArea).toHaveProperty('zone');
      expect(response.body.data.serviceArea).toHaveProperty('serviceAvailability');
      expect(response.body.data.serviceArea.serviceAvailability).toHaveProperty('internet');
      expect(response.body.data.serviceArea.serviceAvailability).toHaveProperty('tv');
      expect(response.body.data.serviceArea.serviceAvailability).toHaveProperty('phone');
      
      // Verify technicalInfo object structure
      expect(response.body.data.technicalInfo).toHaveProperty('connectionType');
      expect(response.body.data.technicalInfo).toHaveProperty('maxSpeed');
      expect(response.body.data.technicalInfo).toHaveProperty('infrastructure');
      
      // Verify data types
      expect(typeof response.body.data.serviceLocation.coordinates.latitude).toBe('number');
      expect(typeof response.body.data.serviceLocation.coordinates.longitude).toBe('number');
      expect(typeof response.body.data.serviceArea.serviceAvailability.internet).toBe('boolean');
      expect(typeof response.body.data.serviceArea.serviceAvailability.tv).toBe('boolean');
      expect(typeof response.body.data.serviceArea.serviceAvailability.phone).toBe('boolean');
    });

    it('should handle location details request with query parameters', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/location-details')
        .query({ includeWeather: 'true', includeTechnical: 'true' })
        .expect(200);

      expect(response.body.message).toBe('Location details retrieved successfully');
    });

    it('should handle location details request with authentication', async () => {
      const response = await request(app)
        .get('/api/v1/dashboard/location-details')
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Customer-ID', '12345')
        .expect(200);

      expect(response.body.message).toBe('Location details retrieved successfully');
    });
  });
});
