const { fetchLocation, fetchStatements, downloadInvoice } = require("../controllers/billing-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// Define routes for fetching location, statements, and downloading invoice
	router.get("/location", fetchLocation);
	router.get("/statements/:custDetailsId", fetchStatements);
	router.get("/invoice/:invoiceId", downloadInvoice);

	// Use the base path passed from index.js
	app.use(`${basePath}/billing`, router); // Mount the router at the base path
};
