const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("customer_details_addresses", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier"
    },
    address_type: {
      type: Sequelize.ENUM('mailing', 'service'),
      allowNull: false,
      comment: "Address type must be service or mailing"
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    name: {
      type: Sequelize.STRING(50),
      comment: "This maps to 'Name' field in Salesforce.",
      allowNull: false
    },
    suit_unit: {
      type: Sequelize.STRING(50),
      comment: "This maps to 'Suite_Unit__c' field in Salesforce."
    },
    street_number: {
      type: Sequelize.STRING(50),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_Street_Number__c; if address_type is 'mailing', it maps to Mailing_Street_Number__c."
    },
    street_name: {
      type: Sequelize.STRING(50),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_Street_Name__c; if address_type is 'mailing', it maps to Mailing_Street_Name__c."
    },
    province: {
      type: Sequelize.STRING(30),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_Province__c; if address_type is 'mailing', it maps to Mailing_Province__c."
    },
    town: {
      type: Sequelize.STRING(30),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_City_Town__c; if address_type is 'mailing', it maps to Mailing_City_Town__c."
    },
    country: {
      type: Sequelize.STRING(30),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_Country__c; if address_type is 'mailing', it maps to Mailing_Country__c."
    },
    postal_code: {
      type: Sequelize.STRING(8),
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Service_Postal_Code__c; if address_type is 'mailing', it maps to Mailing_Postal_Code__c."
    },
    full_address: {
      type: Sequelize.TEXT,
      comment: "This maps the address_type to the corresponding Salesforce field: if address_type is 'service', it maps to Full_Service_Address__c; if address_type is 'mailing', it maps to Full_Mailing_Address__c."
    },
    status: {
      type: Sequelize.STRING(30),
      comment: "This maps to 'Status__c' field in Salesforce."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the 'LastModifiedDate' in Salesforce."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}