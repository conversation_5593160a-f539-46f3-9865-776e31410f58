import { LocationIcon } from "../../assets/Icons";
import { formatUTCDate, removeApostrophes } from "../../utils/helper";
import Button from "../common/Button";

export interface OfferLocationSelectCardProps {
  location: {
    address: string;
    status?: string;
    billDue?: number;
  };
  nextHandler: (locationId: string) => void;
}

const OfferLocationSelectCard = ({
  location,
  nextHandler,
}: OfferLocationSelectCardProps) => {
  return (
    <div
      className={`bg-white text-primary rounded-10 max-xl:p-2.5 p-5 hover:shadow-cardShadow05`}
    >
      <div className="flex items-center justify-between lg:gap-8 md:gap-5 gap-4">
        <div className="flex items-center md:gap-5 gap-2.5 flex-1 lg:max-w-[300px] md:max-w-[200px]">
          <div className="w-5 h-5 flex items-center justify-center">
            <LocationIcon />
          </div>
          <div className="flex flex-col">
            <div className="">
              <div className="flex items-center gap-3">
                <p className="text-sm">Location</p>
              </div>
              <p
                className="lg:text-base text-sm font-medium pt-2 md:truncate md:max-w-[50%]"
                title={removeApostrophes(location.full_address)}
              >
                {removeApostrophes(location.full_address)}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center 2xl:gap-5 lg:gap-3 gap-2.5 justify-end">
          <div className="">
            <Button
              title={location.status ? "Next" : "Add"}
              className="btn-small"
              clickEvent={() => nextHandler(location)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OfferLocationSelectCard;
