import React from "react"
import ContentLoader from "react-content-loader"

const TotalCardSkeleton:React.FC = (props) => (
  <ContentLoader 
    speed={3}
    width={"355px"}
    height={"150px"}
    viewBox="0 0 355 150"
    backgroundColor="#f5f5f5"
    foregroundColor="#dbdbdb"
    {...props}
  >
    {/* top */}
    <rect x="0" y="0" rx="5" ry="5" width="355" height="5" /> 
    {/* bottom */}
    <rect x="0" y="130" rx="5" ry="5" width="355" height="5" /> 
    {/* left */}
    <rect x="0" y="0" rx="5" ry="5" width="5" height="130" /> 
    {/* right */}
    <rect x="350" y="3" rx="5" ry="5" width="5" height="130" /> 
    
    {/* Card heading */}
    <rect x="20" y="20" rx="0" ry="0" width="122" height="20" />
    {/* Due */}
    <rect x="20" y="80" rx="0" ry="0" width="67" height="30" />

  </ContentLoader>
)

export default TotalCardSkeleton