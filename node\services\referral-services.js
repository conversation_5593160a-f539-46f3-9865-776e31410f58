const db = require('../models/index.js');
const { getPagingData, parsePagination } = require("../helpers/privacyAlgorithms.js");

class ReferralServices {
    /**
     * Fetches details of a user's referrals.
     * @param {object} userDetails - Object containing user details.
     * @returns {object} Object containing fetched referral details.
     */
    async fetchDetails(userDetails, elasticLogObj) {
        try {
            const { referral_link, total_referral, total_paid_referral, total_earned_referral } = userDetails;
            const data = {};

            data.total_referrals = total_referral ? total_referral : 0;
            data.total_paid_referrals = total_paid_referral ? total_paid_referral : 0;
            data.total_earned = total_earned_referral ? total_earned_referral : 0.00;
            data.referral_link = referral_link;

            const response = { status: true, data };
            return response;
        } catch (error) {
            console.error(`ReferralServices fetchDetails -> `, error);
            throw error;
        }
    }

    /**
     * Fetches a paginated list of referrals for a customer.
     * @param {number} contact_id - ID of the customer.
     * @param {object} query - Query object containing pagination parameters.
     * @returns {object} Object containing fetched referral list and pagination data.
     */
    async fetchReferralList(contact_id, query, elasticLogObj) {
        try {
            const data = {};
            const excludeArray = ["contact_id", "type", "updatedAt"];
            const { page, limit, offset } = parsePagination(query);
            let { count, rows } = await db.ContactsReferrals.findAndCountAll({
                where: { contact_id },
                order: [['id', 'DESC']],
                attributes: { exclude: excludeArray },
                limit,
                offset,
                raw: true
            });

            data.referrals = rows;
            data.pageData = getPagingData(count, limit, page);
            const response = { status: true, data };
            return response;
        } catch (error) {
            console.error(`ReferralServices fetchReferralList -> `, error);
            throw error;
        }
    }
}

module.exports = ReferralServices;
