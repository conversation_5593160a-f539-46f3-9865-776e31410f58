import moment from "moment-timezone";

export const regEx = {
  EMAIl: /^\w+(\+\w+)?([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
  PASSWORD: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,24}$/,
  PHONE: /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/,
  Name: /^[a-zA-Z\s'-]+$/,
  POSTAL_CODE: /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/
};
export const _TIMEZONE = "America/Halifax";

export const AddressChangeDateDescription =
  "The address is serviceable! Please select a requested service change date as changing your service address will disconnect your current service and transfer it to the new location on the requested date. Please note we can not transfer service on weekends.";

export const cancelServiceDateDescription =
  "Please update below when you can make the current outstanding balance. Once updated you will not need to manually pay the bills as the system will do so automatically. Please note you can only make payment arrangements 28 days past your invoice date.";

export const AddExistingPhoneDescription =
  "Please confirm to add home phone to your account. Once complete your account will be charge XXX and we will mail you your phone box. We will also begin with porting your phone number ************. This process can take up to 10 business days depending on the phone carrier your number is currently with. ";

export const Unauthorized = (error: number) => {
  if (error === 401) {
    window.location.assign("/login");
  }
};

// Format Month
export const formatMonth = (month: number) => {
  return month.toString().padStart(2, "0");
};

// Format Date
export const formatUTCDate = (isoString: string) => {
  // Create an array of month names
  const months = [
    "JAN",
    "FEB",
    "MAR",
    "APR",
    "MAY",
    "JUN",
    "JUL",
    "AUG",
    "SEP",
    "OCT",
    "NOV",
    "DEC",
  ];

  // Create a Date object from the ISO string
  const date = moment(isoString).tz(_TIMEZONE);

  // Extract the day, month, and year from the Date object
  const day = date.date();
  const month = months[date.month()]; // getUTCMonth() returns 0-11
  const year = date.year();

  // Return the formatted date string
  return `${month} ${day}, ${year}`;
};

export const formatDate = (isoString: string) => {
  // Create an array of month names
  const months = [
    "JAN",
    "FEB",
    "MAR",
    "APR",
    "MAY",
    "JUN",
    "JUL",
    "AUG",
    "SEP",
    "OCT",
    "NOV",
    "DEC",
  ];

  // Create a Date object from the ISO string
  const date = new Date(isoString);

  // Extract the day, month, and year from the Date object
  const day = date.getDate();
  const month = months[date.getMonth()]; // getMonth() returns 0-11
  const year = date.getFullYear();

  // Return the formatted date string
  return `${month} ${day}, ${year}`;
};

export const formatPatternDate = (dateString: string | Date): string => {
  const date = new Date(dateString);
  const month = date.getUTCMonth() + 1; // Months are zero-indexed
  const day = date.getUTCDate();
  const year = date.getUTCFullYear().toString().slice(-2); // Get the last two digits of the year

  return `${month}/${day}/${year}`;
};

export const formatDateToLocalString = (dateString: string): string => {
  const date = new Date(dateString);

  // Set the time to 00:00:00
  date.setHours(0, 0, 0, 0);

  // Convert the date to a local string format
  return date.toString();
};

// Converting into Canada curreny
export const formatCurrency = (amount: number | string, fixed?: boolean) => {
  if (fixed) {
    const fixedAmount = parseFloat(amount.toString()).toFixed(2); // Ensure it's a string
    return `$${fixedAmount}`;
  } else {
    return `$${amount}`;
  }
};


export const maskPhoneNumber = (number: string): string => {
  const cleaned = ("" + number).replace(/\D/g, "");
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);

  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }
  return number;
};

export const removeApostrophes = (str: string) => {
  if (str) {
    return str.replace(/^'|'$/g, "");
  }
};

export const Debounce = (func: (...args: any[]) => void, delay: number) => {
  let timeoutId: number | null = null;

  return (...args: any[]) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

// Convert subscription plan and internet speed into one statement
export const getSubscriptionName = (
  speed: '100W' | '300W' | 'GIG15W',
  subscription: 'monthly' | 'yearly'
): string => {
  const subscriptions = {
    monthly: {
      "100W": "Internet 100",
      "300W": "Internet 300",
      "GIG15W": "Internet 1 Gig",
    },
    yearly: {
      "100W": "Internet 100 WiFi Yearly",
      "300W": "Internet 300 WiFi Yearly",
      "GIG15W": "Internet 1 Gig Yearly",
    },
  };
  return subscriptions[subscription]?.[speed] || "NA"; // Returns "NA" if not found
};

export const formatDateToMonthDayYear = (dateString: string): string => {
  if (!dateString) return "-"
  const months = [
    "JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE",
    "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"
  ];


  const [year, month, day] = dateString?.split("-")?.map(Number);
  return `${months[month - 1]} ${day}, ${year}`;
}


export const convertDateStringToReadableDate = (dateString: string): string => {
  const months = [
    "JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE",
    "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"
  ];


  // Parse the date string into a Date object
  const date = new Date(dateString);

  // Extract the year, month, and day
  const year = date.getFullYear();
  const month = months[date.getMonth()];
  const day = date.getDate();

  return `${month} ${day}, ${year}`;
}