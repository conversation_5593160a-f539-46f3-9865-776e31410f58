import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import CryptoJS from "crypto-js";

const API_BASE_URL =
  import.meta.env?.VITE_API_BASE_URL || "http://localhost:5009";
const apiVersion = "/api/v1";
const secretKey = import.meta.env.VITE_CRYPTO_SECRET; // secret key for encryption
const sendEncryptedDatainBody = true; // turn it to true if you want to send encrypted data in body

const encryptData = (plainData: any): any => {
  if (plainData === null || plainData === undefined) {
    return { payload: plainData };
  }
  const encodedSting = CryptoJS.AES.encrypt(
    JSON.stringify(plainData),
    secretKey
  ).toString();
  return { payload: encodedSting };
};

// ==============================================================================================
// ⚠️  IMPORTANT: WHEN ADDING NEW API ENDPOINTS, REGISTER THEM IN AWS FWA FOR AUTHORIZATION!
//      Missing this step can break authentication or expose unauthorized routes.
// ==============================================================================================

export const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_BASE_URL}`,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("access_token");
      headers.set("authorization", `Bearer ${token}`);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (data) => ({
        url: `/auth/login`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    forgotPassword: builder.mutation({
      query: (data) => ({
        url: `/auth/forgot-password`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    resetPassword: builder.mutation({
      query: (data) => ({
        url: `/auth/reset-password`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    logout: builder.mutation({
      query: () => ({
        url: `${apiVersion}/customer/logout`,
        method: "GET",
      }),
    }),
    dashboardUserData: builder.mutation({
      query: () => ({
        url: `${apiVersion}/dashboard/user-details`,
        method: "GET",
      }),
    }),
    dashboardLocationData: builder.mutation({
      query: () => ({
        url: `${apiVersion}/dashboard/location-details`,
        method: "GET",
      }),
    }),

    // Customer
    getCustomer: builder.mutation({
      query: () => ({
        url: `${apiVersion}/customer/details`,
        method: "GET",
      }),
    }),
    updateCustomer: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/customer`,
        method: "PUT",
        body: data, // don't encrypt data because of it contains file
      }),
    }),
    updatePassword: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/customer/reset-password`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),

    // Manage Plan
    managePlan: builder.mutation({
      query: (id) => ({
        url: `${apiVersion}/plan/manage/${id}`,
        method: "GET",
      }),
    }),
    getPlanDetails: builder.mutation({
      query: (query) => ({
        url: `${apiVersion}/plan/details`,
        params: query,
        method: "GET",
      }),
    }),
    updateAddress: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/plan/update-address`,
        method: "PUT",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),

    renewalDate: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/renewal`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    renewalProrated: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/estimate-renewal`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    internetUpdate: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/internet-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    internetUpdateProrated: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/estimate-internet-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    televisionUpdate: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/television-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    televisionUpdateProrated: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/estimate-television-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    phoneUpdate: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/phone-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    phoneUpdateProrated: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/estimate-phone-update`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    cancelService: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/subscription/cancel`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),

    // Referrals
    referralDetails: builder.mutation({
      query: () => ({
        url: `${apiVersion}/referrals/details`,
        method: "GET",
      }),
    }),
    referralList: builder.mutation({
      query: (query) => ({
        url: `${apiVersion}/referrals/list`,
        params: query,
        method: "GET",
      }),
    }),

    // Billing
    allLocation: builder.mutation({
      query: ({ id }) => ({
        url: `${apiVersion}/billing/location/${id}`,
        method: "GET",
      }),
    }),
    fetchStatement: builder.mutation({
      query: ({ id, query }) => ({
        url: `${apiVersion}/billing/statements/${id}`,
        params: query,
        method: "GET",
      }),
    }),
    downloadStatement: builder.mutation({
      query: (id) => ({
        url: `${apiVersion}/billing/invoice/${id}`,
        method: "GET",
      }),
    }),

    // Card
    addCard: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/card`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    updateCard: builder.mutation({
      query: ({ data }) => ({
        url: `${apiVersion}/card`,
        method: "PUT",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    deleteCard: builder.mutation({
      query: (id) => ({
        url: `${apiVersion}/card/${id}`,
        method: "DELETE",
      }),
    }),
    getCards: builder.mutation({
      query: (query) => {
        return {
          url: `${apiVersion}/card?subscriptionId=${query}`,
          method: "GET",
        };
      },
    }),
    getOutstandingList: builder.mutation({
      query: (query) => ({
        url: `${apiVersion}/payment/outstanding`,
        params: query,
        method: "GET",
      }),
    }),
    makePayment: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/payment`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
    paymentArrangements: builder.mutation({
      query: (data) => ({
        url: `${apiVersion}/payment/arrangement`,
        method: "POST",
        body: sendEncryptedDatainBody ? encryptData(data) : data,
      }),
    }),
  }),
});
export const {
  useLoginMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useLogoutMutation,
  useDashboardUserDataMutation,
  useDashboardLocationDataMutation,
  useGetCustomerMutation,
  useUpdateCustomerMutation,
  useUpdatePasswordMutation,

  // Manage Plan
  useManagePlanMutation,
  useGetPlanDetailsMutation,
  useUpdateAddressMutation,
  useRenewalDateMutation,
  useRenewalProratedMutation,
  useInternetUpdateMutation,
  useInternetUpdateProratedMutation,
  useTelevisionUpdateMutation,
  useTelevisionUpdateProratedMutation,
  usePhoneUpdateMutation,
  usePhoneUpdateProratedMutation,
  useCancelServiceMutation,

  // Referrals
  useReferralDetailsMutation,
  useReferralListMutation,
  // Billing
  useAllLocationMutation,
  useFetchStatementMutation,
  useDownloadStatementMutation,

  // Card
  useAddCardMutation,
  useUpdateCardMutation,
  useDeleteCardMutation,
  useGetCardsMutation,
  useGetOutstandingListMutation,
  useMakePaymentMutation,
  usePaymentArrangementsMutation,
} = authApi;
