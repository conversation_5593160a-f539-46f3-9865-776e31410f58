const Joi = require('joi');

const resetPasswordValidation = Joi.object().keys({
    email: Joi.string()
        .trim()
        .email({ minDomainSegments: 2 })
        .label("email")
        .min(5) // Minimum length for email
        .max(96) // Maximum length for email
        .required()
        .messages({
            'any.required': 'Email field is required.',
            'string.email': 'Invalid email format.',
            'string.min': 'Email must be at least {#limit} characters long.',
            'string.max': 'Email cannot exceed {#limit} characters.'
        }),
    password: Joi.string()
        .min(4)
        .max(30)
        .required()
        .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\\-=\\[\\]{}|;:,.<>?]).{3,30}$'))
        .messages({
            'any.required': 'Password field is required.',
            'string.min': 'Password must be at least {#limit} characters long.',
            'string.max': 'Password cannot exceed {#limit} characters.',
            'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character.'
        }),
    confirm_password: Joi.string()
        .valid(Joi.ref('password')) // Ensure confirm_password matches password
        .required()
        .messages({
            'any.required': 'Confirm password field is required.',
            'any.only': 'Confirm password must match the password.'
        }),
    type: Joi.string()
        .valid('new', 'existing')
        .required()
        .messages({
            'any.required': 'Type field is required.',
            'any.only': 'Type must be either `new` or `existing`.'
        }),
    access_token: Joi.string()
        .trim()
        .when('type', {
            is: 'new',
            then: Joi.required(),
            otherwise: Joi.optional()
        })
        .messages({
            'any.required': 'Access token field is required when type is `new`.'
        }),
    confirmation_code: Joi.string()
        .trim()
        .when('type', {
            is: 'existing',
            then: Joi.required(),
            otherwise: Joi.optional()
        })
        .messages({
            'any.required': 'Confirmation code field is required when type is `existing`.'
        })

});

module.exports = { resetPasswordValidation };
