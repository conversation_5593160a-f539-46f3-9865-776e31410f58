const request = require('supertest');
const express = require('express');
const retryRoute = require('../../routes/retry-route');

// Mock the retry subscription service
jest.mock('../../services/retry-subscription-service', () => {
  return jest.fn().mockImplementation(() => ({
    getFailureSubscriptions: jest.fn((req, res) => res.status(200).json({ 
      message: 'Failed subscriptions retrieved successfully', 
      data: {
        failedSubscriptions: [
          {
            id: 'sub_failed_1',
            customerId: '12345',
            subscriptionType: 'internet',
            planName: 'Internet 100',
            failureReason: 'Payment declined',
            failureDate: '2024-01-15T10:30:00Z',
            retryCount: 2,
            maxRetries: 3,
            nextRetryDate: '2024-01-18T10:30:00Z',
            status: 'retry_pending',
            lastPaymentAttempt: {
              amount: 89.99,
              cardLast4: '4242',
              errorCode: 'card_declined',
              errorMessage: 'Your card was declined.'
            }
          },
          {
            id: 'sub_failed_2',
            customerId: '67890',
            subscriptionType: 'bundle',
            planName: 'Internet + TV Bundle',
            failureReason: 'Insufficient funds',
            failureDate: '2024-01-14T08:15:00Z',
            retryCount: 3,
            maxRetries: 3,
            nextRetryDate: null,
            status: 'retry_exhausted',
            lastPaymentAttempt: {
              amount: 129.99,
              cardLast4: '1234',
              errorCode: 'insufficient_funds',
              errorMessage: 'Insufficient funds in account.'
            }
          },
          {
            id: 'sub_failed_3',
            customerId: '11111',
            subscriptionType: 'tv',
            planName: 'Premium TV',
            failureReason: 'Card expired',
            failureDate: '2024-01-16T14:45:00Z',
            retryCount: 1,
            maxRetries: 3,
            nextRetryDate: '2024-01-19T14:45:00Z',
            status: 'retry_scheduled',
            lastPaymentAttempt: {
              amount: 49.99,
              cardLast4: '5678',
              errorCode: 'card_expired',
              errorMessage: 'Your card has expired.'
            }
          }
        ],
        summary: {
          totalFailed: 3,
          retryPending: 1,
          retryScheduled: 1,
          retryExhausted: 1,
          totalAmount: 269.97
        },
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: 3,
          itemsPerPage: 10
        }
      }
    }))
  }));
});

describe('Retry Routes', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    retryRoute(app);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /retry/services', () => {
    it('should fetch failed subscriptions successfully', async () => {
      const response = await request(app)
        .get('/retry/services')
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
      expect(response.body.data).toHaveProperty('failedSubscriptions');
      expect(response.body.data).toHaveProperty('summary');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.failedSubscriptions)).toBe(true);
      expect(response.body.data.failedSubscriptions.length).toBeGreaterThan(0);
      
      // Verify failed subscription object structure
      const firstFailedSub = response.body.data.failedSubscriptions[0];
      expect(firstFailedSub).toHaveProperty('id');
      expect(firstFailedSub).toHaveProperty('customerId');
      expect(firstFailedSub).toHaveProperty('subscriptionType');
      expect(firstFailedSub).toHaveProperty('planName');
      expect(firstFailedSub).toHaveProperty('failureReason');
      expect(firstFailedSub).toHaveProperty('failureDate');
      expect(firstFailedSub).toHaveProperty('retryCount');
      expect(firstFailedSub).toHaveProperty('maxRetries');
      expect(firstFailedSub).toHaveProperty('nextRetryDate');
      expect(firstFailedSub).toHaveProperty('status');
      expect(firstFailedSub).toHaveProperty('lastPaymentAttempt');
      
      // Verify lastPaymentAttempt structure
      expect(firstFailedSub.lastPaymentAttempt).toHaveProperty('amount');
      expect(firstFailedSub.lastPaymentAttempt).toHaveProperty('cardLast4');
      expect(firstFailedSub.lastPaymentAttempt).toHaveProperty('errorCode');
      expect(firstFailedSub.lastPaymentAttempt).toHaveProperty('errorMessage');
      
      // Verify summary structure
      expect(response.body.data.summary).toHaveProperty('totalFailed');
      expect(response.body.data.summary).toHaveProperty('retryPending');
      expect(response.body.data.summary).toHaveProperty('retryScheduled');
      expect(response.body.data.summary).toHaveProperty('retryExhausted');
      expect(response.body.data.summary).toHaveProperty('totalAmount');
      
      // Verify pagination structure
      expect(response.body.data.pagination).toHaveProperty('currentPage');
      expect(response.body.data.pagination).toHaveProperty('totalPages');
      expect(response.body.data.pagination).toHaveProperty('totalItems');
      expect(response.body.data.pagination).toHaveProperty('itemsPerPage');
      
      // Verify data types
      expect(typeof firstFailedSub.id).toBe('string');
      expect(typeof firstFailedSub.customerId).toBe('string');
      expect(typeof firstFailedSub.subscriptionType).toBe('string');
      expect(typeof firstFailedSub.planName).toBe('string');
      expect(typeof firstFailedSub.failureReason).toBe('string');
      expect(typeof firstFailedSub.failureDate).toBe('string');
      expect(typeof firstFailedSub.retryCount).toBe('number');
      expect(typeof firstFailedSub.maxRetries).toBe('number');
      expect(typeof firstFailedSub.status).toBe('string');
      expect(typeof firstFailedSub.lastPaymentAttempt.amount).toBe('number');
      expect(typeof firstFailedSub.lastPaymentAttempt.cardLast4).toBe('string');
      expect(typeof firstFailedSub.lastPaymentAttempt.errorCode).toBe('string');
      expect(typeof firstFailedSub.lastPaymentAttempt.errorMessage).toBe('string');
      expect(typeof response.body.data.summary.totalFailed).toBe('number');
      expect(typeof response.body.data.summary.retryPending).toBe('number');
      expect(typeof response.body.data.summary.retryScheduled).toBe('number');
      expect(typeof response.body.data.summary.retryExhausted).toBe('number');
      expect(typeof response.body.data.summary.totalAmount).toBe('number');
    });

    it('should handle failed subscriptions request with query parameters', async () => {
      const response = await request(app)
        .get('/retry/services')
        .query({ 
          status: 'retry_pending', 
          customerId: '12345',
          page: '1',
          limit: '5'
        })
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
    });

    it('should handle failed subscriptions request with filter parameters', async () => {
      const response = await request(app)
        .get('/retry/services')
        .query({ 
          subscriptionType: 'internet',
          failureReason: 'Payment declined',
          dateFrom: '2024-01-01',
          dateTo: '2024-01-31'
        })
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
    });

    it('should handle failed subscriptions request with sorting parameters', async () => {
      const response = await request(app)
        .get('/retry/services')
        .query({ 
          sortBy: 'failureDate',
          sortOrder: 'desc'
        })
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
    });

    it('should handle failed subscriptions request with authentication headers', async () => {
      const response = await request(app)
        .get('/retry/services')
        .set('Authorization', 'Bearer fake-jwt-token')
        .set('X-Admin-Access', 'true')
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
    });

    it('should handle failed subscriptions request with invalid query parameters', async () => {
      const response = await request(app)
        .get('/retry/services')
        .query({ 
          page: 'invalid',
          limit: 'invalid',
          status: 'invalid_status'
        })
        .expect(200);

      expect(response.body.message).toBe('Failed subscriptions retrieved successfully');
    });

    it('should verify different subscription statuses in response', async () => {
      const response = await request(app)
        .get('/retry/services')
        .expect(200);

      const subscriptions = response.body.data.failedSubscriptions;
      const statuses = subscriptions.map(sub => sub.status);
      
      expect(statuses).toContain('retry_pending');
      expect(statuses).toContain('retry_exhausted');
      expect(statuses).toContain('retry_scheduled');
    });

    it('should verify summary calculations match subscription data', async () => {
      const response = await request(app)
        .get('/retry/services')
        .expect(200);

      const { failedSubscriptions, summary } = response.body.data;
      
      expect(summary.totalFailed).toBe(failedSubscriptions.length);
      
      const retryPendingCount = failedSubscriptions.filter(sub => sub.status === 'retry_pending').length;
      const retryScheduledCount = failedSubscriptions.filter(sub => sub.status === 'retry_scheduled').length;
      const retryExhaustedCount = failedSubscriptions.filter(sub => sub.status === 'retry_exhausted').length;
      
      expect(summary.retryPending).toBe(retryPendingCount);
      expect(summary.retryScheduled).toBe(retryScheduledCount);
      expect(summary.retryExhausted).toBe(retryExhaustedCount);
    });
  });
});
