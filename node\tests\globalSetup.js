/**
 * Jest Global Setup
 * 
 * This file runs once before all tests start.
 * Use it for global test environment setup.
 */

module.exports = async () => {
  console.log('🌍 Global test setup starting...');
  
  // Set global test environment variables
  process.env.NODE_ENV = 'test';
  process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests
  
  // Mock external services
  process.env.MOCK_EXTERNAL_SERVICES = 'true';
  
  // Database configuration for tests
  process.env.DB_HOST = process.env.TEST_DB_HOST || 'localhost';
  process.env.DB_PORT = process.env.TEST_DB_PORT || '3306';
  process.env.DB_USER = process.env.TEST_DB_USER || 'test_user';
  process.env.DB_PASSWORD = process.env.TEST_DB_PASSWORD || 'test_password';
  process.env.DB_NAME = process.env.TEST_DB_NAME || 'test_purple_cow_portal';
  
  // ElasticSearch configuration for tests
  process.env.ELASTIC_SEARCH_NODE = process.env.TEST_ELASTIC_NODE || 'http://localhost:9200';
  process.env.ELASTIC_SEARCH_API_KEY = process.env.TEST_ELASTIC_API_KEY || 'test_api_key';
  
  // AWS configuration for tests
  process.env.AWS_ACCESS_KEY_ID = 'test_access_key';
  process.env.AWS_SECRET_ACCESS_KEY = 'test_secret_key';
  process.env.AWS_REGION = 'us-east-1';
  process.env.AWS_USER_POOL_ID = 'test_user_pool';
  process.env.AWS_CLIENT_ID = 'test_client_id';
  
  // Chargebee configuration for tests
  process.env.CHARGEBEE_SITE = 'test-site';
  process.env.CHARGEBEE_API_KEY = 'test_chargebee_key';
  
  // Salesforce configuration for tests
  process.env.SALESFORCE_USERNAME = '<EMAIL>';
  process.env.SALESFORCE_PASSWORD = 'test_password';
  process.env.SALESFORCE_TOKEN = 'test_token';
  process.env.SALESFORCE_BASE_URL = 'https://test.salesforce.com';
  
  // JWT configuration for tests
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_purposes_only';
  process.env.JWT_EXPIRES_IN = '1h';
  
  // Rate limiting configuration for tests
  process.env.RATE_LIMIT_WINDOW_MS = '60000'; // 1 minute
  process.env.RATE_LIMIT_MAX_REQUESTS = '1000'; // High limit for tests
  
  // Email configuration for tests
  process.env.SMTP_HOST = 'localhost';
  process.env.SMTP_PORT = '587';
  process.env.SMTP_USER = '<EMAIL>';
  process.env.SMTP_PASSWORD = 'test_password';
  
  // File upload configuration for tests
  process.env.UPLOAD_MAX_SIZE = '10485760'; // 10MB
  process.env.UPLOAD_ALLOWED_TYPES = 'image/jpeg,image/png,image/gif,application/pdf';
  
  // API configuration for tests
  process.env.API_BASE_PATH = '/api/v1';
  process.env.API_PORT = '3001'; // Different port for tests
  
  // Logging configuration for tests
  process.env.LOG_PREFIX = 'test';
  process.env.LOG_LEVEL = 'error';
  
  console.log('✅ Global test setup completed');
  console.log('🔧 Test environment variables configured');
  console.log('📊 Mock services enabled');
  console.log('🚀 Ready to run tests!');
};
