import { Link } from "react-router-dom";
import { EditIcon, LocationIcon, RestartIcon } from "../../assets/Icons";
import Button from "../common/Button";
import { useState } from "react";
import Popup from "../common/Popup";
import { removeApostrophes } from "../../utils/helper";

interface PlaceAccordionProps {
  data: {
    id: number;
    stage: keyof typeof statusMapper;
    full_address: string;
    status: string | null;
    customerInternet: {
      live_date: string | null;
      internetElMoveOrder: {
        id: number | null;
        stage: string | null;
      };
    };
  };
}

const statusMapper = {
  Onboarding: "in progress",
  Online: "active",
  Outstanding: "outstanding",
  Offboarding: "disconnected",
  Inactive: "Inactive",
  shipped: "shipped",
  Moving: "Moving",
  "Payment arrangements": "Payment arrangements",
  "Processing move request": "Processing move request",
};

const PlaceAccordion: React.FC<PlaceAccordionProps> = ({ data }) => {
  const [showReactivatePopup, setShowReactivatePopup] =
    useState<boolean>(false);

  // Handle the popup
  const handleShowReactivatePopup = () => {
    setShowReactivatePopup(!showReactivatePopup);
  };
  return (
    <div
      className={`p-2 transition-all duration-500 border border-[#F5EFFF] bg-[#FBF8FF] rounded-xl`}
    >
      <div className="flex items-center justify-between max-lg:gap-2">
        <div className="lg:max-w-[calc(60%)] max-w-[calc(85%)] w-full truncate">
          <div className="flex items-center lg:gap-5 gap-2.5">
            <div className="w-5 h-5 flex items-center justify-center">
              <LocationIcon />
            </div>
            <div className="flex-1">
              <div className="flex items-center md:gap-3 gap-2">
                <p className="text-sm">Location</p>

                {statusMapper[data?.stage] && (
                  <span
                    className={`status-${statusMapper[data?.stage]
                      .replace(/ /g, "")
                      .toLowerCase()} p-2 px-4 text-[10px] rounded-20 lg:hidden capitalize`}
                  >
                    {statusMapper[data?.stage]}
                  </span>
                )}
              </div>
              <p
                className="text-base font-medium pt-2 whitespace-pre-wrap max-w-[80%]"
                title={removeApostrophes(data?.full_address)}
              >
                {removeApostrophes(data?.full_address)}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center lg:gap-5 gap-2.5 lg:max-w-[40%] max-w-[15%]">
          {statusMapper[data?.stage] && (
            <div
              className={`status-${statusMapper[data?.stage]
                .replace(/ /g, "")
                .toLowerCase()} p-2 px-4 font-normal text-sm rounded-[20px] max-lg:hidden capitalize leading-none `}
            >
              {statusMapper[data?.stage]}
            </div>
          )}
          <div className="max-lg:hidden">
            {data?.stage !== "Inactive" ? (
              <Link to={`/manage-plan/${data?.id}`}>
                <Button
                  title="Manage"
                  className="btn-small shadow-buttonShadow"
                />
              </Link>
            ) : (
              <Button
                title={"Reactivate"}
                className="btn-small"
                clickEvent={handleShowReactivatePopup}
              />
            )}
          </div>
          <div className="lg:hidden">
            {data?.stage !== "Inactive" ? (
              <Link to={`/manage-plan/${data?.id}`}>
                <div className="flex items-center justify-center bg-[#FBC400] text-white rounded-xl xs:w-11 xs:h-11 w-8 h-8 cursor-pointer">
                  <EditIcon fill="black" width={15} />
                </div>
              </Link>
            ) : (
              <div
                className="flex items-center justify-center bg-[#FBC400] text-white rounded-xl xs:w-11 xs:h-11 w-8 h-8 cursor-pointer"
                onClick={handleShowReactivatePopup}
              >
                <RestartIcon fill="black" width={15} />
              </div>
            )}
          </div>
        </div>
      </div>

      {showReactivatePopup && (
        <Popup
          title="Reactivate"
          closeHandler={handleShowReactivatePopup}
          height="300px"
          width="600px"
        >
          <div className="py-3">
            <p>
              To reactivate this account please shoot us a text or a call at{" "}
              <Link to={"tel:************"} className="font-semibold underline">
                ************
              </Link>
              . Thank you!
            </p>
          </div>
        </Popup>
      )}
    </div>
  );
};

export default PlaceAccordion;
