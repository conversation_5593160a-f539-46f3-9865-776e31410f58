import React from "react";
import { useDispatch } from "react-redux";
import { toggleShowSelectNumberPopup } from "../../../store/reducers/homePhoneReducer";
import { SelectNumberPopupProps } from "../../../typings/typing";
import Button from "../../common/Button";
import Popup from "../../common/Popup";
import InputFields from "../../forms/InputFields";

const SelectNumberPopup: React.FC<SelectNumberPopupProps> = ({
  homePhonePlan,
  handleSubmit,
  handleHomePhonePlanChange,
  closeHandler,
  isLoading,
  phoneError,
}) => {
  const { haveHomePhonePlan, isNewPhoneNumber, phoneNumber } = homePhonePlan;
  const dispatch = useDispatch();

  const handleIsNewPhone = (e: React.ChangeEvent<HTMLInputElement>): void => {
    handleHomePhonePlanChange(
      "isNewPhoneNumber",
      e.target.id === "is-new-phone"
    );
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === "phoneNumber" && /^\d*$/.test(value)) {
      handleHomePhonePlanChange("phoneNumber", value);
    }
  };

  return (
    <Popup
      title="Would you like to keep your number or get a new one?"
      closeHandler={() => {
        if (!isLoading) {
          dispatch(toggleShowSelectNumberPopup());
        }
      }}
      height="501px"
    >
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-5">
          <p>
            Please indicate one of the two options below for your home phone
            number
          </p>

          {/* New Phone Option */}
          <label
            htmlFor="is-new-phone"
            className="flex items-center gap-2.5 cursor-pointer"
          >
            <input
              id="is-new-phone"
              type="radio"
              name="isNewPhone"
              onChange={handleIsNewPhone}
              checked={isNewPhoneNumber}
              className="sr-only peer"
            />
            <div
              className={`
                w-5 h-5 rounded-full flex items-center justify-center
                bg-white transition-all duration-150
                peer-checked:border-[#FBC400] peer-checked:border-[5px]
                border border-[#E7E0F1]
              `}
            >
              <div className="w-2.5 h-2.5 rounded-full bg-white peer-checked:block hidden" />
            </div>
            <span className="">I would like a new phone number</span>
          </label>

          {/* Existing Phone Option */}
          <label
            htmlFor="is-existing-phone"
            className="flex items-center gap-2.5 cursor-pointer"
          >
            <input
              id="is-existing-phone"
              type="radio"
              name="isNewPhone"
              onChange={handleIsNewPhone}
              checked={!isNewPhoneNumber}
              className="sr-only peer"
            />
            <div
              className={`
                w-5 h-5 rounded-full flex items-center justify-center
                bg-white transition-all duration-150
                peer-checked:border-[#FBC400] peer-checked:border-[5px]
                border border-[#E7E0F1]
              `}
            >
              <div className="w-2.5 h-2.5 rounded-full bg-white peer-checked:block hidden" />
            </div>
            <span className="">
              I would like continue using my existing number
            </span>
          </label>
        </div>

        {/* Phone number input only shown if user wants to keep their number */}
        {!isNewPhoneNumber && haveHomePhonePlan && (
          <div className="flex flex-col gap-5">
            <p className="text-base uppercase">Phone number you want to keep</p>
            <InputFields
              type="tel"
              placeHolder="Enter your phone number"
              changeEvent={handlePhoneNumberChange}
              isErrorMsg={phoneError?.phoneNumber}
              attributes={{
                name: "phoneNumber",
                maxLength: 10,
                value: phoneNumber,
              }}
              className="!bg-[#F5EFFF]"
            />
          </div>
        )}

        {/* Action buttons */}
        <div className="flex gap-2.5 lg:flex-row flex-col">
          <div className="basis-full">
            <Button
              title="Go back"
              btnType="transparent"
              type="button"
              attributes={{ disabled: isLoading }}
              className="disabled:opacity-50"
              clickEvent={closeHandler}
            />
          </div>
          <div className="basis-full">
            <Button
              title="Next"
              clickEvent={() => {
                if (!isLoading) {
                  handleSubmit();
                }
              }}
              isLoading={isLoading}
            />
          </div>
        </div>
      </div>
    </Popup>
  );
};

export default SelectNumberPopup;
