const Joi = require('joi');

const signupValidation = Joi.object().keys({
  email: Joi.string()
    .trim()
    .email({ minDomainSegments: 2 })
    .label("email")
    .min(5) // Minimum length for email
    .max(96) // Maximum length for email
    .required()
    .messages({
      'any.required': 'Email field is required.',
      'string.email': 'Invalid email format.',
      'string.min': 'Email must be at least {#limit} characters long.',
      'string.max': 'Email cannot exceed {#limit} characters.'
    }),
  isCognito: Joi.boolean()
    .required()
    .messages({
      'any.required': 'isCognito field is required.',
      'boolean.base': 'isCognito must be either true or false having only boolean value.'
    })
});

module.exports = { signupValidation };
