const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("contacts_card_detail", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    contact_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'contacts',
        key: 'id'
      },
      allowNull: false,
      comment: "Foreign key mapping to the primary key 'id' in the 'contacts' table, with cascading delete functionality.",
      onDelete: 'CASCADE'
    },
    cb_card_id: {
      type: Sequelize.STRING(30),
      allowNull: false,
      comment: "Chargebee card ID"
    },
    card_name: {
      type: Sequelize.STRING(40),
      comment: "Associated with chargebee first_name and last_name."
    },
    card_number: {
      type: Sequelize.STRING(4),
      allowNull: false,
      comment: "Associated with chargebee card_number."
    },
    expiry_month: {
      type: Sequelize.INTEGER(2),
      allowNull: false,
      comment: "Associated with chargebee expiry_month."
    },
    expiry_year: {
      type: Sequelize.INTEGER(4),
      allowNull: false,
      comment: "Associated with chargebee expiry_year."
    },
    // postal_code: {
    //   type: Sequelize.STRING(6),
    //   comment: "Just storing as per figma."
    // },
    status: {
      type: Sequelize.ENUM('inactive', 'active'),
      defaultValue: 'inactive',
      comment: "Associated with chargebee status."
    },
    is_primary: {
      type: Sequelize.ENUM('0', '1'),
      defaultValue: '0',
      comment: "Associated with chargebee primary card."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}