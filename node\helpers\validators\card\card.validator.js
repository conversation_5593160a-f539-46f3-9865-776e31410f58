const Joi = require('joi');
const currentYear = new Date().getFullYear();

const cardValidation = Joi.object().keys({
    card_name: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Card name field is required.'
        }),
    card_number: Joi.string()
        .trim()
        .creditCard()
        .required()
        .messages({
            'string.creditCard': 'Please enter a valid credit card number.',
            'any.required': 'Credit card number is required.'
        }),
    expiry_month: Joi.string()
        .trim()
        .regex(/^(0[1-9]|1[0-2])$/) // Regular expression to match valid month format (01 to 12)
        .required()
        .messages({
            'string.pattern.base': 'Expiry month must be in the format MM (e.g., 01 for January)',
            'any.required': 'Expiry month is required'
        }),
    expiry_year: Joi.string()
        .trim()
        .regex(/^\d{4}$/) // Regular expression to match exactly 4 digits
        .required()
        .custom((value, helpers) => {
            if (parseInt(value) < currentYear) {
                return helpers.message('Expiry year must be greater than or equal to current year');
            }
            return value;
        })
        .messages({
            'string.pattern.base': 'Expiry year must be a valid 4-digit year',
            'any.required': 'Expiry year is required'
        }),
    cvv: Joi.string()
        .trim()
        .regex(/^\d{3}$/) // Regular expression to match exactly 3 digits
        .required()
        .messages({
            'string.pattern.base': 'CVV must be exactly 3 digits',
            'any.required': 'CVV is required'
        }),
    // postal_code: Joi.string()
    //     .trim()
    //     .alphanum()
    //     .min(5)
    //     .max(6)
    //     .required()
    //     .messages({
    //         'string.alphanum': 'Postal code must be alphanumeric',
    //         'string.min': 'Postal code must be at least {#limit} characters long',
    //         'string.max': 'Postal code cannot be longer than {#limit} characters',
    //         'any.required': 'Postal code is required'
    //     }),
    is_primary: Joi.string()
        .trim()
        .valid('0', '1').required().messages({
            'any.only': 'is_primary must be either 0 or 1',
            'any.required': 'is_primary is required'
        })
});

module.exports = { cardValidation };
