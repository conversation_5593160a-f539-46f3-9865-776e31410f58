import { WifiIcon } from "../../../assets/Icons";
import useMediaQuery from "../../../hooks/MediaQueryHook";
import StatusCard from "../StatusCard";

const OffBoardingStatus = () => {
  const isDesktop = useMediaQuery("(min-width:1025px)");
  return (
    <>
      <StatusCard
        Icon={<WifiIcon width={isDesktop ? '100%' : 20}/>}
        label="Service discontinued"
        status="Disconnected"
      />
    </>
  );
};

export default OffBoardingStatus;
