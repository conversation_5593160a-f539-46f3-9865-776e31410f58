import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  cancelDate,
  showConfirmPopup,
  showFeedbackPopup,
  showOfferPopup,
  showSelectCancelServiceDatePopup,
  deleteOrder,
} from "../../../store/selectors/cancelServiceSelectors";

import { CancelIcon } from "../../../assets/Icons";
import {
  toggleDeleteOrderPopup,
  setCancelDate,
  toggleShowConfirmPopup,
  toggleShowFeedbackPopup,
  toggleShowOfferPopup,
  toggleShowSelectCancelServiceDatePopup,
} from "../../../store/reducers/cancelServiceReducer";
import Popup from "../../common/Popup";
import ServiceDateSelector from "../service-address/ServiceDateSelector";
import { cancelServiceDateDescription } from "../../../utils/helper";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import FeedBackPopup from "./FeedBackPopup";
import OfferPopup from "./OfferPopup";
import Button from "../../common/Button";

const CancelService = () => {
  const today = new Date();
  const dispatch = useDispatch();
  const date = useSelector(cancelDate);
  const confirmPopup = useSelector(showConfirmPopup);
  const feedbackPopup = useSelector(showFeedbackPopup);
  const offerPopup = useSelector(showOfferPopup);
  const selectCancelServiceDatePopup = useSelector(
    showSelectCancelServiceDatePopup
  );
  const isDeleteOrder = useSelector(deleteOrder);
  const [selectedDate, setSelectedDate] = useState<Date | null>(date);

  // Function to handle confirmation submission
  const handleConfirmationSubmit = () => {
    dispatch(toggleShowConfirmPopup());
  };

  // Function to handle delete confirmation
  const handleDeleteConfirm = () => {
    setSelectedDate(null);
    dispatch(setCancelDate(null));
    dispatch(toggleDeleteOrderPopup());
  };
  return (
    <div className="bg-white shadow-cardShadow05 flex flex-col basis-[calc(50%-20px)] 2xl:p-5 lg:p-3 p-2 gap-5 2xl:gap-10 2xl:rounded-20 rounded-10 justify-between">
      <div className="flex items-center basis-full justify-between text-error">
        <div className="flex">
          <p className="text-xl 2xl:text-[26px] font-medium">Cancel service</p>
        </div>
        <div
          className="flex cursor-pointer items-center justify-center border px-2 py-1.5 border-error 2xl:rounded-[12px] rounded-[8px] 2xl:min-w-[44px] 2xl:min-h-[44px]"
          onClick={() => dispatch(toggleShowSelectCancelServiceDatePopup())}
        >
          <span>
            <CancelIcon />
          </span>
        </div>
      </div>
      {date && (
        <div className="bg-white border border-[#FBC400] flex gap-5 p-2.5 rounded-10">
          <div
            className="flex cursor-pointer items-center justify-center"
            onClick={() => {
              dispatch(toggleDeleteOrderPopup());
            }}
          >
            <CancelIcon />
          </div>
          <div className="text-base font-bold">
            <p>{`Service ${
              date?.getTime() <= today.getTime()
                ? "cancelled as on "
                : "will be cancelled on"
            } ${
              date?.toLocaleString("default", {
                month: "long",
              }) +
              " " +
              date?.getDate() +
              ", " +
              date?.getFullYear()
            }`}</p>
          </div>
        </div>
      )}
      {/* Select Cancelation date */}
      {selectCancelServiceDatePopup && (
        <Popup
          title="Cancel Service"
          closeHandler={() =>
            dispatch(toggleShowSelectCancelServiceDatePopup())
          }
        >
          <ServiceDateSelector
            description={cancelServiceDateDescription}
            selectedDate={selectedDate}
            serviceChangeMessage="Select Service Cancel Date"
            setServiceDateSelectorDate={setSelectedDate}
            handleSubmit={() => {
              dispatch(toggleShowSelectCancelServiceDatePopup());
              dispatch(toggleShowFeedbackPopup());
            }}
            closeHandler={() =>
              dispatch(toggleShowSelectCancelServiceDatePopup())
            }
          />
        </Popup>
      )}
      {isDeleteOrder && (
        <Popup
          title="Delete this order"
          closeHandler={() => dispatch(toggleDeleteOrderPopup())}
        >
          <div className="flex flex-col gap-10">
            <div>
              <p className="text-base">
                Please confirm you wish to delete this order.
              </p>
            </div>
            <div className="flex gap-2.5 lg:gap-5 lg:flex-row flex-col ">
              <div className="basis-full">
                <Button
                  title="Go back"
                  btnType="transparent"
                  type="button"
                  clickEvent={() => dispatch(toggleDeleteOrderPopup())}
                  className="!border-black !text-[#111111]"
                />
              </div>
              <div className="basis-full">
                <Button
                  title="Confirm"
                  type="submit"
                  clickEvent={handleDeleteConfirm}
                />
              </div>
            </div>
          </div>
        </Popup>
      )}
      {/* feedback Popup */}
      {feedbackPopup && <FeedBackPopup />}
      {/* offer popup */}
      {offerPopup && <OfferPopup />}
      {confirmPopup && (
        <ConfirmationMessagePopup
          title={"Cancel Service"}
          message={"Confirming Cancel services"}
          closeHandler={() => {
            dispatch(toggleShowConfirmPopup());
            dispatch(toggleShowOfferPopup());
          }}
          handleSubmit={handleConfirmationSubmit}
        />
      )}
    </div>
  );
};

export default CancelService;
