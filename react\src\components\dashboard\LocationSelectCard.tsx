import {
  _TIMEZONE,
  formatCurrency,
  formatUTCDate,
  removeApostrophes,
} from "../../utils/helper";

import moment from "moment-timezone";
import { LocationIcon } from "../../assets/Icons";
import { LocationSelectCardProps } from "../../typings/typing";
import Button from "../common/Button";

const LocationSelectCard = ({
  location,
  nextHandler,
}: LocationSelectCardProps) => {
  const allowPaymentArrangement =
    (location?.status === "PAYMENT_DUE" || location?.status === "NOT_PAID") &&
    location?.expected_payment_date !== null &&
    moment(location?.expected_payment_date)
      .tz(_TIMEZONE)
      .format("YYYY-MM-DD HH:mm:ss") >
      moment(new Date()).tz(_TIMEZONE).format("YYYY-MM-DD HH:mm:ss");
  return (
    <div
      className={`bg-[#FBF8FF] border border-[#F5EFFF] text-primary rounded-10 max-xl:p-2.5  transition-all duration-300 ${
        !location?.status ? "" : "rounded-20 p-2.5"
      } shadow-md hover:shadow-purple-200 `}
    >
      <div className="flex items-center justify-between lg:gap-8 md:gap-5 gap-4 max-xs:flex-col">
        <div className="flex items-center md:gap-5 gap-2.5 flex-1 lg:max-w-[200px] md:max-w-[200px]">
          <div className="w-5 h-5 flex items-center justify-center">
            <LocationIcon />
          </div>
          <div className="flex flex-col">
            <div className="">
              <div className="flex items-center gap-3">
                <p className="text-sm">Location</p>
              </div>
              <p
                className="lg:text-base text-sm font-medium pt-2 custom-ellipsis"
                title={removeApostrophes(location?.full_address)}
              >
                {removeApostrophes(location?.full_address)}
              </p>
            </div>
            {location?.total_outstanding && (
              <div className="flex flex-col mt-2 gap-2 md:hidden">
                <div className="flex gap-3">
                  <div className="flex items-center gap-3">
                    <p className="text-sm">Invoice Date</p>
                  </div>
                  <p className="lg:text-base text-sm font-medium max-w-full whitespace-nowrap">
                    {location?.createdAt ? formatUTCDate(location?.createdAt): 'NA'}
                </p>
                </div>
                <p className="lg:text-base text-sm font-medium">
                  {formatCurrency(location?.total_outstanding)}
                </p>
                <span
                  className={`${
                    allowPaymentArrangement
                      ? `status-paymentarrangements`
                      : `status-${location?.status}`
                  } p-1.5 font-bold text-[10px] rounded-md capitalize inline w-fit bg-[#FFDBDC] text-error_1`}
                >
                  <p className="leading-[1.15]">
                    {allowPaymentArrangement
                      ? `Payment arrangements for ${formatUTCDate(location?.expected_payment_date)}`
                      : "Outstanding"}
                  </p>
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-col max-md:hidden">
          <div className="flex items-center gap-3">
            <p className="text-sm">Invoice Date</p>
          </div>
          <p className="lg:text-base text-sm font-medium pt-2 max-w-full whitespace-nowrap">
            {location?.createdAt
              ? formatUTCDate(location?.createdAt): 'NA'}
          </p>
        </div>
        <div className="flex items-center 2xl:gap-5 lg:gap-3 gap-2.5 justify-end max-xs:w-full">
          {location?.total_outstanding && (
            <div className="max-md:hidden">
              <p className="lg:text-base text-sm font-medium">
                {formatCurrency(location?.total_outstanding, true)}
              </p>
            </div>
          )}
          {location?.status && (
            <div
              className={`${
                allowPaymentArrangement
                  ? `status-paymentarrangements`
                  : `status-${location?.status}`
              } py-1.5 px-3 lg:text-sm text-xs rounded-20 max-md:hidden capitalize bg-[#FFDBDC] text-error_1 !leading-none  break-words text-center`}
            >
              <p className="leading-[1.15]">{allowPaymentArrangement ? `Payment arrangements for ${formatUTCDate(location?.expected_payment_date)}` : "Outstanding"}</p>
            </div>
          )}
          <div className="max-xs:w-full">
            <Button
              title={location?.status ? "Next" : "Add"}
              className="btn-small !bg-black !border-none !text-white !shadow-[0_4px_7px_0_rgba(0,0,0,0.25)] !py-[10px] !px-[20px]"
              clickEvent={() => nextHandler(location)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationSelectCard;
