import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FailureIcon, SuccessIcon, WarningIcon } from "../../assets/Icons";
import { clearNotifications } from "../../store/reducers/toasterReducer";
import { toasterState } from "../../store/selectors/toasterSelectors";

// Define the structure of a notification
type Notification = {
  id: string;
  message: string;
  type: "success" | "error" | "warning";
};

// Map notification types to corresponding icons
const iconMap = {
  success: <SuccessIcon width={16} height={16} />,
  error: <FailureIcon width={16} height={16} />,
  warning: <WarningIcon width={16} height={16} />,
};

// Map notification types to corresponding colors
const colorMap = {
  success: "success-light",
  warning: "warning-light",
  error: "error-light",
};

const Toast: React.FC = () => {
  const notifications = useSelector(toasterState) as Notification[];
  const [isVisible, setIsVisible] = useState(false);
  const dispatch = useDispatch();

  // Function to handle closing of the toast
  const handleClose = () => {
    dispatch(clearNotifications());
  };

  //  Effect to handle toast visibility and timeout
  useEffect(() => {
    setIsVisible(true);
    const timeout = setTimeout(() => {
      setIsVisible(false);
      dispatch(clearNotifications());
    }, 5000);

    return () => clearTimeout(timeout);
  }, [notifications, dispatch]);

  // Get the latest notification
  const latestNotification = notifications[notifications.length - 1];

  return (
    <>
      {isVisible && latestNotification && (
        <div className="flex z-[9999] flex-col gap-2.5 fixed left-1/2 -translate-x-1/2 bottom-0 py-8 max-sm:w-full md:max-w-full max-w-[280px]">
          <div
            className={`text-base text-primary bg-${
              colorMap[latestNotification?.type]
            } flex relative shadow-cardShadow0 gap-2.5 p-2.5 opacity-99 rounded-10 items-center hover:opacity-100`}
          >
            <div className="flex items-center gap-2 flex-1">
              <div className="Icon">{iconMap[latestNotification?.type]}</div>
              <div className="message">{latestNotification?.message}</div>
            </div>
            <div onClick={() => handleClose()} className="cursor-pointer">
              <span className="font-bold ml-2">x</span>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Toast;
