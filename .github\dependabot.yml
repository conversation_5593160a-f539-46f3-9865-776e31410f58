version: 2 # Specify the version of the Dependabot configuration file
updates:
  - package-ecosystem: "npm" # Use "npm" for Node.js packages; change to "yarn" if using Yarn
    directory: "/react" # Path to the React folder where package.json is located
    schedule:
      interval: "weekly" # Frequency at which to check for updates (daily, weekly, or monthly)
      day: "monday" # Set to check every Monday
      time: "03:30" # 3:30 AM UTC (9:00 AM IST)
    target-branch: "dependabot" # The branch you want Dependabot to operate on

  - package-ecosystem: "npm"
    directory: "/node"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "03:30"
    target-branch: "dependabot"

  - package-ecosystem: "npm"
    directory: "/salesforce-sync"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "03:30"
    target-branch: "dependabot"

  - package-ecosystem: "npm"
    directory: "/webhook"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "03:30"
    target-branch: "dependabot"
