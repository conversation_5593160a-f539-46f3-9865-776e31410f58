const {
  fetchOutstanding,
  makePayment,
  makePaymentArrangements
} = require("../controllers/payment-controller");
const router = require("express").Router();
const Validator = require('../helpers/validators');
const { validator } = require("../middleware/validationMid");

module.exports = (app, basePath) => {
  // Route to fetch outstanding payments
  router.get("/outstanding", validator(Validator.outstandingDetailValidation, "query"), fetchOutstanding);
  // Route to process a payment
  router.post("/", makePayment);
  // Route to make payment arrangements
  router.post("/arrangement", validator(Validator.paymentArrangementValidation), makePaymentArrangements);

  // Use the base path passed from index.js
  app.use(`${basePath}/payment`, router); // Mount the router at the base path
};
