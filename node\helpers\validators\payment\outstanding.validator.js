const Joi = require('joi');

const outstandingDetailValidation = Joi.object().keys({
    type: Joi.string()
        .valid('payment', 'payment_arrangement')
        .required()
        .messages({
            'any.required': 'Type field is required in query parameter.',
            'any.only': 'Type must be either `payment` or `payment_arrangement`.'
        }),
});

module.exports = { outstandingDetailValidation };
