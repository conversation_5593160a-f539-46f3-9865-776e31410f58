
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services/subscription-services.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">services</a> subscription-services.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/645</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/426</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/554</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">const db = <span class="cstat-no" title="statement not covered" >require('../models/index.js');</span>
const CONFIG = <span class="cstat-no" title="statement not covered" >require('../config');</span>
const moment = <span class="cstat-no" title="statement not covered" >require('moment');</span>
const ChargebeeClient = <span class="cstat-no" title="statement not covered" >require("../clients/chargebee/chargebee.js");</span>
const { RESPONSES, RESPONSE_CODES, RESPONSE_MESSAGES } = <span class="cstat-no" title="statement not covered" >require('../utils/ResponseCodes.js');</span>
const PlanServices = <span class="cstat-no" title="statement not covered" >require("../services/plan-services");</span>
const ChargebeeService = <span class="cstat-no" title="statement not covered" >require("./chargebee-services")</span>
const CustomError = <span class="cstat-no" title="statement not covered" >require("../utils/errors/CustomError.js");</span>
const SalesforceService = <span class="cstat-no" title="statement not covered" >require("./salesforce-services");</span>
const callChargebee = <span class="cstat-no" title="statement not covered" >new ChargebeeClient();</span>
const planServices = <span class="cstat-no" title="statement not covered" >new PlanServices();</span>
const ElasticSearch = <span class="cstat-no" title="statement not covered" >require("./../clients/elastic-search/elastic-search");</span>
const { getCurrentAtlanticTime, checkSpeedChange, checkTVChange } = <span class="cstat-no" title="statement not covered" >require('../helpers/privacyAlgorithms.js');</span>
const elasticSearch = <span class="cstat-no" title="statement not covered" >new ElasticSearch();</span>
&nbsp;
class SubscriptionServices {
&nbsp;
  // Method to get renewal estimate
<span class="fstat-no" title="function not covered" >  as</span>ync getRenewalEstimate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("fetch_estimate_renewal_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, renewal_date } = <span class="cstat-no" title="statement not covered" >payload;</span>
<span class="cstat-no" title="statement not covered" >      if (!renewal_date || !customer_subscription_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);</span></span>
&nbsp;
      const customerSubscription = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findByPk(customer_subscription_id);</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscription) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const customerId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customer_details_id;</span>
      const validContact = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });</span>
<span class="cstat-no" title="statement not covered" >      if (!validContact) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const isMonthly = <span class="cstat-no" title="statement not covered" >customerSubscription.subscription_type === "monthly";</span>
      const currentNextBillingDate = <span class="cstat-no" title="statement not covered" >moment(customerSubscription.next_billing_at);</span>
      const lastDateOfRenewal = <span class="cstat-no" title="statement not covered" >isMonthly ? moment(customerSubscription.next_billing_at).add(1, 'month') : moment(customerSubscription.next_billing_at).add(1, 'year');</span>
&nbsp;
      const renewalMoment = <span class="cstat-no" title="statement not covered" >moment(renewal_date);</span>
<span class="cstat-no" title="statement not covered" >      if (!renewalMoment.isBetween(currentNextBillingDate, lastDateOfRenewal, null, '[]')) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'The provided date is invalid. Please select a valid date');</span></span>
&nbsp;
      // Convert renewal date to Unix timestamp
      const unixRenewalDate = <span class="cstat-no" title="statement not covered" >moment(renewal_date).unix();</span>
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.estimateTermEnd(customerSubscription?.cb_subscription_id, unixRenewalDate);</span>
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const data = <span class="cstat-no" title="statement not covered" >{ prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0 };</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = data;</span>
      }
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getRenewalEstimate -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to update renewal billing date
<span class="fstat-no" title="function not covered" >  as</span>ync renewalBillingDate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_renewal_date_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, renewal_date } = <span class="cstat-no" title="statement not covered" >payload;</span>
      const customerSubscription = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findByPk(customer_subscription_id);</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscription) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const customerId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customer_details_id;</span>
      const validContact = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });</span>
<span class="cstat-no" title="statement not covered" >      if (!validContact) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const isMonthly = <span class="cstat-no" title="statement not covered" >customerSubscription.subscription_type === "monthly";</span>
      const currentNextBillingDate = <span class="cstat-no" title="statement not covered" >moment(customerSubscription.next_billing_at);</span>
      const lastDateOfRenewal = <span class="cstat-no" title="statement not covered" >isMonthly ? moment(customerSubscription.next_billing_at).add(1, 'month') : moment(customerSubscription.next_billing_at).add(1, 'year');</span>
&nbsp;
      const renewalMoment = <span class="cstat-no" title="statement not covered" >moment(renewal_date);</span>
<span class="cstat-no" title="statement not covered" >      if (!renewalMoment.isBetween(currentNextBillingDate, lastDateOfRenewal, null, '[]')) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'The provided date is invalid. Please select a valid date');</span></span>
&nbsp;
      const unixRenewalDate = <span class="cstat-no" title="statement not covered" >moment(renewal_date).unix();</span>
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.changeTermEnd(customerSubscription?.cb_subscription_id, unixRenewalDate);</span>
<span class="cstat-no" title="statement not covered" >      if (info?.next_billing_at) {</span>
        // Format next billing date
        const next_billing_at = <span class="cstat-no" title="statement not covered" >moment.unix(info?.next_billing_at).format('YYYY-MM-DD HH:mm:ss');</span>
        // Update CustomerSubscription with next billing date
<span class="cstat-no" title="statement not covered" >        await customerSubscription.update({ next_billing_at });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = info;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices renewalBillingDate -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Retrieves details of a specific internet plan based on plan_id
<span class="fstat-no" title="function not covered" >  as</span>ync getInternetPhonePlanDetails(plan_id, planType, subscriptionType) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (planType !== 'internet' &amp;&amp; planType !== 'phone') <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
      const url = <span class="cstat-no" title="statement not covered" >planType === "internet" ? CONFIG.internet.plans : CONFIG.phone.plans;</span>
      const planDetails = <span class="cstat-no" title="statement not covered" >await planServices.getPlanDetailsFromJSON(url);</span>
      let plan, billingDetails;
<span class="cstat-no" title="statement not covered" >      if (planDetails?.length) {</span>
<span class="cstat-no" title="statement not covered" >        plan = planDetails.find(<span class="fstat-no" title="function not covered" >pl</span>an =&gt; <span class="cstat-no" title="statement not covered" >plan.api_name === plan_id)</span>;</span>
<span class="cstat-no" title="statement not covered" >        if (subscriptionType === "monthly") {</span>
<span class="cstat-no" title="statement not covered" >          if (plan) <span class="cstat-no" title="statement not covered" >billingDetails = planType === "internet" ? plan?.billing?.[0].monthly : plan?.billing_period?.[0].monthly;</span></span>
        } else {
<span class="cstat-no" title="statement not covered" >          if (plan) <span class="cstat-no" title="statement not covered" >billingDetails = planType === "internet" ? plan?.billing?.[0].yearly : plan?.billing_period?.[0].yearly;</span></span>
        }
      }
      let response = <span class="cstat-no" title="statement not covered" >{</span>
        api_name: billingDetails?.api_name,
        price: billingDetails?.price || null
      };
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (planType === "internet" &amp;&amp; plan?.speed) <span class="cstat-no" title="statement not covered" >response.speed = plan.speed;</span></span>
<span class="cstat-no" title="statement not covered" >      return response;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getInternetPhonePlanDetails -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Retrieves estimated cost for updating the internet subscription plan
<span class="fstat-no" title="function not covered" >  as</span>ync getEstimateForInternetUpdate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("fetch_estimate_internet_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, plan_id } = <span class="cstat-no" title="statement not covered" >payload;</span>
&nbsp;
      const customerSubscription = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findByPk(customer_subscription_id);</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscription) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const customerId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customer_details_id;</span>
      const validContact = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });</span>
<span class="cstat-no" title="statement not covered" >      if (!validContact) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      // Retrieve internet plan details based on plan_id
      const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(plan_id, "internet", customerSubscription?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!getPlanDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const reqPayload = <span class="cstat-no" title="statement not covered" >{</span>
        subscription: {
          id: customerSubscription?.cb_subscription_id,
          plan_id: getPlanDetails.api_name,
        },
        prorate: true,
        invoice_immediately: true
      };
&nbsp;
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.getEstimateUpdateSubscription(reqPayload);</span>
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const data = <span class="cstat-no" title="statement not covered" >{</span>
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        }
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = data;</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getSubscriptionUpdateEstimate -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Updates the internet subscription with the specified plan
<span class="fstat-no" title="function not covered" >  as</span>ync updateInternet(reqPayload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_internet_logs", { ...elasticLogObj, request: reqPayload, type: "UPDATE" });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, plan_id } = <span class="cstat-no" title="statement not covered" >reqPayload;</span>
&nbsp;
      let customerSubscription = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findOne({</span>
        include: {
          model: db.CustomerDetails,
          as: 'customerDetails',
          include: {
            model: db.CustomerInternet,
            as: 'customerInternet',
            attributes: ["sf_record_id", "retries", "sf_response_status", "speed_change_order", "plan_speed", "plan_name"],
            required: false,
            include: {
              model: db.InternetElSpeedChangeOrder,
              as: 'internetElSpeedChangeOrder',
              attributes: ['stage', 'response_date', 'sf_record_id', 'speed'],
              required: false
            },
          },
          attributes: ["internet_id", "stage"],
          required: false
        },
        attributes: ["customer_details_id", "cb_subscription_id", "subscription_type", "id"],
        where: { id: customer_subscription_id }
      });
<span class="cstat-no" title="statement not covered" >      customerSubscription = JSON.parse(JSON.stringify(customerSubscription));</span>
&nbsp;
      const customerId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customer_details_id;</span>
      const validContact = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findOne({ where: { contact_id, id: customerId } });</span>
<span class="cstat-no" title="statement not covered" >      if (!validContact) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (customerSubscription?.customerDetails?.customerInternet?.sf_response_status === "failure" || customerSubscription?.customerDetails?.customerInternet?.retries &gt; 3) {</span>
<span class="cstat-no" title="statement not covered" >        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span>
      }
      const sfInternetId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.sf_record_id;</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscription || !sfInternetId) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const stage = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.stage;</span>
<span class="cstat-no" title="statement not covered" >      if (stage != "Online") <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
&nbsp;
      const latestSpeedChangeOrder = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder;</span>
      const latestSpeedChangeOrderStage = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.stage;</span>
      const latestSpeedChangeOrderResDate = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.response_date;</span>
      const latestSpeedChangeOrdersfRecId = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.sf_record_id;</span>
      const currentSpeedChangeOrder = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.speed_change_order;</span>
&nbsp;
      let updateLogic;
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!latestSpeedChangeOrder) <span class="cstat-no" title="statement not covered" >updateLogic = "Create";</span></span>
      else <span class="cstat-no" title="statement not covered" >if (latestSpeedChangeOrder &amp;&amp; latestSpeedChangeOrderStage == "Eligible For Submission" &amp;&amp; latestSpeedChangeOrdersfRecId) <span class="cstat-no" title="statement not covered" >updateLogic = "Update";</span></span>
      else <span class="cstat-no" title="statement not covered" >if (latestSpeedChangeOrder &amp;&amp; latestSpeedChangeOrderResDate) <span class="cstat-no" title="statement not covered" >updateLogic = "Create";</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!updateLogic) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
&nbsp;
      // Retrieve internet plan details based on plan_id
      const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(plan_id, "internet", customerSubscription?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!getPlanDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const payload = <span class="cstat-no" title="statement not covered" >{</span>
        plan_id: getPlanDetails.api_name,
        prorate: true,
        invoice_immediately: true
      };
&nbsp;
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.updateSubscription(customerSubscription.cb_subscription_id, payload);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        // const internetObj = { plan_name: plan_id, plan_price: getPlanDetails?.price, plan_speed: getPlanDetails?.speed };
        const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
<span class="cstat-no" title="statement not covered" >        await chargebeeServiceClient.getInvoicesList(customerSubscription);</span>
        const internetObj = <span class="cstat-no" title="statement not covered" >{};</span>
&nbsp;
        const previousSpeed = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.plan_speed;</span>
        const requestedSpeed = <span class="cstat-no" title="statement not covered" >getPlanDetails?.speed;</span>
        const plan_change_type = <span class="cstat-no" title="statement not covered" >checkSpeedChange(previousSpeed, requestedSpeed);</span>
&nbsp;
        const nextBillingDate = <span class="cstat-no" title="statement not covered" >moment.unix(info.next_billing_at).format('YYYY-MM-DD HH:mm:ss');</span>
        const amount = <span class="cstat-no" title="statement not covered" >info.plan_amount ? info.plan_amount / 100 : 0;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        await db.CustomerSubscriptions.update({ next_billing_at: nextBillingDate, amount }, { where: { id: customer_subscription_id } });</span>
        let equalSpeed = <span class="cstat-no" title="statement not covered" >false;</span>
        // Add internet id in SF
        const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
          let updateStatus;
          let orderId;
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (updateLogic === "Create") {</span>
<span class="cstat-no" title="statement not covered" >            ({ returnStatus: updateStatus, orderId } = await salesforceServiceClient.createNewSpeedOrderinSf(sfInternetId, requestedSpeed));</span>
<span class="cstat-no" title="statement not covered" >            if (updateStatus &amp;&amp; orderId) {</span>
              const speedChangeOrderId = <span class="cstat-no" title="statement not covered" >await this.updateSpeedChangeOrder(orderId);</span>
<span class="cstat-no" title="statement not covered" >              if (speedChangeOrderId) <span class="cstat-no" title="statement not covered" >internetObj.speed_change_order = speedChangeOrderId;</span></span>
            }
          } else {
<span class="cstat-no" title="statement not covered" >            if (previousSpeed == requestedSpeed) {</span>
<span class="cstat-no" title="statement not covered" >              equalSpeed = true;</span>
<span class="cstat-no" title="statement not covered" >              ({ returnStatus: updateStatus } = await salesforceServiceClient.deleteSpeedChangeOrder(latestSpeedChangeOrdersfRecId));</span>
<span class="cstat-no" title="statement not covered" >              if (updateStatus) <span class="cstat-no" title="statement not covered" >internetObj.speed_change_order = null;</span></span>
            } else {
<span class="cstat-no" title="statement not covered" >              ({ returnStatus: updateStatus } = await salesforceServiceClient.updateOrderSpeedinSf(latestSpeedChangeOrdersfRecId, requestedSpeed));</span>
            }
<span class="cstat-no" title="statement not covered" >            await db.InternetElSpeedChangeOrder.update({ speed: requestedSpeed }, { where: { sf_record_id: latestSpeedChangeOrdersfRecId } });</span>
          }
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (updateStatus) {</span>
<span class="cstat-no" title="statement not covered" >            internetObj.sf_response_status = "success";</span>
<span class="cstat-no" title="statement not covered" >            returnStatus.message = RESPONSE_MESSAGES.SUCCESS;</span>
          }
<span class="cstat-no" title="statement not covered" >          returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >          if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus, request: { ...reqPayload, previousSpeed, requestedSpeed, plan_change_type } });</span></span>
        } catch (error) {
<span class="cstat-no" title="statement not covered" >          internetObj.sf_error_log = error?.message;</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;</span>
<span class="cstat-no" title="statement not covered" >          if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message }, request: { ...reqPayload, previousSpeed, requestedSpeed, plan_change_type } });</span></span>
<span class="cstat-no" title="statement not covered" >          await this.callForRollBack(customerSubscription);</span>
        } finally {
<span class="cstat-no" title="statement not covered" >          await db.CustomerInternet.update(internetObj, { where: { id: customerSubscription.customerDetails.internet_id } });</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >          if ((updateLogic === "Create" &amp;&amp; currentSpeedChangeOrder &amp;&amp; internetObj?.speed_change_order &amp;&amp; currentSpeedChangeOrder !== internetObj?.speed_change_order) || (updateLogic === "Update" &amp;&amp; equalSpeed)) <span class="cstat-no" title="statement not covered" >await db.InternetElSpeedChangeOrder.destroy({ where: { id: currentSpeedChangeOrder } });</span></span>
<span class="cstat-no" title="statement not covered" >          if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
        }
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices updateInternet -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Retrieves cost estimate for updating television subscription
<span class="fstat-no" title="function not covered" >  as</span>ync getEstimateForTelevisionUpdate(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("fetch_estimate_tv_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id } = <span class="cstat-no" title="statement not covered" >payload;</span>
&nbsp;
      const customerSubscriptionDetails = <span class="cstat-no" title="statement not covered" >await this.getCustomerSubsDetails(customer_subscription_id, "tv");</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const contactId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.contact_id;</span>
<span class="cstat-no" title="statement not covered" >      if (contactId != contact_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      // Retrieve addon details for television plan
      const responseObj = <span class="cstat-no" title="statement not covered" >await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!responseObj) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
      let addonsArray = <span class="cstat-no" title="statement not covered" >responseObj?.addons;</span>
&nbsp;
      // Include phone plan addon if customer has associated phone
      const phoneId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.phone_id;</span>
<span class="cstat-no" title="statement not covered" >      if (phoneId) {</span>
        const phonePlanAPIName = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name;</span>
        const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;</span>
<span class="cstat-no" title="statement not covered" >        if (account_status === 'ACTIVE') {</span>
          const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(phonePlanAPIName, "phone", customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          addonsArray.push({ id: getPlanDetails.api_name });</span>
        }
      }
&nbsp;
      const reqPayload = <span class="cstat-no" title="statement not covered" >{</span>
        subscription: { id: customerSubscriptionDetails?.cb_subscription_id },
        prorate: true,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };
&nbsp;
      // Call Chargebee API to get estimate for subscription update
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.getEstimateUpdateSubscription(reqPayload);</span>
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const data = <span class="cstat-no" title="statement not covered" >{</span>
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        }
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = data;</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getEstimateForTelevisionUpdate -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Updates television subscription details
<span class="fstat-no" title="function not covered" >  as</span>ync updateTelevision(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_tv_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
      const { customer_subscription_id, plan_name, extra_packages, single_channels, iptv_products } = <span class="cstat-no" title="statement not covered" >payload;</span>
      const customerSubscriptionDetails = <span class="cstat-no" title="statement not covered" >await this.getCustomerSubsDetails(customer_subscription_id, "tv");</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
<span class="cstat-no" title="statement not covered" >      if (customerSubscriptionDetails?.customerDetails?.customerTv?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries &gt; 3) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
&nbsp;
      const contactId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.contact_id;</span>
<span class="cstat-no" title="statement not covered" >      if (contactId != contact_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      // Retrieve addon details for television plan
      const responseObj = <span class="cstat-no" title="statement not covered" >await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!responseObj) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
      let addonsArray = <span class="cstat-no" title="statement not covered" >responseObj?.addons;</span>
&nbsp;
      // Include phone plan addon if customer has associated phone
      const phoneId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.phone_id;</span>
<span class="cstat-no" title="statement not covered" >      if (phoneId) {</span>
        const phonePlanAPIName = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name;</span>
        const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;</span>
<span class="cstat-no" title="statement not covered" >        if (account_status === 'ACTIVE') {</span>
          const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(phonePlanAPIName, "phone", customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          addonsArray.push({ id: getPlanDetails.api_name });</span>
        }
      }
&nbsp;
      const reqPayload = <span class="cstat-no" title="statement not covered" >{</span>
        prorate: true,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };
&nbsp;
      // Call Chargebee API to update subscription
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.updateSubscription(customerSubscriptionDetails?.cb_subscription_id, reqPayload);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) {</span>
        const tvId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.tv_id;</span>
        const type = <span class="cstat-no" title="statement not covered" >tvId ? "UPDATE" : "CREATE";</span>
        let request = <span class="cstat-no" title="statement not covered" >{ requestedPlan: payload }</span>
        let currentPackage;
<span class="cstat-no" title="statement not covered" >        if (tvId) {</span>
          const previousPlan = <span class="cstat-no" title="statement not covered" >{</span>
            plan_name: customerSubscriptionDetails?.customerDetails?.customerTv?.plan_name,
            extra_packages: customerSubscriptionDetails?.customerDetails?.customerTv?.extra_packages,
            single_channels: customerSubscriptionDetails?.customerDetails?.customerTv?.single_channels,
            iptv_products: customerSubscriptionDetails?.customerDetails?.customerTv?.iptv_products
          };
<span class="cstat-no" title="statement not covered" >          request.previousPlan = previousPlan;</span>
<span class="cstat-no" title="statement not covered" >          currentPackage = customerSubscriptionDetails?.customerDetails?.customerTv?.plan_name;</span>
        }
        const plan_change_type = <span class="cstat-no" title="statement not covered" >checkTVChange(currentPackage, plan_name);</span>
<span class="cstat-no" title="statement not covered" >        await elasticSearch.updateDocument(_index, _id, { request, type, plan_change_type });</span>
      }
      ;
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
<span class="cstat-no" title="statement not covered" >        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);</span>
        const tvObj = <span class="cstat-no" title="statement not covered" >{</span>
          plan_name,
          extra_packages: JSON.stringify(extra_packages),
          single_channels: JSON.stringify(single_channels),
          iptv_products: JSON.stringify(iptv_products),
          total_service_cost: responseObj.totalAmount,
        };
&nbsp;
        const message = <span class="cstat-no" title="statement not covered" >await this.saveCustomerTv(tvObj, customerSubscriptionDetails, payload);</span>
&nbsp;
        const nextBillingDate = <span class="cstat-no" title="statement not covered" >moment.unix(info?.next_billing_at).format('YYYY-MM-DD HH:mm:ss');</span>
<span class="cstat-no" title="statement not covered" >        await db.CustomerSubscriptions.update({ next_billing_at: nextBillingDate }, { where: { id: customer_subscription_id } });</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.message = message;</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) {</span>
<span class="cstat-no" title="statement not covered" >          await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span>
        }
<span class="cstat-no" title="statement not covered" >        if (message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices updateTelevision -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync saveCustomerTv(tvObj, customerSubscriptionDetails, payload) {
    const tvId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.tv_id;</span>
    const customerDetailsId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customer_details_id;</span>
    let message = <span class="cstat-no" title="statement not covered" >RESPONSE_MESSAGES.SUCCESS;</span>
<span class="cstat-no" title="statement not covered" >    if (tvId) {</span>
<span class="cstat-no" title="statement not covered" >      payload.orderType = "update";</span>
<span class="cstat-no" title="statement not covered" >      payload.customerTvId = customerSubscriptionDetails?.customerDetails?.customerTv?.sf_record_id;</span>
    } else {
<span class="cstat-no" title="statement not covered" >      payload.orderType = "create";</span>
<span class="cstat-no" title="statement not covered" >      payload.customerSfId = customerSubscriptionDetails?.customerDetails?.sf_record_id;</span>
    }
<span class="cstat-no" title="statement not covered" >    try {</span>
      const orderId = <span class="cstat-no" title="statement not covered" >await this.manageTvOrderinSF(payload);</span>
<span class="cstat-no" title="statement not covered" >      tvObj.sf_response_status = orderId ? "success" : "failure";</span>
<span class="cstat-no" title="statement not covered" >      if (!tvId &amp;&amp; orderId) <span class="cstat-no" title="statement not covered" >tvObj.sf_record_id = orderId;</span></span>
<span class="cstat-no" title="statement not covered" >      tvObj.account_status = 'ACTIVE';</span>
<span class="cstat-no" title="statement not covered" >      tvObj.state_text = 'In Progress';</span>
<span class="cstat-no" title="statement not covered" >      if (tvId) {</span>
<span class="cstat-no" title="statement not covered" >        await db.CustomerTv.update(tvObj, { where: { id: tvId } });</span>
      } else {
        const customerTv = <span class="cstat-no" title="statement not covered" >await db.CustomerTv.create(tvObj);</span>
<span class="cstat-no" title="statement not covered" >        await db.CustomerDetails.update({ tv_id: customerTv.id }, { where: { id: customerDetailsId } });</span>
      }
    } catch (error) {
      const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;</span>
      const tvRollBackObj = <span class="cstat-no" title="statement not covered" >{</span>
        cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
        type: "tv",
        serviceDetails: {
          customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
        },
        subscription_type: customerSubscriptionDetails?.subscription_type
      }
<span class="cstat-no" title="statement not covered" >      if (account_status == 'ACTIVE') <span class="cstat-no" title="statement not covered" >tvRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;</span></span>
<span class="cstat-no" title="statement not covered" >      this.rollbackSubscription(tvRollBackObj);</span>
<span class="cstat-no" title="statement not covered" >      console.log("saveCustomerTv---&gt; ", error?.message);</span>
<span class="cstat-no" title="statement not covered" >      message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;</span>
    }
<span class="cstat-no" title="statement not covered" >    return message;</span>
  }
&nbsp;
  // Retrieves addons details for television plan based on payload
<span class="fstat-no" title="function not covered" >  as</span>ync getAddonsTelevisionPlanDetails(payload, subscriptionType) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { plan_name, extra_packages, single_channels, iptv_products } = <span class="cstat-no" title="statement not covered" >payload;</span>
&nbsp;
      // Retrieve television plan details from plan service
      const result = <span class="cstat-no" title="statement not covered" >await planServices.getPlanDetails({ type: 'tv' });</span>
      const planData = <span class="cstat-no" title="statement not covered" >result?.data?.planDetails;</span>
      const plan = <span class="cstat-no" title="statement not covered" >planData.find(<span class="fstat-no" title="function not covered" >p </span>=&gt; <span class="cstat-no" title="statement not covered" >p.api_name === plan_name)</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!plan) <span class="cstat-no" title="statement not covered" >throw new Error(`Plan with name ${plan_name} not found`);</span></span>
&nbsp;
      const isMonthly = <span class="cstat-no" title="statement not covered" >subscriptionType === "monthly";</span>
      let addons = <span class="cstat-no" title="statement not covered" >[];</span>
      let totalAmount = <span class="cstat-no" title="statement not covered" >0;</span>
&nbsp;
      // Process plan details and calculate total amount
<span class="cstat-no" title="statement not covered" >      if (plan.api_name === plan_name) {</span>
        const planBillingPeriod = <span class="cstat-no" title="statement not covered" >isMonthly ? plan.billing_period[0].monthly : plan.billing_period[0].yearly</span>
<span class="cstat-no" title="statement not covered" >        totalAmount += planBillingPeriod.price;</span>
<span class="cstat-no" title="statement not covered" >        addons.push({ id: planBillingPeriod.api_name });</span>
      }
&nbsp;
      // Process extra packages and add to addons list
<span class="cstat-no" title="statement not covered" >      extra_packages.forEach(<span class="fstat-no" title="function not covered" >ex</span>tra =&gt; {</span>
        const extraPackage = <span class="cstat-no" title="statement not covered" >plan.optional_extra_packages.find(<span class="fstat-no" title="function not covered" >pk</span>g =&gt; <span class="cstat-no" title="statement not covered" >pkg.api_name === extra)</span>;</span>
<span class="cstat-no" title="statement not covered" >        if (extraPackage) {</span>
          const extraPackagebillingPeriod = <span class="cstat-no" title="statement not covered" >isMonthly ? extraPackage.billing_period[0].monthly : extraPackage.billing_period[0].yearly</span>
<span class="cstat-no" title="statement not covered" >          totalAmount += extraPackagebillingPeriod.price;</span>
<span class="cstat-no" title="statement not covered" >          addons.push({ id: extraPackagebillingPeriod.api_name });</span>
        }
      });
&nbsp;
      // Process IPTV products and add to addons list
<span class="cstat-no" title="statement not covered" >      iptv_products.forEach(<span class="fstat-no" title="function not covered" >ip</span>tv =&gt; {</span>
        const iptvProduct = <span class="cstat-no" title="statement not covered" >plan.optional_iptv_products.find(<span class="fstat-no" title="function not covered" >pr</span>od =&gt; <span class="cstat-no" title="statement not covered" >prod.api_name === iptv)</span>;</span>
<span class="cstat-no" title="statement not covered" >        if (iptvProduct) {</span>
          const iptvbillingPeriod = <span class="cstat-no" title="statement not covered" >isMonthly ? iptvProduct.billing_period[0].monthly : iptvProduct.billing_period[0].yearly</span>
<span class="cstat-no" title="statement not covered" >          totalAmount += iptvbillingPeriod.price;</span>
<span class="cstat-no" title="statement not covered" >          addons.push({ id: iptvbillingPeriod.api_name });</span>
        }
      });
&nbsp;
      // Process single channels and add to addons list
      const totalSingleChannels = <span class="cstat-no" title="statement not covered" >single_channels.length;</span>
<span class="cstat-no" title="statement not covered" >      if (totalSingleChannels &gt; 0) {</span>
        const X_PICK_5 = <span class="cstat-no" title="statement not covered" >Math.floor(totalSingleChannels / 5);</span>
        const X_SNGL_CHANNEL = <span class="cstat-no" title="statement not covered" >totalSingleChannels % 5;</span>
&nbsp;
        const singleChannels = <span class="cstat-no" title="statement not covered" >isMonthly ? CONFIG.chargebee.phonePlanDetails.monthlySingleChannels : CONFIG.chargebee.phonePlanDetails.yearlySingleChannels</span>
<span class="cstat-no" title="statement not covered" >        if (X_PICK_5 &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          totalAmount += X_PICK_5 * singleChannels.pick5ChannelPrice;</span>
<span class="cstat-no" title="statement not covered" >          addons.push({ id: singleChannels.pick5ChannelName, quantity: X_PICK_5 });</span>
        }
<span class="cstat-no" title="statement not covered" >        if (X_SNGL_CHANNEL &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >          totalAmount += X_SNGL_CHANNEL * singleChannels.pick1ChannelPrice;</span>
<span class="cstat-no" title="statement not covered" >          addons.push({ id: singleChannels.pick1ChannelName, quantity: X_SNGL_CHANNEL });</span>
        }
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      return { addons, totalAmount };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getAddonsTelevisionPlanDetails -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Retrieves cost estimate for updating phone subscription
<span class="fstat-no" title="function not covered" >  as</span>ync getEstimateForupdatePhone(payload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("fetch_estimate_phone_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, phone_plan_id } = <span class="cstat-no" title="statement not covered" >payload;</span>
      const customerSubscriptionDetails = <span class="cstat-no" title="statement not covered" >await this.getCustomerSubsDetails(customer_subscription_id, "phone");</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const contactId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.contact_id;</span>
<span class="cstat-no" title="statement not covered" >      if (contactId != contact_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(phone_plan_id, "phone", customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!getPlanDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const reqPayload = <span class="cstat-no" title="statement not covered" >{</span>
        subscription: {
          id: customerSubscriptionDetails?.cb_subscription_id,
        },
        prorate: true,
        invoice_immediately: true,
        addons: [
          {
            id: getPlanDetails.api_name
          }
        ]
      };
&nbsp;
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.getEstimateUpdateSubscription(reqPayload);</span>
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const data = <span class="cstat-no" title="statement not covered" >{</span>
          prorated_amount: info?.invoice_estimate?.amount_due ? (info?.invoice_estimate?.amount_due / 100) : 0
        };
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = data;</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices getEstimateForupdatePhone -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Updates phone subscription details
<span class="fstat-no" title="function not covered" >  as</span>ync updatePhone(reqPayload, elasticLogObj, contact_id) {
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_phone_logs", { ...elasticLogObj, request: reqPayload, type: "CREATE", plan_change_type: "UPGRADE" });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const { customer_subscription_id, phone_plan_id, phone_number_type, phone_number } = <span class="cstat-no" title="statement not covered" >reqPayload;</span>
&nbsp;
      const customerSubscriptionDetails = <span class="cstat-no" title="statement not covered" >await this.getCustomerSubsDetails(customer_subscription_id, "phone");</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
&nbsp;
      const contactId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.contact_id;</span>
<span class="cstat-no" title="statement not covered" >      if (contactId != contact_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(phone_plan_id, "phone", customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >      if (!getPlanDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const payload = <span class="cstat-no" title="statement not covered" >{</span>
        prorate: true,
        invoice_immediately: true,
        addons: [
          {
            id: getPlanDetails?.api_name,
          }
        ]
      };
&nbsp;
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.updateSubscription(customerSubscriptionDetails?.cb_subscription_id, payload);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
<span class="cstat-no" title="statement not covered" >        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);</span>
        const customerPhoneObj = <span class="cstat-no" title="statement not covered" >{</span>
          plan_price: getPlanDetails?.price,
          api_name: phone_plan_id,
        };
        // if (info?.status === 'active') customerPhoneObj.account_status = 'ACTIVE';
&nbsp;
        const sf_phone_type = <span class="cstat-no" title="statement not covered" >{ sf_phone_type: phone_number_type };</span>
<span class="cstat-no" title="statement not covered" >        sf_phone_type.phone_number = (phone_number_type === "existing" &amp;&amp; phone_number) ? phone_number : "";</span>
<span class="cstat-no" title="statement not covered" >        customerPhoneObj.sf_phone_type = JSON.stringify(sf_phone_type);</span>
&nbsp;
        const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (customerSubscriptionDetails?.customerDetails?.phone_id &amp;&amp; customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status != "DELETED") {</span>
<span class="cstat-no" title="statement not covered" >          await db.CustomerPhone.update(customerPhoneObj, { where: { id: customerSubscriptionDetails?.customerDetails?.phone_id } });</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.message = RESPONSE_MESSAGES.SUCCESS;</span>
        } else {
          const customerSfId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.sf_record_id;</span>
          const customerPhone = <span class="cstat-no" title="statement not covered" >await db.CustomerPhone.create(customerPhoneObj);</span>
          const updateObject = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >          try {</span>
            const { returnStatus: updateStatus, phoneId } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.createNewPhoneOrderinSf(phone_number_type, phone_number, customerSfId);</span>
<span class="cstat-no" title="statement not covered" >            if (updateStatus &amp;&amp; phoneId) {</span>
              const phoneRes = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getPhoneApiValue(phoneId);</span>
              const { status: phStatus, data: pfData } = <span class="cstat-no" title="statement not covered" >phoneRes;</span>
<span class="cstat-no" title="statement not covered" >              if (phStatus) {</span>
<span class="cstat-no" title="statement not covered" >                updateObject.plan_name = pfData?.Calling_Plan__c;</span>
              }
&nbsp;
<span class="cstat-no" title="statement not covered" >              updateObject.sf_record_id = phoneId;</span>
<span class="cstat-no" title="statement not covered" >              updateObject.sf_response_status = "success";</span>
<span class="cstat-no" title="statement not covered" >              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;</span>
<span class="cstat-no" title="statement not covered" >              await db.CustomerPhone.update(updateObject, { where: { id: customerPhone.id } });</span>
<span class="cstat-no" title="statement not covered" >              await db.CustomerDetails.update({ phone_id: customerPhone.id }, { where: { id: customerSubscriptionDetails.customer_details_id } });</span>
            }
          } catch (error) {
            const phoneRollbackObj = <span class="cstat-no" title="statement not covered" >{</span>
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "phone",
              serviceDetails: {
                customerPhone: customerSubscriptionDetails?.customerDetails?.customerPhone,
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
<span class="cstat-no" title="statement not covered" >            this.rollbackSubscription(phoneRollbackObj);</span>
<span class="cstat-no" title="statement not covered" >            console.log("sf phone update -&gt;", error?.message);</span>
<span class="cstat-no" title="statement not covered" >            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;</span>
          } finally {
<span class="cstat-no" title="statement not covered" >            returnStatus.status = true;</span>
          }
        }
      }
<span class="cstat-no" title="statement not covered" >      if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices updatePhone -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync cancelAddonsSubscription(reqPayload, elasticLogObj, contact_id) {
    const { customer_subscription_id, cancel_type } = <span class="cstat-no" title="statement not covered" >reqPayload;</span>
    const elasticIndices = <span class="cstat-no" title="statement not covered" >cancel_type == "tv" ? "update_tv_logs" : "update_phone_logs";</span>
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument(elasticIndices, { ...elasticLogObj, request: reqPayload, type: "CANCEL", plan_change_type: "DOWNGRADE" });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
&nbsp;
      const customerSubscriptionDetails = <span class="cstat-no" title="statement not covered" >await this.getCustomerSubsDetails(customer_subscription_id, "cancel");</span>
<span class="cstat-no" title="statement not covered" >      if (!customerSubscriptionDetails || !customerSubscriptionDetails?.customerDetails) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      const contactId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.contact_id;</span>
<span class="cstat-no" title="statement not covered" >      if (contactId != contact_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (cancel_type === "tv" &amp;&amp; (customerSubscriptionDetails?.customerDetails?.customerTv?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries &gt; 3))</span>
<span class="cstat-no" title="statement not covered" >        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span>
<span class="cstat-no" title="statement not covered" >      if (cancel_type === "phone" &amp;&amp; (customerSubscriptionDetails?.customerDetails?.customerPhone?.sf_response_status === "failure" || customerSubscriptionDetails?.customerDetails?.customerTv?.retries &gt; 3))</span>
<span class="cstat-no" title="statement not covered" >        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span>
&nbsp;
      let addonsArray = <span class="cstat-no" title="statement not covered" >[];</span>
      let prorateValue = <span class="cstat-no" title="statement not covered" >false;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (customerSubscriptionDetails?.customerDetails?.tv_id &amp;&amp; cancel_type === "phone") {</span>
<span class="cstat-no" title="statement not covered" >        prorateValue = true;</span>
        const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerTv?.account_status;</span>
<span class="cstat-no" title="statement not covered" >        if (account_status === 'ACTIVE') {</span>
          const { plan_name, extra_packages, single_channels, iptv_products } = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerTv;</span>
          const payload = <span class="cstat-no" title="statement not covered" >{</span>
            plan_name: plan_name,
            extra_packages: JSON.parse(extra_packages),
            single_channels: JSON.parse(single_channels),
            iptv_products: JSON.parse(iptv_products)
          };
&nbsp;
          const responseObj = <span class="cstat-no" title="statement not covered" >await this.getAddonsTelevisionPlanDetails(payload, customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          if (!responseObj) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
<span class="cstat-no" title="statement not covered" >          addonsArray = responseObj.addons;</span>
        }
      } else <span class="cstat-no" title="statement not covered" >if (customerSubscriptionDetails?.customerDetails?.phone_id &amp;&amp; cancel_type === "tv") {</span>
        const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;</span>
<span class="cstat-no" title="statement not covered" >        if (account_status === 'ACTIVE') {</span>
          const getPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(customerSubscriptionDetails?.customerDetails?.customerPhone?.api_name, "phone", customerSubscriptionDetails?.subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          addonsArray = [{ id: getPlanDetails.api_name }];</span>
        }
      }
&nbsp;
      const subscriptionPayload = <span class="cstat-no" title="statement not covered" >{</span>
        prorate: prorateValue,
        replace_addon_list: true,
        invoice_immediately: true,
        addons: addonsArray
      };
&nbsp;
      const info = <span class="cstat-no" title="statement not covered" >await callChargebee.updateSubscription(customerSubscriptionDetails.cb_subscription_id, subscriptionPayload);</span>
<span class="cstat-no" title="statement not covered" >      if (info) {</span>
        const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
<span class="cstat-no" title="statement not covered" >        await chargebeeServiceClient.getInvoicesList(customerSubscriptionDetails);</span>
        const requested_cancellation_date = <span class="cstat-no" title="statement not covered" >info?.next_billing_at ? moment.unix(info?.next_billing_at).format('YYYY-MM-DD') : null;</span>
<span class="cstat-no" title="statement not covered" >        if (cancel_type === "phone") {</span>
          const sfPhoneId = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.sf_record_id;</span>
          const updateObject = <span class="cstat-no" title="statement not covered" >{ requested_cancellation_date };</span>
<span class="cstat-no" title="statement not covered" >          try {</span>
            const orderId = <span class="cstat-no" title="statement not covered" >await this.managePhoneOrderinSF(sfPhoneId, info?.next_billing_at);</span>
<span class="cstat-no" title="statement not covered" >            if (orderId) {</span>
<span class="cstat-no" title="statement not covered" >              updateObject.sf_response_status = "success";</span>
              // updateObject.account_status = "DELETED";
<span class="cstat-no" title="statement not covered" >              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;</span>
            }
          } catch (error) {
            const account_status = <span class="cstat-no" title="statement not covered" >customerSubscriptionDetails?.customerDetails?.customerPhone?.account_status;</span>
            const phRollBackObj = <span class="cstat-no" title="statement not covered" >{</span>
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "cancel",
              serviceDetails: {
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
<span class="cstat-no" title="statement not covered" >            if (account_status == 'ACTIVE') <span class="cstat-no" title="statement not covered" >phRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;</span></span>
<span class="cstat-no" title="statement not covered" >            this.rollbackSubscription(phRollBackObj);</span>
<span class="cstat-no" title="statement not covered" >            console.error("cancel tv -&gt;", error)</span>
<span class="cstat-no" title="statement not covered" >            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;</span>
          } finally {
<span class="cstat-no" title="statement not covered" >            returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >            await db.CustomerPhone.update(updateObject, { where: { id: customerSubscriptionDetails?.customerDetails?.phone_id } });</span>
          }
        } else <span class="cstat-no" title="statement not covered" >if (cancel_type === "tv") {</span>
          const cancelObj = <span class="cstat-no" title="statement not covered" >{</span>
            orderType: "cancel",
            customerTvId: customerSubscriptionDetails?.customerDetails?.customerTv?.sf_record_id,
            submitTime: info?.next_billing_at
          };
<span class="cstat-no" title="statement not covered" >          try {</span>
            const orderId = <span class="cstat-no" title="statement not covered" >await this.manageTvOrderinSF(cancelObj);</span>
<span class="cstat-no" title="statement not covered" >            if (orderId) {</span>
<span class="cstat-no" title="statement not covered" >              await db.CustomerTv.update({ account_status: 'REMOVED', requested_cancellation_date, state_text: 'In Progress' }, { where: { id: customerSubscriptionDetails?.customerDetails?.tv_id } });</span>
<span class="cstat-no" title="statement not covered" >              returnStatus.message = RESPONSE_MESSAGES.SUCCESS;</span>
            }
          } catch (error) {
            const tvRollBackObj = <span class="cstat-no" title="statement not covered" >{</span>
              cb_subscription_id: customerSubscriptionDetails?.cb_subscription_id,
              type: "cancel",
              serviceDetails: {
                customerTv: customerSubscriptionDetails?.customerDetails?.customerTv
              },
              subscription_type: customerSubscriptionDetails?.subscription_type
            }
<span class="cstat-no" title="statement not covered" >            if (account_status == 'ACTIVE') <span class="cstat-no" title="statement not covered" >tvRollBackObj.serviceDetails.customerPhone = customerSubscriptionDetails?.customerDetails?.customerPhone;</span></span>
<span class="cstat-no" title="statement not covered" >            this.rollbackSubscription(tvRollBackObj);</span>
<span class="cstat-no" title="statement not covered" >            console.error("cancel TV -&gt;", error, customerSubscriptionDetails?.customerDetails?.tv_id);</span>
<span class="cstat-no" title="statement not covered" >            returnStatus.message = RESPONSE_MESSAGES.SUBSCRIPTION_ERROR;</span>
          } finally {
<span class="cstat-no" title="statement not covered" >            returnStatus.status = true;</span>
          }
        }
      }
<span class="cstat-no" title="statement not covered" >      if (returnStatus?.message == RESPONSE_MESSAGES.SUBSCRIPTION_ERROR) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SUBSCRIPTION_ERROR);</span></span>
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`SubscriptionServices cancelPhone -&gt; `, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getCustomerSubsDetails(customer_subscription_id, type) {
<span class="cstat-no" title="statement not covered" >    try {</span>
&nbsp;
      let phoneAttributeArray = <span class="cstat-no" title="statement not covered" >["api_name", "plan_name", "retries", "sf_response_status", "sf_record_id", "account_status"];</span>
      let tvAttributeArray = <span class="cstat-no" title="statement not covered" >["sf_record_id", "retries", "sf_response_status", "plan_name", 'extra_packages', "single_channels", 'iptv_products', "account_status"]</span>
&nbsp;
      let includeObj = <span class="cstat-no" title="statement not covered" >{</span>
        model: db.CustomerDetails,
        as: 'customerDetails',
        attributes: ["internet_id", "phone_id", "tv_id", "sf_record_id", "contact_id"],
        required: false,
        include: [{
          model: db.CustomerPhone,
          as: 'customerPhone',
          attributes: phoneAttributeArray,
          required: false
        },
        {
          model: db.CustomerTv,
          as: 'customerTv',
          attributes: tvAttributeArray,
          required: false
        }]
      };
&nbsp;
      let customerSubscription = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findOne({</span>
        include: includeObj,
        attributes: ["customer_details_id", "cb_subscription_id", "subscription_type", "id"],
        where: { id: customer_subscription_id }
      });
<span class="cstat-no" title="statement not covered" >      return JSON.parse(JSON.stringify(customerSubscription));</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync manageTvOrderinSF(payload) {
    const { orderType, customerSfId, customerTvId } = <span class="cstat-no" title="statement not covered" >payload;</span>
    const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (orderType == "create") {</span>
<span class="cstat-no" title="statement not covered" >        if (!customerSfId) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);</span></span>
        const { returnStatus: updateStatus, orderId } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.createOrUpdateTVObject(customerSfId, payload, "create");</span>
<span class="cstat-no" title="statement not covered" >        if (updateStatus &amp;&amp; orderId) <span class="cstat-no" title="statement not covered" >return orderId;</span></span>
      } else <span class="cstat-no" title="statement not covered" >if (orderType == "update" || orderType == "cancel") {</span>
<span class="cstat-no" title="statement not covered" >        if (!customerTvId) <span class="cstat-no" title="statement not covered" >new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);</span></span>
        const { returnStatus: updateStatus } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.createOrUpdateTVObject(customerSfId, payload, orderType, customerTvId);</span>
<span class="cstat-no" title="statement not covered" >        if (updateStatus) <span class="cstat-no" title="statement not covered" >return customerTvId;</span></span>
      }
<span class="cstat-no" title="statement not covered" >      return false;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync managePhoneOrderinSF(sfPhoneId, submitTime) {
    const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (!sfPhoneId) <span class="cstat-no" title="statement not covered" >new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);</span></span>
      const { returnStatus: retrnStatus, orderId } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.createPhoneCancelObject(sfPhoneId, submitTime);</span>
<span class="cstat-no" title="statement not covered" >      if (retrnStatus) <span class="cstat-no" title="statement not covered" >return orderId;</span></span>
<span class="cstat-no" title="statement not covered" >      return false;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync updateSpeedChangeOrder(orderId) {
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
      const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
      const orderDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getOrderDetails(orderId, "InternetElSpeedChangeOrder");</span>
<span class="cstat-no" title="statement not covered" >      if (!orderDetails) <span class="cstat-no" title="statement not covered" >return null;</span></span>
&nbsp;
      const sfUpdatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(orderDetails?.LastModifiedDate, "sfUpdate");</span>
      const createdAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(orderDetails.CreatedDate, "sfUpdate");</span>
&nbsp;
      let insertOrderDetails = <span class="cstat-no" title="statement not covered" >{ sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, createdAt, sf_name: orderDetails?.Name };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (orderDetails?.Expected_Completion_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >      if (orderDetails?.Speed__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.speed = orderDetails?.Speed__c;</span></span>
<span class="cstat-no" title="statement not covered" >      if (orderDetails?.Stage__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.stage = orderDetails?.Stage__c;</span></span>
<span class="cstat-no" title="statement not covered" >      if (orderDetails?.Response_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);</span></span>
&nbsp;
      const orderInsert = <span class="cstat-no" title="statement not covered" >await db.InternetElSpeedChangeOrder.create(insertOrderDetails);</span>
<span class="cstat-no" title="statement not covered" >      return orderInsert?.id;</span>
    } catch (error) {
      const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
<span class="cstat-no" title="statement not covered" >      await salesforceServiceClient.deleteSpeedChangeOrder(orderId);</span>
<span class="cstat-no" title="statement not covered" >      console.error("Subscription Service updateSpeedChangeOrder -&gt; ", error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync rollbackSubscription(payload) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { cb_subscription_id, type, serviceDetails, subscription_type } = <span class="cstat-no" title="statement not covered" >payload;</span>
<span class="cstat-no" title="statement not covered" >      if (!serviceDetails || !type || !subscription_type) <span class="cstat-no" title="statement not covered" >return; </span></span>// Early return for missing subscription
&nbsp;
      const updateChargebeeSubscription = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >as</span>ync (payload) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        await callChargebee.updateSubscription(cb_subscription_id, payload);</span>
      };
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (type === 'internet') {</span>
        const { plan_name } = <span class="cstat-no" title="statement not covered" >serviceDetails;</span>
<span class="cstat-no" title="statement not covered" >        if (!plan_name) <span class="cstat-no" title="statement not covered" >return;</span></span>
        const internetPlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(plan_name, type, subscription_type);</span>
<span class="cstat-no" title="statement not covered" >        if (!internetPlanDetails) <span class="cstat-no" title="statement not covered" >return; </span></span>// Early return for missing plan details
&nbsp;
        const internetPayload = <span class="cstat-no" title="statement not covered" >{ plan_id: internetPlanDetails.api_name, prorate: true, invoice_immediately: true };</span>
<span class="cstat-no" title="statement not covered" >        await updateChargebeeSubscription(internetPayload);</span>
&nbsp;
      } else <span class="cstat-no" title="statement not covered" >if (type === 'tv' || type === 'cancel') {</span>
        const { customerTv, customerPhone } = <span class="cstat-no" title="statement not covered" >serviceDetails;</span>
&nbsp;
        let addons = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >        if (customerTv) {</span>
          const { plan_name, extra_packages, single_channels, iptv_products } = <span class="cstat-no" title="statement not covered" >customerTv;</span>
          const tvPayload = <span class="cstat-no" title="statement not covered" >{</span>
            plan_name,
            extra_packages: JSON.parse(extra_packages),
            single_channels: JSON.parse(single_channels),
            iptv_products: JSON.parse(iptv_products)
          };
          const { addons: tvAddOn } = <span class="cstat-no" title="statement not covered" >await this.getAddonsTelevisionPlanDetails(tvPayload, subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          addons.push(tvAddOn);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (customerPhone) {</span>
          const phonePlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(customerPhone.api_name, "phone", subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          if (phonePlanDetails) <span class="cstat-no" title="statement not covered" >addons.push({ id: phonePlanDetails.api_name });</span></span>
        }
&nbsp;
        const reqPayload = <span class="cstat-no" title="statement not covered" >{ prorate: true, replace_addon_list: true, invoice_immediately: true, addons };</span>
<span class="cstat-no" title="statement not covered" >        await updateChargebeeSubscription(reqPayload);</span>
      } else <span class="cstat-no" title="statement not covered" >if (type === 'phone') {</span>
        const { customerPhone } = <span class="cstat-no" title="statement not covered" >serviceDetails;</span>
<span class="cstat-no" title="statement not covered" >        if (customerPhone) {</span>
          const { api_name } = <span class="cstat-no" title="statement not covered" >customerPhone;</span>
<span class="cstat-no" title="statement not covered" >          if (!api_name) <span class="cstat-no" title="statement not covered" >return;</span></span>
          const phonePlanDetails = <span class="cstat-no" title="statement not covered" >await this.getInternetPhonePlanDetails(api_name, type, subscription_type);</span>
<span class="cstat-no" title="statement not covered" >          if (!phonePlanDetails) <span class="cstat-no" title="statement not covered" >return; </span></span>// Early return for missing plan details
&nbsp;
          const phonePayload = <span class="cstat-no" title="statement not covered" >{ prorate: true, invoice_immediately: true, addons: [{ id: api_name }] };</span>
<span class="cstat-no" title="statement not covered" >          await updateChargebeeSubscription(phonePayload);</span>
        } else {
          const { customerTv } = <span class="cstat-no" title="statement not covered" >serviceDetails;</span>
<span class="cstat-no" title="statement not covered" >          if (customerTv) {</span>
            const { customerTv } = <span class="cstat-no" title="statement not covered" >serviceDetails;</span>
            const { plan_name, extra_packages, single_channels, iptv_products } = <span class="cstat-no" title="statement not covered" >customerTv;</span>
            const tvPayload = <span class="cstat-no" title="statement not covered" >{</span>
              plan_name,
              extra_packages: JSON.parse(extra_packages),
              single_channels: JSON.parse(single_channels),
              iptv_products: JSON.parse(iptv_products)
            };
&nbsp;
            const { addons } = <span class="cstat-no" title="statement not covered" >await this.getAddonsTelevisionPlanDetails(tvPayload, subscription_type);</span>
            const reqPayload = <span class="cstat-no" title="statement not covered" >{ prorate: true, replace_addon_list: true, invoice_immediately: true, addons };</span>
<span class="cstat-no" title="statement not covered" >            await updateChargebeeSubscription(reqPayload);</span>
          }
        }
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("Error during rollbackSubscription:", error);</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync callForRollBack(customerSubscription) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const cb_subscription_id = <span class="cstat-no" title="statement not covered" >customerSubscription.cb_subscription_id;</span>
      const subscription_type = <span class="cstat-no" title="statement not covered" >customerSubscription.subscription_type;</span>
      let plan_name = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.plan_name;</span>
      const latestSpeedChangeOrderStage = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.stage;</span>
      const latestSpeedChangeOrderSpeed = <span class="cstat-no" title="statement not covered" >customerSubscription?.customerDetails?.customerInternet?.internetElSpeedChangeOrder?.speed;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (latestSpeedChangeOrderStage == "Eligible For Submission" &amp;&amp; latestSpeedChangeOrderSpeed) {</span>
        const internetPlanDetails = <span class="cstat-no" title="statement not covered" >await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);</span>
<span class="cstat-no" title="statement not covered" >        if (internetPlanDetails?.length) {</span>
          const getPrice = <span class="cstat-no" title="statement not covered" >internetPlanDetails.find(<span class="fstat-no" title="function not covered" >in</span>ternet =&gt; <span class="cstat-no" title="statement not covered" >internet.speed === latestSpeedChangeOrderSpeed)</span>;</span>
<span class="cstat-no" title="statement not covered" >          if (getPrice?.api_name) <span class="cstat-no" title="statement not covered" >plan_name = getPrice.api_name;</span></span>
        }
      }
      const internetRollbackObj = <span class="cstat-no" title="statement not covered" >{</span>
        cb_subscription_id,
        type: "internet",
        serviceDetails: { plan_name },
        subscription_type
      }
<span class="cstat-no" title="statement not covered" >      this.rollbackSubscription(internetRollbackObj);</span>
&nbsp;
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("callForRollBack -&gt; ", error)</span>
    }
  }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = SubscriptionServices;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-18T12:23:47.010Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    