const db = require("../models/index.js");
const { Sequelize } = require("sequelize");
const ChargebeeClient = require("../clients/chargebee/chargebee");
const { RESPONSE_CODES } = require("../utils/ResponseCodes");
const CustomError = require('../utils/errors/CustomError.js');
const { Op } = require('sequelize');
const moment = require('moment');
const SalesforceService = require("./salesforce-services");
const { getCurrentAtlanticTime } = require("../helpers/privacyAlgorithms");
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();

class PaymentServices {
  // Fetch outstanding invoices for a specific customer
  async fetchOutstanding(contact_id, type, customer_subscription_id) {
    try {
      // Fetch the customer details IDs
      const resultData = await db.CustomerDetails.findAll({
        attributes: ["id"],
        where: { contact_id }
      });

      const custDetailsArray = resultData.map(data => data.id);

      // Define the where clause based on the type
      let whereClause = {
        status: ['PAYMENT_DUE', 'NOT_PAID'],
        customer_details_id: {
          [Op.in]: custDetailsArray
        }
      };

      if (type === 'payment_arrangement') {
        whereClause.createdAt = {
          [Op.gte]: moment().subtract(28, 'days').toDate()
        };
        whereClause.sf_record_id = {
          [Op.and]: [
            { [Op.not]: null },
            { [Op.ne]: "" }
          ]
        };
      }

      if (customer_subscription_id) whereClause.customer_subscription_id = customer_subscription_id;

      // Fetch the customer invoices with related customer details and service address
      const data = await db.SubscriptionInvoice.findAll({
        include: [
          {
            model: db.CustomerDetails,
            as: "customerDetail",
            attributes: [],
            include: [
              {
                model: db.CustomerAddresses,
                as: "serviceAddress",
                attributes: []
              }
            ]
          }
        ],
        attributes: [
          "id", "customer_details_id", "customer_subscription_id", "cb_invoice_id", "total_outstanding",
          "expected_payment_date", "status", "createdAt",
          [Sequelize.literal('`customerDetail->serviceAddress`.`full_address`'), 'full_address']
        ],
        where: whereClause,
        order: [["id", "DESC"]],
        raw: true
      });

      const result = { status: true, data };
      return result;
    } catch (error) {
      console.error(`PaymentServices fetch Outstanding -> `, error);
      throw error;
    }
  }

  // Make a payment for a specific invoice using a customer's card
  async makePayment(contact_id, body, elasticLogObj) {
    const { _id, _index } = await elasticSearch.insertDocument("update_payment_logs", { ...elasticLogObj, request: { contact_id, body } });
    try {
      const { invoice_id, card_id } = body;

      const cardDetails = await db.ContactsCardDetails.findOne({ where: { contact_id, id: card_id } });
      if (!cardDetails) {
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: 'Card Details not found' } });
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Card Details not found');
      }

      const invoiceDetails = await db.SubscriptionInvoice.findByPk(invoice_id);
      if (!invoiceDetails) {
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: 'Invoice Details not found' } });
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Invoice Details not found');
      }

      const payload = {
        payment_source_id: cardDetails?.cb_card_id
        // amount: invoiceDetails?.total_outstanding ? (invoiceDetails.total_outstanding * 100) : 0
      }

      const callChargebee = new ChargebeeClient();
      const info = await callChargebee.collectInvoicePayment(invoiceDetails?.cb_invoice_id, payload);
      if (info) {
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: info });
        const { status } = info;
        await invoiceDetails.update({ status });
        const subscriptionData = await callChargebee.getSubscriptions(invoiceDetails?.cb_subscription_id);
        if (subscriptionData.length) {
          const subscriptionDetail = subscriptionData[0];
          const { subscription: { total_dues } } = subscriptionDetail;
          const balance_due = total_dues ? total_dues / 100 : 0.00;
          await db.CustomerSubscriptions.update({ balance_due }, { where: { cb_subscription_id: invoiceDetails?.cb_subscription_id } });
        };
      }
      return { status: true, data: info };
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`PaymentServices makePayment -> `, error);
      throw error;
    }
  }

  // Make payment arrangements using Chargebee API
  async makePaymentArrangements(contact_id, body, elasticLogObj) {
    const { _id, _index } = await elasticSearch.insertDocument("update_payment_arrangements_logs", { ...elasticLogObj, request: { contact_id, body } });
    try {
      const { invoice_id, date } = body;

      const invoiceDetails = await this.fetchInvoiceDetails(invoice_id);

      if (!invoiceDetails?.sf_record_id) {
        if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: 'Salesforce record id not found. Please contact support.' } });
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'An error has occurred. Please contact customer support.');
      }

      const contactId = invoiceDetails?.customerDetail?.contact_id;
      if (contactId != contact_id) throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);

      const expected_payment_date = getCurrentAtlanticTime(date, "sfUpdate");

      if (invoiceDetails?.status === "PAYMENT_DUE") {
        if (!invoiceDetails?.cb_invoice_id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'An error has occurred. Please contact customer support.');
        await this.processPaymentDue(invoiceDetails, expected_payment_date);
      } else if (invoiceDetails?.status === "NOT_PAID") {
        await this.updateExpectedPaymentDate(invoiceDetails, expected_payment_date);
      }

      const result = { status: true, data: "success" };
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: result });
      return result;
    } catch (error) {
      if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
      console.error(`PaymentServices makePaymentArrangements -> `, error);
      throw error;
    }
  }

  async fetchInvoiceDetails(invoice_id) {
    const whereClause = {
      status: ['PAYMENT_DUE', 'NOT_PAID'],
      id: invoice_id,
      createdAt: {
        [Op.gte]: moment().subtract(28, 'days').toDate()
      }
    };

    // const invoiceDetails = await db.SubscriptionInvoice.findOne({ where: whereClause });

    let invoiceDetails = await db.SubscriptionInvoice.findOne({
      include: [
        {
          model: db.CustomerDetails,
          as: "customerDetail",
          attributes: ["contact_id"],
        }
      ],
      attributes: ["id", "customer_details_id", "sf_record_id", "status", "cb_invoice_id"],
      where: whereClause
    });

    if (!invoiceDetails) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Invoice details not found');

    return invoiceDetails;
  }

  async processPaymentDue(invoiceDetails, expected_payment_date) {
    try {
      const callChargebee = new ChargebeeClient();
      const info = await callChargebee.stopDunningInvoice(invoiceDetails.cb_invoice_id);
      if (info) await this.updateExpectedPaymentDate(invoiceDetails, expected_payment_date, "updateStatus");
    } catch (error) {
      console.error("processPaymentDue -> ", error);
      throw error;
    }
  }

  async updateExpectedPaymentDate(invoiceDetails, expected_payment_date, type) {
    try {
      const salesforceServiceClient = new SalesforceService();

      const updateObj = { expected_payment_date };
      if (type === "updateStatus") updateObj.status = "NOT_PAID";
      const { returnStatus: updateStatus } = await salesforceServiceClient.updateExpectedPaymentDate(invoiceDetails.sf_record_id, expected_payment_date);

      if (!updateStatus) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'Failed to change payment arrangement. Please contact support.');

      await invoiceDetails.update(updateObj);
    } catch (error) {
      console.error("updateExpectedPaymentDate -> ", error);
      throw error;
    }
  }
}

module.exports = PaymentServices;
