const { Client } = require('@elastic/elasticsearch');
const CONFIG = require("../../config");
const { getCurrentAtlanticTime } = require('../../helpers/privacyAlgorithms');
const isInsert = true;
const indicesPrefix = CONFIG.LOG_PREFIX || 'dev'

class ElasticSearch {
    // Constructor to initialize the S3 client and bucket name
    constructor() {
        this.client = new Client({
            node: CONFIG.elasticSearch.node,
            auth: {
                apiKey: CONFIG.elasticSearch.apiKey
            }
        });
    }

    async createIndices(indexParams) {
        try {
            return await this.client.indices.create(indexParams);
        } catch (error) {
            if (error.meta && error.meta.body && error.meta.body.error && error.meta.body.error.type === 'resource_already_exists_exception') {
                console.warn(`Index ${indexParams?.index} already exists.`);
            } else {
                console.error('Error creating indices:', error);
            }
        }
    }

    async insertDocument(index, document) {
        if (!isInsert) return { _id: null, _index: null };
        try {
            const CURRENT_DATE = getCurrentAtlanticTime(null, "sfUpdate");
            const prefixedIndex = `${indicesPrefix}_${index}`;

            // Extract the URL from the document
            let url = document?.userinfo?.url;

            // Remove the URL from the userinfo object if it exists
            if (document?.userinfo?.url) delete document.userinfo.url;
            if (Object.keys(document.userinfo).length === 0) delete document.userinfo;

            // Create the updated document with the extracted URL separately
            const updatedDocument = { url, ...document, timestamp: CURRENT_DATE };

            // Insert the updated document into the Elasticsearch index
            return await this.client.index({ index: prefixedIndex, document: updatedDocument });
        } catch (error) {
            console.error('Error insert document:', error);
        }
    }

    async updateDocument(index, id, doc) {
        if (!isInsert) return;
        try {
            if (!index && !id) return;
            doc = { ...doc };
            return await this.client.update({ index, id, doc });
        } catch (error) {
            console.error('Error update document:', error, doc, index, id);
        }
    }

    async deleteDocument(index, id) {
        if (!isInsert) return;
        try {
            return await this.client.delete({ index, id });
        } catch (error) {
            console.error('Error insert document:', error);
        }
    }

    async createLogsPayload({ index, body = null, needType, needPlanChangeType }) {
        if (body == "sync") {
            body = {
                mappings: {
                    properties: {
                        "log.level": { type: 'text' },
                        message: { type: 'text' },
                        timestamp: { type: 'date' },
                        createdRecords: { type: 'object' },
                        updatedRecords: { type: 'object' },
                        deletedRecords: { type: 'object' },
                        error: { type: 'nested' }
                    }
                }
            }
        } else if (body == "webhook") {
            body = {
                mappings: {
                    properties: {
                        "log.level": { type: 'text' },
                        type: { type: 'text' },
                        session_id: { type: 'text' },
                        sf_object: { type: 'text' },
                        sf_object_id: { type: 'text' },
                        request: { type: 'object' },
                        sf_createdAt: { type: 'date' },
                        sf_updatedAt: { type: 'date' },
                        timestamp: { type: 'date' }
                    }
                }
            }
        } else {
            body = {
                mappings: {
                    properties: {
                        url: { type: 'text' },
                        "log.level": { type: 'text' },
                        userinfo: { type: 'object' },
                        request: { type: 'object' },
                        response: { type: 'object' },
                        timestamp: { type: 'date' }
                    }
                }
            }
            if (needType) body.mappings.properties.type = { type: 'keyword' };
            if (needPlanChangeType) body.mappings.properties.plan_change_type = { type: 'keyword' };
        }
        return { index, body };
    }

    async createLogIndices() {
        if (!isInsert) return;
        try {
            this.createLogs({ index: `${indicesPrefix}_login_logs` });
            this.createLogs({ index: `${indicesPrefix}_registration_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_account_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_password_logs`, needType: true });
            this.createLogs({ index: `${indicesPrefix}_cognito_user_creation_logs` });

            // card API Logs
            this.createLogs({ index: `${indicesPrefix}_fetch_card_logs` });
            this.createLogs({ index: `${indicesPrefix}_card_update_logs`, needType: true });

            // billing API
            this.createLogs({ index: `${indicesPrefix}_download_billing_invoice_logs` });

            // Customer API
            this.createLogs({ index: `${indicesPrefix}_logout_logs` });

            // dashboard API
            this.createLogs({ index: `${indicesPrefix}_fetch_user_details_logs` });
            this.createLogs({ index: `${indicesPrefix}_fetch_location_details_logs` });

            // payment API
            this.createLogs({ index: `${indicesPrefix}_update_payment_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_payment_arrangements_logs` });

            // plan API
            this.createLogs({ index: `${indicesPrefix}_update_address_logs`, needType: true });

            // promotions API
            this.createLogs({ index: `${indicesPrefix}_fetch_promotions_list_logs` });
            this.createLogs({ index: `${indicesPrefix}_fetch_promotions_eligible_location_logs` });
            this.createLogs({ index: `${indicesPrefix}_claim_promotions_offer_logs` });

            // subscription API
            this.createLogs({ index: `${indicesPrefix}_fetch_estimate_renewal_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_renewal_date_logs` });
            this.createLogs({ index: `${indicesPrefix}_fetch_estimate_internet_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_internet_logs`, needType: true, needPlanChangeType: true });
            this.createLogs({ index: `${indicesPrefix}_fetch_estimate_tv_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_tv_logs`, needType: true, needPlanChangeType: true });
            this.createLogs({ index: `${indicesPrefix}_fetch_estimate_phone_logs` });
            this.createLogs({ index: `${indicesPrefix}_update_phone_logs`, needType: true, needPlanChangeType: true });

            //sync
            this.createLogs({ index: `${indicesPrefix}_sync_logs`, type: "sync" });

            // webhook 
            this.createLogs({ index: `${indicesPrefix}_webhook_logs`, type: "webhook" });

        } catch (error) {
            console.error('Error creating Log Indices:', error);
        }
    }

    async createLogs(data) {
        try {
            const { index, type, needType, needPlanChangeType } = data;
            const payload = await this.createLogsPayload({ index, body: type, needType, needPlanChangeType });
            this.createIndices(payload);
        } catch (error) {
            console.error('Error create Log Indices:', error);
        }
    }

    async deleteIndices(index) {
        try {
            return await this.client.indices.delete({ index });
        } catch (error) {
            if (error.meta && error.meta.body && error.meta.body.error && error.meta.body.error.type === 'index_not_found_exception') {
                console.warn(`Index ${index} already deleted.`);
            } else {
                console.error('Error delete indices:', error);
            }
        }
    }

    async deleteLogIndices() {
        try {
            this.deleteIndices(`${indicesPrefix}_login_logs`);
            this.deleteIndices(`${indicesPrefix}_registration_logs`);
            this.deleteIndices(`${indicesPrefix}_update_account_logs`);
            this.deleteIndices(`${indicesPrefix}_cognito_user_creation_logs`);
            this.deleteIndices(`${indicesPrefix}_update_password_logs`);

            // card API Logs
            this.deleteIndices(`${indicesPrefix}_fetch_card_logs`);
            this.deleteIndices(`${indicesPrefix}_card_update_logs`);

            // billing API
            this.deleteIndices(`${indicesPrefix}_download_billing_invoice_logs`);

            // Customer API
            this.deleteIndices(`${indicesPrefix}_logout_logs`);

            // dashboard API
            this.deleteIndices(`${indicesPrefix}_fetch_user_details_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_location_details_logs`);

            // payment API
            this.deleteIndices(`${indicesPrefix}_update_payment_logs`);
            this.deleteIndices(`${indicesPrefix}_update_payment_arrangements_logs`);

            // plan API
            this.deleteIndices(`${indicesPrefix}_update_address_logs`);

            // promotions API
            this.deleteIndices(`${indicesPrefix}_fetch_promotions_list_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_promotions_eligible_location_logs`);
            this.deleteIndices(`${indicesPrefix}_claim_promotions_offer_logs`);

            // subscription API
            this.deleteIndices(`${indicesPrefix}_fetch_estimate_renewal_logs`);
            this.deleteIndices(`${indicesPrefix}_update_renewal_date_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_estimate_internet_logs`);
            this.deleteIndices(`${indicesPrefix}_update_internet_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_estimate_tv_logs`);
            this.deleteIndices(`${indicesPrefix}_update_tv_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_estimate_phone_logs`);
            this.deleteIndices(`${indicesPrefix}_update_phone_logs`);

            // sync logs
            this.deleteIndices(`${indicesPrefix}_sync_logs`);

            // Needs to be delete (will remove later once properly checked from elastic search)
            //TODO

            // referral API
            this.deleteIndices(`${indicesPrefix}_fetch_referrals_details_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_referral_list_logs`);
            this.deleteIndices(`${indicesPrefix}_card_fetch_logs`);
            this.deleteIndices(`${indicesPrefix}_account_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_outstanding_logs`);
            this.deleteIndices(`${indicesPrefix}_location_fetch_logs`);
            this.deleteIndices(`${indicesPrefix}_statements_fetch_logs`);
            this.deleteIndices(`${indicesPrefix}_invoice_download_logs`);
            this.deleteIndices(`${indicesPrefix}_customer_details_logs`);
            this.deleteIndices(`${indicesPrefix}_user_details_logs`);
            this.deleteIndices(`${indicesPrefix}_location_details_logs`);
            this.deleteIndices(`${indicesPrefix}_make_payment_logs`);
            this.deleteIndices(`${indicesPrefix}_make_payment_arrangements_logs`);
            this.deleteIndices(`${indicesPrefix}_manage_plan_logs`);
            this.deleteIndices(`${indicesPrefix}_get_plan_details_logs`);
            this.deleteIndices(`${indicesPrefix}_fetch_eligible_location_logs`);
            this.deleteIndices(`${indicesPrefix}_claim_offer_logs`);
            this.deleteIndices(`${indicesPrefix}_get_renewal_estimate_logs`);
            this.deleteIndices(`${indicesPrefix}_estimate_internet_update_logs`);
            this.deleteIndices(`${indicesPrefix}_get_estimate_tv_update_logs`);
            this.deleteIndices(`${indicesPrefix}_get_phone_estimate_logs`);
            this.deleteIndices(`${indicesPrefix}_renewal_billing_date_logs`);
            this.deleteIndices(`${indicesPrefix}_reset_password_logs`);
            this.deleteIndices(`${indicesPrefix}_forgot_password_logs`);
            this.deleteIndices(`${indicesPrefix}_card_add_logs`);
            this.deleteIndices(`${indicesPrefix}_card_delete_logs`);
            this.deleteIndices(`${indicesPrefix}_password_reset_logs`);
            this.deleteIndices(`${indicesPrefix}_cancel_addons_subscription_logs`);

            //webhook
            this.deleteIndices(`${indicesPrefix}_webhook_logs`);
            //sync
            this.deleteIndices(`${indicesPrefix}_sync_contacts_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_customers_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_internets_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_service_address_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_mailing_address_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_tv_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_phone_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_promotions_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_promotion_receipts_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_subscriptions_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_referrals_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_orders_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_tech_appointment_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_shipping_order_logs`, "sync");
            this.deleteIndices(`${indicesPrefix}_sync_subscription_invoice_logs`, "sync");

        } catch (error) {
            console.error('Error creating Log Indices:', error);
        }
    }
}

module.exports = ElasticSearch;