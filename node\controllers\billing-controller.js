const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const BillingServices = require("../services/billing-services");
const billingServices = new BillingServices();

class BillingController {

    /**
     * Fetches the billing location details for a user.
     * 
     * @param {Object} req - The request object containing user details.
     * @param {Object} res - The response object for sending the result.
     * @param {Function} next - The next middleware function.
     */

    async fetchLocation(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await billingServices.fetchLocation(id, req.elasticLogObj);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Billing Controller fetch Location ->`, error);
            next(error);
        }
    }

    /**
    * Fetches billing statements for a given customer.
    * 
    * @param {Object} req - The request object containing parameters and query.
    * @param {Object} res - The response object for sending the result.
    * @param {Function} next - The next middleware function.
    */

    async fetchStatements(req, res, next) {
        try {
            const { custDetailsId } = req.params;
            const { id } = req.userDetails;
            const result = await billingServices.fetchStatements(custDetailsId, req.query, req.elasticLogObj, id);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Billing Controller fetch Statements ->`, error);
            next(error);
        }
    }

    /**
     * Downloads an invoice for a given invoice ID.
     * 
     * @param {Object} req - The request object containing invoice ID.
     * @param {Object} res - The response object for sending the result.
     * @param {Function} next - The next middleware function.
     */

    async downloadInvoice(req, res, next) {
        try {
            const { invoiceId } = req.params;
            const { id } = req.userDetails;
            const result = await billingServices.downloadInvoice(invoiceId, req.elasticLogObj, id);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Billing Controller download Invoice ->`, error);
            next(error);
        }
    }
}

// Export an instance of the BillingController class
module.exports = new BillingController();