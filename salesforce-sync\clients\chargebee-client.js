const CONFIG = require("../config")
const Chargebee = require("chargebee");
const moment = require('moment');
const INTERVAL = 1 //day

class ChargebeeClient {
  constructor() {
    // Initialize Chargebee configuration using values from CONFIG
    this.site = CONFIG.chargebee.site;
    this.api_key = CONFIG.chargebee.api_key;

    this.chargebee = new Chargebee({
      site: this.site,
      apiKey: this.api_key,
    });
  }

  // Method to get a list of invoices for a specific subscription
  async getInvoicesList(offset) {
    try {

      const now = moment();
      const dateBefore = now.subtract(INTERVAL, 'days');
      const unixTimestamp = dateBefore.unix();
      const operator = 'after';
      const payload = {
        [`updated_at[${operator}]`]: unixTimestamp,
        limit: 100,
        offset: offset
      };

      const result = await this.chargebee.invoice.list(payload);
      return result;
    } catch (error) {
      console.error("Chargebee service get invoice details -> ", error);
    }
  }

  async getSubscriptions(subscriptionId) {
    try {
      const operator = 'is';
      const payload = {
        [`id[${operator}]`]: subscriptionId,
        limit: 1,
      };

      const result = await this.chargebee.subscription.list(payload)

      // Check if result.list exists
      // if (!result?.list || result.list.length === 0) {
      //   throw new Error(`Failed to retrieve subscriptions for subscription ID: ${subscriptionId}`);
      // }

      return result?.list;
    } catch (error) {
      console.error(`Chargebee get subscriptions error->`, error);
      throw error;
    }
  }

  async getSubInvoicesList(subscriptionId, offset) {
    try {
      const operator = 'is';
      const payload = {
        [`subscription_id[${operator}]`]: subscriptionId,
        limit: 100,
        offset: offset
      };

      const result = await this.chargebee.invoice.list(payload);
      
      return result;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = ChargebeeClient;
