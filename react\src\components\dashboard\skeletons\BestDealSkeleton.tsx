import React from "react";
import ContentLoader from "react-content-loader";

const BestDealSkeleton: React.FC = (props) => (
  <ContentLoader
    width={"100%"}
    height={"450"}
    backgroundColor="#f5f5f5"
    foregroundColor="#dbdbdb"
    {...props}
  >
    {/* //left bar */}
    <rect x="4" y="8" rx="10" ry="10" width="8" height="435" />
    {/* bottom bar*/}
    <rect x="7" y="435px" rx="10" ry="10" width="320" height="8" />
    {/* top bar*/}
    <rect x="5" y="8" rx="10" ry="10" width="435" height="7" />
    {/* right bar*/}
    <rect x="312" y="9" rx="10" ry="10" width="8" height="435" />
    {/* Know More */}
    <rect x="20" y="355" rx="5" ry="5" width="280" height="60" />
  </ContentLoader>
);

export default BestDealSkeleton;
