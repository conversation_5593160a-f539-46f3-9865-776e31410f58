import React from "react";
import ContentLoader from "react-content-loader";

const ReferralHistorySkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      width={"100%"}
      height={450}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {/* main container */}
      {/* //left bar */}
      <rect x="10" y="28" rx="5" ry="3" width="8" height="400" />
      {/* bottom bar*/}
      <rect x="10" y="419" rx="5" ry="3" width={"100%"} height="8" />
      {/* top bar*/}
      <rect x="10" y="28" rx="5" ry="3" width={"100%"} height="7" />
      {/* right bar*/}
      <rect x="99.50%" y="28" rx="5" ry="3" width="8" height="400" />

      {[0, 1, 2, 3].map((item, index) => {
        const spacing = 60 + item * 64 + item * 20;
        const textSpacing = spacing + 30;
        return (
          <React.Fragment key={index}>
            {/* Created On */}
            <rect x="50px" y={textSpacing} ry="5" width="20%" height="12" />
            {/* Referral Amout  */}
            <rect x="15%" y={textSpacing} ry="5" width="20%" height="12" />

            {/*Installed on  */}
            <rect x="45%" y={textSpacing} ry="5" width="20%" height="12" />
            {/* Credit Added on*/}
            <rect x="75%" y={textSpacing} ry="5" width="20%" height="12" />
          </React.Fragment>
        );
      })}
    </ContentLoader>
  );
};

export default ReferralHistorySkeleton;
