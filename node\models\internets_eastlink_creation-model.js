const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("internets_eastlink_creation_order", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    shipping_id: {
      type: Sequelize.INTEGER(11),
      references: {
        model: 'creation_order_shipping',
        key: 'id'
      },
      comment: "Foreign key mapping to the 'Id' in 'creation_order_shipping'"
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      allowNull: false,
      comment: "Salesforce record ID"
    },
    record_type: {
      type: Sequelize.ENUM('None', 'Add Order', 'Move Order', 'Disconnect Order', 'Speed Order', 'Name Change Order', 'Vacation Mode Order'),
      defaultValue: 'None',
      comment: "This refers to the 'RecordType.Name' field in 'Order__c' within Salesforce."
    },
    install_date: {
      type: Sequelize.DATEONLY,
      comment: "This maps to the Install_Date__c in Salesforce."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the LastModifiedDate in Salesforce."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'internets_eastlink_creation_order', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}