import React from "react";
import ContentLoader from "react-content-loader";

const StatementSkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      width={"100%"}
      height={120}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {/* main container */}
      {/* //left bar */}
      <rect x="8" y="8" rx="5" ry="3" width="8" height="100" />
      {/* bottom bar*/}
      <rect x="8" y="100" rx="5" ry="3" width={"930"} height="8" />
      {/* top bar */}
      <rect x="8" y="8" rx="5" ry="3" width={"930"} height="7" />
      {/* right bar*/}
      <rect x="930" y="9" rx="5" ry="3" width="8" height="100" />

      {/* location icon */}
      <rect x="42.5" y="35" rx="5" ry="5" width="29" height="25" />
      {/* address */}
      <rect x="100" y="35" rx="5" ry="3" width="50" height="9" />
      {/* address */}
      <rect x="100" y="55" rx="5" ry="3" width="50" height="22" />
    </ContentLoader>
  );
};

export default StatementSkeleton;
