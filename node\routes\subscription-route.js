// Importing necessary controller functions from the subscription controller
const { 
  getRenewalEstimate, 
  renewalBillingDate, 
  getEstimateForInternetUpdate, 
  updateInternet, 
  getEstimateForTelevisionUpdate, 
  updateTelevision, 
  getEstimateForupdatePhone, 
  updatePhone,
  cancelAddonsSubscription 
} = require("../controllers/subscription-controller");

const router = require("express").Router();
const Validator = require('../helpers/validators')
const { validator } = require("../middleware/validationMid");

module.exports = (app, basePath) => {
  // Defining route for getting a renewal estimate
  router.post("/estimate-renewal", validator(Validator.subscriptionRenewalValidation), getRenewalEstimate);
  // Defining route for getting the renewal billing date
  router.post("/renewal", validator(Validator.subscriptionRenewalValidation), renewalBillingDate);

  // Defining route for getting an estimate for internet update
  router.post("/estimate-internet-update", validator(Validator.internetUpdateValidation),  getEstimateForInternetUpdate);
  // Defining route for updating internet subscription
  router.post("/internet-update", validator(Validator.internetUpdateValidation), updateInternet);

  // Defining route for getting an estimate for television update
  router.post("/estimate-television-update",validator(Validator.televisionUpdateValidation), getEstimateForTelevisionUpdate);
  // Defining route for updating television subscription
  router.post("/television-update",validator(Validator.televisionUpdateValidation), updateTelevision);

  // Defining route for getting an estimate for phone update
  router.post("/estimate-phone-update", validator(Validator.phoneUpdateValidation), getEstimateForupdatePhone);
  // Defining route for updating phone subscription
  router.post("/phone-update",validator(Validator.phoneUpdateValidation), updatePhone);

  // Defining route for cancel phone and tv subscription
  router.post("/cancel",validator(Validator.cancelValidation), cancelAddonsSubscription);

  // Mounting the router at the base path with '/subscription'
  app.use(`${basePath}/subscription`, router); 
};
