const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("internets_eastlink_move_order", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      allowNull: false,
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    requested_install_date: {
      type: Sequelize.DATEONLY,
      comment: "This maps to the Requested_Install_Date__c in Salesforce."
    },
    requested_move_date: {
      type: Sequelize.DATEONLY,
      comment: "This maps to the Requested_Move_Date__c in Salesforce."
    },
    install_date: {
      type: Sequelize.DATEONLY,
      comment: "This maps to the Install_Date__c in Salesforce."
    },
    install_time: {
      type: Sequelize.STRING(30),
      comment: "This maps to the Install_Time__c in Salesforce."
    },
    submit_date: {
      type: Sequelize.DATE,
      comment: "This maps to the Submit_Date__c in Salesforce."
    },
    stage: {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: "This maps to the Stage__c in Salesforce."
    },
    reject_reason: {
      type: Sequelize.TEXT,
      comment: "This maps to the Order_Reject_Reason__c in Salesforce."
    },
    reject_reason_solved: {
      type: Sequelize.ENUM('true', 'false'),
      defaultValue: 'false',
      comment: "This maps to the Order_Reject_Reason_Solved__c in Salesforce."
    },
    unit: {
      type: Sequelize.STRING(50),
      comment: "This maps to the Service_Suite_Unit__c in Salesforce."
    },
    street_number: {
      type: Sequelize.STRING(50),
      comment: "This maps to the Service_Street_Number__c in Salesforce."
    },
    street_name: {
      type: Sequelize.STRING(50),
      comment: "This maps to the Service_Street_Name__c in Salesforce."
    },
    city: {
      type: Sequelize.STRING(30),
      comment: "This maps to the Service_City_Town__c in Salesforce."
    },
    province: {
      type: Sequelize.STRING(30),
      comment: "This maps to the Service_Province__c in Salesforce."
    },
    postal_code: {
      type: Sequelize.STRING(8),
      comment: "This maps to the Service_Postal_Code__c in Salesforce."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "This maps to the LastModifiedDate in Salesforce."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "This maps to the CreatedDate in Salesforce."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'internets_eastlink_move_order', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}