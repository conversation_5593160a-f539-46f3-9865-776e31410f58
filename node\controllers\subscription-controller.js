const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const SubscriptionService = require("../services/subscription-services");
const subscriptionService = new SubscriptionService();

class SubscriptionController {

    // Method to get renewal estimate
    async getRenewalEstimate(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await subscriptionService.getRenewalEstimate(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController getRenewalEstimate ->`, error);
            next(error);
        }
    }

    // Method to handle renewal billing date
    async renewalBillingDate(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await subscriptionService.renewalBillingDate(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController renewalBillingDate ->`, error);
            next(error);
        }
    }

    // Method to get estimate for internet update
    async getEstimateForInternetUpdate(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await subscriptionService.getEstimateForInternetUpdate(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController getEstimateForInternetUpdate -> `, error);
            next(error);
        }
    }

    // Method to update internet subscription
    async updateInternet(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, message } = await subscriptionService.updateInternet(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ status: RESPONSE_CODES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController updateInternet -> `, error);
            next(error);
        }
    }

    // Method to get estimate for television update
    async getEstimateForTelevisionUpdate(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await subscriptionService.getEstimateForTelevisionUpdate(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController getEstimateForTelevisionUpdate -> `, error);
            next(error);
        }
    }

    // Method to update television subscription
    async updateTelevision(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, message } = await subscriptionService.updateTelevision(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ status: RESPONSE_CODES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController updateTelevision -> `, error);
            next(error);
        }
    }

    // Method to get estimate for phone update
    async getEstimateForupdatePhone(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await subscriptionService.getEstimateForupdatePhone(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController getEstimateForupdatePhone ->`, error);
            next(error);
        }
    }

    // Method to update phone subscription
    async updatePhone(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, message } = await subscriptionService.updatePhone(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ status: RESPONSE_CODES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController updatePhone -> `, error);
            next(error);
        }
    }

    // Method to remove phone/tv subscription
    async cancelAddonsSubscription(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, message } = await subscriptionService.cancelAddonsSubscription(req.body, req.elasticLogObj, id);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ status: RESPONSE_CODES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`SubscriptionController cancelPhone -> `, error);
            next(error);
        }
    }
}

// Export the controller as a singleton instance
module.exports = new SubscriptionController();