const Joi = require('joi');

const cognitoCreateUserValidation = Joi.object().keys({
  email: Joi.string()
    .trim()
    .email({ minDomainSegments: 2 })
    .label("email")
    .min(5) // Minimum length for email
    .max(96) // Maximum length for email
    .required()
    .messages({
      'any.required': 'Email field is required.',
      'string.email': 'Invalid email format.',
      'string.min': 'Email must be at least {#limit} characters long.',
      'string.max': 'Email cannot exceed {#limit} characters.'
    })
});

module.exports = { cognitoCreateUserValidation };
