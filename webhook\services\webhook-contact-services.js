const { getCurrentAtlanticTime } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
class ContactWebhookServices {
    async getContactDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get contact details -> ", error);
        }
    }

    async checkAndManageDetails(contactDetails, _id, _index) {
        try {
            if (!Array.isArray(contactDetails)) contactDetails = [contactDetails];
            for (const contactDetail of contactDetails) {
                const { Id: sf_record_id, Email } = contactDetail;
                if (!sf_record_id) {
                    if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": "ERROR", message: "Salesforce record id not found." });
                    return;
                };

                const { contactExist } = await this.checkContactExist(sf_record_id);
                if (!contactExist) {
                    if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": "ERROR", message: `The contact with the email address - ${Email} and salesforce record id - ${sf_record_id} does not exist in database.` });
                    return;
                }

                await this.updateContacts(contactDetail);
                if (_id && _index) elasticSearch.updateDocument(_index, _id, { type: "UPDATE" });
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async updateContacts(contactDetail) {
        try {
            const {
                Id,
                FirstName,
                LastName,
                Company_Name__c,
                Primary_Phone__c,
                Alternate_Phone_Number__c,
                Contact_Name_if_different_than_end_user__c,
                Sticky_Sender__c,
                Referral_Link_Full__c,
                Account_Chargebee_Customer_ID__c,
                Total_Referrals__c,
                Total_Paid_Referrals__c,
                Total_Earned_Referrals__c,
                LastModifiedDate
            } = contactDetail;
    
            // Prepare values
            const sf_record_id = this.cleanValue(Id, 'cleanName');
            const firstName = this.cleanValue(FirstName, 'cleanName');
            const lastName = this.cleanValue(LastName, 'cleanName');
            const companyName = this.cleanValue(Company_Name__c, 'cleanName');
            const cellPhone = this.cleanValue(Primary_Phone__c, 'cleanPhone');
            const secondaryPhone = this.cleanValue(Alternate_Phone_Number__c, 'cleanPhone');
            const additionalName = this.cleanValue(Contact_Name_if_different_than_end_user__c);
            const sticky_sender = this.cleanValue(Sticky_Sender__c);
            const referralLink = this.cleanValue(Referral_Link_Full__c);
            const cbCustomerId = this.cleanValue(Account_Chargebee_Customer_ID__c);
            const totalReferral = this.cleanValue(Total_Referrals__c, 'cleanPrice');
            const totalPaidReferral = this.cleanValue(Total_Paid_Referrals__c, 'cleanPrice');
            const totalEarnedReferral = this.cleanValue(Total_Earned_Referrals__c, 'cleanPrice');
            const formatedDate = getCurrentAtlanticTime(LastModifiedDate);
            const sfUpdatedAt = this.cleanValue(formatedDate);
            const updatedTime = this.cleanValue(getCurrentAtlanticTime());
    
            let sf_name;
    
            if (FirstName && LastName) {
                sf_name = `${FirstName} ${LastName}`;
            } else if (FirstName) {
                sf_name = `${FirstName}`;
            } else if (LastName) {
                sf_name = `${LastName}`;
            } else {
                sf_name = null;
            }
    
            // SQL query with placeholders
            const updateStatement = `
                UPDATE contacts
                SET 
                    updatedAt = ?,
                    first_name = ?,
                    last_name = ?,
                    sf_name = ?,
                    company_name = ?,
                    cell_phone = ?,
                    secondary_phone = ?,
                    additional_name = ?,
                    sticky_sender = ?,
                    referral_link = ?,
                    cb_customer_id = ?,
                    total_referral = ?,
                    total_paid_referral = ?,
                    total_earned_referral = ?,
                    sf_updatedAt = ?
                WHERE sf_record_id = ?;
            `;
    
            // Parameters for the query
            const params = [
                updatedTime,
                firstName,
                lastName,
                sf_name,
                companyName,
                cellPhone,
                secondaryPhone,
                additionalName,
                sticky_sender,
                referralLink,
                cbCustomerId,
                totalReferral,
                totalPaidReferral,
                totalEarnedReferral,
                sfUpdatedAt,
                sf_record_id
            ];
    
            // Execute the query
            await this.executeQuery(updateStatement, params);
        } catch (error) {
            console.error("SalesforceService bulkUpdateContacts -> ", error);
        }
    }

    cleanValue(value, type) {
        if (value === undefined || value === null) return null;

        if (type === "cleanPhone") value = value.replace(/\D/g, '');
        if (type === "cleanPrice") value = isNaN(value) ? 0 : value;

        if (type === "cleanName" && typeof value === 'string') {
            value = value.replace(/['"`]/g, ''); // Remove single quotes, double quotes, and backticks
        }

        return typeof value === 'string' ? (value == '' ? null : `${value}`) : value;
    }

    async checkContactExist(sf_record_id) {
        try {
            const query = `SELECT id FROM contacts WHERE sf_record_id = '${sf_record_id}'`;

            const res = await this.executeQuery(query);
            return {
                contactExist: res.length > 0,
                contactId: res.length ? res[0].id : null
            };
        } catch (error) {
            console.error("Check User Exist -> ", error);
            return {
                contactExist: 0,
                contactId: null
            };
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }
}

module.exports = ContactWebhookServices;
