const { fetchDetails, fetchReferralList} = require("../controllers/referral-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// GET endpoint for fetching referral details
	router.get("/details", fetchDetails);
	// GET endpoint for fetching referral lists
	router.get("/list", fetchReferralList);
	
	// Use the base path passed from index.js
	app.use(`${basePath}/referrals`, router); // Mount the router at the base path
};