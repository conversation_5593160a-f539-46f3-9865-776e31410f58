import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";

import { AddHomePhoneProps } from "../../../typings/typing";
import Button from "../../common/Button";
import HomePhonePlanCard from "./HomePhonePlanCard";
import InternetPlanCardSkeleton from "../internet/skeletons/InternetPlanCardSkeleton";
import { addNotification } from "../../../store/reducers/toasterReducer";
import { homePhoneState } from "../../../store/selectors/homePhoneSelectors";
import { logout } from "../../../store/reducers/authenticationReducer";
import { useGetPlanDetailsMutation } from "../../../services/api";
import { useNavigate } from "react-router-dom";

const AddHomePhone: React.FC<AddHomePhoneProps> = ({
  homePhonePlan,
  handleHomePhonePlanChange,
  handleSubmit,
  handleCancelService,
  closeHandler,
  cancelLoading = false,
}) => {
  const { haveHomePhonePlan } = homePhonePlan;
  const homePlan = useSelector(homePhoneState);
  const [getPlans, plansLoading] = useGetPlanDetailsMutation();
  const [phoneList, setPhoneList] = useState<any>({});
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Fetch phone list on component mount
  useEffect(() => {
    handleGetPhone();
  }, []);

  // Function to fetch phone list
  const handleGetPhone = async () => {
    try {
      const query = {
        type: "phone",
      };
      const response = await getPlans(query).unwrap();
      if (response.status === 200) {
        setPhoneList(response?.data);
      }
    } catch (error:any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle home plan selection
  const handleHomePlanSelect = (plan?: object) => {
    handleHomePhonePlanChange("isPlanRemoved", haveHomePhonePlan);
    handleHomePhonePlanChange("haveHomePhonePlan", plan ? plan : null);
  };

  return (
    <div className="flex flex-col gap-5 xl:gap-10">
      <div className="">
        <p className="text-base">
          Thanks for your interest in home phone. Our home phone is super cool
          and offers a ton of features for only $10 a month. One of my favorite
          is being able to call almost anywhere in North America free of charge.
          Once you sign up for your home phone we will mail you a device that
          will connect between your internet modem and your home phone. Its
          super easy to install and only takes a minute.{" "}
        </p>
      </div>
      <div className="flex flex-col gap-5 xl:gap-10">

        <div className="flex justify-center">

        {plansLoading?.isLoading ? (
          <InternetPlanCardSkeleton />
        ) : phoneList?.planDetails?.length > 0 ? (
          phoneList?.planDetails.map((item:any) => {
            return (
              <HomePhonePlanCard
                selected={haveHomePhonePlan}
                defaultSelected={
                  phoneList?.planDetails?.length <= 1
                    ? phoneList?.planDetails?.[0]
                    : null
                }
                defaultList={item}
                handlePlanSelect={handleHomePlanSelect}
                key={item?.id}
                />
            );
          })
        ) : null}
        </div>

        <div className="flex gap-2.5 lg:flex-row flex-col">
          <div className="basis-full">
            <Button
              title="Go back"
              btnType="transparent"
              type="button"
              attributes={{
                disabled: homePlan.haveHomePhonePlan ? cancelLoading : false,
              }}
              className="disabled:opacity-50"
              clickEvent={closeHandler}
            />
          </div>
          <div className="basis-full">
            <Button
              title={
                homePlan?.haveHomePhonePlan ? "Cancel Phone Service" : "Next"
              }
              attributes={{
                disabled: !haveHomePhonePlan || plansLoading?.isLoading,
              }}
              className="disabled:opacity-50"
              clickEvent={
                homePlan.haveHomePhonePlan ? handleCancelService : handleSubmit
              }
              isLoading={homePlan.haveHomePhonePlan ? cancelLoading : false}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddHomePhone;
