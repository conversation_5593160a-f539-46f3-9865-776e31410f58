const dotenv = require('dotenv');
dotenv.config();
dotenv.config({
    path: `./${process.env.NODE_ENV}.env`
});
module.exports = {
    NODE_ENV: process.env.NODE_ENV,
    HOST: process.env.HOST,
    PORT: process.env.PORT,
    LOG_PREFIX: process.env.LOG_PREFIX,
    RESGISTRATION_TOKEN: process.env.RESGISTRATION_TOKEN,
    RESGISTRATION_USERNAME: process.env.RESGISTRATION_USERNAME,
    RESGISTRATION_PASSWORD: process.env.RESGISTRATION_PASSWORD,
    CRYPTO_SECRET: process.env.CRYPTO_SECRET,
    ORGANIZATIONID: process.env.ORGANIZATIONID,
    database: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        db: process.env.DB_NAME,
        dialect: process.env.DB_DIALECT,
        logging: process.env.DB_LOGGING,
        pool: {
            "max": 5,
            "min": 0,
            "acquire": 30000,
            "idle": 10000
        },
    },
    elasticSearch: {
        node: process.env.ELASTIC_SEARCH_NODE,
        apiKey: process.env.ELASTIC_SEARCH_API_KEY
    },
    chargebee: {
        webhookUsername: process.env.CB_WEBHOOK_USERNAME,
        webhookPassword: process.env.CB_WEBHOOK_PASSWORD,
        site: process.env.CHARGEBEE_SITE,
        api_key: process.env.CHARGEBEE_APIKEY,
        gateway_account_id: process.env.GATEWAY_ACCOUNT_ID,
        phonePlanDetails: {
            monthlySingleChannels: {
                pick5ChannelName: "monthly_X_PICK_5",
                pick5ChannelPrice: 15,
                pick1ChannelName: "monthly_X_SNGL_CHANNEL",
                pick1ChannelPrice: 3,
            },
            yearlySingleChannels: {
                pick5ChannelName: "yearly_X_PICK_5",
                pick5ChannelPrice: 180,
                pick1ChannelName: "yearly_X_SNGL_CHANNEL",
                pick1ChannelPrice: 36,
            }
        }
    },
    salesforce: {
        baseurl: process.env.SALESFORCE_BASE_URL,
        username: process.env.SALESFORCE_USERNAME,
        password: process.env.SALESFORCE_PASSWORD,
        token: process.env.SALESFORCE_TOKEN,
        retryCount: 3,
        recordTypeId: {
            service: {
                moveOrderId: "0122R0000002ckNQAQ",
                installTime: "NTR",
                shipppingAdrs: "Update Right Away",
                adrsCheck: true
            },
            mailing: {
                confirmed: false
            },
            internet: {
                speedChange: "0122R0000002ckPQAQ"
            },
            tv: {
                create: "0122R0000002ckSQAQ",
                createOrder: "0127V000000gjtCQAQ",
                changeOrder: "0127V000000gjtBQAQ",
                cancelOrder: "0127V000000gjtAQAQ"
            },
            phone: {
                porting: {
                    recId: "0127V000000kwigQAA",
                    staging: "Asked if they need port"
                },
                newNumber: {
                    recId: "0127V000000kwigQAA",
                    staging: "Does not need port (New Number)"
                },
                cancelOrder: "0127V000000QAolQAG"
            }
        }
    },
    aws: {
        accessKeyId: process.env.DEV_AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.DEV_AWS_SECRET_ACCESS_KEY,
        region: process.env.DEV_AWS_REGION,
        userPoolId: process.env.AWS_USER_POOL_ID,
        clientId: process.env.AWS_CLIENT_ID,
        clientSecret: process.env.AWS_CLIENT_SECRET,
        bucketName: process.env.AWS_BUCKET_NAME
    },
    internet: {
        plans: process.env.INTERNET_PLANS,
        addons: process.env.INTERNET_ADD_ONS,
        assets: process.env.INTERNET_ASSETS
    },
    tv: {
        plans: process.env.TV_PLANS,
        addons: process.env.TV_ADD_ONS,
        assets: process.env.TV_ASSETS
    },
    phone: {
        plans: process.env.PHONE_PLANS,
        addons: process.env.PHONE_ADD_ONS,
        assets: process.env.PHONE_ASSETS
    }
}