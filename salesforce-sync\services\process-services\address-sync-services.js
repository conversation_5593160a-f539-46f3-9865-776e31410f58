const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const customerServices = new CustomerServices();
const INTERVAL = CONFIG.interval;
const { getCurrentAtlanticTime } = require("../../helper/custom-helper");

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class AddressTestServices {

    async getAddressDetails() {
        try {
            const query = `SELECT id, sf_record_id, address_type FROM customer_details_addresses`;
            const res = await customerServices.executeQuery(query);
            if (res.length > 0) {
                const syncAddressRes = await this.checkAndManageAddress(res);
                if (syncAddressRes.execute) return { synctype: "getAddressDetails", ...syncAddressRes };
            }
            return { execute: true, synctype: "getAddressDetails", status: true, message: "customer_details_addresses not found" };
        } catch (error) {
            console.error("Sync service address details -> ", error);
            return { execute: true, synctype: "getAddressDetails", status: false, error: error?.message || null };
        }
    }

    // Get contacts status whether exist in database or not  
    async checkAndManageAddress(sfAddressDetails) {
        try {

            let mailingAddressSfRecId = [];
            let serviceAddressSfRecId = [];
            for (const sfAddressDetail of sfAddressDetails) {
                const { id, sf_record_id, address_type } = sfAddressDetail;
                if (address_type == "service") serviceAddressSfRecId.push(sf_record_id);
                else if (address_type == "mailing") mailingAddressSfRecId.push(sf_record_id);
            }

            // Split addressSfRecId into chunks of 2000 elements
            const chunkSize = 2000;

            const mailingChunks = [];
            for (let i = 0; i < mailingAddressSfRecId.length; i += chunkSize) mailingChunks.push(mailingAddressSfRecId.slice(i, i + chunkSize));

            const serviceChunks = [];
            for (let i = 0; i < serviceAddressSfRecId.length; i += chunkSize) serviceChunks.push(serviceAddressSfRecId.slice(i, i + chunkSize));

            const syncAddressRes = await this.callSalesforceApi(mailingChunks, serviceChunks);
            return { execute: true, status: true, ...syncAddressRes };
        } catch (error) {
            console.error("SalesforceService 100 check And Manage Contacts -> ", error);
            return { execute: true, status: false, error: error?.message || null };
        }
    }

    async callSalesforceApi(mailingChunks, serviceChunks) {
        let syncCounts = { mailingCount: 0, serviceCount: 0 }; // Initialize total counters
        try {
            for (const mailingChunk of mailingChunks) {
                let mailingIdsFormatted = mailingChunk.map(id => `'${id}'`).join(",");

                let query = `SELECT Id, Name, Mailing_Suite_Unit__c, Mailing_Street_Number__c, Mailing_Street_Name__c, Mailing_City_Town__c, Mailing_Province__c, Mailing_Postal_Code__c, Full_Mailing_Address__c, Mailing_Country__c, Status__c, LastModifiedDate, CreatedDate FROM Mailing_Address__c WHERE Id IN (${mailingIdsFormatted})`;

                if (INTERVAL === "regular") query += " AND LastModifiedDate >= YESTERDAY";
                query += " ORDER BY LastModifiedDate DESC";

                const mailingData = await salesforceConnection.getBulkDetails(query);
                if (mailingData?.length) {
                    await this.bulkUpdateAddress(mailingData, "mailing");
                    syncCounts.mailingCount += mailingData.length;
                }
            }

            for (const serviceChunk of serviceChunks) {
                let serviceIdsFormatted = serviceChunk.map(id => `'${id}'`).join(",");

                let query = `SELECT Id, Name, Service_Suite_Unit__c, Service_Street_Number__c, Service_Street_Name__c, Service_City_Town__c, Service_Province__c, Service_Postal_Code__c, Full_Service_Address__c, Service_Country__c, Status__c, LastModifiedDate, CreatedDate FROM Service_Address__c WHERE Id IN (${serviceIdsFormatted})`;

                if (INTERVAL === "regular") query += " AND LastModifiedDate >= YESTERDAY";
                query += " ORDER BY LastModifiedDate DESC";

                const serviceData = await salesforceConnection.getBulkDetails(query);
                if (serviceData?.length) {
                    await this.bulkUpdateAddress(serviceData, "service");
                    syncCounts.serviceCount += serviceData.length;
                }
            }

            return syncCounts; // Return total counts in one object
        } catch (error) {
            console.error("Sync address callSalesforceApi -> ", error);
            return syncCounts; // Still return count even if error occurs
        }
    }

    async bulkUpdateAddress(updateQueries, type) {
        try {
            if (!updateQueries.length) return;

            const updatedTime = getCurrentAtlanticTime();
            const name = "Name";
            const suit_unit = type == "mailing" ? "Mailing_Suite_Unit__c" : "Service_Suite_Unit__c";
            const street_number = type == "mailing" ? "Mailing_Street_Number__c" : "Service_Street_Number__c";
            const street_name = type == "mailing" ? "Mailing_Street_Name__c" : "Service_Street_Name__c";
            const town = type == "mailing" ? "Mailing_City_Town__c" : "Service_City_Town__c";
            const province = type == "mailing" ? "Mailing_Province__c" : "Service_Province__c";
            const postal_code = type == "mailing" ? "Mailing_Postal_Code__c" : "Service_Postal_Code__c";
            const full_address = type == "mailing" ? "Full_Mailing_Address__c" : "Full_Service_Address__c";
            const country = type == "mailing" ? "Mailing_Country__c" : "Service_Country__c";
            const status = "Status__c";
            const sf_updatedAt = "LastModifiedDate";

            const updateStatement = `
                UPDATE customer_details_addresses
                SET 
                    updatedAt = '${updatedTime}',
                    name = CASE ${this.buildCaseStatement(updateQueries, name, "cleanName")} END,
                    suit_unit = CASE ${this.buildCaseStatement(updateQueries, suit_unit, "cleanName")} END,
                    street_number = CASE ${this.buildCaseStatement(updateQueries, street_number, "cleanName")} END,
                    street_name = CASE ${this.buildCaseStatement(updateQueries, street_name, "cleanName")} END,
                    town = CASE ${this.buildCaseStatement(updateQueries, town, "cleanName")} END,
                    province = CASE ${this.buildCaseStatement(updateQueries, province, "cleanName")} END,
                    postal_code = CASE ${this.buildCaseStatement(updateQueries, postal_code, "cleanName")} END,
                    full_address = CASE ${this.buildCaseStatement(updateQueries, full_address, "cleanName")} END,
                    country = CASE ${this.buildCaseStatement(updateQueries, country, "cleanName")} END,
                    status = CASE ${this.buildCaseStatement(updateQueries, status, "cleanName")} END,
                    sf_updatedAt = CASE ${this.buildCaseStatement(updateQueries, sf_updatedAt)} END
                    WHERE sf_record_id IN (${updateQueries.map(query => `'${query.Id}'`).join(', ')})
            `;

            await customerServices.executeQuery(updateStatement);
        } catch (error) {
            console.error("SalesforceService bulkUpdateAddresss -> ", error);
        }
    }

    buildCaseStatement(queries, field, type) {

        return queries.map(query => {
            let value = query?.[field] || 'NULL';
            if (field == "LastModifiedDate") value = getCurrentAtlanticTime(value);

            // Sanitize special characters
            if (type === "cleanName" && typeof value === 'string') {
                value = value.replace(/['"`]/g, ''); // Remove single quotes, double quotes, and backticks
            }

            return value === 'NULL'
                ? `WHEN sf_record_id = '${query.Id}' THEN ${value}`
                : `WHEN sf_record_id = '${query.Id}' THEN '${value}'`;
        }).join(' ');
    }
}

module.exports = AddressTestServices;
