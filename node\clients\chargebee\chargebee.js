const CONFIG = require("../../config")
const Chargebee = require("chargebee");

class ChargebeeClient {
    constructor() {
        // Initialize Chargebee configuration using values from CONFIG
        this.site = CONFIG.chargebee.site;
        this.api_key = CONFIG.chargebee.api_key;

        this.chargebee = new Chargebee({
            site: this.site,
            apiKey: this.api_key,
        });
    }

    // Method to get a list of subscriptions
    async getSubscriptions(subscriptionId) {
        try {
            const operator = 'is';
            const payload = {
                [`id[${operator}]`]: subscriptionId,
                limit: 1,
            };

            const result = await this.chargebee.subscription.list(payload)

            // // Check if result.list exists
            // if (!result?.list || result.list.length === 0) {
            //     throw new Error(`Failed to retrieve subscriptions for subscription ID: ${subscriptionId}`);
            // }

            return result?.list;
        } catch (error) {
            console.error(`Chargebee get subscriptions error->`, error);
            throw error;
        }
    }

    // Method to download an invoice PDF
    async downloadInvoice(invoiceId) {
        try {
            const result = await this.chargebee.invoice.pdf(invoiceId);

            // Check if result.download exists
            // if (!result.download) {
            //     throw new Error(`Failed to retrieve the invoice download link for invoice ID: ${invoiceId}`);
            // }
            return result?.download;
        } catch (error) {
            console.error(`Chargebee download invoice error->`, error);
            throw error;
        }
    }

    // Method to retrieve a payment source for a customer
    async retrievePaymentSource(customerId, offset) {
        try {
            const operator = 'is';
            const payload = {
                [`customer_id[${operator}]`]: customerId,
                limit: 10,
                offset: offset
            };

            const result = await this.chargebee.paymentSource.list(payload);

            // if (!result?.list || result.list.length === 0) {
            //     throw new Error(`Failed to retrieve payment sources for customer ID: ${customerId}`);
            // }

            return result;
        } catch (error) {
            console.error(`Chargebee retrieve payment source error->`, error);
            throw error;
        }
    }

    // Method to update a card payment source
    async updateCardSource(source_id, card) {
        try {
            const result = await this.chargebee.paymentSource.updateCard(source_id, { card });

            // Check if the result is valid
            // if (!result) {
            //     throw new Error(`Failed to update the card payment source for source ID: ${source_id}`);
            // }

            return result;
        } catch (error) {
            throw error;
        }
    }

    // Method to create a new card payment source
    async createCardSource(customer_id, card) {
        try {

            const result = await this.chargebee.paymentSource.createCard({ customer_id, card });

            // Check if the result is valid
            // if (!result) {
            //     throw new Error(`Failed to create the card payment source for source ID: ${source_id}`);
            // }
            return result;
        } catch (error) {
            throw error;
        }
    }

    // Method to delete a card payment source
    async deleteCardSource(custPaymentSourceId) {
        try {

            const result = await this.chargebee.paymentSource.deleteLocal(custPaymentSourceId);

            // if (!result || !result?.payment_source) {
            //     throw new Error(`Failed to delete the card payment source for source ID: ${custPaymentSourceId}`);
            // }

            return result?.payment_source;
        } catch (error) {
            throw error;
        }
    }

    // Method to collect payment for an invoice
    async collectInvoicePayment(invoiceId, payload) {
        try {

            const result = await this.chargebee.invoice.collectPayment(invoiceId, payload);

            // if (!result || !result?.invoice) {
            //     throw new Error(`Failed to collect payment for invoice ID: ${invoiceId}`);
            // }

            return result?.invoice;
        } catch (error) {
            throw error;
        }
    }

    // Method to get estimate change the term end date for a subscription.
    async estimateTermEnd(subscriptionId, newdate) {
        try {
            const payload = {
                term_ends_at: newdate,
                prorate: true,
                invoice_immediately: true
            };

            const result = await this.chargebee.estimate.changeTermEnd(subscriptionId, payload);

            // if (!result || !result?.estimate) {
            //     throw new Error(`Failed to get estimate for term end for subscription ID: ${subscriptionId}`);
            // }

            return result?.estimate;
        } catch (error) {
            throw error;
        }
    }

    // Method to change the term end date for a subscription
    async changeTermEnd(subscriptionId, newdate) {
        try {
            const payload = {
                term_ends_at: newdate,
                prorate: true,
                invoice_immediately: true
            };

            const result = await this.chargebee.subscription.changeTermEnd(subscriptionId, payload);

            // if (!result || !result?.subscription) {
            //     throw new Error(`Failed to change term end for subscription ID: ${subscriptionId}`);
            // }

            return result?.subscription;

        } catch (error) {
            throw error;
        }
    }

    // Method to get a list of invoices for a specific subscription
    async getInvoicesList(subscriptionId, offset) {
        try {
            const operator = 'is';
            const payload = {
                [`subscription_id[${operator}]`]: subscriptionId,
                limit: 100,
                offset: offset
            };

            const result = await this.chargebee.invoice.list(payload);

            return result;
        } catch (error) {
            throw error;
        }
    }

    // 
    async getEstimateUpdateSubscription(payload) {
        try {
            const result = await this.chargebee.estimate.updateSubscription(payload);
            return result?.estimate;
        } catch (error) {
            throw error;
        }
    }

    //
    async updateSubscription(subscriptionId, payload) {
        try {
            const result = await this.chargebee.subscription.update(subscriptionId, payload);
            return result?.subscription;
        } catch (error) {
            throw error;
        }
    }

    // Method to change primary card in cb
    async changeCardStatus(customer_id, payload) {
        try {
            const result = await this.chargebee.customer.assignPaymentRole(customer_id, payload);
            return result?.payment_source;
        } catch (error) {
            throw error;
        }
    }

    // Method to retrieve a payment source for a customer
    async getCustomerData(customerId) {
        try {
            return await this.chargebee.customer.retrieve(customerId);
        } catch (error) {
            throw error;
        }
    }

    // Method to stop dunning for an invoice
    async stopDunningInvoice(invoiceId) {
        try {
            const result = await this.chargebee.invoice.stopDunning(invoiceId);
            return result?.invoice;
        } catch (error) {
            throw error;
        }
    }

    async updatePaymentSource(subscriptionId, payload) {
        try {
            const result = await this.chargebee.subscription.overrideBillingProfile(subscriptionId, payload);
            return result;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = ChargebeeClient;