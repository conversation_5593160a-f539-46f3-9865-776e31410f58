{"name": "backend", "version": "1.0.0", "description": "purple-cow", "main": "index.js", "scripts": {"start": "node index.js", "server": "nodemon index.js"}, "keywords": [], "author": "Lucent Innovation", "license": "ISC", "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.572.0", "@aws-sdk/client-s3": "^3.577.0", "@elastic/elasticsearch": "^8.14.0", "aws-sdk": "^2.1691.0", "aws-serverless-express": "^3.4.0", "axios": "^1.6.7", "chargebee": "^3.5.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.3.1", "generate-password": "^1.7.1", "helmet": "^8.0.0", "joi": "^17.12.2", "jsforce": "^3.6.2", "jsonwebtoken": "^9.0.2", "jwk-to-pem": "^2.0.5", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "multer": "^2.0.0", "mysql2": "^3.9.2", "nodemon": "^3.1.0", "sequelize": "^6.37.1", "winston": "^3.12.0", "winston-daily-rotate-file": "^5.0.0"}}