{"name": "purple-cow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development-local", "type-check": "tsc --noEmit", "build": "vite build", "build:development": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.5.0", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "crypto-js": "^4.2.0", "moment-timezone": "^0.6.0", "react": "^19.0.0", "react-calendar": "^6.0.0", "react-content-loader": "^7.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.0.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/crypto-js": "^4.2.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.1", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.10.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.17", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "sass": "^1.77.6", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^6.0.1"}}