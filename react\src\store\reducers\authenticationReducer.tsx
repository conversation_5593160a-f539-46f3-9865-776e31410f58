import { AuthAction, AuthActionTypes, AuthState } from "../../typings/typing";

const initialState: AuthState = {
  isLoggedIn: false,
};

const authenticationReducer = (
  state = initialState,
  action: AuthAction
): AuthState => {
  switch (action.type) {
    case AuthActionTypes.LOGIN:
      return {
        ...state,
        isLoggedIn: true,
      };
    case AuthActionTypes.LOGOUT:
      return {
        ...state,
        isLoggedIn: false,
      };
    default:
      return state;
  }
};

export const login = (): AuthAction => ({
  type: AuthActionTypes.LOGIN,
});

export const logout = (): AuthAction => ({
  type: AuthActionTypes.LOGOUT,
});

export default authenticationReducer;
