import { combineReducers } from "@reduxjs/toolkit";
import homePhoneReducer from "./homePhoneReducer";
import internetReducer from "./internetReducer";
import mailingAddressReducer from "./mailingAddressReducer";
import nextPaymentDateReducer from "./nextPaymentDateReducer";
import serviceAddressReducer from "./serviceAddressReducer";
import televisionReducer from "./televisionReducer";
import cancelServiceReducer from "./cancelServiceReducer";
import authenticationReducer from "./authenticationReducer";
import { authApi } from "../../services/api";
import toasterReducer from "./toasterReducer";
import customerSubscriptionReducer from "./customerSubscriptionReducer";
import userReducer from "./userReducer";
export const rootReducer = combineReducers({
  serviceAddressReducer,
  mailingAddressReducer,
  nextPaymentDateReducer,
  internetReducer,
  televisionReducer,
  homePhoneReducer,
  cancelServiceReducer,
  authenticationReducer,
  toasterReducer,
  customerSubscriptionReducer,
  user: userReducer,
  [authApi.reducerPath]: authApi.reducer,
});

export type rootState = ReturnType<typeof rootReducer>;
