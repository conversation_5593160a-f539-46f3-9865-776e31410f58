
//  if any holiday comees in weekend then add manually into this list 
//  example
//   {
//   "Date": "01/01/xxxx",
//   "Type": "New Year's Day",
//   "Weekday": "Saturday"
// },

//  then we have to add

//  {
//   "Date": "03/01/xxxx", next monday's date in MM/DD/YYYY format
//   "Type": "New Year's Day",   add message whatevery you want to show in feedback eg. extended holiday
//   "Weekday": "Monday"
// },
export const holidayList = [
  {
    "Date": "25/12/2024",
    "Type": "Christmas Day",
    "Weekday": "Wednesday"
  },
  {
    "Date": "26/12/2024",
    "Type": "Boxing Day",
    "Weekday": "Thursday"
  },
  {
    "Date": "01/01/2025",
    "Type": "New Year's Day",
    "Weekday": "Wednesday"
  },
  {
    "Date": "18/04/2025",
    "Type": "Good Friday",
    "Weekday": "Friday"
  },
  {
    "Date": "19/05/2025",
    "Type": "Victoria Day",
    "Weekday": "Monday"
  },
  {
    "Date": "01/07/2025",
    "Type": "Canada Day",
    "Weekday": "Tuesday"
  },
  {
    "Date": "04/08/2025",
    "Type": "Natal Day",
    "Weekday": "Monday"
  },
  {
    "Date": "01/09/2025",
    "Type": "Labour Day",
    "Weekday": "Monday"
  },
  {
    "Date": "30/09/2025",
    "Type": "National Day for Truth and Reconciliation",
    "Weekday": "Tuesday"
  },
  {
    "Date": "13/10/2025",
    "Type": "Thanksgiving Day",
    "Weekday": "Monday"
  },
  {
    "Date": "11/11/2025",
    "Type": "Remembrance Day",
    "Weekday": "Tuesday"
  },
  {
    "Date": "25/12/2025",
    "Type": "Christmas Day",
    "Weekday": "Thursday"
  },
  {
    "Date": "26/12/2025",
    "Type": "Boxing Day",
    "Weekday": "Friday"
  }
]
