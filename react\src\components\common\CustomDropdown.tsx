import React, { useEffect, useRef, useState } from "react";
import { InvalidIcon } from "../../assets/Icons";

interface Option {
  id: number;
  value: string;
  label: string;
}

interface CustomDropdownProps {
  placeholder: string;
  error?: string;
  name: string;
  onChange: (value: any) => void;
  options: Option[];
  defaultValue?: string;
  isDisabled?: boolean;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  placeholder,
  error,
  name,
  onChange,
  options,
  defaultValue,
  isDisabled,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string | undefined>(
    defaultValue
  );
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (value: string) => {
    let e = {
      target: {
        name: name,
        value: value,
      },
    };
    setSelectedValue(value);
    onChange(e);
    setIsOpen(false);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  return (
    <div
      ref={dropdownRef}
      className={`custom-dropdown relative w-full h-full ${error && "error"}`}
    >
      <button
        type="button"
        className={
          isDisabled
            ? "w-full h-auto relative bg-[#f7f7f7] border cursor-not-allowed  shadow-sm text-left sm:lg:text-base focus:outline-none text-sm"
            : `w-full h-auto relative bg-[#f7f7f7] border ${
                error ? "border-red-500" : "border-[#f7f7f7]"
              } shadow-sm text-left rounded-20 cursor-pointer focus:outline-none focus:ring-0 focus:ring-[#7f1b7f] focus:border-[#7f1b7f] sm:lg:text-base text-sm`
        }
        onClick={isDisabled ? () => {} : () => toggleDropdown()}
      >
        {selectedValue ? (
          options.find((option) => option.value === selectedValue)?.label
        ) : (
          <p className="text-[#a9a9a9] font-medium">
            Select {placeholder.toLowerCase()}{" "}
          </p>
        )}
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg
            className="h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.23 7.21a.75.75 0 011.06.02L10 11.667l3.71-4.438a.75.75 0 011.14.98l-4 4.75a.75.75 0 01-1.14 0l-4-4.75a.75.75 0 01.02-1.06z"
              clipRule="evenodd"
            />
          </svg>
        </span>
      </button>

      {isOpen && (
        <ul className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 lg:text-base text-sm ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
          {options.map((option, index) => (
            <li
              key={index}
              className={`cursor-pointer select-none relative py-2 pl-3 pr-9 ${
                selectedValue === option.value
                  ? "text-indigo-600"
                  : "text-gray-900"
              } hover:bg-gray-100`}
              onClick={() => handleOptionClick(option.value)}
            >
              <span
                className={`block truncate ${
                  selectedValue === option.value
                    ? "font-semibold"
                    : "font-normal"
                }`}
              >
                {option.label}
              </span>
            </li>
          ))}
        </ul>
      )}
      {error && (
        <p className="error-text flex gap-2 items-center pt-3.5 text-sm">
          <span className="icon">
            <InvalidIcon />
          </span>
          {error}
        </p>
      )}
    </div>
  );
};

export default CustomDropdown;
