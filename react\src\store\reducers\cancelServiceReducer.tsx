import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { cancelServiceReducerState } from "../../typings/typing";

const initialState = {
  cancelDate: null,
  deleteOrder: false,
  feedback: "",
  showDeleteOrderPopup: false,
  showSelectCancelServiceDatePopup: false,
  showFeedbackPopup: false,
  showOfferPopup: false,
  showConfirmPopup: false,
} satisfies cancelServiceReducerState as cancelServiceReducerState;

const cancelServiceReducer = createSlice({
  name: "CANCEL_SERVICE",
  initialState,
  reducers: {
    setCancelDate: (state, action: PayloadAction<Date | null>) => {
      state.cancelDate = action.payload;
    },
    setFeedback: (state, action: PayloadAction<string>) => {
      state.feedback = action.payload;
    },
    toggleShowSelectCancelServiceDatePopup: (state) => {
      state.showSelectCancelServiceDatePopup =
        !state.showSelectCancelServiceDatePopup;
    },
    toggleShowFeedbackPopup: (state) => {
      state.showFeedbackPopup = !state.showFeedbackPopup;
    },
    toggleShowOfferPopup: (state) => {
      state.showOfferPopup = !state.showOfferPopup;
    },
    toggleShowConfirmPopup: (state) => {
      state.showConfirmPopup = !state.showConfirmPopup;
    },
    toggleDeleteOrderPopup: (state) => {
      state.deleteOrder = !state.deleteOrder;
    },
  },
});

export const {
  setCancelDate,
  setFeedback,
  toggleShowSelectCancelServiceDatePopup,
  toggleShowFeedbackPopup,
  toggleShowOfferPopup,
  toggleShowConfirmPopup,
  toggleDeleteOrderPopup,
} = cancelServiceReducer.actions;

export default cancelServiceReducer.reducer;
