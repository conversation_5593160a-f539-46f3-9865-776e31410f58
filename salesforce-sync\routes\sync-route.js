const { deleteContact, getContactsAndSync, getOrderssAndSync } = require("../controllers/salesforce-sync-controller");
const { syncInvoicesFromChargebee, removeServicesFromDatabase } = require("../controllers/chargebee-sync-controller");

module.exports = app => {

	let router = require("express").Router();

	// delete specific contact from database
	router.post("/delete-contact", deleteContact);

	// get contacts and sync
	router.get("/get-contacts-and-sync", getContactsAndSync);

	// sync install date for creation order.
	router.get("/get-orders-and-sync", getOrderssAndSync);

	app.use('/', router);
};