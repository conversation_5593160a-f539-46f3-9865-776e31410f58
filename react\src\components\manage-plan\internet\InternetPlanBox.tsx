import moment from "moment-timezone";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { AddIcon, EditIcon } from "../../../assets/Icons";
import useMediaQuery from "../../../hooks/MediaQueryHook";
import {
  useInternetUpdateMutation,
  useInternetUpdateProratedMutation,
} from "../../../services/api";
import { logout } from "../../../store/reducers/authenticationReducer";
import {
  setNewInternetPlan,
  toggleShowInternetConfirmationPopup,
  toggleShowInternetPopup,
} from "../../../store/reducers/internetReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";
import {
  internetPlan,
  newInternetPlan,
  showInternetConfirmationPopup,
  showInternetPopup,
} from "../../../store/selectors/internetSelectors";
import {
  _TIMEZONE,
  formatCurrency,
  getSubscriptionName,
} from "../../../utils/helper";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import Popup from "../../common/Popup";
import ServiceBox from "../ServiceBox";
import InternetPlanPopup from "./InternetPlanPopup";
interface InternetPlanBoxProps {
  refetch: () => void;
  subscriptionID?: number;
  stage?: string;
  isMoveOrder: any;
}

const InternetPlanBox: React.FC<InternetPlanBoxProps> = ({
  refetch,
  subscriptionID,
  stage,
  isMoveOrder,
}) => {
  //  plan id
  const currentPlan = useSelector(internetPlan);
  const subscriptionType = useSelector(customerSubscriptionType);

  // new Plan
  const newPlan = useSelector(newInternetPlan);
  //  popup flag
  const showPopup = useSelector(showInternetPopup);
  //  confirmation popup flag
  const showConfirmationPopup = useSelector(showInternetConfirmationPopup);
  //  On Edit
  const [plan, setPlan] = useState<object>(currentPlan);
  const [internetEstimate, internetEstimateLoading] =
    useInternetUpdateProratedMutation();
  const [internetUpdate, internetUpdateLoading] = useInternetUpdateMutation();
  const [proratedData, setProratedData] = useState({});
  const [serviceModification, setServiceModification] =
    useState<boolean>(false);
  const [serviceModificationDelay, setServiceModificationDelay] =
    useState<boolean>(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const isDesktop = useMediaQuery("(min-width:1300px");
  const [isEditable, setIsEditable] = useState<boolean>(false);
  let currentDate = "";
  let targetDateObj = "";

  // useEffect to update the plan state when currentPlan changes
  useEffect(() => {
    if (currentPlan && Object.keys(currentPlan).length > 0) {
      setPlan(() => ({ ...currentPlan, api_name: currentPlan?.plan_name }));

      if (currentPlan?.internetElSpeedChangeOrder?.expected_completion_date) {
        // Get the current date
        currentDate = moment(new Date()).tz(_TIMEZONE).format("YYYY-MM-DD");

        // Format the current date to match the target date format (YYYY-MM-DD)
        targetDateObj = moment(
          currentPlan?.internetElSpeedChangeOrder?.expected_completion_date,
          "MMM D, YYYY"
        ).format("YYYY-MM-DD");

        // Check if the current date matches the target date
        setServiceModification(currentDate <= targetDateObj);
        setServiceModificationDelay(currentDate > targetDateObj);
      }
    }
  }, [currentPlan]);

  // Function to handle internet plan submission
  const handleInternetPlanSubmit = async (plan: object) => {
    if (!internetEstimateLoading?.isLoading) {
      try {
        const data = {
          customer_subscription_id: subscriptionID,
          plan_id: plan?.api_name,
        };
        const response = await internetEstimate(data).unwrap();
        if (response.status === 200) {
          setProratedData({
            ...plan,
            prorated_amount: response?.data?.prorated_amount,
          });
          // setPlan(plan);
          dispatch(toggleShowInternetPopup());
          dispatch(toggleShowInternetConfirmationPopup());
        }
      } catch (error: any) {
        if (error?.data?.error?.length > 0) {
          dispatch(
            addNotification({
              type: "error",
              message: error?.data?.error?.[0]?.message,
            })
          );
        } else {
          dispatch(
            addNotification({ type: "error", message: error?.data?.message })
          );
        }
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to handle confirmation submission
  const handleConfirmationSubmit = async () => {
    try {
      const data = {
        customer_subscription_id: subscriptionID,
        plan_id: proratedData?.api_name,
      };
      const response = await internetUpdate(data).unwrap();
      if (response.status === 200) {
        if (plan) {
          dispatch(setNewInternetPlan(plan));
        }
        dispatch(toggleShowInternetConfirmationPopup());
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
        refetch();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  const handleCancelConfirmation = () => {
    dispatch(toggleShowInternetConfirmationPopup());
    dispatch(toggleShowInternetPopup());
  };

  useEffect(() => {
    if (
      subscriptionID &&
      (stage === "Online" ||
        stage === "Outstanding" ||
        stage === "Payment arrangements")
    ) {
      if (currentPlan?.speed_change_order === null) {
        setIsEditable(true);
      }
      if (
        currentPlan?.speed_change_order != null &&
        currentPlan?.internetElSpeedChangeOrder?.stage ===
          "Eligible For Submission"
      ) {
        setIsEditable(true);
      }
      if (
        currentPlan?.speed_change_order !== null &&
        currentPlan?.internetElSpeedChangeOrder?.response_date !== null
      ) {
        setIsEditable(true);
      }
    }
  }, [subscriptionID, stage, currentPlan]);

  return (
    <ServiceBox
      border={
        currentPlan && Object?.keys(currentPlan).length > 0 ? false : true
      }
    >
      {currentPlan && Object?.keys(currentPlan).length > 0 ? (
        <div className="flex 2xl:gap-5 gap-2.5 flex-col">
          <div className="flex justify-between">
            <div className="flex gap-5">
              <p className="text-xl 2xl:text-[26px] font-medium">Internet</p>
            </div>
            <div
              className={`border border-[#D7B9FF] 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 bg-white hover:bg-purple-50 ${
                !isMoveOrder && isEditable
                  ? ""
                  : "opacity-50 custom-cursor-not-allowed"
              }`}
              onClick={() => {
                !isMoveOrder &&
                  isEditable &&
                  dispatch(toggleShowInternetPopup());
              }}
            >
              {currentPlan ? (
                <EditIcon width={20} height={20} />
              ) : (
                <AddIcon width={20} height={20} />
              )}
            </div>
          </div>
          <div className="">
            <p className="font-medium">
              {formatCurrency(
                currentPlan?.plan_price ? currentPlan?.plan_price : 0,
                true
              )}{" "}
              - {getSubscriptionName(currentPlan?.plan_speed, subscriptionType)}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex rounded-20 justify-between items-center">
          <div className="items-center">
            <p className="text-xl 2xl:text-[26px] font-medium">Internet</p>
          </div>
          <div
            className="border border-black 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey"
            onClick={() => dispatch(toggleShowInternetPopup())}
          >
            <AddIcon width={20} height={20} />
          </div>
        </div>
      )}

      {(serviceModification || serviceModificationDelay) &&
        currentPlan?.internetElSpeedChangeOrder?.stage !== "Complete" && (
          <div className="bg-white border border-[#FBC400] flex flex-col 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
            <div className="flex 2xl:gap-5 xl:gap-2.5 gap-2 items-center">
              <div className="bg-warning-light  rounded-[6px] p-2 gap-2.5">
                <p className=" text-warning">Processing</p>
              </div>
            </div>
            <div>
              <p className="2xl:text-base font-bold">
                {`Plan change to ${getSubscriptionName(
                  currentPlan?.internetElSpeedChangeOrder?.speed,
                  subscriptionType
                )} scheduled for ${
                  currentPlan?.internetElSpeedChangeOrder
                    ?.expected_completion_date
                    ? currentPlan?.internetElSpeedChangeOrder
                        ?.expected_completion_date
                    : ""
                }`}
                .{" "}
                {serviceModificationDelay &&
                  `We're sorry this is taking longer than expected.`}
              </p>
            </div>
          </div>
        )}

      {/*Internet Popup*/}
      {showPopup && (
        <Popup
          title="Internet"
          width={isDesktop ? "840px" : "760px"}
          height="1000px"
          closeHandler={() => {
            if (!internetEstimateLoading?.isLoading) {
              dispatch(toggleShowInternetPopup());
            }
          }}
        >
          <InternetPlanPopup
            selectedPlan={
              plan?.internetElSpeedChangeOrder?.stage ===
                "Eligible For Submission" &&
              plan?.internetElSpeedChangeOrder?.speed
                ? plan?.internetElSpeedChangeOrder?.speed
                : plan?.plan_speed
            }
            closeHandler={() => {
              setProratedData("");
              dispatch(toggleShowInternetPopup());
            }}
            currentPlan={currentPlan}
            subscriptionType={subscriptionType}
            handleSubmit={handleInternetPlanSubmit}
            isLoading={internetEstimateLoading?.isLoading}
          />
        </Popup>
      )}
      {showConfirmationPopup && (
        <ConfirmationMessagePopup
          title={
            !plan ? "Confirm internet plan" : "Confirm internet plan change"
          }
          message={`By changing your internet plan, you are accepting the <a href='https://purplecowinternet.com/terms-of-use/' target='_blank' style='color:#D958DA'>terms of service</a> of the new plan. Service will change within 3 business days and you will be billed a prorated amount of ${formatCurrency(
            proratedData ? proratedData?.prorated_amount : 0,
            true
          )}. Your new ${subscriptionType} internet price is ${formatCurrency(
            proratedData
              ? proratedData?.billing?.[0]?.[subscriptionType]?.price
              : 0,
            true
          )} only.`}
          handleSubmit={handleConfirmationSubmit}
          closeHandler={handleCancelConfirmation}
          isLoading={internetUpdateLoading?.isLoading}
          btnText="Confirm"
        />
      )}
    </ServiceBox>
  );
};

export default InternetPlanBox;
