const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const CustomerServices = require("./customer-sync-services");
const customerServices = new CustomerServices();
const PromotionsServices = require("../services/promotion-services");
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

class ReferralServices {
    async getReferralDetails(interval) {
        try {
            console.time("getReferralDetails"); // Start measuring execution time
            const { status, data } = await this.getAllReferralDetails(interval);
            await elasticSearch.insertDocument("sync_referrals_logs", { "log.level": "INFO", total_document_fetched: data?.length });
            if (status && Array.isArray(data) && data.length) {
                await this.checkAndManageDetails(data);
            }

            const { deleteStatus, deletedData } = await salesforceConnection.getDeletedData("Referral__c");

            if (deleteStatus && Array.isArray(deletedData) && deletedData.length) {
                await this.checkAndDeleteDetails(deletedData);
            }
            console.timeEnd("getReferralDetails"); // End measuring execution time
        } catch (error) {
            console.error("Sync service get bulk Referral Details -> ", error);
        }
    }

    async getAllReferralDetails(interval) {
        try {
            let query = "SELECT Id, Name, Contact__c, Referral__c, Referrals_Name__c, Referrals_Live_Date__c, Credit_Added_to_Account__c, Credit_Amount__c, CreatedDate, LastModifiedDate FROM Referral__c";

            if (interval === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const data = await salesforceConnection.getBulkDetails(query);

            return { status: true, data };
        } catch (error) {
            console.error("SalesforceService getAllReferralDetails -> ", error);
            return { status: false, data: [] };
        }
    }

    async checkAndManageDetails(referralDetails) {
        try {
            const promotionsServices = new PromotionsServices();
            for (const referralDetail of referralDetails) {

                const { Id: sf_record_id, Contact__c } = referralDetail;

                if (!Contact__c) continue;

                const { dataExist: contactExist, dataId: contactId } = await promotionsServices.checkExist(Contact__c, "contacts");
                if (!contactExist) continue;

                const { dataExist: refExist, dataId: refData } = await promotionsServices.checkExist(sf_record_id, "contacts_referrals");

                if (refExist) await this.updateRefDetails(referralDetail, refData);
                else await this.createNewRefDetails(referralDetail, contactId);
            }
            return { execute: true, status: true, referralDetails }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
            return { execute: true, status: false, referralDetails, error: error?.message || null }
        }
    }

    async createNewRefDetails(referralDetail, contactId) {
        try {
            const { Id: sf_record_id, Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, CreatedDate, LastModifiedDate, Referrals_Name__c: referral_name, Name: sf_name } = referralDetail;

            const { id: contact_id, sf_record_id: refSfRecId } = contactId;
            let queryParams = [];

            const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate);
            const createdAt = getCurrentAtlanticTime(CreatedDate);
            const updatedTime = getCurrentAtlanticTime();

            let query = `INSERT IGNORE INTO contacts_referrals (contact_id, sf_record_id, sf_name, referral_name, referred_amount, createdAt, sf_updatedAt, updatedAt`;
            queryParams = [contact_id, sf_record_id, sf_name, referral_name, referred_amount, createdAt, sf_updatedAt, updatedTime];
            if (installed_on) {
                query += `, installed_on`;
                queryParams.push(installed_on);
            }

            if (credit_added_on) {
                query += `, credit_added_on`;
                queryParams.push(credit_added_on);
            }

            query += `) VALUES (?, ?, ?, ?, ?, ?, ?, ?`;
            if (installed_on) query += `, ?`;
            if (credit_added_on) query += `, ?`;

            query += `)`;

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService createNewRefDetails -> ", error);
        }
    }

    async updateRefDetails(referralDetail, contactId) {
        try {
            const { Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, LastModifiedDate, Referrals_Name__c: referral_name, Name: sf_name } = referralDetail;

            const sf_updatedAt = getCurrentAtlanticTime(LastModifiedDate);
            const updatedTime = getCurrentAtlanticTime();

            const { sf_record_id: refSfRecId } = contactId;
            let queryParams = [];

            let query = `UPDATE contacts_referrals SET  referred_amount = ?, referral_name = ?, sf_updatedAt = ?, updatedAt = ?`;
            queryParams.push(referred_amount, referral_name, sf_updatedAt, updatedTime);

            query += `, installed_on = ?`;
            if (installed_on) queryParams.push(installed_on);
            else queryParams.push(null);

            query += `, credit_added_on = ?`;
            if (credit_added_on) queryParams.push(credit_added_on);
            else queryParams.push(null);

            query += `, sf_name = ?`;
            if (sf_name) queryParams.push(sf_name);
            else queryParams.push(null);

            query += ` WHERE sf_record_id = ?`;
            queryParams.push(refSfRecId);

            await customerServices.executeQuery(query, queryParams);

        } catch (error) {
            console.error("SalesforceService updateRefDetails -> ", error);
        }
    }

    async checkAndDeleteDetails(deletedData) {
        try {
            const ids = deletedData.map(record => record.id);
            const placeholders = ids.map(() => '?').join(', ');
            const deleteQuery = `DELETE FROM contacts_referrals WHERE sf_record_id IN (${placeholders})`;
            await customerServices.executeQuery(deleteQuery, ids);
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
        }
    }
}

module.exports = ReferralServices;