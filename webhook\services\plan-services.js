const axios = require('axios');
const CONFIG = require('../config');

class PlanServices {

    // Method to fetch plan details from JSON data
    async getPlanDetailsFromJSON(url) {
        try {
            const response = await axios.get(url);
            return response?.data || [];
        } catch (error) {
            return [];
        }
    }

    async getAddonsTelevisionPlanDetails(payload, subscriptionType) {
        try {
            const { plan_name, extra_packages, single_channels, iptv_products } = payload;

            // Retrieve television plan details from plan service
            const result = await this.getPlanDetails({ type: 'tv' });
            const planData = result?.data?.planDetails;
            const plan = planData.find(p => p.api_name === plan_name);

            const isMonthly = subscriptionType === "monthly";
            let addons = [];
            let totalAmount = 0;

            // Process plan details and calculate total amount
            if (plan.api_name === plan_name) {
                const planBillingPeriod = isMonthly ? plan.billing_period[0].monthly : plan.billing_period[0].yearly
                totalAmount += planBillingPeriod.price;
                addons.push({ id: planBillingPeriod.api_name });
            }

            // Process extra packages and add to addons list
            extra_packages.forEach(extra => {
                const extraPackage = plan.optional_extra_packages.find(pkg => pkg.api_name === extra);
                if (extraPackage) {
                    const extraPackagebillingPeriod = isMonthly ? extraPackage.billing_period[0].monthly : extraPackage.billing_period[0].yearly
                    totalAmount += extraPackagebillingPeriod.price;
                    addons.push({ id: extraPackagebillingPeriod.api_name });
                }
            });

            // Process IPTV products and add to addons list
            iptv_products.forEach(iptv => {
                const iptvProduct = plan.optional_iptv_products.find(prod => prod.api_name === iptv);
                if (iptvProduct) {
                    const iptvbillingPeriod = isMonthly ? iptvProduct.billing_period[0].monthly : iptvProduct.billing_period[0].yearly
                    totalAmount += iptvbillingPeriod.price;
                    addons.push({ id: iptvbillingPeriod.api_name });
                }
            });

            // Process single channels and add to addons list
            const totalSingleChannels = single_channels.length;
            if (totalSingleChannels > 0) {
                const X_PICK_5 = Math.floor(totalSingleChannels / 5);
                const X_SNGL_CHANNEL = totalSingleChannels % 5;

                const singleChannels = isMonthly ? CONFIG.chargebee.phonePlanDetails.monthlySingleChannels : CONFIG.chargebee.phonePlanDetails.yearlySingleChannels
                if (X_PICK_5 > 0) {
                    totalAmount += X_PICK_5 * singleChannels.pick5ChannelPrice;
                    addons.push({ id: singleChannels.pick5ChannelName, quantity: X_PICK_5 });
                }
                if (X_SNGL_CHANNEL > 0) {
                    totalAmount += X_SNGL_CHANNEL * singleChannels.pick1ChannelPrice;
                    addons.push({ id: singleChannels.pick1ChannelName, quantity: X_SNGL_CHANNEL });
                }
            }

            return { addons, totalAmount };
        } catch (error) {
            console.error("getAddonsTelevisionPlanDetails", error);
            return {};  // Return empty object if error occurs
        }
    }

    // Method to fetch plan details based on query type
    async getPlanDetails(query) {
        let data = {};
        try {
            const { type } = query;
            const planUrl = CONFIG[type]?.plans;
            const addOnsUrl = CONFIG[type]?.addons;
            const assetsUrl = CONFIG[type]?.assets;
            if (planUrl && addOnsUrl && assetsUrl) {
                data.planDetails = await this.getPlanDetailsFromJSON(planUrl);
                data.addonsDetails = await this.getPlanDetailsFromJSON(addOnsUrl);
                data.assetsDetails = await this.getPlanDetailsFromJSON(assetsUrl);
            }
            return { status: true, data };
        } catch (error) {
            console.error("getPlanDetails", error);
            return { status: false, data };
        }
    }
}

module.exports = PlanServices;
