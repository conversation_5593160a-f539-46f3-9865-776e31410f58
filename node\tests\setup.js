/**
 * Jest Setup File
 * 
 * This file runs before each test file and sets up the testing environment.
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_USER = 'test_user';
process.env.DB_PASSWORD = 'test_password';
process.env.DB_NAME = 'test_database';
process.env.JWT_SECRET = 'test_jwt_secret';
process.env.ELASTIC_SEARCH_NODE = 'http://localhost:9200';
process.env.ELASTIC_SEARCH_API_KEY = 'test_api_key';

// Global test utilities
global.testUtils = {
  // Mock user data
  mockUser: {
    id: '12345',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+1234567890'
  },

  // Mock authentication token
  mockAuthToken: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token',

  // Mock request headers
  mockHeaders: {
    'Authorization': 'Bearer test-token',
    'Content-Type': 'application/json',
    'X-Customer-ID': '12345'
  },

  // Helper function to create mock request
  createMockRequest: (data = {}, headers = {}) => ({
    body: data,
    headers: { ...global.testUtils.mockHeaders, ...headers },
    query: {},
    params: {}
  }),

  // Helper function to create mock response
  createMockResponse: () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.setHeader = jest.fn().mockReturnValue(res);
    return res;
  },

  // Helper function to create mock next function
  createMockNext: () => jest.fn()
};

// Global test configuration
global.testConfig = {
  timeout: 10000,
  retries: 2
};

// Console override for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Suppress console.error and console.warn during tests unless explicitly needed
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  // Restore original console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  
  // Clear all mocks after each test
  jest.clearAllMocks();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

console.log('🧪 Jest setup completed - Test environment ready!');
