const ContactSyncServices = require('../services/process-services/contact-sync-services');
const pool = require('../db');
const ElasticSearchMock = require('./mocks/elastic-search');
const SalesforceClientMock = require('./mocks/salesforce-client');
const ContactServices = require('../services/contact-sync-services');
const CustomerServices = require('../services/customer-sync-services');
const ReferralServices = require('../services/referral-services');
const SubscriptionInvoiceServices = require('../services/subscription-invoices-services');
const axios = require('axios');

// Mock all dependencies
jest.mock('../db');
jest.mock('../clients/elastic-search', () => jest.fn().mockImplementation(() => new ElasticSearchMock()));
jest.mock('../clients/salesforce-client', () => jest.fn().mockImplementation(() => new SalesforceClientMock()));
jest.mock('../services/contact-sync-services');
jest.mock('../services/customer-sync-services');
jest.mock('../services/referral-services', () => jest.fn().mockImplementation(() => new ReferralServicesMock()));
const ReferralServicesMock = require('./mocks/referral-services');
jest.mock('../services/subscription-invoices-services');
jest.mock('axios');

describe('ContactSyncServices', () => {
    let contactSyncServices;
    let mockElasticSearch;

    beforeEach(() => {
        jest.clearAllMocks();
        contactSyncServices = new ContactSyncServices();
        mockElasticSearch = new ElasticSearch();
        
        // Setup default mocks
        mockElasticSearch.insertDocument.mockResolvedValue({ _id: 'test-id', _index: 'test-index' });
        mockElasticSearch.updateDocument.mockResolvedValue({ result: 'updated' });

        const mockSalesforceConnection = new SalesforceClient();
        mockSalesforceConnection.getDeletedData.mockResolvedValue({
            deleteStatus: true,
            deletedData: [{ id: 1 }]
        });
        mockSalesforceConnection.getAddressDetailsById.mockResolvedValue({
            returnStatus: true,
            addresstData: { id: 1 }
        });
        mockSalesforceConnection.getDetailsByFieldAndApi.mockResolvedValue({
            returnStatus: true,
            salesforceData: [{ id: 1 }]
        });
    });

    describe('getContactDetails', () => {
        it('should sync contacts for email sync', async () => {
            const email = '<EMAIL>';
            const mockContacts = [{ id: 1, sf_record_id: 'SF123' }];
            const mockSingleContactRecords = [{ synctype: 'updateContacts', status: true }];
            const mockCardDetails = [{ synctype: 'getAndMapCardDetails', status: true }];

            // Mock database query
            pool.query.mockImplementation((query, values, callback) => {
                callback(null, mockContacts);
            });

            // Mock elastic search
            mockElasticSearch.insertDocument.mockResolvedValue({ _id: '123', _index: 'test' });
            mockElasticSearch.updateDocument.mockResolvedValue({});

            // Mock class methods
            jest.spyOn(contactSyncServices, 'checkAndManageSingleContacts')
                .mockResolvedValue(mockSingleContactRecords);
            jest.spyOn(contactSyncServices, 'getAndMapCardDetails')
                .mockResolvedValue(mockCardDetails);

            await contactSyncServices.getContactDetails(email);

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining("SELECT id, sf_record_id FROM contacts where email = '<EMAIL>'"),
                expect.any(Array),
                expect.any(Function)
            );
            expect(contactSyncServices.checkAndManageSingleContacts).toHaveBeenCalledWith(mockContacts);
            expect(contactSyncServices.getAndMapCardDetails).toHaveBeenCalledWith(mockContacts[0]);
        });

        it('should sync contacts for cron sync', async () => {
            const mockContacts = [{ id: 1, sf_record_id: 'SF123' }];
            const mockContactRecords = [{ synctype: 'getBulkDetails', status: true }];

            // Mock database query
            pool.query.mockImplementation((query, values, callback) => {
                callback(null, mockContacts);
            });

            // Mock elastic search
            mockElasticSearch.insertDocument.mockResolvedValue({ _id: '123', _index: 'test' });
            mockElasticSearch.updateDocument.mockResolvedValue({});

            // Mock methods
            jest.spyOn(contactSyncServices, 'checkAndManageContacts')
                .mockResolvedValue(mockContactRecords);
            jest.spyOn(contactSyncServices, 'deleteDetails')
                .mockResolvedValue({ execute: true, status: true });

            await contactSyncServices.getContactDetails();

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('SELECT id, sf_record_id FROM contacts'),
                expect.any(Array),
                expect.any(Function)
            );
            expect(contactSyncServices.checkAndManageContacts).toHaveBeenCalledWith(mockContacts);
        });

        it('should handle errors during sync', async () => {
            const error = new Error('Sync failed');
            pool.query.mockImplementation((query, values, callback) => {
                callback(error);
            });

            mockElasticSearch.insertDocument.mockResolvedValue({ _id: '123', _index: 'test' });
            mockElasticSearch.updateDocument.mockResolvedValue({});

            await contactSyncServices.getContactDetails();

            expect(mockElasticSearch.updateDocument).toHaveBeenCalledWith(
                'test',
                '123',
                expect.objectContaining({
                    'log.level': 'ERROR',
                    message: '24-hour sync failed'
                })
            );
        });
    });

    describe('deleteDetails', () => {
        it('should delete records from multiple services', async () => {
            const mockDeletedData = [{ id: 1 }];
            
            // Mock salesforce connection
            jest.spyOn(contactSyncServices, 'getDeleteDataFromSF')
                .mockResolvedValueOnce({ status: true, data: mockDeletedData }) // Contact
                .mockResolvedValueOnce({ status: true, data: mockDeletedData }) // Customer
                .mockResolvedValueOnce({ status: true, data: mockDeletedData }) // Referral
                .mockResolvedValueOnce({ status: true, data: mockDeletedData }); // Invoice

            const result = await contactSyncServices.deleteDetails();

            expect(result).toEqual({
                execute: true,
                synctype: 'deleteDetails',
                status: true,
                deleteCounts: {
                    contactDeleteCount: 1,
                    customerDeleteCount: 1,
                    referralDeleteCount: 1,
                    invoiceDeleteCount: 1
                }
            });
        });

        it('should handle errors during deletion', async () => {
            jest.spyOn(contactSyncServices, 'getDeleteDataFromSF')
                .mockRejectedValue(new Error('Delete failed'));

            const result = await contactSyncServices.deleteDetails();

            expect(result).toEqual({
                execute: false,
                synctype: 'deleteDetails',
                status: false,
                deleteCounts: {
                    contactDeleteCount: 0,
                    customerDeleteCount: 0,
                    referralDeleteCount: 0,
                    invoiceDeleteCount: 0
                },
                error: 'Delete failed'
            });
        });
    });

    describe('checkAndManageContacts', () => {
        it('should process contact details in chunks', async () => {
            const mockSfContacts = [
                { sf_record_id: 'SF1' },
                { sf_record_id: 'SF2' }
            ];

            const mockSalesforceConnection = new SalesforceClient();
            mockSalesforceConnection.getBulkDetails.mockResolvedValue([
                { Id: 'SF1', FirstName: 'John' },
                { Id: 'SF2', FirstName: 'Jane' }
            ]);

            const result = await contactSyncServices.checkAndManageContacts(mockSfContacts);

            expect(result).toContainEqual(
                expect.objectContaining({
                    synctype: 'getBulkDetails',
                    status: true,
                    contactDataCount: 2
                })
            );
        });

        it('should handle empty contact list', async () => {
            const result = await contactSyncServices.checkAndManageContacts([]);
            expect(result).toBeUndefined();
        });
    });

    describe('updateContacts', () => {
        it('should update contact details in database', async () => {
            const mockUpdateQuery = {
                id: 1,
                contactData: {
                    Id: 'SF1',
                    FirstName: 'John',
                    LastName: 'Doe',
                    Company_Name__c: 'Test Corp',
                    Primary_Phone__c: '1234567890',
                    LastModifiedDate: new Date().toISOString()
                }
            };

            pool.query.mockImplementation((query, values, callback) => {
                callback(null, { affectedRows: 1 });
            });

            const result = await contactSyncServices.updateContacts(mockUpdateQuery);

            expect(result).toEqual({
                execute: true,
                status: true,
                sf_record_id: 'SF1'
            });
            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('UPDATE contacts'),
                expect.any(Array),
                expect.any(Function)
            );
        });

        it('should handle update errors', async () => {
            const mockUpdateQuery = {
                id: 1,
                contactData: {
                    Id: 'SF1'
                }
            };

            pool.query.mockImplementation((query, values, callback) => {
                callback(new Error('Update failed'));
            });

            const result = await contactSyncServices.updateContacts(mockUpdateQuery);

            expect(result).toEqual({
                execute: true,
                status: false,
                sf_record_id: 'SF1',
                error: expect.any(Error)
            });
        });
    });

    describe('cleanValue', () => {
        it('should clean phone numbers', () => {
            expect(contactSyncServices.cleanValue('************', 'cleanPhone'))
                .toBe("'1234567890'");
        });

        it('should handle null values', () => {
            expect(contactSyncServices.cleanValue(null)).toBe('NULL');
        });

        it('should clean names', () => {
            expect(contactSyncServices.cleanValue('John"s`Name', 'cleanName'))
                .toBe("'JohnsName'"); // Note: No space as per implementation
        });

        it('should handle price values', () => {
            expect(contactSyncServices.cleanValue('not a number', 'cleanPrice')).toBe('NULL');
            const numericValue = contactSyncServices.cleanValue('123.45', 'cleanPrice');
            expect(numericValue).toBe('123.45');
        });
    });

    describe('getAndMapCardDetails', () => {
        let oldEnv;
        
        beforeEach(() => {
            oldEnv = process.env;
            process.env = { ...oldEnv, NODE_ENV: 'local', LOG_PREFIX: 'dev' };
            axios.get.mockReset();
            axios.get.mockResolvedValue({ data: { status: 'success' } });
        });

        afterEach(() => {
            process.env = oldEnv;
        });

        it('should map card details for a contact', async () => {
            const mockContact = { id: 1 };
            const result = await contactSyncServices.getAndMapCardDetails(mockContact);

            expect(axios.get).toHaveBeenCalledWith(
                'http://localhost:5009/auth/map-subscription',
                expect.any(Object)
            );
            expect(result).toEqual([{
                synctype: 'getAndMapCardDetails',
                status: true,
                contactID: 1
            }]);
        });

        it('should handle mapping errors', async () => {
            const mockContact = { id: 1 };
            axios.get.mockRejectedValue(new Error('Mapping failed'));

            const result = await contactSyncServices.getAndMapCardDetails(mockContact);
            expect(result).toEqual([{
                synctype: 'getAndMapCardDetails',
                status: false,
                contactID: 1,
                error: 'Mapping failed'
            }]);
        });

        it('should handle different environment URLs', async () => {
            const mockContact = { id: 1 };

            // Test local environment
            process.env.NODE_ENV = 'local';
            await contactSyncServices.getAndMapCardDetails(mockContact);
            expect(axios.get).toHaveBeenCalledWith(
                'http://localhost:5009/auth/map-subscription',
                expect.any(Object)
            );

            // Reset mock and test prod environment
            axios.get.mockClear();
            process.env.NODE_ENV = 'prod';
            process.env.LOG_PREFIX = 'prod';
            await contactSyncServices.getAndMapCardDetails(mockContact);
            expect(axios.get).toHaveBeenCalledWith(
                'https://customer-portal-prod-env.purplecowinternet.com/auth/map-subscription',
                expect.any(Object)
            );
        });
    });

    describe('getDeleteDataFromSF', () => {
        let mockSalesforceConnection;

        beforeEach(() => {
            mockSalesforceConnection = new SalesforceClientMock();
            contactSyncServices.salesforceConnection = mockSalesforceConnection;
        });

        it('should get deleted data from Salesforce', async () => {
            mockSalesforceConnection.getDeletedData.mockResolvedValue({
                deleteStatus: true,
                deletedData: [{ id: 1 }]
            });

            const result = await contactSyncServices.getDeleteDataFromSF('Contact');
            expect(result).toEqual({
                status: true,
                data: [{ id: 1 }]
            });
        });

        it('should handle empty deleted data', async () => {
            mockSalesforceConnection.getDeletedData.mockResolvedValue({
                deleteStatus: true,
                deletedData: []
            });

            const result = await contactSyncServices.getDeleteDataFromSF('Contact');
            expect(result).toEqual({
                status: false,
                data: null
            });
        });

        it('should handle Salesforce errors', async () => {
            mockSalesforceConnection.getDeletedData.mockRejectedValue(new Error('Salesforce error'));

            const result = await contactSyncServices.getDeleteDataFromSF('Contact');
            expect(result).toEqual({
                status: false,
                data: null
            });
        });
    });

    describe('checkAndManageSingleContacts', () => {
        it('should process single contact details', async () => {
            const mockSfContactDetails = [
                { id: 1, sf_record_id: 'SF1' }
            ];
            
            const mockSalesforceConnection = new SalesforceClient();
            mockSalesforceConnection.getAddressDetailsById.mockResolvedValue({
                returnStatus: true,
                addresstData: {
                    Id: 'SF1',
                    FirstName: 'John'
                }
            });

            jest.spyOn(contactSyncServices, 'updateContacts')
                .mockResolvedValue({ execute: true, status: true });

            const result = await contactSyncServices.checkAndManageSingleContacts(mockSfContactDetails);
            
            expect(result).toContainEqual(
                expect.objectContaining({
                    synctype: 'updateContacts'
                })
            );
        });

        it('should handle non-existent contact', async () => {
            const mockSfContactDetails = [
                { id: 1, sf_record_id: 'SF1' }
            ];

            const mockSalesforceConnection = new SalesforceClient();
            mockSalesforceConnection.getAddressDetailsById.mockResolvedValue({
                returnStatus: false,
                addresstData: null
            });

            const mockContactServices = new ContactServices();
            mockContactServices.deleteOtherContactDetails.mockResolvedValue({
                execute: true,
                status: true
            });

            const result = await contactSyncServices.checkAndManageSingleContacts(mockSfContactDetails);
            
            expect(result).toContainEqual(
                expect.objectContaining({
                    synctype: 'deleteOtherContactDetails'
                })
            );
        });

        it('should handle referral details', async () => {
            const mockSfContactDetails = [
                { id: 1, sf_record_id: 'SF1' }
            ];

            const mockSalesforceConnection = new SalesforceClient();
            mockSalesforceConnection.getAddressDetailsById.mockResolvedValue({
                returnStatus: true,
                addresstData: { Id: 'SF1' }
            });
            mockSalesforceConnection.getDetailsByFieldAndApi.mockResolvedValue({
                returnStatus: true,
                salesforceData: [{ id: 'REF1' }]
            });

            const result = await contactSyncServices.checkAndManageSingleContacts(mockSfContactDetails);
            
            expect(result).toContainEqual(
                expect.objectContaining({
                    synctype: 'referralServicesManageDetails'
                })
            );
        });
    });

    describe('executeQuery', () => {
        it('should execute database query successfully', async () => {
            pool.query.mockImplementation((query, values, callback) => {
                callback(null, [{ id: 1 }]);
            });

            const result = await contactSyncServices.executeQuery('SELECT * FROM contacts');
            expect(result).toEqual([{ id: 1 }]);
        });

        it('should handle database query errors', async () => {
            pool.query.mockImplementation((query, values, callback) => {
                callback(new Error('Database error'));
            });

            await expect(contactSyncServices.executeQuery('SELECT * FROM contacts'))
                .rejects.toThrow('Database error');
        });
    });
});
