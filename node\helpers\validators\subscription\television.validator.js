const Joi = require('joi');

const televisionUpdateValidation = Joi.object({
    customer_details_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer details ID is required.',
            'number.base': 'Customer details ID must be a number.',
            'number.integer': 'Customer details ID must be an integer.'
        }),
    customer_subscription_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer subscription ID is required.',
            'number.base': 'Customer subscription ID must be a number.',
            'number.integer': 'Customer subscription ID must be an integer.'
        }),
    plan_name: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Plan name is required.'
        }),
    extra_packages: Joi.array()
        .items(Joi.string().trim())
        .required()
        .messages({
            'any.required': 'Extra packages are required.',
            'array.base': 'Extra packages must be an array of strings.'
        }),
    single_channels: Joi.array()
        .items(Joi.string().trim())
        .required()
        .messages({
            'any.required': 'Single channels are required.',
            'array.base': 'Single channels must be an array of strings.'
        }),
    iptv_products: Joi.array()
        .items(Joi.string().trim())
        .required()
        .messages({
            'any.required': 'IPTV products are required.',
            'array.base': 'IPTV products must be an array of strings.'
        })
});

module.exports = { televisionUpdateValidation };
