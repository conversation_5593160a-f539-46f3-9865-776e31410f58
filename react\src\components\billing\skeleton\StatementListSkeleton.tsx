import React from "react";
import ContentLoader from "react-content-loader";

const StatementListSkeleton: React.FC = (props) => {
  return (
    <ContentLoader
      width={"100%"}
      height={200}
      backgroundColor="#f5f5f5"
      foregroundColor="#dbdbdb"
      {...props}
    >
      {[1, 2, 3].map((item) => {
        return (
          <React.Fragment key={item}>
            <rect x="0" y="10" rx="5" ry="5" width="100%" height="50" />
            <rect x="0" y="70" rx="5" ry="5" width="100%" height="50" />
            <rect x="0" y="130" rx="5" ry="5" width="100%" height="50" />
          </React.Fragment>
        );
      })}
    </ContentLoader>
  );
};

export default StatementListSkeleton;
