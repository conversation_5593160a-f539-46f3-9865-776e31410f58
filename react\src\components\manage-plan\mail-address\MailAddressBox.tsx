import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { AddIcon, EditIcon } from "../../../assets/Icons";
import { useUpdateAddressMutation } from "../../../services/api";
import { logout } from "../../../store/reducers/authenticationReducer";
import {
  setCurrentMailingAddress,
  toggleShowMailingAddressConfirmationPopup,
  toggleShowMailingAddressPopup,
} from "../../../store/reducers/mailingAddressReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import {
  currentAddress,
  showMailingAddressPopup,
} from "../../../store/selectors/mailingAddressSelectors";
import AddLocationPopup from "../../dashboard/AddLocationPopup";
import ServiceBox from ".././ServiceBox";

interface MailAddressProps {
  refetch: () => void;
}

const MailAddressBox: React.FC<MailAddressProps> = ({ refetch }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const currAddress = useSelector(currentAddress);
  const showPopup = useSelector(showMailingAddressPopup);
  const [updateAddress, updateAddressLoading] = useUpdateAddressMutation();

  const initialAddressState = {
    address: "",
    apartment: "",
    city: "",
    province: "",
    pinCode: "",
  };
  const [mailingAddressData, setMailingAddressData] =
    useState(initialAddressState);

  // Function to handle popup submission
  const handlePopupSubmit = async () => {
    try {
      const data = {
        customer_details_id: parseInt(id as string),
        address_id: currAddress?.id,
        street_number: mailingAddressData?.streetNumber,
        street_name: mailingAddressData?.streetName,
        suit_unit: mailingAddressData?.apartment,
        town: mailingAddressData?.city,
        province: mailingAddressData?.province,
        postal_code: mailingAddressData?.pinCode,
        country: "Canada",
      };

      const response = await updateAddress(data).unwrap();

      if (response.status === 200) {
        dispatch(
          addNotification({ type: "success", message: response.message })
        );
        dispatch(toggleShowMailingAddressPopup());
        dispatch(setCurrentMailingAddress(mailingAddressData));
        dispatch(toggleShowMailingAddressConfirmationPopup());
        refetch();
      }
    } catch (error: any) {
      if (error?.data?.error?.length > 0) {
        dispatch(
          addNotification({
            type: "error",
            message: error?.data?.error?.[0]?.message,
          })
        );
      } else {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
      }
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  return (
    <ServiceBox
      isEdit={currAddress?.full_address}
      attributes={{
        onClick: () => dispatch(toggleShowMailingAddressPopup()),
      }}
    >
      {currAddress?.full_address ? (
        <div className="flex 2xl:gap-5 gap-2.5 flex-col">
          <div className="flex gap-5 justify-between h-[40px] items-center">
            <div className="flex">
              <p className="text-xl 2xl:text-[26px] font-medium">
                Mailing address
              </p>
            </div>
            <div
              className="border h-max border-[#D7B9FF] bg-white 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-purple-50"
              onClick={() => dispatch(toggleShowMailingAddressPopup())}
            >
              <EditIcon width={20} height={20} />
            </div>
          </div>
          <div>
            <p className="text-base uppercase">{currAddress.full_address}</p>
          </div>
        </div>
      ) : (
        <div className="flex rounded-20 justify-between items-center">
          <div className="flex justify-center">
            <p className="text-xl 2xl:text-[26px] font-medium">
              Mailing address
            </p>
          </div>
          <div
            className="border border-black 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey"
            onClick={() => dispatch(toggleShowMailingAddressPopup())}
          >
            <AddIcon width={20} height={20} />
          </div>
        </div>
      )}
      {showPopup && (
        <AddLocationPopup
          formData={mailingAddressData}
          setFormData={setMailingAddressData}
          title={
            currAddress?.full_address
              ? "Update mailing address"
              : "Check your mail address"
          }
          description={
            "Enter your new address below to check service availability."
          }
          currAddress={currAddress}
          handleSubmit={handlePopupSubmit}
          closeHandler={() => {
            if (!updateAddressLoading?.isLoading) {
              setMailingAddressData({});
              dispatch(toggleShowMailingAddressPopup());
            }
          }}
          isLoading={updateAddressLoading?.isLoading}
        />
      )}
    </ServiceBox>
  );
};

export default MailAddressBox;
