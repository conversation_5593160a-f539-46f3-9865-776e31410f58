import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { AddIcon, EditIcon } from "../../../assets/Icons";
import {
  useCancelServiceMutation,
  usePhoneUpdateMutation,
  usePhoneUpdateProratedMutation,
} from "../../../services/api";
import {
  setPhonePlan,
  toggleShowDeleteOrderPopup,
  toggleShowHomePhoneChangeConfirmationPopup,
  toggleShowHomePhonePopup,
  toggleShowSelectNumberPopup,
} from "../../../store/reducers/homePhoneReducer";
import {
  homePhoneState,
  showHomePhoneChangeConfirmationPopup,
  showHomePhonePopup,
} from "../../../store/selectors/homePhoneSelectors";
import {
  _TIMEZONE,
  formatCurrency,
  formatDate,
  regEx,
} from "../../../utils/helper";

import useMediaQuery from "../../../hooks/MediaQueryHook";
import { logout } from "../../../store/reducers/authenticationReducer";
import { addNotification } from "../../../store/reducers/toasterReducer";
import { HomePhonePlanState } from "../../../typings/typing";
import Button from "../../common/Button";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import Popup from "../../common/Popup";
import ServiceBox from "../ServiceBox";
import AddHomePhone from "./AddHomePhone";
import SelectNumberPopup from "./SelectNumberPopup";
import moment from "moment-timezone";

interface HomePhoneBoxProps {
  refetch: () => void;
  subscriptionID?: number;
  billingDate?: string;
  stage?: string;
  isMoveOrder: any;
}

const validateFormData = (data) => {
  const errors = {};
  let isValid = true;

  if (data?.phoneNumber?.length < 8) {
    isValid = false;
    errors.phoneNumber = "Enter valid phone number";
  } else if (!regEx.PHONE.test(data?.phoneNumber)) {
    isValid = false;
    errors.phoneNumber = "Invalid phone number";
  }

  return { isValid, errors };
};

const HomePhoneBox: React.FC<HomePhoneBoxProps> = ({
  refetch,
  subscriptionID,
  billingDate,
  stage,
  isMoveOrder,
}) => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const homePlan = useSelector(homePhoneState);
  const isDesktop = useMediaQuery("(min-width:1300px");
  const showPopup = useSelector(showHomePhonePopup);
  const [phoneError, setPhoneError] = useState({});
  const showConfirmationPopup = useSelector(
    showHomePhoneChangeConfirmationPopup
  );
  const [phoneUpdate, phoneUpdateLoading] = usePhoneUpdateMutation();
  const [phoneEstimate, phoneEstimateLoading] =
    usePhoneUpdateProratedMutation();
  const [cancelPhoneService, cancelPhoneServiceLoading] =
    useCancelServiceMutation();
  const [proratedData, setProratedData] = useState<any>({});
  const [isCreate, setIsCreate] = useState<boolean>(false);
  const [isCancel, setIsCancel] = useState<boolean>(false);

  // Initial state
  const initialHomePhonePlanState = {
    haveHomePhonePlan: homePlan?.haveHomePhonePlan,
    isNewPhoneNumber: homePlan?.isNewPhoneNumber,
    phoneNumber: homePlan?.phoneNumber,
    isPlanRemoved: homePlan?.isPlanRemoved,
  };
  const [homePhonePlan, setHomePhonePlan] = useState<HomePhonePlanState>(
    initialHomePhonePlanState
  );

  // Function to handle home phone plan change
  const handleHomePhonePlanChange = (key: string, value: string | boolean) => {
    setHomePhonePlan((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Function to handle cancel service
  const handleCancelService = async () => {
    dispatch(toggleShowHomePhoneChangeConfirmationPopup());
    dispatch(toggleShowHomePhonePopup());
  };

  // Function to handle the cancel confirmation
  const handleCancelConfirmation = async () => {
    if (!cancelPhoneServiceLoading?.isLoading) {
      try {
        const data = {
          cancel_type: "phone",
          customer_details_id: parseInt(id as string),
          customer_subscription_id: subscriptionID,
        };
        const response = await cancelPhoneService(data).unwrap();
        if (response.status === 200) {
          setHomePhonePlan((prev) => ({
            ...prev,
            isPlanRemoved: true,
          }));
          dispatch(toggleShowHomePhoneChangeConfirmationPopup());
          dispatch(setPhonePlan(homePhonePlan));
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
          refetch();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to handle popup submission
  const handleNextPopup = () => {
    dispatch(toggleShowHomePhonePopup());
    // if (homePhonePlan.isPlanRemoved) {
    //   dispatch(toggleShowHomePhoneChangeConfirmationPopup());
    //   setHomePhonePlan({
    //     haveHomePhonePlan: {},
    //     isNewPhoneNumber: true,
    //     phoneNumber: "",
    //     isPlanRemoved: true,
    //   });
    // } else {
    //   dispatch(toggleShowSelectNumberPopup());
    // }
    dispatch(toggleShowSelectNumberPopup());
  };

  // Function to handle number selection
  const handleSubmitNumber = async () => {
    try {
      let data = {
        customer_details_id: parseInt(id as string),
        customer_subscription_id: subscriptionID,
        phone_plan_id: homePhonePlan?.haveHomePhonePlan?.api_name,
      };
      if (homePhonePlan?.isNewPhoneNumber) {
        data = {
          ...data,
          phone_number_type: "new",
        };
      } else {
        data = {
          ...data,
          phone_number_type: "existing",
          phone_number: homePhonePlan?.phoneNumber,
        };
      }
      const response = await phoneEstimate(data).unwrap();
      if (response.status === 200) {
        setProratedData({
          ...data,
          prorated_amount: response?.data?.prorated_amount,
        });
        dispatch(toggleShowSelectNumberPopup());
        dispatch(toggleShowHomePhoneChangeConfirmationPopup());
      }
    } catch (error: any) {
      if (error?.data?.error?.length > 0) {
        dispatch(
          addNotification({
            type: "error",
            message: error?.data?.error?.[0]?.message,
          })
        );
      } else {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
      }
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle form submission
  const handleSubmit = (): void => {
    if (homePhonePlan?.isNewPhoneNumber) {
      handleSubmitNumber();
    } else {
      const { isValid, errors } = validateFormData(homePhonePlan);
      setPhoneError(errors);
      if (isValid && !phoneEstimateLoading?.isLoading) {
        setPhoneError({});
        handleSubmitNumber();
      }
    }
  };

  // Function to handle confirmation submission
  const handleConfirmationSubmit = async () => {
    if (!phoneUpdateLoading?.isLoading) {
      try {
        const data = {
          ...proratedData,
        };
        const response = await phoneUpdate(data).unwrap();
        if (response.status === 200) {
          dispatch(setPhonePlan(homePhonePlan));
          dispatch(toggleShowHomePhoneChangeConfirmationPopup());
          dispatch(
            addNotification({ type: "success", message: response?.message })
          );
          refetch();
        }
      } catch (error: any) {
        dispatch(
          addNotification({ type: "error", message: error?.data?.message })
        );
        if (
          error?.data?.status === 403 ||
          error?.data?.status === 401 ||
          error?.data?.message === "User not found."
        ) {
          localStorage.clear();
          dispatch(logout());
          navigate("/login");
        }
      }
    }
  };

  // Function to handle delete order confirmation
  const handleDeleteOrderConfirm = () => {
    setHomePhonePlan({
      haveHomePhonePlan: null,
      isNewPhoneNumber: true,
      phoneNumber: "",
      isPlanRemoved: false,
    });
    dispatch(
      setPhonePlan({
        haveHomePhonePlan: null,
        isNewPhoneNumber: true,
        phoneNumber: "",
        isPlanRemoved: false,
      })
    );
    dispatch(toggleShowDeleteOrderPopup());
  };

  // FOR LOGIC
  // CP_Stage__c =>  stage
  // Latest_Phone_VOIP__c =>  homePlan?.haveHomePhonePlan
  // Latest_Phone_VOIP__c.Account_Status__c => homePlan?.haveHomePhonePlan?.account_status
  // Latest_Cancel_Phone_Order__c => homePlan?.haveHomePhonePlan?.requested_cancellation_date
  useEffect(() => {
    if (subscriptionID) {
      if (
        (stage === "Online" ||
          stage === "Outstanding" ||
          stage === "Payment arrangements") &&
        homePlan?.haveHomePhonePlan !== null &&
        homePlan?.haveHomePhonePlan?.requested_cancellation_date === null &&
        homePlan?.haveHomePhonePlan?.account_status === "ACTIVE"
      ) {
        setIsCancel(true);
      } else if (
        stage === "Onboarding" ||
        stage === "Online" ||
        stage === "Outstanding" ||
        stage === "Payment arrangements"
      ) {
        if (homePlan?.haveHomePhonePlan === null) {
          setIsCreate(true);
        } else if (
          homePlan?.haveHomePhonePlan !== null &&
          homePlan?.haveHomePhonePlan?.account_status === "DELETED" &&
          homePlan?.haveHomePhonePlan?.requested_cancellation_date == null
        ) {
          setIsCreate(true);
        } else {
          setIsCreate(false);
          setIsCancel(false);
        }
      }
    }
  }, [subscriptionID, stage, homePlan?.haveHomePhonePlan]);

  const forNewNumber =
    homePlan?.haveHomePhonePlan !== null &&
    homePlan?.haveHomePhonePlan?.account_status !== "ACTIVE";

  const forPortingNumber =
    homePlan?.haveHomePhonePlan !== null &&
    homePlan?.haveHomePhonePlan?.sf_phone_type?.sf_phone_type === "existing" &&
    homePlan?.haveHomePhonePlan?.account_status === "ACTIVE" &&
    homePlan?.haveHomePhonePlan?.service_start_date &&
    moment(new Date()).tz(_TIMEZONE).format("YYYY-MM-DD HH:mm:ss") <=
      moment(homePlan?.haveHomePhonePlan?.service_start_date)
        .tz(_TIMEZONE)
        .format("YYYY-MM-DD HH:mm:ss");

  const showHomePhoneBannerCreate = forNewNumber || forPortingNumber;

  return (
    <ServiceBox border={homePlan?.haveHomePhonePlan ? false : true}>
      <div className="flex flex-col 2xl:gap-5 gap-2.5">
        <div className="flex gap-5 justify-between">
          <div className="flex">
            <p className="text-xl 2xl:text-[26px] font-medium">Home phone</p>
          </div>
          <div
            className={`border border-[#D7B9FF] 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 bg-white hover:bg-purple-50 ${
              !isMoveOrder && (isCreate || isCancel)
                ? ""
                : "opacity-50 custom-cursor-not-allowed"
            }`}
            onClick={() => {
              !isMoveOrder &&
                (isCreate || isCancel) &&
                dispatch(toggleShowHomePhonePopup());
            }}
          >
            {homePlan?.haveHomePhonePlan ? (
              <EditIcon width={20} height={20} />
            ) : (
              <AddIcon width={20} height={20} />
            )}
          </div>
        </div>

        {homePlan?.haveHomePhonePlan &&
          !showPopup &&
          !showConfirmationPopup && (
            <div>
              <p className="text-base font-medium">
                {homePlan?.haveHomePhonePlan?.plan_price
                  ? formatCurrency(
                      homePlan?.haveHomePhonePlan?.plan_price,
                      true
                    )
                  : 0}{" "}
                - {homePlan?.haveHomePhonePlan?.plan_name}
              </p>
            </div>
          )}
      </div>

      {/* After Creation request  */}
      {!homePlan?.haveHomePhonePlan?.requested_cancellation_date &&
        showHomePhoneBannerCreate && (
          <div className="bg-white border border-[#FBC400] flex flex-col justify-center 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
            <div className="flex gap-2.5 items-center">
              <div className="bg-warning-light  rounded-[6px] p-2 gap-2.5">
                <p className=" text-warning">Processing</p>
              </div>
            </div>
            <div>
              <p className="text-base font-bold">
                Your home phone plan will be updated.
              </p>
            </div>
          </div>
        )}

      {/* After Cancellation request  */}
      {homePlan?.haveHomePhonePlan?.requested_cancellation_date && (
        <div className="bg-white border border-[#FBC400] flex flex-col justify-center 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
          <div className="flex gap-2.5 items-center">
            <div className="bg-warning-light  rounded-[6px] p-2 gap-2.5">
              <p className=" text-warning">Processing</p>
            </div>
          </div>
          <div>
            <p className="text-base font-bold">{`Home Phone service will be cancel as of ${
              homePlan?.haveHomePhonePlan?.requested_cancellation_date
                ? formatDate(
                    homePlan?.haveHomePhonePlan?.requested_cancellation_date
                  )
                : ""
            }`}</p>
          </div>
        </div>
      )}
      {homePlan?.showDeleteOrderPopup && (
        <Popup
          title="Delete this order"
          closeHandler={() => dispatch(toggleShowDeleteOrderPopup())}
        >
          <div className="flex flex-col gap-10">
            <div>
              <p className="text-base">
                Please confirm your wish to delete this order.
              </p>
            </div>
            <div className="flex gap-2.5 lg:gap-5 lg:flex-row flex-col ">
              <div className="basis-full">
                <Button
                  title="Go back"
                  btnType="transparent"
                  type="button"
                  clickEvent={() => dispatch(toggleShowDeleteOrderPopup())}
                  className="!border-black !text-[#111111]"
                />
              </div>
              <div className="basis-full">
                <Button
                  title="Confirm"
                  type="submit"
                  clickEvent={handleDeleteOrderConfirm}
                />
              </div>
            </div>
          </div>
        </Popup>
      )}
      {/* Home Phone Popup */}
      {homePlan?.showHomePhonePopup && (
        <Popup
          title="Home Phone"
          closeHandler={() => {
            if (!cancelPhoneServiceLoading?.isLoading) {
              setHomePhonePlan(homePlan);
              dispatch(toggleShowHomePhonePopup());
            }
          }}
          width={isDesktop ? "974px" : "840px"}
          height="501px"
        >
          <AddHomePhone
            homePhonePlan={homePhonePlan}
            handleHomePhonePlanChange={handleHomePhonePlanChange}
            handleSubmit={handleNextPopup}
            closeHandler={() => {
              setHomePhonePlan(homePlan);
              dispatch(toggleShowHomePhonePopup());
            }}
            handleCancelService={handleCancelService}
            cancelLoading={cancelPhoneServiceLoading?.isLoading}
          />
        </Popup>
      )}
      {homePlan?.showHomePhoneChangeConfirmationPopup && (
        <ConfirmationMessagePopup
          title={
            homePlan?.haveHomePhonePlan ? "Removing Phone" : "Adding Home Phone"
          }
          message={
            homePlan?.haveHomePhonePlan
              ? `Your home phone will be cancel at the end of your billing cycle of <b>${billingDate}</b> You should be receiving a text shortly with instructions on how to return the home phone box.`
              : homePhonePlan.isNewPhoneNumber
              ? `Please confirm to add home phone to your account. Once complete your account will be charge ${formatCurrency(
                  proratedData?.prorated_amount
                    ? proratedData?.prorated_amount
                    : 0
                )} and we will mail you your phone box. We will also text you your new phone number as soon as it is provisioned.`
              : `Please confirm to add home phone to your account. Once complete your account will be charge ${formatCurrency(
                  proratedData?.prorated_amount
                    ? proratedData?.prorated_amount
                    : 0
                )} and we will mail you your phone box. We will also begin with porting your phone number ${
                  proratedData?.phone_number
                }. This process can take up to 10 business days depending on the phone carrier your number is currently with.`
          }
          isLoading={
            homePlan?.haveHomePhonePlan
              ? cancelPhoneServiceLoading?.isLoading
              : phoneUpdateLoading?.isLoading
          }
          handleSubmit={
            homePlan?.haveHomePhonePlan
              ? handleCancelConfirmation
              : handleConfirmationSubmit
          }
          btnText="Confirm"
          closeHandler={() => {
            dispatch(toggleShowHomePhoneChangeConfirmationPopup());
            if (homePlan?.haveHomePhonePlan) {
              dispatch(toggleShowHomePhonePopup());
              setHomePhonePlan((prev) => ({
                ...prev,
                haveHomePhonePlan: null,
              }));
            } else {
              dispatch(toggleShowSelectNumberPopup());
            }
          }}
        />
      )}
      {homePlan?.showSelectNumberPopup && (
        <SelectNumberPopup
          homePhonePlan={homePhonePlan}
          handleHomePhonePlanChange={handleHomePhonePlanChange}
          handleSubmit={handleSubmit}
          isLoading={phoneEstimateLoading?.isLoading}
          phoneError={phoneError}
          closeHandler={() => {
            dispatch(toggleShowSelectNumberPopup());
            dispatch(toggleShowHomePhonePopup());
          }}
        />
      )}
    </ServiceBox>
  );
};

export default HomePhoneBox;
