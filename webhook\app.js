const express = require('express');
const app = express();
const { exceptionHandling } = require('./helper/exceptionHandling');
const cors = require("cors");
const bodyParser = require('body-parser');
require('body-parser-xml')(bodyParser);
require('./db');
const CONFIG = require('./config');
const axios = require('axios');

// Allow Cross-Origin requests
app.use(cors());
app.set('trust proxy', 1);

// Configure body-parser to handle XML
app.use(bodyParser.xml({
  xmlParseOptions: {
    explicitArray: false,
    ignoreAttrs: false,   // Allows attributes to be parsed
    attrkey: '$'          // Key used to store attributes
  }
}));

app.use(express.urlencoded({ extended: true }));
app.use(express.json({ limit: "50mb" }));

// Load the routes
require("./routes/webhook-route")(app);

// Middleware to define a route for the root URL
app.get('/', (req, res) => {
  res.status(200).send({ status: 200 })
});

const checkAuthorization = (req) => {
  const authHeader = req.headers['authorization'];

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    throw new Error('Unauthorized access');
  }

  const base64Credentials = authHeader.split(' ')[1];
  const credentials = Buffer.from(base64Credentials, 'base64').toString('utf8');
  const [username, password] = credentials.split(':');

  if (username !== CONFIG.RESGISTRATION_USERNAME || password !== CONFIG.RESGISTRATION_PASSWORD) {
    throw new Error('Unauthorized access');
  }
};

const handleSyncRequest = async (url, res) => {
  try {
    const response = await axios.get(url);
    res.send(response.data);
  } catch (err) {
    res.status(400).send('Error in syncing contacts');
  }
};

const handleDeleteContactRequest = async (email, res) => {
  try {
    const response = await axios.post('http://127.0.0.1:4000/delete-contact', { email });
    res.send(response.data);
  } catch (err) {
    res.status(400).send('Error in deleting contact');
  }
};

app.get('/get-contacts-and-sync', async (req, res, next) => {
  try {
    checkAuthorization(req);
    const { email } = req.query;
    const baseUrl = 'http://127.0.0.1:4000/get-contacts-and-sync';
    const url = email ? `${baseUrl}?email=${encodeURIComponent(email)}` : baseUrl;
    await handleSyncRequest(url, res);
  } catch (error) {
    next(error);
  }
})

app.post('/delete-contact', async (req, res, next) => {

  try {
    checkAuthorization(req);
    const { email } = req.body;
    if (!email) throw new Error('Email not found.');
    await handleDeleteContactRequest(email, res);
  } catch (error) {
    next(error);
  }
});


// Exception handling middleware
app.use(exceptionHandling);

const PORT = CONFIG.NODE_ENV == "local" ? process.env.PORT : 8080;
app.listen(PORT, () => {
  console.log(`The server is running on port ${PORT}`);
});