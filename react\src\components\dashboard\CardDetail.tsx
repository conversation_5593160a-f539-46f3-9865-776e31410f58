import React, { MouseEvent, useEffect, useState } from "react";

import { AddIcon } from "../../assets/Icons";
import Button from "../common/Button";
import CardDetailSkeleton from "./skeletons/CardDetailSkeleton";
import PaymentCard from "../common/PaymentCard";
import { addNotification } from "../../store/reducers/toasterReducer";
import { formatCurrency } from "../../utils/helper";
import { useDispatch } from "react-redux";
import { useMakePaymentMutation } from "../../services/api";
import useMediaQuery from "../../hooks/MediaQueryHook";
import { useNavigate } from "react-router-dom";
import { logout } from "../../store/reducers/authenticationReducer";

interface cardDetailProps {
  outstandingData: object;
  page?: string;
  cardsData: object;
  cardLoading?: boolean;
  setIsAddCard: (data?: any) => void;
  refetch: () => void;
  closeHandler?: (event: MouseEvent<HTMLSpanElement>) => void;
  showNestedPopup: boolean;
  setShowNestedPopup: void;
  setCustomIndex: void;
}

const CardDetail: React.FC<cardDetailProps> = ({
  outstandingData,
  cardsData,
  cardLoading,
  page,
  setIsAddCard,
  refetch,
  closeHandler,
  showNestedPopup,
  setShowNestedPopup,
  setCustomIndex,
}) => {
  const [isSelected, setIsSelected] = useState(Array(3).fill(false));
  // const [displaySubmit, setDisplaySubmit] = useState<boolean>(false);
  const isDesktop = useMediaQuery("(min-width:1025px)");
  const [makePayment, makePaymentLoading] = useMakePaymentMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const primaryCard =
    cardsData?.length > 0 && cardsData.find((card) => card.is_primary === "1");

  useEffect(() => {
    if (primaryCard) {
      setIsSelected(primaryCard);
    }
  }, [primaryCard]);

  // Handle the selected card if no card is selected it'll take the default primary card
  const handleSelectCard = (data: object) => {
    if (isSelected?.id == data?.id) {
      setIsSelected(primaryCard);
    } else {
      setIsSelected(data);
    }
  };

  // Handle Payment
  const handleMakePayment = async (data) => {
    try {
      const paymentData = {
        invoice_id: outstandingData?.id,
        card_id: data?.id,
      };
      const response = await makePayment(paymentData).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({
            type: "success",
            message: response?.message,
          })
        );
        refetch();
        closeHandler();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  return (
    <div className="make-payment-container">
      <div className="description lg:w-[664px] lg:h-[40px] text-base  leading-[21.6px]">
        <span className=" font-normal">
          The following balance is due on your account. Review and submit to
          make a one-time payment for this amount to the payment method noted
          below.
        </span>
      </div>
      {cardLoading ? (
        <div className="mt-[40px]">
          <CardDetailSkeleton />
        </div>
      ) : (
        <>
          <div className="card-info bg-[#F5EFFF] mt-[40px] lg:p-30 lg:pb-[10px] p-5 rounded-[20px]">
            <div className="payment-method flex flex-col">
              {page !== "billing" && (
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium">
                    <span>Automatic Payment method</span>
                  </div>
                  <div className="max-lg:hidden due-balance flex items-center gap-30">
                    <div>
                      <span className="text-sm font-medium">
                        Your balance due
                      </span>
                    </div>
                    <div className="text-[26px] font-bold">
                      <span>
                        {formatCurrency(
                          outstandingData?.total_outstanding
                            ? outstandingData?.total_outstanding
                            : 0,
                          true
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              )}
              <div className="card-details-container flex max-lg:flex-col gap-5 lg:gap-[14px] justify-between">
                <div className="flex gap-4 overflow-x-auto flex-1 pb-4">
                  {cardsData?.length > 0 &&
                    cardsData.map((card, index) => {
                      return (
                        <React.Fragment key={card?.id}>
                          <PaymentCard
                            data={card}
                            isPrimary={card?.is_primary === "1" ? true : false}
                            handleSelectCard={handleSelectCard}
                            isSelected={isSelected?.id === card?.id}
                            refetch={refetch}
                            showNestedPopup={showNestedPopup}
                            setShowNestedPopup={setShowNestedPopup}
                            setCustomIndex={setCustomIndex}
                            index={index}
                          />
                        </React.Fragment>
                      );
                    })}
                </div>
                <div className="edit-button lg:mt-5 flex max-lg:w-full">
                  <button
                    onClick={() => setIsAddCard(true)}
                    className={`${
                      isDesktop && "vertical-text"
                    } text-base font-medium max-lg:w-full flex flex-wrap justify-center items-center gap-2.5 p-[10px] border border-black rounded-20 cursor-pointer lg:h-[156px]`}
                  >
                    Add New Card &nbsp; +
                  </button>
                </div>
                {page !== "billing" && (
                  <div className="lg:hidden due-balance flex flex-col gap-5">
                    <div>
                      <span className="text-sm font-medium">
                        YOUR BALANCE DUE
                      </span>
                    </div>
                    <div className="text-[26px] font-bold">
                      <span>
                        $
                        {outstandingData?.total_outstanding
                          ? outstandingData?.total_outstanding
                          : 0}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-[20px]">
            <Button
              title="Submit"
              clickEvent={() => handleMakePayment(isSelected)}
              isLoading={makePaymentLoading?.isLoading}
              className="shadow-buttonShadow"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default CardDetail;
