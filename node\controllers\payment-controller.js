const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const PaymentServices = require("../services/payment-services");
const paymentServices = new PaymentServices();

class PaymentController {

    /**
     * Fetch outstanding payments for the authenticated user.
     * Responds with success and data if successful, else throws an error.
     */
    async fetchOutstanding(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { type, subscriptionId } = req.query;
            const { status, data } = await paymentServices.fetchOutstanding(id, type, subscriptionId);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`PaymentController fetchOutstanding ->`, error);
            next(error);
        }
    }

    /**
     * Make a payment for the authenticated user.
     * Responds with success and data if successful, else throws an error.
     */
    async makePayment(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status, data } = await paymentServices.makePayment(id, req.body, req.elasticLogObj);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`PaymentController makePayment ->`, error);
            next(error);
        }
    }

    /**
     * Make payment arrangements for the authenticated user.
     * Currently only returns success as status is hardcoded.
     * In real implementation, should call service method and handle response.
     */
    async makePaymentArrangements(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { status } = await paymentServices.makePaymentArrangements(id, req.body, req.elasticLogObj);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`PaymentController makePaymentArrangements ->`, error);
            next(error);
        }
    }
}

// Export an instance of the PaymentController class
module.exports = new PaymentController();