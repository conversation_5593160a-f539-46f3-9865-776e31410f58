import Button from "../common/Button";
import CustomCalendar from "../common/CustomCalendar";
import React from "react";

interface PaymentArrangementsProps {
  closeHandler?: () => void;
  setPaymentDate?: (value: Date) => void;
  paymentDate: Date | null;
  locationData: object;
  onSubmit?: () => void;
}

const MakePaymentArrangementsPopup: React.FC<PaymentArrangementsProps> = ({
  closeHandler,
  setPaymentDate,
  paymentDate,
  locationData,
  onSubmit,
}) => {
  let createdDate = new Date(locationData?.createdAt);
  let currentDate = new Date();
  let minDate = createdDate < currentDate ? currentDate : createdDate;

  if (minDate && minDate !== currentDate) {
    minDate = new Date(
      minDate.getFullYear(),
      minDate.getMonth(),
      minDate.getDate()
    );
  } else {
    minDate = new Date(
      minDate.getFullYear(),
      minDate.getMonth(),
      minDate.getDate() + 1
    );
  }

  let maxDate = createdDate
    ? new Date(
        createdDate.getFullYear(),
        createdDate.getMonth(),
        createdDate.getDate() + 28
      )
    : null;

  return (
    <>
      <div className="flex flex-col justify-center gap-5">
        <div>
          <p className="text-base">
            Please update below when you can make the current outstanding
            balance. Once updated you will not need to manually pay the bills as
            the system will do so automatically. Please note you can only make
            payment arrangements 28 days past your invoice date.
          </p>
        </div>
        
        <div>
          <CustomCalendar
            selectedDate={paymentDate}
            setDate={setPaymentDate}
            minDate={minDate}
            maxDate={maxDate}
          />
        </div>
        <div className="flex flex-col flex-wrap gap-2.5 md:gap-5 md:flex-row">
          <div className="flex-1">
            <Button
              title="Go Back"
              btnType="transparent"
              clickEvent={closeHandler}
              type="button"
              

            />
          </div>
          <div className="flex-1">
            <Button
              title="Next"
              clickEvent={onSubmit}
              type="button"
              className="disabled:opacity-50"
              attributes={{
                disabled: !paymentDate,
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default MakePaymentArrangementsPopup;
