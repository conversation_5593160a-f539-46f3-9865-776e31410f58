import React from "react";
import Popup from "../common/Popup";
import { CloseHandlerProps } from "../../typings/typing";
import MODEM from "../../assets/images/MODEM.png";

const ViewEquipmentPopup: React.FC<CloseHandlerProps> = ({ closeHandler }) => {
  return (
    <Popup title="Your Equipment" closeHandler={closeHandler}>
      <div className="flex gap-10 flex-col max-h-[510px] overflow-auto">
        <div className="flex gap-5 flex-col ">
          <div>
            <p className="uppercase text-base">MODEM</p>
          </div>
          <div className="flex max-sm:flex-col px-5 py-5 gap-5">
            <div className="w-[158px]">
              <img src={MODEM} alt="" />
            </div>
            <div className="flex py-5 gap-30 flex-col w-full md:max-w-[calc(100%-158px)]">
              <div>
                <p className="text-base font-bold">Hirton Coda - 4582</p>
              </div>
              <div className="flex gap-5 flex-col sm:flex-row flex-wrap">
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">IP Adress</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">129.44.333.33</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5 ">
                  <div>
                    <p className="font-medium uppercase">UpTime</p>
                  </div>
                  <div>
                    <p className="text-base font-bold ">5 days 3 hours</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">MAC address</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">9C34264A4002</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-5 flex-col ">
          <div>
            <p className="uppercase text-base">MODEM</p>
          </div>
          <div className="flex max-sm:flex-col px-5 py-5 gap-5">
            <div className="w-[158px]">
              <img src={MODEM} alt="" />
            </div>
            <div className="flex py-5 gap-30 flex-col w-full md:max-w-[calc(100%-158px)]">
              <div>
                <p className="text-base font-bold">Hirton Coda - 4582</p>
              </div>
              <div className="flex gap-5 flex-col sm:flex-row flex-wrap">
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">IP Adress</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">129.44.333.33</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5 ">
                  <div>
                    <p className="font-medium uppercase">UpTime</p>
                  </div>
                  <div>
                    <p className="text-base font-bold ">5 days 3 hours</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">MAC address</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">9C34264A4002</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex gap-5 flex-col ">
          <div>
            <p className="uppercase text-base">MODEM</p>
          </div>
          <div className="flex max-sm:flex-col px-5 py-5 gap-5">
            <div className="w-[158px]">
              <img src={MODEM} alt="" />
            </div>
            <div className="flex py-5 gap-30 flex-col w-full md:max-w-[calc(100%-158px)]">
              <div>
                <p className="text-base font-bold">Hirton Coda - 4582</p>
              </div>
              <div className="flex gap-5 flex-col sm:flex-row flex-wrap">
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">IP Adress</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">129.44.333.33</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5 ">
                  <div>
                    <p className="font-medium uppercase">UpTime</p>
                  </div>
                  <div>
                    <p className="text-base font-bold ">5 days 3 hours</p>
                  </div>
                </div>
                <div className="flex flex-col gap-5">
                  <div>
                    <p className="font-medium uppercase">MAC address</p>
                  </div>
                  <div>
                    <p className="text-base font-bold">9C34264A4002</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </Popup>
  );
};

export default ViewEquipmentPopup;
