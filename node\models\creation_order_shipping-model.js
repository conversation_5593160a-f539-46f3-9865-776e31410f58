const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("creation_order_shipping", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      allowNull: false,
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    full_mailing_address: {
      type: Sequelize.TEXT,
      comment: "Associated with Full_Mailing_Address__c field of salesforce Shipping__c object."
    },
    ship_date: {
      type: Sequelize.DATEONLY,
      comment: "Associated with Ship_Date__c field of salesforce Shipping__c object."
    },
    ship_drop_off_date: {
      type: Sequelize.DATEONLY,
      comment: "Associated with Ship_Drop_Off_Date__c field of salesforce Shipping__c object."
    },
    package_deliverted_at: {
      type: Sequelize.DATEONLY,
      comment: "Associated with Package_Delivered__c field of salesforce Shipping__c object."
    },
    tracking_url: {
      type: Sequelize.STRING,
      comment: "Associated with Tracking_URL__c field of salesforce Shipping__c object."
    },
    courier: {
      type: Sequelize.STRING,
      comment: "Associated with Courier__c field of salesforce Shipping__c object."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of Shipping__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'creation_order_shipping', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}
