# Purple Cow Portal API - Test Suite

This directory contains comprehensive test cases for all API routes in the Purple Cow Portal Node.js application.

## 📁 Test Structure

```
tests/
├── routes/                    # Route-specific tests
│   ├── auth-route.test.js     # Authentication routes
│   ├── billing-route.test.js  # Billing routes
│   ├── card-route.test.js     # Payment card routes
│   ├── customer-route.test.js # Customer management routes
│   ├── dashboard-route.test.js# Dashboard routes
│   ├── payment-route.test.js  # Payment processing routes
│   ├── plan-route.test.js     # Plan management routes
│   ├── promotion-route.test.js# Promotion routes
│   ├── referral-route.test.js # Referral program routes
│   ├── retry-route.test.js    # Retry subscription routes
│   ├── subscription-route.test.js # Subscription routes
│   ├── worker-route.test.js   # Worker/background job routes
│   └── index.test.js          # Test suite runner
├── setup.js                   # Jest setup configuration
├── globalSetup.js            # Global test setup
├── globalTeardown.js         # Global test cleanup
├── testResultsProcessor.js   # Custom test results processor
└── README.md                 # This file
```

## 🚀 Running Tests

### Install Dependencies
```bash
npm install
```

### Run All Tests
```bash
npm test
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

### Run Only Route Tests
```bash
npm run test:routes
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Route Tests in Watch Mode
```bash
npm run test:routes:watch
```

### Run Tests with Verbose Output
```bash
npm run test:verbose
```

### Run Tests Silently
```bash
npm run test:silent
```

## 📋 Test Coverage

The test suite covers all API endpoints across 12 route files:

### Auth Routes (`/auth/*`)
- ✅ POST `/auth/register` - User registration
- ✅ POST `/auth/cognito/create-user` - Cognito user creation
- ✅ POST `/auth/login` - User login
- ✅ POST `/auth/reset-password` - Password reset
- ✅ POST `/auth/forgot-password` - Forgot password
- ✅ POST `/auth/delete-contact` - Delete contact
- ✅ GET `/auth/map-subscription` - Map subscription

### Customer Routes (`/api/v1/customer/*`)
- ✅ GET `/api/v1/customer/details` - Get customer details
- ✅ POST `/api/v1/customer/reset-password` - Reset password
- ✅ PUT `/api/v1/customer/` - Update customer details
- ✅ GET `/api/v1/customer/logout` - Customer logout

### Billing Routes (`/api/v1/billing/*`)
- ✅ GET `/api/v1/billing/location` - Fetch locations
- ✅ GET `/api/v1/billing/statements/:custDetailsId` - Fetch statements
- ✅ GET `/api/v1/billing/invoice/:invoiceId` - Download invoice

### Payment Routes (`/api/v1/payment/*`)
- ✅ GET `/api/v1/payment/outstanding` - Fetch outstanding payments
- ✅ POST `/api/v1/payment/` - Process payment
- ✅ POST `/api/v1/payment/arrangement` - Create payment arrangement

### Subscription Routes (`/api/v1/subscription/*`)
- ✅ POST `/api/v1/subscription/estimate-renewal` - Renewal estimate
- ✅ POST `/api/v1/subscription/renewal` - Update renewal
- ✅ POST `/api/v1/subscription/estimate-internet-update` - Internet estimate
- ✅ POST `/api/v1/subscription/internet-update` - Update internet
- ✅ POST `/api/v1/subscription/estimate-television-update` - TV estimate
- ✅ POST `/api/v1/subscription/television-update` - Update TV
- ✅ POST `/api/v1/subscription/estimate-phone-update` - Phone estimate
- ✅ POST `/api/v1/subscription/phone-update` - Update phone
- ✅ POST `/api/v1/subscription/cancel` - Cancel subscription

### Plan Routes (`/api/v1/plan/*`)
- ✅ GET `/api/v1/plan/manage/:custDetailsId` - Plan management
- ✅ PUT `/api/v1/plan/update-address` - Update address
- ✅ GET `/api/v1/plan/details` - Get plan details

### Dashboard Routes (`/api/v1/dashboard/*`)
- ✅ GET `/api/v1/dashboard/user-details` - User details
- ✅ GET `/api/v1/dashboard/location-details` - Location details

### Card Routes (`/api/v1/card/*`)
- ✅ GET `/api/v1/card/` - Fetch cards
- ✅ POST `/api/v1/card/` - Add card
- ✅ PUT `/api/v1/card/` - Update card
- ✅ DELETE `/api/v1/card/:cardId` - Delete card

### Promotion Routes (`/api/v1/promotions/*`)
- ✅ GET `/api/v1/promotions/list` - Fetch promotions
- ✅ POST `/api/v1/promotions/locations` - Fetch eligible locations
- ✅ POST `/api/v1/promotions/claim` - Claim offer

### Referral Routes (`/api/v1/referrals/*`)
- ✅ GET `/api/v1/referrals/details` - Fetch referral details
- ✅ GET `/api/v1/referrals/list` - Fetch referral list

### Retry Routes (`/retry/*`)
- ✅ GET `/retry/services` - Get failed subscriptions

### Worker Routes (`/worker/*`)
- ✅ POST `/worker/register-process` - User signup process

## 🧪 Test Features

### Comprehensive Testing
- **Route Testing**: All API endpoints tested
- **Request Validation**: Various input scenarios
- **Response Validation**: Structure and data type verification
- **Error Handling**: Invalid data and edge cases
- **Authentication**: Header and token testing
- **File Upload**: Multipart form data testing

### Mock Implementation
- **Controllers**: All controllers are mocked
- **Middleware**: Validation middleware mocked
- **External Services**: AWS, Chargebee, Salesforce mocked
- **Database**: Database operations mocked

### Test Scenarios
- ✅ Successful operations
- ✅ Invalid input data
- ✅ Missing required fields
- ✅ Authentication scenarios
- ✅ Different parameter combinations
- ✅ Edge cases and error conditions

## 📊 Test Results

The test suite provides detailed reporting including:
- Total test count and pass/fail statistics
- Route-specific test results
- Coverage reports (HTML, LCOV, text)
- Performance metrics
- Failed test details

## 🔧 Configuration

### Jest Configuration
- Environment: Node.js
- Test timeout: 30 seconds
- Coverage collection enabled
- Verbose output available
- Custom test results processor

### Environment Variables
Tests use isolated environment variables to prevent interference with development/production environments.

## 🚨 Important Notes

1. **Mocked Services**: All external services are mocked for testing
2. **Test Database**: Uses separate test database configuration
3. **Isolated Environment**: Tests run in isolated environment
4. **No Side Effects**: Tests don't affect real data or services
5. **Comprehensive Coverage**: All routes and scenarios covered

## 🤝 Contributing

When adding new routes or modifying existing ones:

1. Create corresponding test files in `tests/routes/`
2. Follow the existing test structure and naming conventions
3. Include comprehensive test scenarios
4. Update this README if needed
5. Ensure all tests pass before committing

## 📈 Continuous Integration

These tests are designed to run in CI/CD pipelines and provide:
- Fast feedback on code changes
- Comprehensive API validation
- Coverage reporting
- Performance monitoring

---

**Happy Testing! 🎉**
