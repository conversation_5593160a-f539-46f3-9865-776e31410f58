const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const CardServices = require("../services/card-services");
const cardServices = new CardServices();

class CardController {
    /**
    * Fetches a card associated with the user
    * @param {Object} req - Express request object
    * @param {Object} res - Express response object
    * @param {Function} next - Express next middleware function
    */
    async fetchCard(req, res, next) {
        try {
            const { id } = req.userDetails;
            const { subscriptionId } = req.query;
            const { status, data } = await cardServices.fetchCard(id, req.elasticLogObj, subscriptionId);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`Card Controller fetch Card ->`, error);
            next(error);
        }
    }

    /**
    * Adds a new card for the user
    * @param {Object} req - Express request object
    * @param {Object} res - Express response object
    * @param {Function} next - Express next middleware function
    */
    async addCard(req, res, next) {
        try {
            const result = await cardServices.addCardService(req.userDetails, req.body, req.elasticLogObj);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`CardController addCard ->`, error);
            next(error);
        }
    }

    /**
    * Updates an existing card for the user
    * @param {Object} req - Express request object
    * @param {Object} res - Express response object
    * @param {Function} next - Express next middleware function
    */
    async updateCard(req, res, next) {
        try {
            const result = await cardServices.updateCardService(req.userDetails, req.body, req.elasticLogObj);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`CardController updateCard ->`, error);
            next(error);
        }
    }

    /**
   * Deletes a card specified by cardId
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
    async deleteCard(req, res, next) {
        try {
            const { cardId } = req.params;
            const result = await cardServices.deleteCard(cardId, req.elasticLogObj, req.userDetails);
            const { status, message } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, message });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`CardController deleteCard ->`, error);
            next(error);
        }
    }
}

// Export an instance of the CardController class
module.exports = new CardController();