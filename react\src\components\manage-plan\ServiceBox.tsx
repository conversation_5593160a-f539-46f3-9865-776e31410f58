import React from "react";
import useMediaQuery from "../../hooks/MediaQueryHook";

type ServiceBoxProps = {
  attributes?: React.HTMLAttributes<HTMLDivElement>;
  children?: React.ReactNode;
  isEdit?: boolean;
  showIcons?: boolean;
  border?: boolean;
};

const ServiceBox: React.FC<ServiceBoxProps> = ({
  border = false,
  children,
}) => {
  const isTablet = useMediaQuery("(max-width:1024px)");
  return (
    // max-2xl:max-h-[58px] 2xl:max-h-[94px]
    <div
      className={`flex-1 ${
        border
          ? "border border-dashed bg-[#FBF8FF] border-[#EBDBFF] p-2.5 "
          : "bg-[#FBF8FF] border border-[#EBDBFF] shadow-[0_0_15.7px_0_rgba(170,125,230,0.25)]"
      } p-2.5 2xl:p-5 2xl:pb-30 2xl:rounded-20 rounded-10 flex justify-between basis-full gap-5 ${
        isTablet ? "items-center" : ""
      }`}
    >
      <div className="flex flex-col gap-5 2xl:gap-10 basis-full">
        {children}
      </div>
    </div>
  );
};

export default ServiceBox;
