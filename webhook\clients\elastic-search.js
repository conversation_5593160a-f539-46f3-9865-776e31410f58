const { Client } = require('@elastic/elasticsearch');
const CONFIG = require("../config");
const { getCurrentAtlanticTime } = require('../helper/custom-helper');
const isInsert = true;
const indicesPrefix = CONFIG.LOG_PREFIX || 'dev'

class ElasticSearch {
    // Constructor to initialize the S3 client and bucket name
    constructor() {
        this.client = new Client({
            node: CONFIG.elasticSearch.node,
            auth: {
                apiKey: CONFIG.elasticSearch.apiKey
            }
        });
    }

    async insertDocument(index, document) {
        if (!isInsert) return { _id: null, _index: null };
        try {
            if (indicesPrefix == "dev" && document?.session_id) delete document.session_id;  // Delete session_id for development server
            const prefixedIndex = `${indicesPrefix}_${index}`;
            // Insert the updated document into the Elasticsearch index
            return await this.client.index({ index: prefixedIndex, document });
        } catch (error) {
            console.error('Error insert document:', error);
        }
    }

    async updateDocument(index, id, doc) {
        if (!isInsert) return;
        try {
            if (!index && !id) return;
            doc = { ...doc };
            return await this.client.update({ index, id, doc });
        } catch (error) {
            // console.error('Error update document:', error, doc, index, id);
        }
    }

    async deleteDocument(index, id) {
        if (!isInsert) return;
        try {
            return await this.client.delete({ index, id });
        } catch (error) {
            console.error('Error insert document:', error);
        }
    }
}

module.exports = ElasticSearch;