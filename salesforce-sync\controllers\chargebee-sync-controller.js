
const InvoicesSyncService = require("../services/chargebee/invoice-sync-services")
const invoicesSyncService = new InvoicesSyncService();
const CronService = require("../services/cron-services")
const cronService = new CronService();

class ChargebeeSyncController {
    async syncInvoicesFromChargebee(req, res, next) {
        try {
            await invoicesSyncService.getInvoicesList();
            res.status(200).send({ message: `Success` });
        } catch (error) {
            console.error(`ChargebeeSyncController syncInvoicesFromChargebee-> ${JSON.stringify(error)}`);
            next(error);
        }
    }

    async removeServicesFromDatabase(req, res, next) {
        try {
            await cronService.updateSpeedchange();
            // await cronService.updateTvDate();
            // await cronService.updatePhoneDate();
            res.status(200).send({ message: `Success` });
        } catch (error) {
            console.error(`ChargebeeSyncController syncInvoicesFromChargebee-> ${JSON.stringify(error)}`);
            next(error);
        }
    }
}

module.exports = new ChargebeeSyncController();