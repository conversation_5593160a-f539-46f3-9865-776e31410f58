const Joi = require('joi');

const internetUpdateValidation = Joi.object({
    customer_subscription_id: Joi.number()
        .integer()
        .required()
        .messages({
            'any.required': 'Customer subscription ID is required.',
            'number.base': 'Customer subscription ID must be a number.',
            'number.integer': 'Customer subscription ID must be an integer.'
        }),
    plan_id: Joi.string()
        .trim()
        .required()
        .messages({
            'any.required': 'Plan ID is required.'
        }),
});

module.exports = { internetUpdateValidation };
