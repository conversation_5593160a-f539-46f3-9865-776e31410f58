const db = require('../models');
const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const CognitoConnection = require("../clients/aws/cognito");
const awsCognitoConnection = new CognitoConnection();
const S3Client = require("../clients/aws/s3");
const { getFileExtension, isImageFile } = require("./../helpers/privacyAlgorithms");
const SalesforceServices = require('./salesforce-services');
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();

class CustomerServices {
    // Method to reset password using AWS Cognito
    async resetPassword(payload, elasticLogObj) {
        const { token } = payload;
        const { _id, _index } = await elasticSearch.insertDocument("update_password_logs", { ...elasticLogObj, request: { token }, type: "RESET" });
        try {
            const returnStatus = { status: false };
            const response = await awsCognitoConnection.changePassword(payload);

            if (response && response?.$metadata?.httpStatusCode == 200) {
                returnStatus.status = true;
                returnStatus.message = "Password changed successfully";
            }

            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
            return returnStatus;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Customer Reset Password -> `, error);
            throw error;
        }
    }

    // Method to logout using AWS Cognito
    async logout(token, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("logout_logs", { ...elasticLogObj, request: { token } });
        try {
            const returnStatus = { status: false };
            const response = await awsCognitoConnection.logout(token);
            if (response) {
                returnStatus.status = true;
                returnStatus.message = "Logged out successfully";
            }

            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });
            return returnStatus;
        } catch (error) {
            if (_id && _index) await elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Customer service logout -> `, error);
            throw error;
        }
    }

    // Method to update customer details, including Salesforce integration and file upload to S3
    async updateCustomerDetails(payload, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("update_account_logs", { ...elasticLogObj, request: payload?.body });

        try {
            const returnStatus = { status: false };
            const { userDetails: { id, first_name, sf_record_id }, body, file } = payload;

            if (file?.buffer) {
                const s3Client = new S3Client();
                const fileExtension = getFileExtension(file.originalname);
                let path = `customers/${first_name.toLowerCase()}-${new Date().getTime()}.${fileExtension}`;
                if (!isImageFile(fileExtension)) throw new CustomError(RESPONSE_CODES.NOT_ACCEPTABLE, "Error: Invalid file format. Please upload an image file in one of the following formats: PNG, JPG, or JPEG.");
                const uploadFileResp = await s3Client.uploadFile(path, file.buffer);
                body.image_url = uploadFileResp;
            }

            if (body.image_url == "null") delete body.image_url;

            const { secondary_phone, additional_name, company_name } = body;

            // Convert empty strings to null in body
            body.secondary_phone = body.secondary_phone === "" ? null : body.secondary_phone;
            body.additional_name = body.additional_name === "" ? null : body.additional_name;
            body.company_name = body.company_name === "" ? null : body.company_name;

            const updateObj = {
                secondary_phone: secondary_phone === "" ? null : secondary_phone || null,
                additional_name: additional_name === "" ? null : additional_name || null,
                company_name: company_name === "" ? null : company_name || null
            }

            // Need to update data in salesforce
            const salesforceServiceClient = new SalesforceServices();
            const { returnStatus: updateResponse } = await salesforceServiceClient.updateProfileDetails({ updateObj, sf_record_id });
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: { success: updateResponse } });
            if (updateResponse) {
                const [response] = await db.Contacts.update(body, { where: { id } });
                if (response) {
                    returnStatus.status = true;
                    returnStatus.message = "Customer profile updated sucessfully";
                }
            }
            return returnStatus;
        } catch (error) {
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Customer Service update details-> `, error);
            throw error;
        }
    }

    // Method to retrieve customer details (filtered)
    async getCustomerDetails(userDetails, elasticLogObj) {
        try {
            const { sf_record_id, cb_customer_id, aws_cognito_id, referral_link, createdAt, ...filteredDetails } = userDetails;
            const result = { status: true, data: { ...filteredDetails } };
            return result;
        } catch (error) {
            console.error(`Customer service get details -> `, error);
            throw error;
        }
    }
}

module.exports = CustomerServices;