const db = require('../models');
const CONFIG = require("../config")
const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const ChargebeeClient = require("../clients/chargebee/chargebee");
const ElasticSearch = require("./../clients/elastic-search/elastic-search");
const elasticSearch = new ElasticSearch();
const ChargebeeService = require("./chargebee-services.js");

class CardServices {

    // Method to fetch cards
    async fetchCard(contact_id, elasticLogObj, customerSubscriptionId) {
        const customer_subscription_id = customerSubscriptionId || null;
        const { _id, _index } = await elasticSearch.insertDocument("fetch_card_logs", { ...elasticLogObj, request: { contact_id, customer_subscription_id } });
        try {
            const excludeArray = ["contact_id", "cb_card_id", "createdAt", "updatedAt"];
            let data = await db.ContactsCardDetails.findAll({ where: { contact_id }, attributes: { exclude: excludeArray }, order: [['is_primary', 'DESC']] });

            if (data && data.length) {
                data = JSON.parse(JSON.stringify(data));
                if (customer_subscription_id) {
                    const mappingData = await db.SubscriptionCardMapping.findOne({
                        where: { contact_id, customer_subscription_id },
                        attributes: ["id", "contact_card_id"]
                    });

                    if (mappingData) {
                        data.forEach(card => {
                            card.is_primary = '0'; // Directly set is_primary to '0'
                        });

                        data = data.map(card => ({
                            ...card,
                            is_primary: card.id === mappingData.contact_card_id ? "1" : "0"
                        }));
                    }
                }

                data.sort((a, b) => b.is_primary - a.is_primary);

                // Log the successful response
                if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: { status: true, data } });
            }

            return { status: true, data };
        } catch (error) {
            // Log the error response
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`Customer service get details -> `, error);
            throw error;
        }
    }

    // Method to add cards
    async addCardService(userDetails, payload, elasticLogObj) {
        let { card_number, card_name, expiry_month, expiry_year, is_primary, subscription_id } = payload;
        if (is_primary === "1" && !subscription_id) throw new CustomError(RESPONSE_CODES.CONFLICT, "Unable to add card. Please contact customer support");
        const lastFourDigits = card_number.slice(-4);
        const { _id, _index } = await elasticSearch.insertDocument("card_update_logs", { ...elasticLogObj, request: { card_name, lastFourDigits, is_primary }, type: "CREATE" });
        try {
            const returnRes = { status: false }
            const { id: contact_id, cb_customer_id } = userDetails;

            payload.customer_id = contact_id;

            //create card in Chargebee
            if (!cb_customer_id) throw new CustomError(RESPONSE_CODES.CONFLICT, "Unable to add card. Please contact customer support");
            const info = await this.createorUpdateCardSourceinCB(cb_customer_id, payload, "create");

            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: info });
            if (info) {
                const { payment_source } = info;
                if (payment_source?.id) {

                    // if (is_primary === "1") await this.changePrimaryStatus(contact_id);
                    // else {
                    const checkCardExist = await db.ContactsCardDetails.count({ where: { contact_id } });
                    // if (!checkCardExist) is_primary = "1";
                    // }

                    const existingCard = await db.ContactsCardDetails.findOne({ where: { cb_card_id: payment_source.id } });
                    let cardObject = {
                        cb_card_id: payment_source?.id,
                        card_number: lastFourDigits,
                        card_name, expiry_month, expiry_year,
                        is_primary: !checkCardExist ? "1" : "0",
                        contact_id,
                        status: "active"
                    }

                    let cb_card_id;

                    if (!existingCard) {
                        let insertDetails = await db.ContactsCardDetails.create(cardObject);
                        cb_card_id = insertDetails?.id;
                    } else {
                        await existingCard.update(cardObject);
                        cb_card_id = existingCard?.id;
                    }

                    const customerSubscription = await db.CustomerSubscriptions.findOne({ where: { id: subscription_id } });

                    if (is_primary === "1" && cb_card_id && customerSubscription) await this.mapSubscriptionCard(cb_card_id, subscription_id, contact_id, payment_source?.id, customerSubscription.cb_subscription_id);
                    returnRes.status = true;
                    returnRes.message = `Card added successfully`;
                }
            }

            return returnRes;
        } catch (error) {
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`CardService add card ->`, error);
            throw error;
        }
    }

    async mapSubscriptionCard(contact_card_id, customer_subscription_id, contact_id, payment_source_id, cb_subscription_id) {
        try {

            const callChargebee = new ChargebeeClient();

            const cbPayload = {
                payment_source_id,
                auto_collection: "on"
            };

            const response = await callChargebee.updatePaymentSource(cb_subscription_id, cbPayload);

            if (response) {

                await db.SubscriptionCardMapping.destroy({
                    where: { contact_id, customer_subscription_id }
                });

                const cardMappingData = { contact_id, contact_card_id, customer_subscription_id, is_primary: "1" };
                let [cardMappingDetails, created] = await db.SubscriptionCardMapping.findOrCreate({
                    where: { contact_id, contact_card_id, customer_subscription_id },
                    defaults: cardMappingData,
                });

                if (!created) await cardMappingDetails.update(cardMappingData);
            }

            return response;
        } catch (error) {
            console.error(`CardService mapSubscriptionCard ->`, error);
            throw error;
        }
    }

    // Method to update cards
    async updateCardService(userDetails, payload, elasticLogObj) {
        const { _id, _index } = await elasticSearch.insertDocument("card_update_logs", { ...elasticLogObj, request: payload, type: "UPDATE" });
        const { is_primary, card_id, subscription_id } = payload;
        if (!subscription_id) throw new CustomError(RESPONSE_CODES.CONFLICT, "Unable to add card. Please contact customer support");
        try {
            const { id: contact_id, cb_customer_id } = userDetails;
            if (!cb_customer_id) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.NOT_FOUND);

            if (!card_id) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.UNPROCESSABLE_ENTITY);

            const customerSubscription = await db.CustomerSubscriptions.findOne({ where: { id: subscription_id } });
            if (!customerSubscription) throw new CustomError(RESPONSE_CODES.CONFLICT, "Unable to add card. Please contact customer support");

            const cardDetails = await db.ContactsCardDetails.findByPk(card_id);
            if (!cardDetails) throw new CustomError(RESPONSE_CODES.CONFLICT, "Unable to add card. Please contact customer support");

            // if (is_primary === "1") await this.changePrimaryStatus(contact_id);
            // const callChargebee = new ChargebeeClient();

            // const cbPayload = {
            //     payment_source_id: cardDetails?.cb_card_id,
            //     role: "PRIMARY"
            // };

            // const response = await callChargebee.changeCardStatus(cb_customer_id, cbPayload);

            let response = {};

            if (is_primary === "1" && cardDetails?.cb_card_id && customerSubscription) {
                response = await this.mapSubscriptionCard(card_id, subscription_id, contact_id, cardDetails?.cb_card_id, customerSubscription.cb_subscription_id);

                // Log the successful response to ElasticSearch
                if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: response || {} });
            }

            // if (response?.id) {
            //     await cardDetails.update({ is_primary });
            return { status: true, data: payload, message: "Card updated successfully", error: null };
            // }
            // return { status: false };
        } catch (error) {

            // Log the error response to ElasticSearch
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });

            console.error(`CardService update card ->`, error);
            throw error;
        }
    }

    // Method to change primary cards
    async changePrimaryStatus(contact_id) {
        try {
            if (!contact_id) return false;
            await db.ContactsCardDetails.update({ is_primary: "0" }, { where: { contact_id } });
            return true;
        } catch (error) {
            console.error(`CardService Change Primary Status -> `, error);
            throw error;
        }
    }

    // Method to manage cards in database
    async createorUpdateCardSourceinCB(custOrCardId, payload, type) {
        try {
            const callChargebee = new ChargebeeClient();
            const { card_number, card_name, expiry_month, expiry_year, cvv, is_primary } = payload;
            let info;

            let cardName = card_name.split(/\s+(.+)/);
            const firstName = cardName[0]
            const lastName = cardName[1] || ''

            const card = { expiry_year, expiry_month };
            if (type === "create") {
                card.first_name = firstName;
                card.last_name = lastName;
                card.number = card_number;
                card.cvv = cvv;
                card.gateway_account_id = CONFIG.chargebee.gateway_account_id;

                info = await callChargebee.createCardSource(custOrCardId, card);
                // if (is_primary === "1") {
                //     const { payment_source } = info;
                //     if (payment_source?.id) {
                //         const cbPayload = {
                //             payment_source_id: payment_source.id,
                //             role: "PRIMARY"
                //         }
                //         await callChargebee.changeCardStatus(custOrCardId, cbPayload);
                //     }
                // }
            } else info = await callChargebee.updateCardSource(custOrCardId, card);
            return info;
        } catch (error) {
            console.error(`CardService Create Card Source in CB ->`, error);
            const { http_status_code, message } = error;
            if (http_status_code) error.status = http_status_code
            if (message) error.message = message
            throw error;
        }
    }

    // Method to delete cards
    async deleteCard(card_id, elasticLogObj, userDetails) {
        const { id: contact_id } = userDetails;

        // Log the delete card request
        const { _id, _index } = await elasticSearch.insertDocument("card_update_logs", { ...elasticLogObj, request: { card_id }, type: "DELETE" });

        try {
            if (!card_id) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.UNPROCESSABLE_ENTITY);

            const cardDetails = await db.ContactsCardDetails.findByPk(card_id);
            if (!cardDetails) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.NOT_FOUND);
            if (cardDetails?.contact_id != contact_id) throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.NOT_FOUND);

            const mappingData = await db.SubscriptionCardMapping.count({
                where: { contact_id, contact_card_id: card_id }
            });

            if (mappingData) throw new CustomError(RESPONSE_CODES.CONFLICT, "This card cannot be deleted, as it`s already mapped with other subscriptions.");

            const callChargebee = new ChargebeeClient();
            const { deleted } = await callChargebee.deleteCardSource(cardDetails.cb_card_id);
            if (deleted) {
                await this.getCardDetailsAndUpdate(userDetails);
                await cardDetails.destroy();

                // Log the successful deletion response
                if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: { status: true, message: "Card Deleted successfully" } });

                return { status: true, message: "Card Deleted successfully", error: null };
            }
            return { status: false };
        } catch (error) {
            // Log the error response
            if (_id && _index) elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });
            console.error(`CardService delete Card ->`, error);
            throw error;
        }
    }

    async getCardDetailsAndUpdate(userDetails) {
        try {
            if (!userDetails?.cb_customer_id) return;
            const chargebeeServiceClient = new ChargebeeService();
            await chargebeeServiceClient.getAllCards(userDetails.id, userDetails.cb_customer_id);
            const customerData = await db.CustomerDetails.findAll({
                include: {
                    model: db.CustomerSubscriptions,
                    as: "customerSubscription",
                    attributes: ["cb_subscription_id", "id"],
                    required: true,
                },
                where: { contact_id: userDetails.id },
            });

            await db.SubscriptionCardMapping.destroy({
                where: { contact_id: userDetails.id }
            });

            if (customerData?.length) await this.getSubscriptionPaymentDetails(JSON.parse(JSON.stringify(customerData)));
        } catch (error) {
            console.error(`CardService Get Card Details And Update -> `, error);
            throw error;
        }
    }

    async getSubscriptionPaymentDetails(customerDetails) {
        try {
            const chargebeeServiceClient = new ChargebeeService();
            for (const customerDetail of customerDetails) {
                const cbSubscriptionId = customerDetail?.customerSubscription?.cb_subscription_id;
                if (!cbSubscriptionId) continue;
                const subscriptionDbId = customerDetail?.customerSubscription?.id;
                const { status: cbStatus, subscriptionDetail } = await chargebeeServiceClient.getSubscriptions(cbSubscriptionId);
                if (!cbStatus) return;
                const { subscription: { payment_source_id } } = subscriptionDetail;
                if (payment_source_id) await this.mapSubscriptionAndCard(payment_source_id, subscriptionDbId);
            }
        } catch (error) {
            console.error(`CardService Get Subscription Payment Details ->`, error);
            throw error;
        }
    }

    async mapSubscriptionAndCard(cb_card_id, customer_subscription_id) {
        try {
            const checkCardExist = await db.ContactsCardDetails.findOne({ where: { cb_card_id } });
            if (checkCardExist) {
                const { id: contact_card_id, contact_id } = checkCardExist;
                const cardMappingData = { contact_id, contact_card_id, customer_subscription_id, is_primary: "1" };
                let [cardMappingDetails, created] = await db.SubscriptionCardMapping.findOrCreate({
                    where: { contact_id, contact_card_id, customer_subscription_id },
                    defaults: cardMappingData,
                });

                if (!created) await cardMappingDetails.update(cardMappingData);
            }
        } catch (error) {
            console.error(`AuthService mapSubscriptionCard ->`, error);
            throw error;
        }
    }
}

module.exports = CardServices;