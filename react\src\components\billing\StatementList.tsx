import { PaginationData, Statement } from "../../typings/typing"; // Assuming Statement and PaginationData types are defined in typings/typing
import React, { useEffect, useState } from "react";
import {
  useDownloadStatementMutation,
  useFetchStatementMutation,
} from "../../services/api";

import { DownloadIcon } from "../../assets/Icons";
import Pagination from "../common/Pagination";
import StatementListSkeleton from "./skeleton/StatementListSkeleton";
import { addNotification } from "../../store/reducers/toasterReducer";
import { _TIMEZONE, formatCurrency, formatUTCDate } from "../../utils/helper";
import { logout } from "../../store/reducers/authenticationReducer";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import moment from "moment-timezone";

interface StatementListProps {
  id: number; // Assuming id is of type number
}

const StatementList: React.FC<StatementListProps> = ({ id }) => {
  const [statements, setStatements] = useState<Statement[]>([]); // Added type annotation for statements
  const [fetchStatement, statementLoading] = useFetchStatementMutation();
  const [downloadStatement] = useDownloadStatementMutation();
  const [pagination, setPagination] = useState<PaginationData>({
    limit: 3,
    page: 1,
    total: 0,
    totalPage: 0,
  });
  const [downloading, setDownloading] = useState<number | null>(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch statements when the component mounts or pagination.page changes
  useEffect(() => {
    id && handleFetchStatement();
  }, [pagination.page, id]);

  // Function to fetch statements from the API
  const handleFetchStatement = async () => {
    try {
      const query = {
        page: pagination.page,
        limit: 3,
      };

      const response = await fetchStatement({ id, query }).unwrap();
      if (response.status === 200) {
        setStatements(response.data.invoices);
        setPagination((prevState) => ({
          ...prevState,
          ...response.data.pageData,
        }));
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to download a statement by its ID
  const handleDownloadStatement = async (statementID: number) => {
    try {
      setDownloading(statementID);
      const response = await downloadStatement(statementID).unwrap();
      if (response.status === 200) {
        window.open(response.data.download_url, "_self");
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: "Something went wrong" })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    } finally {
      setDownloading(null);
    }
  };

  // Function to handle pagination change
  const handlePaginationChange = (pageNumber: number) => {
    setPagination((prevState) => ({
      ...prevState,
      page: pageNumber,
    }));
  };

  return (
    <>
      <>
        <table className="statement-table">
          <thead>
            <tr>
              <th>Billed</th>
              <th>Billing amount</th>
              <th className="!text-right">Status</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {statementLoading?.isLoading || !id ? (
              <tr>
                <td className="!text-center" colSpan={100000}>
                  <StatementListSkeleton />
                </td>
              </tr>
            ) : statements?.length > 0 ? (
              statements.map((statement) => {
                const allowPaymentArrangement =
                  (statement?.status === "PAYMENT_DUE" ||
                    statement?.status === "NOT_PAID") &&
                  statement?.expected_payment_date !== null &&
                  moment(statement?.expected_payment_date)
                    .tz(_TIMEZONE)
                    .format("YYYY-MM-DD HH:mm:ss") >
                    moment(new Date())
                      .tz(_TIMEZONE)
                      .format("YYYY-MM-DD HH:mm:ss");

                return (
                  <tr key={statement?.id}>
                    <td>
                      {statement?.createdAt
                        ? formatUTCDate(statement?.createdAt)
                        : "---"}
                    </td>
                    <td>
                      <div>{formatCurrency(statement?.amount || 0, true)}</div>
                      {statement?.credit_issue ? (
                        <div
                          className={`whitespace-pre w-max lg:py-[4.5px] border mt-1 lg:px-2 py-[3.5px] px-1.5 lg:text-sm text-[10px] rounded-md`}
                        >
                          Credits Issued &nbsp;
                          {formatCurrency(statement?.credit_issue || 0, true)}
                          &nbsp; CAD
                        </div>
                      ) : statement?.amount_adjusted ? (
                        <div
                          className={`whitespace-pre w-max lg:py-[4.5px] border mt-1 lg:px-2 py-[3.5px] px-1.5 lg:text-sm text-[10px] rounded-md`}
                        >
                          Adjusted &nbsp;
                          {formatCurrency(
                            statement?.amount_adjusted || 0,
                            true
                          )}
                          &nbsp; CAD
                        </div>
                      ) : (
                        ""
                      )}
                    </td>
                    <td className="!text-right">
                      <span
                        className={`lg:py-[4.5px] lg:px-2 py-[3.5px] px-1.5 lg:text-sm text-[10px] rounded-md ${
                          statement?.status === "PAID"
                            ? "text-success bg-success-light"
                            : allowPaymentArrangement
                            ? "text-warning bg-warning-light"
                            : statement?.status === "NOT_PAID" ||
                              statement?.status === "PAYMENT_DUE"
                            ? "text-error bg-error-light"
                            : ""
                        }`}
                      >
                        {allowPaymentArrangement
                          ? "Payment arrangements"
                          : statement?.status === "PAYMENT_DUE" ||
                            statement?.status === "NOT_PAID"
                          ? "Outstanding"
                          : statement?.status
                              .toLowerCase()
                              .replace(/^./, (char: any) => char?.toUpperCase())
                              .replaceAll("_", " ")}
                      </span>
                    </td>
                    <td>
                      <span
                        className="xs:w-11 xs:h-11 w-8 h-8 xs:rounded-xl rounded-lg bg-white border border-primary flex items-center justify-center text-black cursor-pointer"
                        onClick={() =>
                          handleDownloadStatement(statement?.cb_invoice_id)
                        }
                      >
                        {downloading === statement?.cb_invoice_id ? (
                          <span className="icon-loader"></span>
                        ) : (
                          <DownloadIcon />
                        )}
                      </span>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td
                  className="!text-center font-semibold !pt-8"
                  colSpan={100000}
                >
                  No Statements Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {pagination?.total > 3 ? (
          <Pagination
            totalPageCount={pagination?.totalPage}
            currentPage={pagination?.page}
            onChange={handlePaginationChange}
          />
        ) : null}
      </>
    </>
  );
};

export default StatementList;
