import "../../assets/scss/pages/yourBalanceCard.scss";
import { formatCurrency, formatDate } from "../../utils/helper";
import {
  useDashboardUserDataMutation,
  useDeleteCardMutation,
  usePaymentArrangementsMutation,
} from "../../services/api";
import { useEffect, useState } from "react";
import ConfirmationMessagePopup from "../common/ConfirmationMessagePopup";
import MakePaymentArrangementsPopup from "./MakePaymentArrangementsPopup";
import { MakePaymentPopUp } from "./MakePaymentPopup";
import Popup from "../common/Popup";
import { RightCaretIcon } from "../../assets/Icons";
import SelectLocationForPaymentPopup from "./SelectLocationForPaymentPopup";
import YourBalanceCardSkeleton from "./skeletons/YourBalanceCardSkeleton";
import { addNotification } from "../../store/reducers/toasterReducer";
import { logout } from "../../store/reducers/authenticationReducer";
import { useDispatch } from "react-redux";
import useMediaQuery from "../../hooks/MediaQueryHook";
import { useNavigate } from "react-router-dom";
import { YourBalanceCardProps } from "../../typings/typing";

export const YourBalanceCard: React.FC<YourBalanceCardProps> = ({
  refreshLocation,
}) => {
  const isDesktop = useMediaQuery("screen and (min-width: 1281px)");
  const [userData, setUserData] = useState<any>();
  const [getUserData, userLoading] = useDashboardUserDataMutation();
  const isPaymentDue = true;
  const [outstandingData, setOutstandingData] = useState({});
  const [locationArrangement, setLocationArrangement] = useState<any>({});
  const [
    showSelectServiceForPaymentPopup,
    setShowSelectServiceForPaymentPopup,
  ] = useState<boolean>(false);
  const [
    showSelectServiceForPaymentArrangementPopup,
    setShowSelectServiceForPaymentArrangementPopup,
  ] = useState<boolean>(false);
  const [isMakePayment, setIsMakePayment] = useState(false);
  const [showPaymentArrangement, setShowPaymentArrangement] =
    useState<boolean>(false);
  const [confirmationPopup, setConfirmationPopup] = useState<boolean>(false);
  const [paymentDate, setPaymentDate] = useState<Date | null>(null);
  const [showNestedPopup, setShowNestedPopup] = useState<boolean>(false);
  const [customIndex, setCustomIndex] = useState<number | null>(null);
  const [cardCustomData, setCardCustomData] = useState({});
  const [deleteCard, deleteCardLoading] = useDeleteCardMutation();
  const [arrangePayment, arrangePaymentLoading] =
    usePaymentArrangementsMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Fetch user data on component mount
  useEffect(() => {
    handleGetUserData();
  }, []);

  // Function to handle fetching user data
  const handleGetUserData = async () => {
    try {
      const response = await getUserData({}).unwrap();
      if (response.status === 200) {
        setUserData(response?.data);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  const handleRefetchData = () => {
    handleGetUserData();
    refreshLocation();
  };

  // Toggles the visibility of the Make Payment popup
  const handleMakePaymentPopup = () => {
    setIsMakePayment(!isMakePayment);
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
  };

  // Handles the next step after selecting a location for payment
  const handleLocationSelectPaymentNext = (location: any) => {
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
    setOutstandingData(location);
    setIsMakePayment(location?.customer_subscription_id);
  };

  // Handles the next step after selecting a location for payment arrangements
  const handleLocationSelectPaymentArrangementNext = (location: string) => {
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
    setLocationArrangement(location);
    setShowPaymentArrangement(!showPaymentArrangement);
  };

  // Toggles the visibility of the Select Location for Payment popup
  const handleLocationSelectPaymentPopup = () => {
    setShowSelectServiceForPaymentPopup(!showSelectServiceForPaymentPopup);
  };

  // Toggles the visibility of the Select Location for Payment Arrangement popup
  const handleLocationSelectPaymentArrangementPopup = () => {
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
  };

  // Toggles the visibility of the Payment Arrangements popup
  const handlePaymentArrangementPopup = () => {
    setShowPaymentArrangement(!showPaymentArrangement);
    setShowSelectServiceForPaymentArrangementPopup(
      !showSelectServiceForPaymentArrangementPopup
    );
    setPaymentDate(null);
  };

  // Handle the confirmation on payment
  const handleConfirmation = () => {
    setConfirmationPopup(!confirmationPopup);
    setShowPaymentArrangement(false);
  };

  // Handle the submission on payment
  const HandleSavePaymentDate = async () => {
    try {
      const paymentDateWithTime = new Date(paymentDate);
      const currentDateTime = new Date();
      paymentDateWithTime.setHours(currentDateTime.getHours());
      paymentDateWithTime.setMinutes(currentDateTime.getMinutes());
      paymentDateWithTime.setSeconds(currentDateTime.getSeconds());
      const data = {
        invoice_id: locationArrangement?.id,
        date: paymentDateWithTime,
      };

      const response = await arrangePayment(data).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({ type: "success", message: response?.message })
        );
        setShowPaymentArrangement(false);
        setConfirmationPopup(false);
        setPaymentDate(new Date());
        handleGetUserData();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };
  if (userLoading?.isLoading) {
    return <YourBalanceCardSkeleton />;
  }

  const handleCloseNestedPopup = () => {
    setShowNestedPopup(!showNestedPopup);
    setCustomIndex(null);
  };

  // Delete card from nested popup
  const handleDeleteCard = async () => {
    try {
      const response = await deleteCard(
        cardCustomData[customIndex]?.id
      ).unwrap();
      if (response?.status === 200) {
        dispatch(
          addNotification({
            type: "success",
            message: response?.message,
          })
        );
        handleRefetchData();
        setShowNestedPopup(false);
        setCustomIndex(null);
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  return (
    <>
      <div className="flex flex-col md:flex-row justify-between gap-3 lg:gap-9">
        <div className="p-5 md:w-[40%] bg-medium_purple 2xl:px-30 2xl:py-[38px] relative rounded-[20px]">
          <div className="relative z-[2] flex justify-between md:gap-2 gap-5 md:flex-row flex-col">
            <div className="flex flex-col text-white">
              <h1 className="font-anton text-4xl uppercase">
                Hey There, {userData?.first_name}!
              </h1>
              <p className="mt-4">Welcome back!</p>
            </div>
          </div>
        </div>

        <div className="p-5 md:w-[60%] bg-medium_purple 2xl:px-30 2xl:py-[38px] relative rounded-[20px]">
          <div className="relative z-[2] flex justify-between md:gap-2 gap-5 md:flex-row flex-col">
            <div className="flex flex-col justify-between 2xl:gap-[60px] gap-30">
              {isPaymentDue && (
                <div className="flex gap-5 md:gap-10">
                  {/* Your balance due */}
                  <div className="flex flex-col gap-2 text-white ">
                    <p className="text-sm uppercase">
                      Your balance due
                    </p>
                    <p className="2xl:text-[26px] text-xl">
                      {formatCurrency(
                        userData?.total_outstanding
                          ? userData?.total_outstanding
                          : 0,
                        true
                      )}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2 text-white ">
                    <p className="text-sm uppercase">Member for</p>
                    <div className="2xl:text-[26px] text-xl font-medium flex gap-2">
                      {userData?.member_for?.year == null &&
                      userData?.member_for?.month == null &&
                      userData?.member_for?.day == 0 ? (
                        "Today's Your First Day!"
                      ) : (
                        <>
                          {userData?.member_for?.year && (
                            <p className="2xl:text-[26px] text-xl font-medium text-white">
                              {userData?.member_for?.year}{" "}
                              <span className="text-sm font-medium ">
                                {" "}
                                {userData?.member_for?.year > 1
                                  ? "Years"
                                  : "Year"}
                              </span>
                            </p>
                          )}
                          {userData?.member_for?.month && (
                            <p className="2xl:text-[26px] text-xl font-medium text-white">
                              {userData?.member_for?.month}{" "}
                              <span className="text-sm font-medium ">
                                {userData?.member_for?.month > 1
                                  ? "Months"
                                  : "Month"}
                              </span>
                            </p>
                          )}
                          {userData?.member_for?.day && (
                            <p className="2xl:text-[26px] text-xl font-medium text-white">
                              {userData?.member_for?.day}{" "}
                              <span className="text-sm font-medium ">
                                {userData?.member_for?.day > 1 ? "Days" : "Day"}
                              </span>
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          {userData?.total_outstanding > 0 && (
            <div className="flex flex-col md:flex-row md:gap-5 gap-2.5 mt-5">
              {userData?.total_outstanding > 0 && (
                <>
                  <div
                    className="btn bg-white text-black flex items-center justify-between cursor-pointer  md:w-[70%]"
                    onClick={handleLocationSelectPaymentPopup}
                  >
                    <span className="font-normal">Make payment</span>
                    <RightCaretIcon />
                  </div>
                </>
              )}
              {userData?.payment_arrangement && (
                <div
                  className="btn btn-white flex items-center !bg-transparent !text-white hover:border-slate-300 justify-between gap-2 cursor-pointer "
                  onClick={handleLocationSelectPaymentArrangementPopup}
                >
                  <span className="font-normal">Make payment arrangements</span>
                  <RightCaretIcon />
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {showSelectServiceForPaymentPopup && (
        <SelectLocationForPaymentPopup
          type="payment"
          subtitle="Please select invoice you would like to pay"
          closeHandler={() =>
            setShowSelectServiceForPaymentPopup(
              !showSelectServiceForPaymentPopup
            )
          }
          nextHandler={handleLocationSelectPaymentNext}
        />
      )}

      {isMakePayment && !showNestedPopup && (
        <MakePaymentPopUp
          isMakePayment={isMakePayment}
          outstandingData={outstandingData}
          closeHandler={handleMakePaymentPopup}
          refetch={handleRefetchData}
          showNestedPopup={showNestedPopup}
          setShowNestedPopup={setShowNestedPopup}
          setCustomIndex={setCustomIndex}
          setCardCustomData={setCardCustomData}
        />
      )}

      {showNestedPopup && (
        <ConfirmationMessagePopup
          title={"Important: Delete Card?"}
          message={
            "Deleting this card will remove it from your account and you will no longer be able to use it for transactions. Are you sure you want to proceed?"
          }
          closeHandler={handleCloseNestedPopup}
          handleSubmit={handleDeleteCard}
          isLoading={deleteCardLoading?.isLoading}
        />
      )}
      {showPaymentArrangement && (
        <Popup
          title="Payment arrangements"
          width={isDesktop ? "700px" : "600px"}
          height={"501px"}
          closeHandler={handlePaymentArrangementPopup}
        >
          <MakePaymentArrangementsPopup
            closeHandler={handlePaymentArrangementPopup}
            setPaymentDate={setPaymentDate}
            paymentDate={paymentDate}
            locationData={locationArrangement}
            onSubmit={handleConfirmation}
          />
        </Popup>
      )}
      {confirmationPopup && (
        <ConfirmationMessagePopup
          title="Confirm payment arrangements"
          message={`You have made arrangement for your account on file to be bill ${formatCurrency(
            locationArrangement?.total_outstanding
              ? locationArrangement?.total_outstanding
              : 0,
            true
          )} on ${formatDate(paymentDate)}`}
          handleSubmit={HandleSavePaymentDate}
          closeHandler={() => {
            setConfirmationPopup(false);
            setShowPaymentArrangement(true);
          }}
          isLoading={arrangePaymentLoading?.isLoading}
          btnText="Confirm"
        />
      )}
      {showSelectServiceForPaymentArrangementPopup && (
        <SelectLocationForPaymentPopup
          type="payment_arrangement"
          subtitle="Please select invoice you wish to arrange payment on"
          closeHandler={() =>
            setShowSelectServiceForPaymentArrangementPopup(
              !showSelectServiceForPaymentArrangementPopup
            )
          }
          nextHandler={handleLocationSelectPaymentArrangementNext}
        />
      )}
    </>
  );
};
