const moment = require('moment');
const pool = require('../db');

class CronService {
  async updateSpeedchange() {
    const currentDate = moment().format('YYYY-MM-DD');

    const fetchQuery = `SELECT id FROM internets_eastlink_speed_change_order WHERE expected_completion_date <= ?;`;
    const updateQuery = `UPDATE customer_details_internets_eastlink SET speed_change_order = NULL WHERE speed_change_order IN (?);`;
    const deleteQuery = `DELETE FROM internets_eastlink_speed_change_order WHERE id IN (?);`;

    try {
      const results = await this.executeQuery(fetchQuery, [currentDate]);
      const speedChangeOrderIds = results.map(row => row.id);

      if (speedChangeOrderIds.length === 0) {
        return { status: true, message: 'No records to update or delete.' };
      }

      await this.executeQuery(updateQuery, [speedChangeOrderIds]);
      await this.executeQuery(deleteQuery, [speedChangeOrderIds]);

      return { status: true };
    } catch (error) {
      console.error(`CronService updateSpeedchange -> ${JSON.stringify(error)}`, "error");
      throw error;
    }
  }

  async updateTvDate() {
    const currentDate = moment().format('YYYY-MM-DD');

    const fetchQuery = `SELECT id FROM customer_details_tvs WHERE account_status = 'REMOVED' AND requested_cancellation_date <= ?;`;
    const updateQuery = `UPDATE contacts_customer_details SET tv_id = NULL WHERE tv_id IN (?);`;
    const deleteQuery = `DELETE FROM customer_details_tvs WHERE id IN (?);`;

    try {
      // Fetch records
      const results = await this.executeQuery(fetchQuery, [currentDate]);
      const tvIds = results.map(row => row.id);

      if (tvIds.length === 0) {
        return { status: true, message: 'No records to update or delete.' };
      }

      await this.executeQuery(updateQuery, [tvIds]);
      await this.executeQuery(deleteQuery, [tvIds]);

      return { status: true };
    } catch (error) {
      console.error(`CronService updateTvDate -> ${JSON.stringify(error)}`, "error");
      throw error;
    }
  }

  async updatePhoneDate() {
    const currentDate = moment().format('YYYY-MM-DD');

    const fetchQuery = `SELECT id FROM customer_details_phones WHERE account_status = 'DELETED' AND requested_cancellation_date <= ?;`;
    const updateQuery = `UPDATE contacts_customer_details SET phone_id = NULL WHERE phone_id IN (?);`;
    const deleteQuery = `DELETE FROM customer_details_phones WHERE id IN (?);`;

    try {
      
      const results = await this.executeQuery(fetchQuery, [currentDate]);
      const phoneIds = results.map(row => row.id);

      if (phoneIds.length === 0) {
        return { status: true, message: 'No records to update or delete.' };
      }
    
      await this.executeQuery(updateQuery, [phoneIds]);
      await this.executeQuery(deleteQuery, [phoneIds]);

      return { status: true };
    } catch (error) {
      console.error(`CronService updatePhoneDate -> ${JSON.stringify(error)}`, "error");
      throw error;
    }
  }

  async executeQuery(query, values) {
    return new Promise((resolve, reject) => {
      pool.query(query, values, (err, results) => {
        if (err) {
          return reject(err);
        }
        resolve(results);
      });
    });
  }
}

module.exports = CronService;
