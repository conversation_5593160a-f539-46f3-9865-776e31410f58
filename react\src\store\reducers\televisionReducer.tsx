import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import {
  selectedTVPlanProps,
  televisionReducerState,
} from "../../typings/typing";

const initialState = {
  plan: {
    basePlan: "",
    additionalPlan: [],
    additionalChannels: [],
    DVR: [],
    isPlanRemoved: false,
    isPlanUpdated: false,
  },
  showDeleteOrderPopup: false,
  showTelevisionPopup: false,
  showTelevisionConfirmationPopup: false,
} satisfies televisionReducerState as televisionReducerState;

const televisionReducer = createSlice({
  name: "TELEVISION_REDUCER",
  initialState,
  reducers: {
    toggleShowTelevisionPopup: (state) => {
      state.showTelevisionPopup = !state.showTelevisionPopup;
    },
    toggleShowTelevisionConfirmationPopup: (state) => {
      state.showTelevisionConfirmationPopup =
        !state.showTelevisionConfirmationPopup;
    },
    toggleDeleteOrderPopup: (state) => {
      state.showDeleteOrderPopup = !state.showDeleteOrderPopup;
    },
    setIsPlanRemoved: (state, action: PayloadAction<boolean>) => {
      state.plan.isPlanRemoved = action.payload;
    },
    setPlan: (state, action: PayloadAction<selectedTVPlanProps>) => {
      state.plan = action.payload;
    },
    setBasePlan: (state, action: PayloadAction<string>) => {
      state.plan.basePlan = action.payload;
    },
    setAdditionalPlan: (state, action: PayloadAction<string>) => {
      state.plan.additionalPlan = action.payload;
    },
    setAdditionalChannels: (state, action: PayloadAction<number>) => {
      if (state.plan.additionalChannels.includes(action.payload)) {
        state.plan.additionalChannels = state.plan.additionalChannels.filter(
          (item) => item !== action.payload
        );
      } else {
        state.plan.additionalChannels.push(action.payload);
      }
    },
    setDVR: (state) => {
      state.plan.DVR = !state.plan.DVR;
    },
    resetTVPopups: (state) => {
      state.plan = {
        basePlan: "",
        additionalPlan: [],
        additionalChannels: [],
        DVR: [],
        isPlanRemoved: false,
        isPlanUpdated: false,
      };
      state.showDeleteOrderPopup = false;
      state.showTelevisionPopup = false;
      state.showTelevisionConfirmationPopup = false;
    },
  },
});

export const {
  toggleShowTelevisionPopup,
  toggleShowTelevisionConfirmationPopup,
  toggleDeleteOrderPopup,
  setPlan,
  setBasePlan,
  setAdditionalPlan,
  setAdditionalChannels,
  setDVR,
  setIsPlanRemoved,
  resetTVPopups,
} = televisionReducer.actions;

export default televisionReducer.reducer;
