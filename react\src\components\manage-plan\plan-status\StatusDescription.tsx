import React from "react";
import { Link } from "react-router-dom";
import { RightCaretIcon } from "../../../assets/Icons";

interface InternalProcessing {
  full_mailing_address?: string;
  ship_date?: string;
}

interface ShipPackage {
  ship_date?: string;
  courier?: string;
  tracking_url?: string;
}

interface ModemInstallation {
  install_date?: string;
  install_time?: string;
  live_date?: string;
}

interface Plan {
  internetElMoveOrder?: any;
  internalProcessing?: InternalProcessing;
  shipPackage?: ShipPackage;
  modemInstallation?: ModemInstallation;
  internal_processing_status?: string;
  ship_package_status?: string;
  modem_activation_status?: string;
  modem_installation_status?: string;
  tech_appointment?: any; // Adjust according to the actual type if possible
  internetElCreationOrder: any;
  live_date: any;
}

interface StatusDescriptionProps {
  plan?: Plan;
}

const StatusDescription: React.FC<StatusDescriptionProps> = ({ plan }) => {
  if (
    plan?.internalProcessing &&
    plan?.internal_processing_status === "In Progress"
  ) {
    return (
      <div className="flex flex-col 2xl:w-[320px] bg-white lg:w-[280px] w-full 2xl:p-30 lg:p-5 p-4 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 lg:mt-3 mt-1.5">
        <div>
          <p className="text-base font-medium uppercase">INTERNAL PROCESSING</p>
        </div>
        <div>
          <p className="text-base">
            Thanks for joining the herd. We are currently processing your order
            and setting everything up. The next step is shipping your modem to{" "}
            <b>{plan?.internalProcessing?.full_mailing_address}</b>. We normally
            schedule shipments to arrive close to your requested installation
            date, and right now, your package is expected to ship on{" "}
            <b>{plan?.internalProcessing?.ship_date}</b>.
          </p>
        </div>
      </div>
    );
  }

  if (plan?.shipPackage && plan?.ship_package_status === "In Progress") {
    const trackingUrl =
      plan?.internetElCreationOrder?.creationOrderShipping?.tracking_url?.startsWith(
        "http://"
      ) ||
      plan?.internetElCreationOrder?.creationOrderShipping?.tracking_url?.startsWith(
        "https://"
      )
        ? plan?.internetElCreationOrder?.creationOrderShipping?.tracking_url
        : `https://${plan?.internetElCreationOrder?.creationOrderShipping?.tracking_url}`;

    return (
      <div className="flex bg-white flex-col 2xl:w-[320px] lg:w-[280px] w-full 2xl:p-30 lg:p-5 p-4 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 lg:mt-3 mt-1.5">
        <div>
          <p className="text-base font-medium uppercase">Ship Package</p>
        </div>
        <div className="flex flex-col gap-5">
          <div>
            <p className="text-base">
              Modem has shipped on <b>{plan?.shipPackage?.ship_date}</b> through{" "}
              <b>{plan?.shipPackage?.courier}</b>. Normally this is dropped off
              outside your home without having to sign for it.
            </p>
          </div>
          {trackingUrl && (
            <Link
              className="btn btn-fill flex items-center justify-between gap-2 cursor-pointer"
              to={trackingUrl}
              target="_blank"
            >
              <span>View tracking information</span>
              <RightCaretIcon />
            </Link>
          )}
        </div>
      </div>
    );
  }

  if (plan?.modem_activation_status === "In Progress") {
    return (
      <div className="flex flex-col bg-white 2xl:w-[320px] lg:w-[280px] w-full 2xl:p-30 lg:p-5 p-4 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 lg:mt-3 mt-1.5">
        <div>
          <p className="text-base font-medium uppercase">Modem Activation</p>
        </div>
        <div>
          <p className="text-base">
            We are currently in the process of turning your modem live and will
            message you once complete.
          </p>
        </div>
      </div>
    );
  }

  if (
    plan?.modemInstallation &&
    plan?.modem_installation_status === "In Progress" &&
    plan?.tech_appointment !== null
  ) {
    return (
      <div className="flex flex-col bg-white 2xl:w-[320px] lg:w-[280px] w-full 2xl:p-30 lg:p-5 p-4 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 lg:mt-3 mt-1.5">
        <div>
          <p className="text-base font-medium uppercase">Modem Installation</p>
        </div>
        <div className="flex flex-col gap-5">
          <div>
            <p className="text-base">
              Looks like we are going to need to send a technician to your home.
              We have scheduled this for{" "}
              <b>{plan?.modemInstallation?.install_date}</b> between{" "}
              <b>{plan?.modemInstallation?.install_time}</b>. If this time does
              not work for you please let us know through text. Thanks.
            </p>
          </div>
          <div
            onClick={() => {
              window.open(
                "https://purplecowinternet.com/internet/setup"
              );
            }}
            className="btn btn-fill flex items-center justify-between gap-2 cursor-pointer"
          >
            <span>How to setup your internet</span>
            <RightCaretIcon />
          </div>
        </div>
      </div>
    );
  }

  if (
    plan?.modem_installation_status === "In Progress" &&
    plan?.tech_appointment === null
  ) {
    return (
      <div className="flex flex-col bg-white 2xl:w-[320px] lg:w-[280px] w-full 2xl:p-30 lg:p-5 p-4 2xl:gap-5 gap-2.5 rounded-20 shadow-cardShadow05 2xl:mt-5 lg:mt-3 mt-1.5">
        <div>
          <p className="text-base font-medium uppercase">Modem Installation</p>
        </div>
        <div className="flex flex-col gap-5">
          <div>
            <p className="text-base">
              Got some great news for you! Your modem should be activated and
              good to plug in on{" "}
              <b>
                {plan?.internetElCreationOrder?.sf_record_id ===
                plan?.internetElMoveOrder?.sf_record_id
                  ? plan?.internetElMoveOrder?.install_date // if creation and move order are same then show date from move order
                  : plan?.internetElCreationOrder?.install_date
                  ? plan?.internetElCreationOrder?.install_date // else show date from creation order if it is not there then show NA
                  : "NA"}
              </b>
              .
            </p>
          </div>
          <div
            onClick={() => {
              window.open(
                "https://purplecowinternet.com/internet/setup"
              );
            }}
            className="btn btn-fill flex items-center justify-between gap-2 cursor-pointer"
          >
            <span>How to setup your internet</span>
            <RightCaretIcon />
          </div>
        </div>
      </div>
    );
  }

  return null; // In case none of the conditions match
};

export default StatusDescription;
