@use "./fonts.scss";
@use "./mixin.scss" as mixin;
@use "./variables.scss" as *;

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: "Roboto", sans-serif;
  font-variant-ligatures: none !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: $color-primary;
  line-height: 1.35;
  min-height: 100dvh;
}
body:has(.popup-open, .account-option) {
  // overflow: hidden;

  .popup-backdrop {
    display: block;
  }
}
body:has(.best-popup-open) {
  .best-popup-backdrop {
    display: block;
  }
}

.sidebarMenu-wrapper:has(.menu-open) {
  // position: relative;

  &:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    top: 0;
    left: 0;
    z-index: 6;
  }
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform: rotate(180deg);
}

// Scrollbar
::-webkit-scrollbar {
  width: 6px; /* Set the width of the scrollbar */
  height: 8px;
}
::-webkit-scrollbar-thumb {
  background-color: #7421b5; /* Set the color of the thumb */
  border-radius: 6px; /* Set the border radius of the thumb */
}

::-webkit-scrollbar-track {
  background-color: #dedede; /* Set the color of the track */
}

.popup-backdrop,
.best-popup-backdrop {
  display: none;
}
.popup-open code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}
.scrollbarNone {
  &::-webkit-scrollbar {
    display: none;
  }
}
* {
  box-sizing: border-box;
}
img {
  vertical-align: top;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.wrapper {
  width: 100%;
  margin: 0 auto;
  max-width: 1440px;
}

// Status Color
.status-active,
.status-complete {
  background-color: $color-success-light;
  color: $color-success;
}
.status-outstanding,
.status-PAYMENT_DUE {
  background-color: $color-outstanding-light;
  color: $color-outstanding;
}
.status-disconnected,
.status-inactive {
  background-color: $color-outstanding-light;
  color: $color-outstanding;
}
.status-shipped,
.status-arrangement,
.status-paymentarrangements,
.status-inprogress {
  background-color: $color-warning-light;
  color: $color-warning;
}

.status-processingmoverequest,
.status-moving {
  background-color: #1f8cb5;
  color: white;
}

.loader {
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid $color-white;
  border-bottom-color: transparent;
  box-sizing: border-box;
  transform: translate(-50%, -50%);
  animation: rotation 1s linear infinite;
  vertical-align: middle;
}

// Input Radio Button
.input-radio {
  opacity: 0;

  &:checked + .input-radio-label {
    &:before {
      opacity: 1;
    }
  }
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  appearance: none;
  -webkit-appearance: none;
}
.input-radio-label {
  opacity: 1;
  &:before {
    content: "";
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #c136c2;
    position: absolute;
    top: 50%;
    left: 6px;
    transform: translate(-50%, -50%);
    transition: 0.3s ease-in-out;
    opacity: 0;
  }
  &:after {
    content: "";
    display: block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid #a9a9a9;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
}

// Glass style
.glass-style {
  position: relative;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    // background: inherit;

    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(15px); /* Blur effect */
    z-index: -1;
    border-radius: 30px;
  }
}

// input Styling
.input-wrapper {
  .error-text {
    display: none;
  }
  &.error {
    .error-text {
      display: flex;
    }
    .input-default {
      border-color: $color-error;
    }
  }
  .input-default {
    padding: 14px;
    background-color: #f7f7f7;
    border-radius: 20px;
    outline: none;
    width: 100%;
    border: 1px solid #f7f7f7;
    transition: 0.3s;
    font-size: 16px;
    font-variant-ligatures: none !important;

    &::placeholder {
      color: $color-A9A9A9;
      font-size: 16px;
      font-weight: 500;

      @include mixin.mq("phone-and-tablet") {
        font-size: 14px;
      }
    }

    &:focus,
    &:hover {
      border-color: $color-secondary;
    }

    @include mixin.mq("phone-and-tablet") {
      padding: 10px;
      font-size: 14px;
    }
    @include mixin.mq("small-desk-to-mid-lap") {
      font-size: 14px;
      padding: 14px;
    }
    @include mixin.mq("lap-to-small-desk") {
      font-size: 14px;
      padding: 14px;
    }
    @include mixin.mq("lap-and-up") {
      font-size: 16px;
      padding: 15px;
    }

    @media screen and (-webkit-min-device-pixel-ratio: 0) and (min-color-index: 0) and (-webkit-min-device-pixel-ratio: 0) {
      &.password {
        /* Disable autocomplete for Safari */
        // -webkit-autofill: password; /* Custom property to avoid suggestions */
        &:focus {
          padding-right: 35px;
        }
      }
    }
  }
}

.custom-dropdown {
  .error-text {
    display: none;
  }
  &.error {
    .error-text {
      display: flex;
    }
    .input-default {
      border-color: $color-error;
    }
  }
  // pl-3 pr-10 lg:py-[13px] py-[9px]
  button {
    font-size: 16px;
    padding-left: 12px;
    padding-right: 40px;
    padding-top: 13px;
    padding-bottom: 13px;

    @include mixin.mq("phone-and-tablet") {
      font-size: 14px;
      padding: 10px;
      padding-top: 9px;
      padding-bottom: 9px;
    }
    @include mixin.mq("lap-to-small-desk") {
      font-size: 14px;
      padding-top: 12px;
      padding-bottom: 11px;
    }
    @include mixin.mq("lap-and-up") {
      font-size: 16px;
      padding-top: 18px;
      padding-bottom: 17px;
    }
  }
}

// Button Styling
.btn,
.btn-small {
  font-size: 16px;
  font-weight: 400;
  padding: 15px;
  transition: 0.3s all;
  line-height: 1.33;
  border-radius: 20px;
  width: 100%;
  text-align: center;
  margin: 0 auto;
  font-variant-ligatures: none !important;

  &-fill {
    background-color: #fbc400;
    color: black;
    border: 2px solid #fbc400;

    &:hover {
      background-color: #f8d000;
    }
  }

  &-transparent {
    background-color: transparent;
    color: black;
    border: 2px solid black;
  }

  &-white {
    background-color: transparent;
    color: $color-white;
    border: 1px solid $color-white;

    &:hover {
      background-color: $color-white;
      color: $color-primary;
    }
  }

  @include mixin.mq("phone-and-tablet") {
    padding: 13px 20px;
    font-size: 14px;
  }
  @include mixin.mq("mid-lap") {
    padding: 12px;
  }
  @include mixin.mq("mini-phone") {
    padding: 13px 15px;
    font-size: 14px;
  }

  .btn-loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid black;
    border-bottom-color: transparent;
    box-sizing: border-box;
    transform: translate(-50%, -50%);
    animation: rotation 1s linear infinite;
    vertical-align: middle;
  }

  &:hover {
    .btn-loader {
      border-bottom-color: transparent;
    }
  }
}

.icon-loader {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid $color-primary;
  border-bottom-color: transparent;
  box-sizing: border-box;
  transform: translate(-50%, -50%);
  animation: rotation 1s linear infinite;
  vertical-align: middle;
}
@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.btn-small {
  padding: 10px 16px;
}

// Popup Styling
.sub-text {
  font-size: 16px;
  line-height: 1.35;
  color: $color-primary;
}

// Menus Styling
.menuBar {
  .menu-item {
    position: relative;
    transition: 0.2s;

    svg {
      transition: 0.2s;
    }
    .menu-label {
      transition: 0.2s;
    }

    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: 4px;
      background-color: #aa7de6;
      border-radius: 4px;
      opacity: 0;
      transition: all 0.2s ease-in-out;
    }
    &.active {
      &:after {
        opacity: 1;
      }
    }
  }
}

.nav-item-group:has(.menu-item.active) {
  background-color: rgba(170, 125, 230, 0.1); /* example soft purple bg */
}
.referral-history-table {
  td,
  th {
    padding: 10px;
    text-align: left;
    font-size: 16px;
    font-weight: 500;

    @include mixin.mq("phone-and-tablet") {
      padding: 6px;
      font-size: 14px;
    }
  }
  td {
    font-weight: 700;
  }
}
.view-referral {
  position: fixed;
  top: calc(100% - 95px);
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  @include mixin.mq("phone-and-tablet") {
    transition: 0.5s ease-in-out;
  }

  &.active {
    top: 15%;
    z-index: 102;
  }
}
.balance-card {
  padding: 20px;
}

// OTP Verification Style
.verification-code {
  position: relative;

  .control-label {
    display: block;
    font-weight: 900;
    margin-bottom: 10px;
  }
  .verification-code--inputs {
    width: fit-content;
    display: flex;
    gap: 10px;

    &.error {
      border-color: $color-error;
    }

    input[type="text"] {
      max-width: 46px;
      width: 16.66%;
      height: 46px;
      padding: 10px;
      text-align: center;
      display: inline-block;
      box-sizing: border-box;
      border-radius: 10px;
      border: 1px solid #dddddd;
      outline-color: $color-primary;

      @include mixin.mq("phone") {
        padding: 6px;
      }
    }
  }
}

// Address Fields
.suggestions-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  border: 1px solid #ccc;
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  position: absolute;
  width: calc(100% - 20px);
  background-color: white;
  z-index: 1000; /* Ensure it appears above other elements */
}

.suggestion-item {
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
}

.suggestion-item:hover {
  background-color: #f0f0f0;
}

// Password Regex
.password-regex {
  font-size: 14px;

  li {
    position: relative;
    padding-left: 20px;
    margin-top: 5px;
    transition: 0.3s ease-in-out;

    &:before {
      content: "";
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14' xml:space='preserve' transform='rotate(90)' width='14' height='14'%3E%3Cpath d='M7.723 13.789a0.308 0.308 0 0 1 -0.29 0.211 0.28 0.28 0 0 1 -0.094 -0.015c-0.27 -0.087 -0.409 -0.087 -0.679 0a0.304 0.304 0 0 1 -0.384 -0.196 0.304 0.304 0 0 1 0.196 -0.384c0.389 -0.126 0.665 -0.126 1.054 0 0.161 0.051 0.248 0.224 0.196 0.384m4.27 -3.789c-0.295 0.427 -0.806 0.681 -1.369 0.681 -0.49 0 -1.038 -0.25 -1.618 -0.516 -0.658 -0.301 -1.339 -0.612 -2.007 -0.617 -0.667 0.004 -1.348 0.315 -2.007 0.617 -0.58 0.265 -1.127 0.516 -1.618 0.516 -0.563 0 -1.074 -0.255 -1.369 -0.681 -0.559 -0.81 -0.129 -1.726 0.252 -2.534 0 0 1.965 -4.019 2.068 -4.235C5.13 1.542 5.636 0.577 6.009 0.315 6.302 0.108 6.643 0 7 0s0.698 0.108 0.988 0.313c0.373 0.263 0.88 1.228 1.686 2.917 0.104 0.217 2.068 4.235 2.068 4.235 0.38 0.808 0.811 1.724 0.251 2.534m-0.803 -2.275s-1.286 -2.651 -1.803 -3.687c-0.068 -0.136 -0.159 -0.326 -0.263 -0.546C8.773 2.755 7.951 1.032 7.637 0.811a1.104 1.104 0 0 0 -1.275 0c-0.314 0.221 -1.135 1.945 -1.486 2.681 -0.105 0.22 -2.067 4.233 -2.067 4.233 -0.341 0.723 -0.662 1.407 -0.301 1.928 0.181 0.262 0.505 0.418 0.867 0.418 0.357 0 0.847 -0.224 1.365 -0.461 0.684 -0.313 1.46 -0.667 2.259 -0.672h0.004c0.799 0.005 1.575 0.359 2.259 0.672 0.518 0.237 1.007 0.461 1.365 0.461 0.362 0 0.687 -0.157 0.867 -0.418 0.36 -0.522 0.039 -1.205 -0.301 -1.928m-1.775 4.534a0.961 0.961 0 0 1 -0.532 0.539 0.933 0.933 0 0 1 -0.329 0.063 0.887 0.887 0 0 1 -0.378 -0.085c-0.989 -0.462 -1.363 -0.462 -2.351 0a0.887 0.887 0 0 1 -0.708 0.021 0.961 0.961 0 0 1 -0.532 -0.539c-0.197 -0.492 0.02 -1.067 0.483 -1.282 1.499 -0.701 2.366 -0.701 3.865 0 0.463 0.216 0.679 0.791 0.483 1.282m-0.74 -0.73c-1.33 -0.622 -2.019 -0.622 -3.35 0 -0.173 0.08 -0.252 0.311 -0.175 0.504 0.037 0.094 0.105 0.164 0.19 0.199a0.28 0.28 0 0 0 0.226 -0.007c0.576 -0.269 1.004 -0.404 1.433 -0.404s0.858 0.134 1.434 0.404a0.28 0.28 0 0 0 0.226 0.007 0.345 0.345 0 0 0 0.19 -0.199c0.077 -0.193 -0.003 -0.424 -0.175 -0.504' fill='%232c212c'/%3E%3C/svg%3E");
      position: absolute;
      left: 0;
      top: 2.5px;
      width: 14px;
      height: 14px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: contain;
    }

    &.valid {
      color: $color-success;
      &:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14' xml:space='preserve' transform='rotate(90)' width='14' height='14'%3E%3Cpath d='M7.723 13.789a0.308 0.308 0 0 1 -0.29 0.211 0.28 0.28 0 0 1 -0.094 -0.015c-0.27 -0.087 -0.409 -0.087 -0.679 0a0.304 0.304 0 0 1 -0.384 -0.196 0.304 0.304 0 0 1 0.196 -0.384c0.389 -0.126 0.665 -0.126 1.054 0 0.161 0.051 0.248 0.224 0.196 0.384m4.27 -3.789c-0.295 0.427 -0.806 0.681 -1.369 0.681 -0.49 0 -1.038 -0.25 -1.618 -0.516 -0.658 -0.301 -1.339 -0.612 -2.007 -0.617 -0.667 0.004 -1.348 0.315 -2.007 0.617 -0.58 0.265 -1.127 0.516 -1.618 0.516 -0.563 0 -1.074 -0.255 -1.369 -0.681 -0.559 -0.81 -0.129 -1.726 0.252 -2.534 0 0 1.965 -4.019 2.068 -4.235C5.13 1.542 5.636 0.577 6.009 0.315 6.302 0.108 6.643 0 7 0s0.698 0.108 0.988 0.313c0.373 0.263 0.88 1.228 1.686 2.917 0.104 0.217 2.068 4.235 2.068 4.235 0.38 0.808 0.811 1.724 0.251 2.534m-0.803 -2.275s-1.286 -2.651 -1.803 -3.687c-0.068 -0.136 -0.159 -0.326 -0.263 -0.546C8.773 2.755 7.951 1.032 7.637 0.811a1.104 1.104 0 0 0 -1.275 0c-0.314 0.221 -1.135 1.945 -1.486 2.681 -0.105 0.22 -2.067 4.233 -2.067 4.233 -0.341 0.723 -0.662 1.407 -0.301 1.928 0.181 0.262 0.505 0.418 0.867 0.418 0.357 0 0.847 -0.224 1.365 -0.461 0.684 -0.313 1.46 -0.667 2.259 -0.672h0.004c0.799 0.005 1.575 0.359 2.259 0.672 0.518 0.237 1.007 0.461 1.365 0.461 0.362 0 0.687 -0.157 0.867 -0.418 0.36 -0.522 0.039 -1.205 -0.301 -1.928m-1.775 4.534a0.961 0.961 0 0 1 -0.532 0.539 0.933 0.933 0 0 1 -0.329 0.063 0.887 0.887 0 0 1 -0.378 -0.085c-0.989 -0.462 -1.363 -0.462 -2.351 0a0.887 0.887 0 0 1 -0.708 0.021 0.961 0.961 0 0 1 -0.532 -0.539c-0.197 -0.492 0.02 -1.067 0.483 -1.282 1.499 -0.701 2.366 -0.701 3.865 0 0.463 0.216 0.679 0.791 0.483 1.282m-0.74 -0.73c-1.33 -0.622 -2.019 -0.622 -3.35 0 -0.173 0.08 -0.252 0.311 -0.175 0.504 0.037 0.094 0.105 0.164 0.19 0.199a0.28 0.28 0 0 0 0.226 -0.007c0.576 -0.269 1.004 -0.404 1.433 -0.404s0.858 0.134 1.434 0.404a0.28 0.28 0 0 0 0.226 0.007 0.345 0.345 0 0 0 0.19 -0.199c0.077 -0.193 -0.003 -0.424 -0.175 -0.504' fill='%2324cc20'/%3E%3C/svg%3E");
      }
    }
    &.invalid {
      color: $color-error;
      &:before {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 14' xml:space='preserve' transform='rotate(90)' width='14' height='14'%3E%3Cpath d='M7.723 13.789a0.308 0.308 0 0 1 -0.29 0.211 0.28 0.28 0 0 1 -0.094 -0.015c-0.27 -0.087 -0.409 -0.087 -0.679 0a0.304 0.304 0 0 1 -0.384 -0.196 0.304 0.304 0 0 1 0.196 -0.384c0.389 -0.126 0.665 -0.126 1.054 0 0.161 0.051 0.248 0.224 0.196 0.384m4.27 -3.789c-0.295 0.427 -0.806 0.681 -1.369 0.681 -0.49 0 -1.038 -0.25 -1.618 -0.516 -0.658 -0.301 -1.339 -0.612 -2.007 -0.617 -0.667 0.004 -1.348 0.315 -2.007 0.617 -0.58 0.265 -1.127 0.516 -1.618 0.516 -0.563 0 -1.074 -0.255 -1.369 -0.681 -0.559 -0.81 -0.129 -1.726 0.252 -2.534 0 0 1.965 -4.019 2.068 -4.235C5.13 1.542 5.636 0.577 6.009 0.315 6.302 0.108 6.643 0 7 0s0.698 0.108 0.988 0.313c0.373 0.263 0.88 1.228 1.686 2.917 0.104 0.217 2.068 4.235 2.068 4.235 0.38 0.808 0.811 1.724 0.251 2.534m-0.803 -2.275s-1.286 -2.651 -1.803 -3.687c-0.068 -0.136 -0.159 -0.326 -0.263 -0.546C8.773 2.755 7.951 1.032 7.637 0.811a1.104 1.104 0 0 0 -1.275 0c-0.314 0.221 -1.135 1.945 -1.486 2.681 -0.105 0.22 -2.067 4.233 -2.067 4.233 -0.341 0.723 -0.662 1.407 -0.301 1.928 0.181 0.262 0.505 0.418 0.867 0.418 0.357 0 0.847 -0.224 1.365 -0.461 0.684 -0.313 1.46 -0.667 2.259 -0.672h0.004c0.799 0.005 1.575 0.359 2.259 0.672 0.518 0.237 1.007 0.461 1.365 0.461 0.362 0 0.687 -0.157 0.867 -0.418 0.36 -0.522 0.039 -1.205 -0.301 -1.928m-1.775 4.534a0.961 0.961 0 0 1 -0.532 0.539 0.933 0.933 0 0 1 -0.329 0.063 0.887 0.887 0 0 1 -0.378 -0.085c-0.989 -0.462 -1.363 -0.462 -2.351 0a0.887 0.887 0 0 1 -0.708 0.021 0.961 0.961 0 0 1 -0.532 -0.539c-0.197 -0.492 0.02 -1.067 0.483 -1.282 1.499 -0.701 2.366 -0.701 3.865 0 0.463 0.216 0.679 0.791 0.483 1.282m-0.74 -0.73c-1.33 -0.622 -2.019 -0.622 -3.35 0 -0.173 0.08 -0.252 0.311 -0.175 0.504 0.037 0.094 0.105 0.164 0.19 0.199a0.28 0.28 0 0 0 0.226 -0.007c0.576 -0.269 1.004 -0.404 1.433 -0.404s0.858 0.134 1.434 0.404a0.28 0.28 0 0 0 0.226 0.007 0.345 0.345 0 0 0 0.19 -0.199c0.077 -0.193 -0.003 -0.424 -0.175 -0.504' fill='%23cc2b20'/%3E%3C/svg%3E");
      }
    }
  }
}

// custom text overflow elipsis for address

.custom-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

// add this css to show disabled cursor,
// make sure you also have to restrict click as well
// this css will only change cursor style
.custom-cursor-not-allowed {
  cursor: not-allowed;
}
