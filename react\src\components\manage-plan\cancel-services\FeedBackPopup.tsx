import { useState } from "react";
import Popup from "../../common/Popup";
import { useDispatch, useSelector } from "react-redux";
import { feedback } from "../../../store/selectors/cancelServiceSelectors";
import {
  setFeedback,
  toggleShowFeedbackPopup,
  toggleShowOfferPopup,
} from "../../../store/reducers/cancelServiceReducer";
import Button from "../../common/Button";

const feedbackList = [
  "Moving to a non serviceable address",
  "Not happy with customer service",
  "Not happy with internet service",
  "Higher download or upload required",
  "Switched for better price",
];
const FeedBackPopup = () => {
  const dispatch = useDispatch();
  const newFeedback = useSelector(feedback);
  const [userFeedback, setUserFeedback] = useState<string>(newFeedback);
  return (
    <Popup title="We value your feedback. Why are you leaving the herd?">
      <div className="flex flex-col gap-10">
        <div className="flex flex-col gap-5">
          <div>
            <p>
              Please let us understand why you’re leaving the herd so we
              continuously improve.{" "}
            </p>
          </div>
          <div className="flex flex-col gap-5">
            {feedbackList.map((feedbackText, index) => (
              <div className="flex gap-2.5 relative">
                <input
                  type="radio"
                  name={`feedback-${index}`}
                  id={`feedback-${index}`}
                  onChange={() => setUserFeedback(feedbackText)}
                  className="input-radio"
                  checked={userFeedback === feedbackText}
                />
                <label
                  htmlFor={`feedback-${index}`}
                  className="input-radio-label"
                >
                  {feedbackText}
                </label>
              </div>
            ))}
          </div>
        </div>
        <div className="flex gap-2.5 lg:flex-row flex-col">
          <div className="basis-full">
            <Button
              title="Keep Purple Cow"
              btnType="transparent"
              type="button"
              clickEvent={() => dispatch(toggleShowFeedbackPopup())}
              
            />
          </div>
          <div className="basis-full">
            <Button
              title="Confirm"
              clickEvent={() => {
                dispatch(toggleShowFeedbackPopup());
                dispatch(setFeedback(userFeedback));
                dispatch(toggleShowOfferPopup());
              }}
              disabled={userFeedback ? false : true}
            />
          </div>
        </div>
      </div>
    </Popup>
  );
};

export default FeedBackPopup;
