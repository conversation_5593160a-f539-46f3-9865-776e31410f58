import React from "react";
import { TVPlanProps } from "../../../typings/typing";
import Button from "../../common/Button";
import AdditionalTVChannels from "./AdditionalTVChannels";
import AdditionalTVPackage from "./AdditionalTVPackage";
import BaseTVPackage from "./BaseTVPackage";
import CloudDVR from "./CloudDVR";
import { useSelector } from "react-redux";
import { plan } from "../../../store/selectors/televisionSelectors";
import PackageSkeleton from "./skelaton/PackageSkelaton";
import InternetPlanCardSkeleton from "../internet/skeletons/InternetPlanCardSkeleton";

const TVPlan: React.FC<TVPlanProps> = ({
  tvPlan,
  tvList,
  isLoading,
  isCancel,
  handleChannelSelect,
  handleBasePackageSelect,
  handleAdditionalPackageSelect,
  handleDVRSelect,
  handleTVPlanSubmit,
  closeHandler,
  handleViewAll,
  btnLoading,
  handleTVCancelService,
}) => {
  const currentPlan = useSelector(plan);

  return (
    <div
      className={`flex flex-col gap-5 2xl:gap-10 ${
        btnLoading && "pointer-events-none"
      }`}
    >
      <div>
        <p className="text-base flex-wrap">
          {
            "Feel free to select a packages to add this to your account or deselect any packages you want to cancel or remove. "
          }
        </p>
      </div>
      <div className="flex flex-col xl:gap-5 gap-2.5">
        <div>
          <p className="uppercase text-base font-medium">Base PACKAGE</p>
        </div>
        <div className="flex justify-center flex-wrap gap-2.5">
          {isLoading
            ? [1, 2, 3].map((index) => <PackageSkeleton key={index} />)
            : tvList?.planDetails?.length > 0 &&
              tvList?.planDetails.map((list: any) => {
                return (
                  <BaseTVPackage
                    key={list?.id}
                    currentPackage={currentPlan?.plan_name}
                    list={list}
                    basePackage={tvPlan?.basePlan}
                    handlePackageSelect={handleBasePackageSelect}
                    handleViewAll={handleViewAll}
                  />
                );
              })}
        </div>
      </div>
      {!tvPlan?.isPlanRemoved && (
        <>
          {tvPlan?.basePlan && (
            <div className="flex flex-col gap-5">
              <div>
                <p className="uppercase text-base font-medium">
                  Add Additional Packages
                </p>
              </div>
              <div className="flex justify-center flex-wrap gap-2.5">
                {isLoading ? (
                  <InternetPlanCardSkeleton />
                ) : (
                  tvList?.planDetails?.length > 0 &&
                  tvList?.planDetails.map((list: any) => {
                    if (list?.api_name === tvPlan?.basePlan) {
                      return list?.optional_extra_packages.map((item: any) => {
                        return (
                          <AdditionalTVPackage
                            key={item?.id}
                            packageId={tvPlan?.additionalPlan?.api_name}
                            selectedPlan={tvPlan?.additionalPlan}
                            additionalPackage={item}
                            handlePackageSelect={() => {
                              handleAdditionalPackageSelect(item);
                            }}
                          />
                        );
                      });
                    }
                  })
                )}
              </div>
            </div>
          )}
          {tvPlan?.basePlan && (
            <div className="flex flex-col gap-5">
              <div>
                <p className="uppercase text-base font-medium">
                  Pick additional channels $3 Each or 5 for $15
                </p>
              </div>
              {isLoading ? (
                <InternetPlanCardSkeleton />
              ) : (
                tvList?.planDetails?.length > 0 &&
                tvList?.planDetails.map((list: any, index: number) => {
                  if (list?.api_name === tvPlan?.basePlan) {
                    return (
                      <AdditionalTVChannels
                        key={index}
                        selectedChannel={tvPlan?.additionalChannels}
                        additionalChannels={list?.optional_single_channels}
                        handleChannelSelect={handleChannelSelect}
                      />
                    );
                  }
                })
              )}
            </div>
          )}
          {tvPlan?.basePlan && (
            <div className={`flex flex-col rounded-20 gap-5 cursor-pointer`}>
              <div>
                <p className="uppercase font-medium text-base">Cloud DVR</p>
              </div>
              <div className="flex justify-center">
                {isLoading ? (
                  <InternetPlanCardSkeleton />
                ) : (
                  tvList?.planDetails?.length > 0 &&
                  tvList?.planDetails.map((list: any) => {
                    if (list?.api_name === tvPlan?.basePlan) {
                      return list?.optional_iptv_products.map((item: any) => {
                        return (
                          <CloudDVR
                            key={item?.id}
                            selectedDVR={tvPlan?.DVR}
                            DVR={item}
                            handleDVRSelect={handleDVRSelect}
                          />
                        );
                      });
                    }
                  })
                )}
              </div>
            </div>
          )}
        </>
      )}
      {(tvPlan?.basePlan || (currentPlan?.basePlan && !tvPlan?.basePlan)) && (
        <div className="flex gap-[19px] lg:flex-row flex-col">
          <div className="basis-full">
            <Button
              title="Go back"
              btnType="transparent"
              type="button"
              attributes={{
                disabled: btnLoading,
              }}
              className="disabled:opacity-50"
              clickEvent={closeHandler}
            />
          </div>
          <div className="basis-full">
            <Button
              title={tvPlan?.isPlanRemoved ? "Cancel TV Service" : "Next"}
              attributes={{
                disabled:
                  isLoading ||
                  (currentPlan && tvPlan?.isPlanRemoved && !isCancel) ||
                  !tvPlan?.basePlan ||
                  (currentPlan?.plan_name === tvPlan?.basePlan &&
                    currentPlan?.extra_packages ===
                      JSON.stringify(tvPlan?.additionalPlan) &&
                    currentPlan?.single_channels ===
                      JSON.stringify(tvPlan?.additionalChannels) &&
                    currentPlan?.iptv_products === JSON.stringify(tvPlan?.DVR)),
              }}
              className="disabled:opacity-50"
              clickEvent={
                tvPlan?.isPlanRemoved
                  ? handleTVCancelService
                  : handleTVPlanSubmit
              }
              isLoading={btnLoading}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default TVPlan;
