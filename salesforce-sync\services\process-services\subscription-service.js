const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const SubscriptionServices = require("../../services/subscription-services");
const customerServices = new CustomerServices();
const subscriptionServices = new SubscriptionServices();
const InvoicesSyncService = require("../../services/chargebee/invoice-sync-services");
const SubscriptionInvoiceServices = require("../../services/subscription-invoices-services");
const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);
const INTERVAL = CONFIG.interval;

class SubscriptionTestServices {

    async getSubdDetails() {
        let subscriptionCount = 0;
        try {
            let query = `SELECT Id, chargebeeapps__CB_Subscription_Id__c, chargebeeapps__Plan_Amount__c, chargebeeapps__Subcription_Activated_At__c, chargebeeapps__Next_billing__c, chargebeeapps__Subscription_status__c, LastModifiedDate, chargebeeapps__Subscription_Created_At__c, Customer_Details__c, (SELECT Id, chargebeeapps__CB_Invoice_Id__c, chargebeeapps__Due_Amount__c, chargebeeapps__Amount__c, Expected_Payment_Date_Time__c, chargebeeapps__Status__c, LastModifiedDate, chargebeeapps__Subscription_CB_Id__c, chargebeeapps__Invoice_Date__c FROM chargebeeapps__CB_Invoices__r) FROM chargebeeapps__CB_Subscription__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const subscriptionData = await salesforceConnection.fetchAllRecords(query);
            if (subscriptionData?.length) {
                subscriptionCount = subscriptionData?.length;
                for (const subscriptionDetail of subscriptionData) {
                    await subscriptionServices.updateSubsDetails(subscriptionDetail);
                    const { chargebeeapps__CB_Invoices__r } = subscriptionDetail;

                    if (chargebeeapps__CB_Invoices__r && chargebeeapps__CB_Invoices__r?.records?.length) {
                        const invoiceData = chargebeeapps__CB_Invoices__r?.records;
                        const subscriptionInvoiceServices = new SubscriptionInvoiceServices();
                        await subscriptionInvoiceServices.checkAndManageDetails(invoiceData);
                    }
                }
            }

            return { execute: true, synctype: "getSubdDetails", status: true, subscriptionCount };
        } catch (error) {
            console.error("Sync service get subs details -> ", error);
            return { execute: true, synctype: "getSubdDetails", status: false, subscriptionCount, error: error?.message || null };
        }
    }
}

module.exports = SubscriptionTestServices;
