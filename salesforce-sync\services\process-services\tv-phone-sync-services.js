const SalesforceClient = require('../../clients/salesforce-client');
const CONFIG = require('../../config');
const CustomerServices = require("../../services/customer-sync-services");
const customerServices = new CustomerServices();
const INTERVAL = CONFIG.interval;
const PlanServices = require("../../services/plan-services");
const { getCurrentAtlanticTime } = require('../../helper/custom-helper');

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

class TvPhoneSyncTestServices {

    async getTvPhoneDetails() {
        try {
            const tvResult = await this.getTVDetails();
            const phoneResult = await this.getPhoneDetails();

            const tvCount = tvResult?.tvCount || 0;
            const phoneCount = phoneResult?.phoneCount || 0;

            return { execute: true, synctype: "getTvPhoneDetails", status: true, tvCount, phoneCount };
        } catch (error) {
            console.error("Sync service getTvPhoneDetails Detailss -> ", error);
            return { execute: true, synctype: "getTvPhoneDetails", status: false, tvCount, phoneCount, error: error?.message || null };
        }
    }

    async getTVDetails() {
        const tvCount = 0;
        try {
            let query = `SELECT Id, Name, Current_Base_Package__c, Current_Extra_Packages__c, State_Text__c, Current_Single_Channels__c, current_iptv_products__c, current_account_status__c, LastModifiedDate, Login_Details_Last_Sent__c, CreatedDate, Requested_Cancellation_Date__c FROM TV__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const tVDetails = await salesforceConnection.fetchAllRecords(query);


            if (!tVDetails?.length) return { tvCount };

            await this.checkAndManageService(tVDetails, "customer_details_tvs");

            return { tvCount };
        } catch (error) {
            console.error("Sync service get tv details -> ", error);
            return { tvCount };
        }
    }

    // Get contacts status whether exist in database or not  
    async checkAndManageService(sfServiceDetails, tableName) {
        try {

            const columnName = tableName == "customer_details_tvs" ? "tv_id" : "phone_id";

            let sfIds = sfServiceDetails.map(sfServiceDetail => `'${sfServiceDetail.Id}'`).join(",");
            if (!sfIds?.length) return;

            const query = `SELECT cd.id, cd.sf_record_id, cds.subscription_type as subsType
                            FROM ${tableName} cd 
                            left join contacts_customer_details ccd on cd.id = ccd.${columnName}
                            left join customer_details_subscriptions cds on ccd.id = cds.customer_details_id
                            where cd.sf_record_id in (${sfIds})`;

            const res = await customerServices.executeQuery(query);
            if (!res?.length) return;
            const fetchedDataFromDb = JSON.parse(JSON.stringify(res));

            if (tableName == "customer_details_tvs") await this.updateTVDetails(sfServiceDetails, fetchedDataFromDb);
            if (tableName == "customer_details_phones") await this.updatePhoneDetails(sfServiceDetails, fetchedDataFromDb);
        } catch (error) {
            console.error("SalesforceService 100 check And Manage Service -> ", error);
        }
    }

    async updateTVDetails(fetchedData, prevData) {
        try {
            for (const prev of prevData) {
                const sfTvDetail = fetchedData.find(fetched => fetched.Id === prev.sf_record_id);
                const updatedTime = getCurrentAtlanticTime();
                if (!sfTvDetail) continue;
                const {
                    Name: sf_name,
                    Current_Base_Package__c: plan_name,
                    Current_Extra_Packages__c,
                    Current_Single_Channels__c,
                    current_iptv_products__c,
                    current_account_status__c: account_status,
                    LastModifiedDate,
                    Login_Details_Last_Sent__c,
                    Requested_Cancellation_Date__c,
                    State_Text__c
                } = sfTvDetail;

                const { id: existingId, sf_record_id: tvId, subsType } = prev;

                if (account_status == "REMOVED" && State_Text__c == "Complete") {
                    const updateQuery = `
                    UPDATE contacts_customer_details 
                    SET tv_id = ?, updatedAt = ?
                    WHERE tv_id = ?`;
                    await customerServices.executeQuery(updateQuery, [null, updatedTime, existingId]);
                    await customerServices.deleteQuery("customer_details_tvs", existingId);
                    continue;
                }

                if (!plan_name) continue;

                const extra_packages = Current_Extra_Packages__c ? JSON.stringify(Current_Extra_Packages__c.split(";")) : "[]";
                const single_channels = Current_Single_Channels__c ? JSON.stringify(Current_Single_Channels__c.split(";")) : "[]";
                const iptv_products = current_iptv_products__c ? JSON.stringify(current_iptv_products__c.split(";")) : "[]";

                const getCostObj = {
                    plan_name,
                    extra_packages: Current_Extra_Packages__c ? Current_Extra_Packages__c.split(";") : [],
                    single_channels: Current_Single_Channels__c ? Current_Single_Channels__c.split(";") : [],
                    iptv_products: current_iptv_products__c ? current_iptv_products__c.split(";") : []
                };

                const planServices = new PlanServices();
                let total_service_cost = null;

                if (subsType && plan_name) {
                    const { totalAmount } = await planServices.getAddonsTelevisionPlanDetails(getCostObj, subsType);
                    total_service_cost = totalAmount;
                } else total_service_cost = 0;

                let updateQuery = "UPDATE customer_details_tvs SET ";
                let updateFields = [];
                let updateValues = [];

                const modifiedDate = getCurrentAtlanticTime(LastModifiedDate);

                if (account_status !== "REMOVED") {
                    updateFields.push("plan_name = ?");
                    updateValues.push(plan_name);

                    updateFields.push("extra_packages = ?");
                    updateValues.push(extra_packages);

                    updateFields.push("single_channels = ?");
                    updateValues.push(single_channels);

                    updateFields.push("iptv_products = ?");
                    updateValues.push(iptv_products);

                    updateFields.push("total_service_cost = ?");
                    updateValues.push(total_service_cost);

                }

                if (Login_Details_Last_Sent__c) {
                    updateFields.push("login_details_last_sent = ?");
                    updateValues.push(getCurrentAtlanticTime(Login_Details_Last_Sent__c));
                }

                if (sf_name) {
                    updateFields.push("sf_name = ?");
                    updateValues.push(sf_name);
                }

                if (Requested_Cancellation_Date__c) {
                    updateFields.push("requested_cancellation_date = ?");
                    updateValues.push(Requested_Cancellation_Date__c);
                }

                if (account_status) {
                    updateFields.push("account_status = ?");
                    updateValues.push(account_status);
                }

                updateFields.push("sf_updatedAt = ?");
                updateValues.push(modifiedDate);

                updateFields.push("state_text = ?");
                updateValues.push(State_Text__c);

                updateFields.push("updatedAt = ?");
                updateValues.push(updatedTime);

                updateQuery += updateFields.join(", ") + " WHERE sf_record_id = ?";
                updateValues.push(tvId);

                await customerServices.executeQuery(updateQuery, updateValues);
            }
        } catch (error) {
            console.error("SalesforceService updateTVDetails -> ", error);
        }
    }

    async getPhoneDetails() {
        const phoneCount = 0;
        try {
            let query = `SELECT Id, Name, Account_Status__c, Calling_Plan__c, Latest_Cancel_Phone_Order__c, Requested_Cancellation_Date__c, Phone_Number_To_Port__c, Service_Start_Date__c, LastModifiedDate FROM Phone__c`;

            if (INTERVAL === "regular") query += " WHERE LastModifiedDate >= YESTERDAY";
            query += " ORDER BY LastModifiedDate DESC";

            const phoneDetails = await salesforceConnection.fetchAllRecords(query);
            if (!phoneDetails?.length) return { phoneCount };

            await this.checkAndManageService(phoneDetails, "customer_details_phones");

            return { phoneCount };
        } catch (error) {
            console.error("Sync service get phone details -> ", error);
            return { phoneCount };
        }
    }

    async updatePhoneDetails(fetchedData, prevData) {
        try {
            for (const prev of prevData) {
                const sfPhoneDetail = fetchedData.find(fetched => fetched.Id === prev.sf_record_id);
                const updatedTime = getCurrentAtlanticTime();
                if (!sfPhoneDetail) continue;

                const { Account_Status__c: account_status, LastModifiedDate, Calling_Plan__c, Latest_Cancel_Phone_Order__c, Service_Start_Date__c, Phone_Number_To_Port__c, Requested_Cancellation_Date__c } = sfPhoneDetail;

                const { id: existingId, sf_record_id: phoneId, subsType: subscription_type } = prev;

                if (account_status == "Deleted") {
                    const updateQuery = `
                    UPDATE contacts_customer_details 
                    SET phone_id = ?, updatedAt = ?
                    WHERE phone_id = ?`;

                    await customerServices.executeQuery(updateQuery, [null, updatedTime, existingId]);
                    await customerServices.deleteQuery("customer_details_phones", existingId);
                    return;
                }

                const modifiedDate = getCurrentAtlanticTime(LastModifiedDate);

                const planServices = new PlanServices();
                const phonePlanDetails = await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);

                if (phonePlanDetails?.length) {

                    let updateFields = 'updatedAt = ?, sf_updatedAt = ?';
                    let updateValues = [updatedTime, modifiedDate];

                    const phoneCallRes = await salesforceConnection.getPhoneApiValue(phoneId);
                    const { status: phStatus, data: phData } = phoneCallRes;

                    if (phStatus) {
                        updateFields += ', plan_name = ?';
                        updateValues.push(phData?.Calling_Plan__c);
                    }

                    if (Requested_Cancellation_Date__c) {
                        const requested_cancellation_date = getCurrentAtlanticTime(Requested_Cancellation_Date__c);
                        updateFields += ', requested_cancellation_date = ?';
                        updateValues.push(requested_cancellation_date);
                    }

                    if (Service_Start_Date__c) {
                        const service_start_date = getCurrentAtlanticTime(Service_Start_Date__c);
                        updateFields += ', service_start_date = ?';
                        updateValues.push(service_start_date);
                    }

                    // Check the port type of home phone
                    let sf_phone_type = { sf_phone_type: "new", phone_number: "" };
                    if (Phone_Number_To_Port__c) {
                        sf_phone_type.phone_number = Phone_Number_To_Port__c;
                        sf_phone_type.sf_phone_type = "existing";
                    }
                    updateFields += ', sf_phone_type = ?';
                    updateValues.push(JSON.stringify(sf_phone_type));

                    if (Calling_Plan__c) {
                        updateFields += ', api_name = ?';
                        updateValues.push(Calling_Plan__c);
                    }

                    const getPrice = phonePlanDetails.find(phone => phone.api_name === Calling_Plan__c);
                    updateFields += ', plan_price = ?';

                    if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {
                        updateValues.push(getPrice.billing_period[0][subscription_type].price);
                    } else {
                        updateValues.push(null);
                    }

                    // Handle account_status
                    if (account_status) {
                        updateFields += ", account_status = ?";
                        updateValues.push(account_status);
                    } else {
                        updateFields += ", account_status = NULL";
                    }

                    updateValues.push(phoneId);

                    const updateQuery = `
                    UPDATE customer_details_phones 
                    SET ${updateFields} 
                    WHERE sf_record_id = ?`;

                    await customerServices.executeQuery(updateQuery, updateValues);
                }
            }
        } catch (error) {
            console.error("SalesforceService updatePhoneDetails -> ", error);
        }
    }
}

module.exports = TvPhoneSyncTestServices;