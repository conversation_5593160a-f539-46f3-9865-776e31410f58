import React from "react";
import { TotalCardProps } from "../../typings/typing";

const TotalCard: React.FC<TotalCardProps> = ({ cardType, value, icon, fill = false }) => {
  return (
    <div
      className={`card-wrapper flex h-full rounded-20 shadow-cardShadow05
        ${fill ? "bg-medium_purple text-white" : "bg-white text-black"}
        p-4 2xl:px-5 2xl:py-8
        items-center gap-4`}
    >
      <div className="flex-shrink-0">{icon}</div>
      <div className="flex flex-col justify-center">
        <p className="text-sm uppercase tracking-wide">
          {"TOTAL " + cardType}
        </p>
        <p className="font-normal mt-2 text-lg lg:text-xl 2xl:text-2xl">
          {value}
        </p>
      </div>
    </div>
  );
};

export default TotalCard;
