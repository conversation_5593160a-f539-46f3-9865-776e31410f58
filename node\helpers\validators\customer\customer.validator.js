const Joi = require('joi');

// Regular expression to validate mobile numbers
const mobileNumberRegex = /^[0-9]{10,15}$/;

const customerUpdateValidation = Joi.object().keys({
    secondary_phone: Joi.string()
        .trim()
        .pattern(mobileNumberRegex)
        .allow('') // Allow empty string
        .messages({
            'string.pattern.base': 'Secondary number must be a valid mobile number.'
        }),
    additional_name: Joi.string()
        .trim()
        .min(4)
        .max(50)
        .allow('') // Allow empty string
        .messages({
            'string.min': 'Additional name must be at least {#limit} characters long.',
            'string.max': 'Additional name cannot exceed {#limit} characters.'
        }),
    company_name: Joi.string()
        .trim()
        .min(4)
        .max(50)
        .allow('') // Allow empty string
        .messages({
            'string.min': 'Company name must be at least {#limit} characters long.',
            'string.max': 'Company name cannot exceed {#limit} characters.'
        })
});

module.exports = { customerUpdateValidation };
