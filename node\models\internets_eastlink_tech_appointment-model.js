const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("internets_eastlink_tech_appointment", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      allowNull: false,
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    install_date: {
      type: Sequelize.DATEONLY,
      comment: "Associated with Install_Date__c field of salesforce Tech_Appointment__c object."
    },
    install_time: {
      type: Sequelize.STRING(18),
      comment: "Associated with Install_Time__c field of salesforce Tech_Appointment__c object."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of Tech_Appointment__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      tableName: 'internets_eastlink_tech_appointment', // Specify the table name explicitly
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}