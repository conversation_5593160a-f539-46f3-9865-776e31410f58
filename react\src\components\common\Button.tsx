import React from "react";
import { ButtonProps } from "../../typings/typing";

const Button: React.FC<ButtonProps> = ({
  clickEvent,
  btnType = "fill",
  title,
  className,
  isLoading,
  type,
  attributes,
}) => {

  // Handle the event on click of Button
  const checkEvent = () => {
    if (clickEvent) {
      clickEvent();
    }
  };
  return (
    <>
      <button
        onClick={checkEvent}
        className={`btn btn-${btnType} ${className}`}
        type={type}
        {...attributes}
      >
        {isLoading && <span className="btn-loader"></span>}
        <span className={`${isLoading && "hidden"}`}>{title}</span>
      </button>
    </>
  );
};

export default Button;
