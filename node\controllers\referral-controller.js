const CustomError = require("../utils/errors/CustomError");
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = require("../utils/ResponseCodes");
const ReferralServices = require("../services/referral-services");
const referralServices = new ReferralServices();

class ReferralController {

    // Method to fetch details of a referral
    async fetchDetails(req, res, next) {
        try {
            const { status, data } = await referralServices.fetchDetails(req.userDetails, req.elasticLogObj);
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`ReferralController fetch Details ->`, error);
            next(error);
        }
    }

    // Method to fetch a list of referrals for a specific user
    async fetchReferralList(req, res, next) {
        try {
            const { id } = req.userDetails;
            const result = await referralServices.fetchReferralList(id, req.query, req.elasticLogObj);
            const { status, data } = result;
            if (status) res.status(RESPONSE_CODES.SUCCESS).send({ ...RESPONSES.SUCCESS, data });
            else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);
        } catch (error) {
            console.error(`ReferralController fetch ReferralList ->`, error);
            next(error);
        }
    }
}

// Export an instance of the ReferralController class
module.exports = new ReferralController();