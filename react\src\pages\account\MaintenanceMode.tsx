const MaintenanceMode: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen px-4 py-12 bg-gradient-to-br from-purple-100 via-white to-purple-200">

      {/* Logo/Title */}
      <h1 className="text-5xl sm:text-6xl font-anton uppercase text-dark_purple mb-2 tracking-wider flex gap-3">
        <span>Purple</span>
        <span>Cow</span>
      </h1>

      {/* Sub-heading */}
      <h2 className="text-xl sm:text-2xl font-semibold mb-5 text-gray-800">
        We're moo-ving things around!
      </h2>

      {/* Description */}
      <p className="text-gray-700 mb-3 px-4 md:px-0 max-w-lg text-center leading-relaxed">
        Our herd is hard at work behind the scenes. We're upgrading systems to serve you better 🐄.
      </p>

      <p className="text-gray-600 mb-6 px-4 md:px-0 max-w-lg text-center">
        We’ll be back online shortly — smoother, faster, and even more fabulous!
      </p>

      {/* Footer pulse message */}
      <div className="mt-4">
        <span className="text-sm">
          Thank you for your patience 
        </span>
      <span className="animate-pulse">💜</span>
      </div>
    </div>
  );
};

export default MaintenanceMode;
