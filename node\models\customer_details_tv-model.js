const Sequelize = require('sequelize');

module.exports = (sequelize) => {
  return sequelize.define("customer_details_tv", {
    id: {
      type: Sequelize.INTEGER(11),
      primaryKey: true,
      autoIncrement: true,
      comment: "Primary key, auto-incrementing identifier."
    },
    sf_record_id: {
      type: Sequelize.STRING(18),
      unique: "sf_record_id",
      comment: "Salesforce record ID"
    },
    sf_name: {
      type: Sequelize.STRING(50),
      allowNull: true,
      comment: "This maps to 'Name' field in Salesforce."
    },
    plan_name: {
      type: Sequelize.STRING(100),
      comment: "Associated with api_name field of json object in s3."
    },
    total_service_cost: {
      type: Sequelize.FLOAT(16, 2),
      comment: "Associated with price field of json object in s3 plus we also calculate as per the bussiness logic."
    },
    extra_packages: {
      type: Sequelize.TEXT,
      defaultValue: "[]",
      comment: "Associated with Current_Extra_Packages__c field of salesforce TV__c object."
    },
    single_channels: {
      type: Sequelize.TEXT,
      defaultValue: "[]",
      comment: "Associated with Current_Single_Channels__c field of salesforce TV__c object."
    },
    iptv_products: {
      type: Sequelize.TEXT,
      defaultValue: "[]",
      comment: "Associated with current_iptv_products__c field of salesforce TV__c object."
    },
    sf_updatedAt: {
      type: Sequelize.DATE,
      comment: "Associated with LastModifiedDate field of TV__c in sf."
    },
    sf_response_status: {
      type: Sequelize.ENUM('pending', 'success', 'failure'),
      defaultValue: 'pending',
      comment: "Initially the value was pending"
    },
    state_text: {
      type: Sequelize.TEXT(),
      comment: "Associated with State_Text__c field of salesforce TV__c object."
    },
    retries: {
      type: Sequelize.TINYINT,
      defaultValue: 0,
      comment: "How many times getting error during update in salesforce."
    },
    sf_response_type: {
      type: Sequelize.ENUM('create', 'change', 'cancel'),
      defaultValue: null,
      comment: "Initially the value was null, and this column is to track the error type during creation/ updation/ cancel of TV."
    },
    sf_error_log: {
      type: Sequelize.TEXT,
      comment: "Reason for getting error from salesforce."
    },
    login_details_last_sent: {
      type: Sequelize.DATE,
      allowNull: true,
      comment: "This maps to the field 'Login_Details_Last_Sent__c' field in 'TV__c'. When it's filled out, we can assume the TV creation order has been processed & the customer has access to TV services."
    },
    account_status: {
      type: Sequelize.ENUM('ACTIVE', 'REMOVED'),
      comment: "Associated with current_account_status__c field of TV__c in sf."
    },
    requested_cancellation_date: {
      type: Sequelize.DATEONLY,
      allowNull: true,
      comment: "Associated with Requested_Cancellation_Date__c field of TV__c in sf."
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was created in the database."
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      comment: "The timestamp when the record was last updated in the database."
    }
  },
    {
      collate: 'utf8mb4_unicode_ci',
      timestamps: true
    });
}