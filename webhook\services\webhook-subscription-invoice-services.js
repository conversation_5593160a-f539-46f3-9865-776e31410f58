const { getCurrentAtlanticTime, billindTypeCbSubs } = require("../helper/custom-helper");
const pool = require("../db");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();
const moment = require('moment');

const ChargebeeClient = require('../clients/chargebee-client');

class SubscriptionInvoiceWebhookServices {
    async getSubscriptionInvoiceDetails(webhookData, _id, _index) {
        try {
            await this.checkAndManageDetails(webhookData, _id, _index);
        } catch (error) {
            console.error("Sync service get subscription invoice details -> ", error);
        }
    }

    async checkAndManageDetails(invDetails, _id, _index) {
        try {
            if (!Array.isArray(invDetails)) invDetails = [invDetails];
            for (const invDetail of invDetails) {
                const { Id: sf_record_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id, Expected_Payment_Date_Time__c: expected_payment_date, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, LastModifiedDate: sf_updatedAt } = invDetail;

                const subsQuery = `SELECT id, customer_details_id FROM customer_details_subscriptions WHERE cb_subscription_id = '${cb_subscription_id}' LIMIT 1`;

                const { dataExist: subExist, dataId: subDetail } = await this.checkExist(subsQuery);
                if (!subExist) return;

                const customer_details_id = subDetail.customer_details_id;
                const customer_subscription_id = subDetail.id;

                await this.callChargeeBeeInvoice(customer_details_id, customer_subscription_id, cb_invoice_id, _id, _index);

                const invoiceQuery = `
                            SELECT id, status FROM subscriptions_invoices
                            WHERE cb_invoice_id = '${cb_invoice_id}'
                            AND customer_subscription_id = '${customer_subscription_id}'
                            AND customer_details_id = '${customer_details_id}'
                            LIMIT 1
                        `;

                const { dataExist: invoiceExist, dataId: invoiceDetail } = await this.checkExist(invoiceQuery);

                let query;
                let queryParams = [];

                const modifiedDate = getCurrentAtlanticTime(sf_updatedAt);
                const expectedPaymentDate = getCurrentAtlanticTime(expected_payment_date);
                const updatedTime = getCurrentAtlanticTime();

                let type;
                if (invoiceExist) {
                    type = "UPDATE";
                    query = `UPDATE subscriptions_invoices SET sf_record_id = ?, sf_updatedAt = ?, updatedAt = ?`;

                    queryParams.push(sf_record_id, modifiedDate, updatedTime);
                    query += `, expected_payment_date = ?`;
                    if (expected_payment_date) queryParams.push(expectedPaymentDate);
                    else queryParams.push(null);

                    query += ` WHERE id = ?`;
                    queryParams.push(invoiceDetail.id);

                    await this.executeQuery(query, queryParams);
                }
            }
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async checkExist(query) {
        try {
            const res = await this.executeQuery(query);
            return {
                dataExist: res.length > 0,
                dataId: res.length ? res[0] : null
            };
        } catch (error) {
            console.error("Check Exist -> ", error);
            return {
                dataExist: 0,
                dataId: null
            };
        }
    }

    async callChargeeBeeInvoice(customer_details_id, customer_subscription_id, cb_invoice_id, _id, _index) {
        try {
            const chargebeeClient = new ChargebeeClient();
            const invoiceData = await chargebeeClient.retrieveInvoice(cb_invoice_id);
            const invoice = invoiceData?.invoice;
            const credit_issue = invoice?.issued_credit_notes.reduce((sum, note) => {
                if (note?.cn_total) return sum + note.cn_total;
                return sum;
            }, 0);

            const invData = {
                customer_details_id: customer_details_id,
                customer_subscription_id: customer_subscription_id,
                cb_subscription_id: invoice?.subscription_id,
                cb_invoice_id: invoice?.id,
                amount: invoice?.total ? invoice.total / 100 : 0,
                credit_issue: credit_issue ? credit_issue / 100 : 0,
                amount_adjusted: invoice?.amount_adjusted ? invoice.amount_adjusted / 100 : 0,
                expected_payment_date: invoice?.expected_payment_date ? moment.unix(invoice.expected_payment_date).format('YYYY-MM-DD HH:mm:ss') : null,
                status: invoice?.status,
                total_outstanding: invoice?.amount_due ? invoice.amount_due / 100 : 0,
                createdAt: invoice?.date ? moment.unix(invoice.date).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss'),
                // updatedAt: invoice?.updated_at ? moment.unix(invoice.updated_at).format('YYYY-MM-DD HH:mm:ss') : moment().format('YYYY-MM-DD HH:mm:ss'),
                updatedAt: getCurrentAtlanticTime()
            };

            const invoiceQuery = `
                SELECT id, status FROM subscriptions_invoices
                WHERE cb_invoice_id = ? 
                AND customer_subscription_id = ? 
                AND customer_details_id = ?
                LIMIT 1
              `;
            const invoiceDetails = await this.executeQuery(invoiceQuery, [
                invData.cb_invoice_id,
                invData.customer_subscription_id,
                invData.customer_details_id,
            ]);

            let type;

            if (!invoiceDetails?.length) {
                type = "CREATE";
                const insertInvoiceQuery = `
                  INSERT IGNORE INTO subscriptions_invoices 
                  (customer_details_id, customer_subscription_id, cb_subscription_id, cb_invoice_id, amount, credit_issue, amount_adjusted, expected_payment_date, status, total_outstanding, createdAt, updatedAt)
                  VALUES 
                  (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;
                await this.executeQuery(insertInvoiceQuery, [
                    invData.customer_details_id,
                    invData.customer_subscription_id,
                    invData.cb_subscription_id,
                    invData.cb_invoice_id,
                    invData.amount,
                    invData.credit_issue,
                    invData.amount_adjusted,
                    invData.expected_payment_date,
                    invData.status,
                    invData.total_outstanding,
                    invData.createdAt,
                    invData.updatedAt,
                ]);
            } else {
                type = "UPDATE";
                const invoiceId = invoiceDetails?.[0]?.id;
                let updateSts = invData.status.toLowerCase();
                const updateInvoiceQuery = `
                  UPDATE subscriptions_invoices 
                  SET amount = ?, status = ?, total_outstanding = ?, createdAt = ?, updatedAt = ?, credit_issue = ?, amount_adjusted = ?
                  WHERE id = ?`;

                await this.executeQuery(updateInvoiceQuery, [
                    invData.amount,
                    updateSts,
                    invData.total_outstanding,
                    invData.createdAt,
                    invData.updatedAt,
                    invData.credit_issue,
                    invData.amount_adjusted,
                    invoiceId
                ]);
            }
            if (_id && _index && type) elasticSearch.updateDocument(_index, _id, { type });
        } catch (error) {
            console.error("SalesforceService checkAndManagDetails -> ", error);
        }
    }
}

module.exports = SubscriptionInvoiceWebhookServices;
