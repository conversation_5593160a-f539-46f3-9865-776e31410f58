const request = require('supertest');
const express = require('express');
const authRoute = require('../../routes/auth-route');

// Mock the auth controller
jest.mock('../../controllers/auth-controller', () => ({
  userLogin: jest.fn((req, res) => res.status(200).json({ message: 'Login successful' })),
  userResetPassword: jest.fn((req, res) => res.status(200).json({ message: 'Password reset successful' })),
  userForgotPassword: jest.fn((req, res) => res.status(200).json({ message: 'Forgot password email sent' })),
  userCreateCognitoAccount: jest.fn((req, res) => res.status(201).json({ message: 'Cognito account created' })),
  deleteContact: jest.fn((req, res) => res.status(200).json({ message: 'Contact deleted' })),
  mapSubscription: jest.fn((req, res) => res.status(200).json({ message: 'Subscription mapped' })),
  userSignupQueue: jest.fn((req, res) => res.status(201).json({ message: 'User registered successfully' }))
}));

// Mock the validation middleware
jest.mock('../../middleware/validationMid', () => ({
  validator: jest.fn(() => (req, res, next) => next())
}));

// Mock validators
jest.mock('../../helpers/validators', () => ({
  signupValidation: {},
  cognitoCreateUserValidation: {},
  loginValidation: {},
  resetPasswordValidation: {},
  forgotPasswordValidation: {}
}));

describe('Auth Routes', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    authRoute(app);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const response = await request(app)
        .post('/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.message).toBe('User registered successfully');
    });

    it('should handle registration with invalid data', async () => {
      const invalidData = {
        email: 'invalid-email'
      };

      await request(app)
        .post('/auth/register')
        .send(invalidData)
        .expect(201); // Mocked to return success
    });
  });

  describe('POST /auth/cognito/create-user', () => {
    it('should create cognito user successfully', async () => {
      const cognitoData = {
        email: '<EMAIL>',
        temporaryPassword: 'TempPass123!'
      };

      const response = await request(app)
        .post('/auth/cognito/create-user')
        .send(cognitoData)
        .expect(201);

      expect(response.body.message).toBe('Cognito account created');
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.message).toBe('Login successful');
    });

    it('should handle login with missing credentials', async () => {
      const incompleteData = {
        email: '<EMAIL>'
      };

      await request(app)
        .post('/auth/login')
        .send(incompleteData)
        .expect(200); // Mocked to return success
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should reset password successfully', async () => {
      const resetData = {
        email: '<EMAIL>',
        newPassword: 'newPassword123',
        confirmationCode: '123456'
      };

      const response = await request(app)
        .post('/auth/reset-password')
        .send(resetData)
        .expect(200);

      expect(response.body.message).toBe('Password reset successful');
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should send forgot password email successfully', async () => {
      const forgotData = {
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/auth/forgot-password')
        .send(forgotData)
        .expect(200);

      expect(response.body.message).toBe('Forgot password email sent');
    });
  });

  describe('POST /auth/delete-contact', () => {
    it('should delete contact successfully', async () => {
      const deleteData = {
        email: '<EMAIL>'
      };

      const response = await request(app)
        .post('/auth/delete-contact')
        .send(deleteData)
        .expect(200);

      expect(response.body.message).toBe('Contact deleted');
    });
  });

  describe('GET /auth/map-subscription', () => {
    it('should map subscription successfully', async () => {
      const response = await request(app)
        .get('/auth/map-subscription')
        .expect(200);

      expect(response.body.message).toBe('Subscription mapped');
    });
  });
});
