
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services/auth-services.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">services</a> auth-services.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/760</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/551</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/38</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/643</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a>
<a name='L672'></a><a href='#L672'>672</a>
<a name='L673'></a><a href='#L673'>673</a>
<a name='L674'></a><a href='#L674'>674</a>
<a name='L675'></a><a href='#L675'>675</a>
<a name='L676'></a><a href='#L676'>676</a>
<a name='L677'></a><a href='#L677'>677</a>
<a name='L678'></a><a href='#L678'>678</a>
<a name='L679'></a><a href='#L679'>679</a>
<a name='L680'></a><a href='#L680'>680</a>
<a name='L681'></a><a href='#L681'>681</a>
<a name='L682'></a><a href='#L682'>682</a>
<a name='L683'></a><a href='#L683'>683</a>
<a name='L684'></a><a href='#L684'>684</a>
<a name='L685'></a><a href='#L685'>685</a>
<a name='L686'></a><a href='#L686'>686</a>
<a name='L687'></a><a href='#L687'>687</a>
<a name='L688'></a><a href='#L688'>688</a>
<a name='L689'></a><a href='#L689'>689</a>
<a name='L690'></a><a href='#L690'>690</a>
<a name='L691'></a><a href='#L691'>691</a>
<a name='L692'></a><a href='#L692'>692</a>
<a name='L693'></a><a href='#L693'>693</a>
<a name='L694'></a><a href='#L694'>694</a>
<a name='L695'></a><a href='#L695'>695</a>
<a name='L696'></a><a href='#L696'>696</a>
<a name='L697'></a><a href='#L697'>697</a>
<a name='L698'></a><a href='#L698'>698</a>
<a name='L699'></a><a href='#L699'>699</a>
<a name='L700'></a><a href='#L700'>700</a>
<a name='L701'></a><a href='#L701'>701</a>
<a name='L702'></a><a href='#L702'>702</a>
<a name='L703'></a><a href='#L703'>703</a>
<a name='L704'></a><a href='#L704'>704</a>
<a name='L705'></a><a href='#L705'>705</a>
<a name='L706'></a><a href='#L706'>706</a>
<a name='L707'></a><a href='#L707'>707</a>
<a name='L708'></a><a href='#L708'>708</a>
<a name='L709'></a><a href='#L709'>709</a>
<a name='L710'></a><a href='#L710'>710</a>
<a name='L711'></a><a href='#L711'>711</a>
<a name='L712'></a><a href='#L712'>712</a>
<a name='L713'></a><a href='#L713'>713</a>
<a name='L714'></a><a href='#L714'>714</a>
<a name='L715'></a><a href='#L715'>715</a>
<a name='L716'></a><a href='#L716'>716</a>
<a name='L717'></a><a href='#L717'>717</a>
<a name='L718'></a><a href='#L718'>718</a>
<a name='L719'></a><a href='#L719'>719</a>
<a name='L720'></a><a href='#L720'>720</a>
<a name='L721'></a><a href='#L721'>721</a>
<a name='L722'></a><a href='#L722'>722</a>
<a name='L723'></a><a href='#L723'>723</a>
<a name='L724'></a><a href='#L724'>724</a>
<a name='L725'></a><a href='#L725'>725</a>
<a name='L726'></a><a href='#L726'>726</a>
<a name='L727'></a><a href='#L727'>727</a>
<a name='L728'></a><a href='#L728'>728</a>
<a name='L729'></a><a href='#L729'>729</a>
<a name='L730'></a><a href='#L730'>730</a>
<a name='L731'></a><a href='#L731'>731</a>
<a name='L732'></a><a href='#L732'>732</a>
<a name='L733'></a><a href='#L733'>733</a>
<a name='L734'></a><a href='#L734'>734</a>
<a name='L735'></a><a href='#L735'>735</a>
<a name='L736'></a><a href='#L736'>736</a>
<a name='L737'></a><a href='#L737'>737</a>
<a name='L738'></a><a href='#L738'>738</a>
<a name='L739'></a><a href='#L739'>739</a>
<a name='L740'></a><a href='#L740'>740</a>
<a name='L741'></a><a href='#L741'>741</a>
<a name='L742'></a><a href='#L742'>742</a>
<a name='L743'></a><a href='#L743'>743</a>
<a name='L744'></a><a href='#L744'>744</a>
<a name='L745'></a><a href='#L745'>745</a>
<a name='L746'></a><a href='#L746'>746</a>
<a name='L747'></a><a href='#L747'>747</a>
<a name='L748'></a><a href='#L748'>748</a>
<a name='L749'></a><a href='#L749'>749</a>
<a name='L750'></a><a href='#L750'>750</a>
<a name='L751'></a><a href='#L751'>751</a>
<a name='L752'></a><a href='#L752'>752</a>
<a name='L753'></a><a href='#L753'>753</a>
<a name='L754'></a><a href='#L754'>754</a>
<a name='L755'></a><a href='#L755'>755</a>
<a name='L756'></a><a href='#L756'>756</a>
<a name='L757'></a><a href='#L757'>757</a>
<a name='L758'></a><a href='#L758'>758</a>
<a name='L759'></a><a href='#L759'>759</a>
<a name='L760'></a><a href='#L760'>760</a>
<a name='L761'></a><a href='#L761'>761</a>
<a name='L762'></a><a href='#L762'>762</a>
<a name='L763'></a><a href='#L763'>763</a>
<a name='L764'></a><a href='#L764'>764</a>
<a name='L765'></a><a href='#L765'>765</a>
<a name='L766'></a><a href='#L766'>766</a>
<a name='L767'></a><a href='#L767'>767</a>
<a name='L768'></a><a href='#L768'>768</a>
<a name='L769'></a><a href='#L769'>769</a>
<a name='L770'></a><a href='#L770'>770</a>
<a name='L771'></a><a href='#L771'>771</a>
<a name='L772'></a><a href='#L772'>772</a>
<a name='L773'></a><a href='#L773'>773</a>
<a name='L774'></a><a href='#L774'>774</a>
<a name='L775'></a><a href='#L775'>775</a>
<a name='L776'></a><a href='#L776'>776</a>
<a name='L777'></a><a href='#L777'>777</a>
<a name='L778'></a><a href='#L778'>778</a>
<a name='L779'></a><a href='#L779'>779</a>
<a name='L780'></a><a href='#L780'>780</a>
<a name='L781'></a><a href='#L781'>781</a>
<a name='L782'></a><a href='#L782'>782</a>
<a name='L783'></a><a href='#L783'>783</a>
<a name='L784'></a><a href='#L784'>784</a>
<a name='L785'></a><a href='#L785'>785</a>
<a name='L786'></a><a href='#L786'>786</a>
<a name='L787'></a><a href='#L787'>787</a>
<a name='L788'></a><a href='#L788'>788</a>
<a name='L789'></a><a href='#L789'>789</a>
<a name='L790'></a><a href='#L790'>790</a>
<a name='L791'></a><a href='#L791'>791</a>
<a name='L792'></a><a href='#L792'>792</a>
<a name='L793'></a><a href='#L793'>793</a>
<a name='L794'></a><a href='#L794'>794</a>
<a name='L795'></a><a href='#L795'>795</a>
<a name='L796'></a><a href='#L796'>796</a>
<a name='L797'></a><a href='#L797'>797</a>
<a name='L798'></a><a href='#L798'>798</a>
<a name='L799'></a><a href='#L799'>799</a>
<a name='L800'></a><a href='#L800'>800</a>
<a name='L801'></a><a href='#L801'>801</a>
<a name='L802'></a><a href='#L802'>802</a>
<a name='L803'></a><a href='#L803'>803</a>
<a name='L804'></a><a href='#L804'>804</a>
<a name='L805'></a><a href='#L805'>805</a>
<a name='L806'></a><a href='#L806'>806</a>
<a name='L807'></a><a href='#L807'>807</a>
<a name='L808'></a><a href='#L808'>808</a>
<a name='L809'></a><a href='#L809'>809</a>
<a name='L810'></a><a href='#L810'>810</a>
<a name='L811'></a><a href='#L811'>811</a>
<a name='L812'></a><a href='#L812'>812</a>
<a name='L813'></a><a href='#L813'>813</a>
<a name='L814'></a><a href='#L814'>814</a>
<a name='L815'></a><a href='#L815'>815</a>
<a name='L816'></a><a href='#L816'>816</a>
<a name='L817'></a><a href='#L817'>817</a>
<a name='L818'></a><a href='#L818'>818</a>
<a name='L819'></a><a href='#L819'>819</a>
<a name='L820'></a><a href='#L820'>820</a>
<a name='L821'></a><a href='#L821'>821</a>
<a name='L822'></a><a href='#L822'>822</a>
<a name='L823'></a><a href='#L823'>823</a>
<a name='L824'></a><a href='#L824'>824</a>
<a name='L825'></a><a href='#L825'>825</a>
<a name='L826'></a><a href='#L826'>826</a>
<a name='L827'></a><a href='#L827'>827</a>
<a name='L828'></a><a href='#L828'>828</a>
<a name='L829'></a><a href='#L829'>829</a>
<a name='L830'></a><a href='#L830'>830</a>
<a name='L831'></a><a href='#L831'>831</a>
<a name='L832'></a><a href='#L832'>832</a>
<a name='L833'></a><a href='#L833'>833</a>
<a name='L834'></a><a href='#L834'>834</a>
<a name='L835'></a><a href='#L835'>835</a>
<a name='L836'></a><a href='#L836'>836</a>
<a name='L837'></a><a href='#L837'>837</a>
<a name='L838'></a><a href='#L838'>838</a>
<a name='L839'></a><a href='#L839'>839</a>
<a name='L840'></a><a href='#L840'>840</a>
<a name='L841'></a><a href='#L841'>841</a>
<a name='L842'></a><a href='#L842'>842</a>
<a name='L843'></a><a href='#L843'>843</a>
<a name='L844'></a><a href='#L844'>844</a>
<a name='L845'></a><a href='#L845'>845</a>
<a name='L846'></a><a href='#L846'>846</a>
<a name='L847'></a><a href='#L847'>847</a>
<a name='L848'></a><a href='#L848'>848</a>
<a name='L849'></a><a href='#L849'>849</a>
<a name='L850'></a><a href='#L850'>850</a>
<a name='L851'></a><a href='#L851'>851</a>
<a name='L852'></a><a href='#L852'>852</a>
<a name='L853'></a><a href='#L853'>853</a>
<a name='L854'></a><a href='#L854'>854</a>
<a name='L855'></a><a href='#L855'>855</a>
<a name='L856'></a><a href='#L856'>856</a>
<a name='L857'></a><a href='#L857'>857</a>
<a name='L858'></a><a href='#L858'>858</a>
<a name='L859'></a><a href='#L859'>859</a>
<a name='L860'></a><a href='#L860'>860</a>
<a name='L861'></a><a href='#L861'>861</a>
<a name='L862'></a><a href='#L862'>862</a>
<a name='L863'></a><a href='#L863'>863</a>
<a name='L864'></a><a href='#L864'>864</a>
<a name='L865'></a><a href='#L865'>865</a>
<a name='L866'></a><a href='#L866'>866</a>
<a name='L867'></a><a href='#L867'>867</a>
<a name='L868'></a><a href='#L868'>868</a>
<a name='L869'></a><a href='#L869'>869</a>
<a name='L870'></a><a href='#L870'>870</a>
<a name='L871'></a><a href='#L871'>871</a>
<a name='L872'></a><a href='#L872'>872</a>
<a name='L873'></a><a href='#L873'>873</a>
<a name='L874'></a><a href='#L874'>874</a>
<a name='L875'></a><a href='#L875'>875</a>
<a name='L876'></a><a href='#L876'>876</a>
<a name='L877'></a><a href='#L877'>877</a>
<a name='L878'></a><a href='#L878'>878</a>
<a name='L879'></a><a href='#L879'>879</a>
<a name='L880'></a><a href='#L880'>880</a>
<a name='L881'></a><a href='#L881'>881</a>
<a name='L882'></a><a href='#L882'>882</a>
<a name='L883'></a><a href='#L883'>883</a>
<a name='L884'></a><a href='#L884'>884</a>
<a name='L885'></a><a href='#L885'>885</a>
<a name='L886'></a><a href='#L886'>886</a>
<a name='L887'></a><a href='#L887'>887</a>
<a name='L888'></a><a href='#L888'>888</a>
<a name='L889'></a><a href='#L889'>889</a>
<a name='L890'></a><a href='#L890'>890</a>
<a name='L891'></a><a href='#L891'>891</a>
<a name='L892'></a><a href='#L892'>892</a>
<a name='L893'></a><a href='#L893'>893</a>
<a name='L894'></a><a href='#L894'>894</a>
<a name='L895'></a><a href='#L895'>895</a>
<a name='L896'></a><a href='#L896'>896</a>
<a name='L897'></a><a href='#L897'>897</a>
<a name='L898'></a><a href='#L898'>898</a>
<a name='L899'></a><a href='#L899'>899</a>
<a name='L900'></a><a href='#L900'>900</a>
<a name='L901'></a><a href='#L901'>901</a>
<a name='L902'></a><a href='#L902'>902</a>
<a name='L903'></a><a href='#L903'>903</a>
<a name='L904'></a><a href='#L904'>904</a>
<a name='L905'></a><a href='#L905'>905</a>
<a name='L906'></a><a href='#L906'>906</a>
<a name='L907'></a><a href='#L907'>907</a>
<a name='L908'></a><a href='#L908'>908</a>
<a name='L909'></a><a href='#L909'>909</a>
<a name='L910'></a><a href='#L910'>910</a>
<a name='L911'></a><a href='#L911'>911</a>
<a name='L912'></a><a href='#L912'>912</a>
<a name='L913'></a><a href='#L913'>913</a>
<a name='L914'></a><a href='#L914'>914</a>
<a name='L915'></a><a href='#L915'>915</a>
<a name='L916'></a><a href='#L916'>916</a>
<a name='L917'></a><a href='#L917'>917</a>
<a name='L918'></a><a href='#L918'>918</a>
<a name='L919'></a><a href='#L919'>919</a>
<a name='L920'></a><a href='#L920'>920</a>
<a name='L921'></a><a href='#L921'>921</a>
<a name='L922'></a><a href='#L922'>922</a>
<a name='L923'></a><a href='#L923'>923</a>
<a name='L924'></a><a href='#L924'>924</a>
<a name='L925'></a><a href='#L925'>925</a>
<a name='L926'></a><a href='#L926'>926</a>
<a name='L927'></a><a href='#L927'>927</a>
<a name='L928'></a><a href='#L928'>928</a>
<a name='L929'></a><a href='#L929'>929</a>
<a name='L930'></a><a href='#L930'>930</a>
<a name='L931'></a><a href='#L931'>931</a>
<a name='L932'></a><a href='#L932'>932</a>
<a name='L933'></a><a href='#L933'>933</a>
<a name='L934'></a><a href='#L934'>934</a>
<a name='L935'></a><a href='#L935'>935</a>
<a name='L936'></a><a href='#L936'>936</a>
<a name='L937'></a><a href='#L937'>937</a>
<a name='L938'></a><a href='#L938'>938</a>
<a name='L939'></a><a href='#L939'>939</a>
<a name='L940'></a><a href='#L940'>940</a>
<a name='L941'></a><a href='#L941'>941</a>
<a name='L942'></a><a href='#L942'>942</a>
<a name='L943'></a><a href='#L943'>943</a>
<a name='L944'></a><a href='#L944'>944</a>
<a name='L945'></a><a href='#L945'>945</a>
<a name='L946'></a><a href='#L946'>946</a>
<a name='L947'></a><a href='#L947'>947</a>
<a name='L948'></a><a href='#L948'>948</a>
<a name='L949'></a><a href='#L949'>949</a>
<a name='L950'></a><a href='#L950'>950</a>
<a name='L951'></a><a href='#L951'>951</a>
<a name='L952'></a><a href='#L952'>952</a>
<a name='L953'></a><a href='#L953'>953</a>
<a name='L954'></a><a href='#L954'>954</a>
<a name='L955'></a><a href='#L955'>955</a>
<a name='L956'></a><a href='#L956'>956</a>
<a name='L957'></a><a href='#L957'>957</a>
<a name='L958'></a><a href='#L958'>958</a>
<a name='L959'></a><a href='#L959'>959</a>
<a name='L960'></a><a href='#L960'>960</a>
<a name='L961'></a><a href='#L961'>961</a>
<a name='L962'></a><a href='#L962'>962</a>
<a name='L963'></a><a href='#L963'>963</a>
<a name='L964'></a><a href='#L964'>964</a>
<a name='L965'></a><a href='#L965'>965</a>
<a name='L966'></a><a href='#L966'>966</a>
<a name='L967'></a><a href='#L967'>967</a>
<a name='L968'></a><a href='#L968'>968</a>
<a name='L969'></a><a href='#L969'>969</a>
<a name='L970'></a><a href='#L970'>970</a>
<a name='L971'></a><a href='#L971'>971</a>
<a name='L972'></a><a href='#L972'>972</a>
<a name='L973'></a><a href='#L973'>973</a>
<a name='L974'></a><a href='#L974'>974</a>
<a name='L975'></a><a href='#L975'>975</a>
<a name='L976'></a><a href='#L976'>976</a>
<a name='L977'></a><a href='#L977'>977</a>
<a name='L978'></a><a href='#L978'>978</a>
<a name='L979'></a><a href='#L979'>979</a>
<a name='L980'></a><a href='#L980'>980</a>
<a name='L981'></a><a href='#L981'>981</a>
<a name='L982'></a><a href='#L982'>982</a>
<a name='L983'></a><a href='#L983'>983</a>
<a name='L984'></a><a href='#L984'>984</a>
<a name='L985'></a><a href='#L985'>985</a>
<a name='L986'></a><a href='#L986'>986</a>
<a name='L987'></a><a href='#L987'>987</a>
<a name='L988'></a><a href='#L988'>988</a>
<a name='L989'></a><a href='#L989'>989</a>
<a name='L990'></a><a href='#L990'>990</a>
<a name='L991'></a><a href='#L991'>991</a>
<a name='L992'></a><a href='#L992'>992</a>
<a name='L993'></a><a href='#L993'>993</a>
<a name='L994'></a><a href='#L994'>994</a>
<a name='L995'></a><a href='#L995'>995</a>
<a name='L996'></a><a href='#L996'>996</a>
<a name='L997'></a><a href='#L997'>997</a>
<a name='L998'></a><a href='#L998'>998</a>
<a name='L999'></a><a href='#L999'>999</a>
<a name='L1000'></a><a href='#L1000'>1000</a>
<a name='L1001'></a><a href='#L1001'>1001</a>
<a name='L1002'></a><a href='#L1002'>1002</a>
<a name='L1003'></a><a href='#L1003'>1003</a>
<a name='L1004'></a><a href='#L1004'>1004</a>
<a name='L1005'></a><a href='#L1005'>1005</a>
<a name='L1006'></a><a href='#L1006'>1006</a>
<a name='L1007'></a><a href='#L1007'>1007</a>
<a name='L1008'></a><a href='#L1008'>1008</a>
<a name='L1009'></a><a href='#L1009'>1009</a>
<a name='L1010'></a><a href='#L1010'>1010</a>
<a name='L1011'></a><a href='#L1011'>1011</a>
<a name='L1012'></a><a href='#L1012'>1012</a>
<a name='L1013'></a><a href='#L1013'>1013</a>
<a name='L1014'></a><a href='#L1014'>1014</a>
<a name='L1015'></a><a href='#L1015'>1015</a>
<a name='L1016'></a><a href='#L1016'>1016</a>
<a name='L1017'></a><a href='#L1017'>1017</a>
<a name='L1018'></a><a href='#L1018'>1018</a>
<a name='L1019'></a><a href='#L1019'>1019</a>
<a name='L1020'></a><a href='#L1020'>1020</a>
<a name='L1021'></a><a href='#L1021'>1021</a>
<a name='L1022'></a><a href='#L1022'>1022</a>
<a name='L1023'></a><a href='#L1023'>1023</a>
<a name='L1024'></a><a href='#L1024'>1024</a>
<a name='L1025'></a><a href='#L1025'>1025</a>
<a name='L1026'></a><a href='#L1026'>1026</a>
<a name='L1027'></a><a href='#L1027'>1027</a>
<a name='L1028'></a><a href='#L1028'>1028</a>
<a name='L1029'></a><a href='#L1029'>1029</a>
<a name='L1030'></a><a href='#L1030'>1030</a>
<a name='L1031'></a><a href='#L1031'>1031</a>
<a name='L1032'></a><a href='#L1032'>1032</a>
<a name='L1033'></a><a href='#L1033'>1033</a>
<a name='L1034'></a><a href='#L1034'>1034</a>
<a name='L1035'></a><a href='#L1035'>1035</a>
<a name='L1036'></a><a href='#L1036'>1036</a>
<a name='L1037'></a><a href='#L1037'>1037</a>
<a name='L1038'></a><a href='#L1038'>1038</a>
<a name='L1039'></a><a href='#L1039'>1039</a>
<a name='L1040'></a><a href='#L1040'>1040</a>
<a name='L1041'></a><a href='#L1041'>1041</a>
<a name='L1042'></a><a href='#L1042'>1042</a>
<a name='L1043'></a><a href='#L1043'>1043</a>
<a name='L1044'></a><a href='#L1044'>1044</a>
<a name='L1045'></a><a href='#L1045'>1045</a>
<a name='L1046'></a><a href='#L1046'>1046</a>
<a name='L1047'></a><a href='#L1047'>1047</a>
<a name='L1048'></a><a href='#L1048'>1048</a>
<a name='L1049'></a><a href='#L1049'>1049</a>
<a name='L1050'></a><a href='#L1050'>1050</a>
<a name='L1051'></a><a href='#L1051'>1051</a>
<a name='L1052'></a><a href='#L1052'>1052</a>
<a name='L1053'></a><a href='#L1053'>1053</a>
<a name='L1054'></a><a href='#L1054'>1054</a>
<a name='L1055'></a><a href='#L1055'>1055</a>
<a name='L1056'></a><a href='#L1056'>1056</a>
<a name='L1057'></a><a href='#L1057'>1057</a>
<a name='L1058'></a><a href='#L1058'>1058</a>
<a name='L1059'></a><a href='#L1059'>1059</a>
<a name='L1060'></a><a href='#L1060'>1060</a>
<a name='L1061'></a><a href='#L1061'>1061</a>
<a name='L1062'></a><a href='#L1062'>1062</a>
<a name='L1063'></a><a href='#L1063'>1063</a>
<a name='L1064'></a><a href='#L1064'>1064</a>
<a name='L1065'></a><a href='#L1065'>1065</a>
<a name='L1066'></a><a href='#L1066'>1066</a>
<a name='L1067'></a><a href='#L1067'>1067</a>
<a name='L1068'></a><a href='#L1068'>1068</a>
<a name='L1069'></a><a href='#L1069'>1069</a>
<a name='L1070'></a><a href='#L1070'>1070</a>
<a name='L1071'></a><a href='#L1071'>1071</a>
<a name='L1072'></a><a href='#L1072'>1072</a>
<a name='L1073'></a><a href='#L1073'>1073</a>
<a name='L1074'></a><a href='#L1074'>1074</a>
<a name='L1075'></a><a href='#L1075'>1075</a>
<a name='L1076'></a><a href='#L1076'>1076</a>
<a name='L1077'></a><a href='#L1077'>1077</a>
<a name='L1078'></a><a href='#L1078'>1078</a>
<a name='L1079'></a><a href='#L1079'>1079</a>
<a name='L1080'></a><a href='#L1080'>1080</a>
<a name='L1081'></a><a href='#L1081'>1081</a>
<a name='L1082'></a><a href='#L1082'>1082</a>
<a name='L1083'></a><a href='#L1083'>1083</a>
<a name='L1084'></a><a href='#L1084'>1084</a>
<a name='L1085'></a><a href='#L1085'>1085</a>
<a name='L1086'></a><a href='#L1086'>1086</a>
<a name='L1087'></a><a href='#L1087'>1087</a>
<a name='L1088'></a><a href='#L1088'>1088</a>
<a name='L1089'></a><a href='#L1089'>1089</a>
<a name='L1090'></a><a href='#L1090'>1090</a>
<a name='L1091'></a><a href='#L1091'>1091</a>
<a name='L1092'></a><a href='#L1092'>1092</a>
<a name='L1093'></a><a href='#L1093'>1093</a>
<a name='L1094'></a><a href='#L1094'>1094</a>
<a name='L1095'></a><a href='#L1095'>1095</a>
<a name='L1096'></a><a href='#L1096'>1096</a>
<a name='L1097'></a><a href='#L1097'>1097</a>
<a name='L1098'></a><a href='#L1098'>1098</a>
<a name='L1099'></a><a href='#L1099'>1099</a>
<a name='L1100'></a><a href='#L1100'>1100</a>
<a name='L1101'></a><a href='#L1101'>1101</a>
<a name='L1102'></a><a href='#L1102'>1102</a>
<a name='L1103'></a><a href='#L1103'>1103</a>
<a name='L1104'></a><a href='#L1104'>1104</a>
<a name='L1105'></a><a href='#L1105'>1105</a>
<a name='L1106'></a><a href='#L1106'>1106</a>
<a name='L1107'></a><a href='#L1107'>1107</a>
<a name='L1108'></a><a href='#L1108'>1108</a>
<a name='L1109'></a><a href='#L1109'>1109</a>
<a name='L1110'></a><a href='#L1110'>1110</a>
<a name='L1111'></a><a href='#L1111'>1111</a>
<a name='L1112'></a><a href='#L1112'>1112</a>
<a name='L1113'></a><a href='#L1113'>1113</a>
<a name='L1114'></a><a href='#L1114'>1114</a>
<a name='L1115'></a><a href='#L1115'>1115</a>
<a name='L1116'></a><a href='#L1116'>1116</a>
<a name='L1117'></a><a href='#L1117'>1117</a>
<a name='L1118'></a><a href='#L1118'>1118</a>
<a name='L1119'></a><a href='#L1119'>1119</a>
<a name='L1120'></a><a href='#L1120'>1120</a>
<a name='L1121'></a><a href='#L1121'>1121</a>
<a name='L1122'></a><a href='#L1122'>1122</a>
<a name='L1123'></a><a href='#L1123'>1123</a>
<a name='L1124'></a><a href='#L1124'>1124</a>
<a name='L1125'></a><a href='#L1125'>1125</a>
<a name='L1126'></a><a href='#L1126'>1126</a>
<a name='L1127'></a><a href='#L1127'>1127</a>
<a name='L1128'></a><a href='#L1128'>1128</a>
<a name='L1129'></a><a href='#L1129'>1129</a>
<a name='L1130'></a><a href='#L1130'>1130</a>
<a name='L1131'></a><a href='#L1131'>1131</a>
<a name='L1132'></a><a href='#L1132'>1132</a>
<a name='L1133'></a><a href='#L1133'>1133</a>
<a name='L1134'></a><a href='#L1134'>1134</a>
<a name='L1135'></a><a href='#L1135'>1135</a>
<a name='L1136'></a><a href='#L1136'>1136</a>
<a name='L1137'></a><a href='#L1137'>1137</a>
<a name='L1138'></a><a href='#L1138'>1138</a>
<a name='L1139'></a><a href='#L1139'>1139</a>
<a name='L1140'></a><a href='#L1140'>1140</a>
<a name='L1141'></a><a href='#L1141'>1141</a>
<a name='L1142'></a><a href='#L1142'>1142</a>
<a name='L1143'></a><a href='#L1143'>1143</a>
<a name='L1144'></a><a href='#L1144'>1144</a>
<a name='L1145'></a><a href='#L1145'>1145</a>
<a name='L1146'></a><a href='#L1146'>1146</a>
<a name='L1147'></a><a href='#L1147'>1147</a>
<a name='L1148'></a><a href='#L1148'>1148</a>
<a name='L1149'></a><a href='#L1149'>1149</a>
<a name='L1150'></a><a href='#L1150'>1150</a>
<a name='L1151'></a><a href='#L1151'>1151</a>
<a name='L1152'></a><a href='#L1152'>1152</a>
<a name='L1153'></a><a href='#L1153'>1153</a>
<a name='L1154'></a><a href='#L1154'>1154</a>
<a name='L1155'></a><a href='#L1155'>1155</a>
<a name='L1156'></a><a href='#L1156'>1156</a>
<a name='L1157'></a><a href='#L1157'>1157</a>
<a name='L1158'></a><a href='#L1158'>1158</a>
<a name='L1159'></a><a href='#L1159'>1159</a>
<a name='L1160'></a><a href='#L1160'>1160</a>
<a name='L1161'></a><a href='#L1161'>1161</a>
<a name='L1162'></a><a href='#L1162'>1162</a>
<a name='L1163'></a><a href='#L1163'>1163</a>
<a name='L1164'></a><a href='#L1164'>1164</a>
<a name='L1165'></a><a href='#L1165'>1165</a>
<a name='L1166'></a><a href='#L1166'>1166</a>
<a name='L1167'></a><a href='#L1167'>1167</a>
<a name='L1168'></a><a href='#L1168'>1168</a>
<a name='L1169'></a><a href='#L1169'>1169</a>
<a name='L1170'></a><a href='#L1170'>1170</a>
<a name='L1171'></a><a href='#L1171'>1171</a>
<a name='L1172'></a><a href='#L1172'>1172</a>
<a name='L1173'></a><a href='#L1173'>1173</a>
<a name='L1174'></a><a href='#L1174'>1174</a>
<a name='L1175'></a><a href='#L1175'>1175</a>
<a name='L1176'></a><a href='#L1176'>1176</a>
<a name='L1177'></a><a href='#L1177'>1177</a>
<a name='L1178'></a><a href='#L1178'>1178</a>
<a name='L1179'></a><a href='#L1179'>1179</a>
<a name='L1180'></a><a href='#L1180'>1180</a>
<a name='L1181'></a><a href='#L1181'>1181</a>
<a name='L1182'></a><a href='#L1182'>1182</a>
<a name='L1183'></a><a href='#L1183'>1183</a>
<a name='L1184'></a><a href='#L1184'>1184</a>
<a name='L1185'></a><a href='#L1185'>1185</a>
<a name='L1186'></a><a href='#L1186'>1186</a>
<a name='L1187'></a><a href='#L1187'>1187</a>
<a name='L1188'></a><a href='#L1188'>1188</a>
<a name='L1189'></a><a href='#L1189'>1189</a>
<a name='L1190'></a><a href='#L1190'>1190</a>
<a name='L1191'></a><a href='#L1191'>1191</a>
<a name='L1192'></a><a href='#L1192'>1192</a>
<a name='L1193'></a><a href='#L1193'>1193</a>
<a name='L1194'></a><a href='#L1194'>1194</a>
<a name='L1195'></a><a href='#L1195'>1195</a>
<a name='L1196'></a><a href='#L1196'>1196</a>
<a name='L1197'></a><a href='#L1197'>1197</a>
<a name='L1198'></a><a href='#L1198'>1198</a>
<a name='L1199'></a><a href='#L1199'>1199</a>
<a name='L1200'></a><a href='#L1200'>1200</a>
<a name='L1201'></a><a href='#L1201'>1201</a>
<a name='L1202'></a><a href='#L1202'>1202</a>
<a name='L1203'></a><a href='#L1203'>1203</a>
<a name='L1204'></a><a href='#L1204'>1204</a>
<a name='L1205'></a><a href='#L1205'>1205</a>
<a name='L1206'></a><a href='#L1206'>1206</a>
<a name='L1207'></a><a href='#L1207'>1207</a>
<a name='L1208'></a><a href='#L1208'>1208</a>
<a name='L1209'></a><a href='#L1209'>1209</a>
<a name='L1210'></a><a href='#L1210'>1210</a>
<a name='L1211'></a><a href='#L1211'>1211</a>
<a name='L1212'></a><a href='#L1212'>1212</a>
<a name='L1213'></a><a href='#L1213'>1213</a>
<a name='L1214'></a><a href='#L1214'>1214</a>
<a name='L1215'></a><a href='#L1215'>1215</a>
<a name='L1216'></a><a href='#L1216'>1216</a>
<a name='L1217'></a><a href='#L1217'>1217</a>
<a name='L1218'></a><a href='#L1218'>1218</a>
<a name='L1219'></a><a href='#L1219'>1219</a>
<a name='L1220'></a><a href='#L1220'>1220</a>
<a name='L1221'></a><a href='#L1221'>1221</a>
<a name='L1222'></a><a href='#L1222'>1222</a>
<a name='L1223'></a><a href='#L1223'>1223</a>
<a name='L1224'></a><a href='#L1224'>1224</a>
<a name='L1225'></a><a href='#L1225'>1225</a>
<a name='L1226'></a><a href='#L1226'>1226</a>
<a name='L1227'></a><a href='#L1227'>1227</a>
<a name='L1228'></a><a href='#L1228'>1228</a>
<a name='L1229'></a><a href='#L1229'>1229</a>
<a name='L1230'></a><a href='#L1230'>1230</a>
<a name='L1231'></a><a href='#L1231'>1231</a>
<a name='L1232'></a><a href='#L1232'>1232</a>
<a name='L1233'></a><a href='#L1233'>1233</a>
<a name='L1234'></a><a href='#L1234'>1234</a>
<a name='L1235'></a><a href='#L1235'>1235</a>
<a name='L1236'></a><a href='#L1236'>1236</a>
<a name='L1237'></a><a href='#L1237'>1237</a>
<a name='L1238'></a><a href='#L1238'>1238</a>
<a name='L1239'></a><a href='#L1239'>1239</a>
<a name='L1240'></a><a href='#L1240'>1240</a>
<a name='L1241'></a><a href='#L1241'>1241</a>
<a name='L1242'></a><a href='#L1242'>1242</a>
<a name='L1243'></a><a href='#L1243'>1243</a>
<a name='L1244'></a><a href='#L1244'>1244</a>
<a name='L1245'></a><a href='#L1245'>1245</a>
<a name='L1246'></a><a href='#L1246'>1246</a>
<a name='L1247'></a><a href='#L1247'>1247</a>
<a name='L1248'></a><a href='#L1248'>1248</a>
<a name='L1249'></a><a href='#L1249'>1249</a>
<a name='L1250'></a><a href='#L1250'>1250</a>
<a name='L1251'></a><a href='#L1251'>1251</a>
<a name='L1252'></a><a href='#L1252'>1252</a>
<a name='L1253'></a><a href='#L1253'>1253</a>
<a name='L1254'></a><a href='#L1254'>1254</a>
<a name='L1255'></a><a href='#L1255'>1255</a>
<a name='L1256'></a><a href='#L1256'>1256</a>
<a name='L1257'></a><a href='#L1257'>1257</a>
<a name='L1258'></a><a href='#L1258'>1258</a>
<a name='L1259'></a><a href='#L1259'>1259</a>
<a name='L1260'></a><a href='#L1260'>1260</a>
<a name='L1261'></a><a href='#L1261'>1261</a>
<a name='L1262'></a><a href='#L1262'>1262</a>
<a name='L1263'></a><a href='#L1263'>1263</a>
<a name='L1264'></a><a href='#L1264'>1264</a>
<a name='L1265'></a><a href='#L1265'>1265</a>
<a name='L1266'></a><a href='#L1266'>1266</a>
<a name='L1267'></a><a href='#L1267'>1267</a>
<a name='L1268'></a><a href='#L1268'>1268</a>
<a name='L1269'></a><a href='#L1269'>1269</a>
<a name='L1270'></a><a href='#L1270'>1270</a>
<a name='L1271'></a><a href='#L1271'>1271</a>
<a name='L1272'></a><a href='#L1272'>1272</a>
<a name='L1273'></a><a href='#L1273'>1273</a>
<a name='L1274'></a><a href='#L1274'>1274</a>
<a name='L1275'></a><a href='#L1275'>1275</a>
<a name='L1276'></a><a href='#L1276'>1276</a>
<a name='L1277'></a><a href='#L1277'>1277</a>
<a name='L1278'></a><a href='#L1278'>1278</a>
<a name='L1279'></a><a href='#L1279'>1279</a>
<a name='L1280'></a><a href='#L1280'>1280</a>
<a name='L1281'></a><a href='#L1281'>1281</a>
<a name='L1282'></a><a href='#L1282'>1282</a>
<a name='L1283'></a><a href='#L1283'>1283</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">const db = <span class="cstat-no" title="statement not covered" >require('../models');</span>
const CustomError = <span class="cstat-no" title="statement not covered" >require("../utils/errors/CustomError");</span>
const { RESPONSE_CODES, RESPONSES, RESPONSE_MESSAGES } = <span class="cstat-no" title="statement not covered" >require("../utils/ResponseCodes");</span>
const SalesforceService = <span class="cstat-no" title="statement not covered" >require("./salesforce-services");</span>
const ChargebeeService = <span class="cstat-no" title="statement not covered" >require("./chargebee-services.js");</span>
const PlanServices = <span class="cstat-no" title="statement not covered" >require("../services/plan-services");</span>
const CognitoConnection = <span class="cstat-no" title="statement not covered" >require("../clients/aws/cognito");</span>
const awsCognitoConnection = <span class="cstat-no" title="statement not covered" >new CognitoConnection();</span>
const passwordGenerator = <span class="cstat-no" title="statement not covered" >require('generate-password');</span>
const salesforceServiceClient = <span class="cstat-no" title="statement not covered" >new SalesforceService();</span>
const CONFIG = <span class="cstat-no" title="statement not covered" >require('../config');</span>
const SubscriptionServices = <span class="cstat-no" title="statement not covered" >require("./subscription-services");</span>
const ElasticSearch = <span class="cstat-no" title="statement not covered" >require("./../clients/elastic-search/elastic-search");</span>
const elasticSearch = <span class="cstat-no" title="statement not covered" >new ElasticSearch();</span>
const { billindTypeCbSubs, getCurrentAtlanticTime, generateAccessToken } = <span class="cstat-no" title="statement not covered" >require("../helpers/privacyAlgorithms");</span>
const moment = <span class="cstat-no" title="statement not covered" >require('moment');</span>
const sqs = <span class="cstat-no" title="statement not covered" >require('../config/aws-sqs');</span>
&nbsp;
// Service class for handling user authentication and other user related operations
class AuthServices {
&nbsp;
  // Enqueue user signup request to SQS and log to ElasticSearch
<span class="fstat-no" title="function not covered" >  as</span>ync userSignupQueue(requestBody, elasticLogObj) {
    // Log the request to ElasticSearch
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("registration_logs", { ...elasticLogObj, request: requestBody });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { email } = <span class="cstat-no" title="statement not covered" >requestBody;</span>
      // Check if the user already exists
      const { userExist } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.EMAIL_ALREADY_REGISTERED);</span></span>
&nbsp;
      // Retrieve Salesforce contact details (external call)
      const sfContactDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getContactDetails(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!sfContactDetails?.Id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      // Prepare SQS message
      const elasticLogRef = <span class="cstat-no" title="statement not covered" >{ _id, _index };</span>
      const params = <span class="cstat-no" title="statement not covered" >{</span>
        MessageBody: JSON.stringify({ requestBody, elasticLogObj: elasticLogRef }),
        QueueUrl: CONFIG.aws.registerQueueUrl,
      };
&nbsp;
      // Send message to SQS
      const result = <span class="cstat-no" title="statement not covered" >await sqs.sendMessage(params).promise();</span>
<span class="cstat-no" title="statement not covered" >      if (result?.MessageId) {</span>
        const response = <span class="cstat-no" title="statement not covered" >{ status: true, data: requestBody, message: "User registration is queued and will be processed shortly." };</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });</span></span>
<span class="cstat-no" title="statement not covered" >        return { status: true, message: "User registration is queued and will be processed shortly." };</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: 'Failed to queue the user registration.' } });</span></span>
<span class="cstat-no" title="statement not covered" >      return { status: false, message: "Failed to queue the user registration." };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService userSignupQueue -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Handle user registration logic (called directly or by worker)
<span class="fstat-no" title="function not covered" >  as</span>ync signup(payload, elasticLogObj, isQueueProcess = <span class="branch-0 cbranch-no" title="branch not covered" >false)</span> {
    const { email, isCognito } = <span class="cstat-no" title="statement not covered" >payload;</span>
    let _id, _index;
&nbsp;
    // Determine ElasticSearch logging context
<span class="cstat-no" title="statement not covered" >    if (isQueueProcess &amp;&amp; elasticLogObj &amp;&amp; elasticLogObj._id &amp;&amp; elasticLogObj._index) {</span>
<span class="cstat-no" title="statement not covered" >      _id = elasticLogObj._id;</span>
<span class="cstat-no" title="statement not covered" >      _index = elasticLogObj._index;</span>
    } else {
      const esLog = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("registration_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >      _id = esLog._id;</span>
<span class="cstat-no" title="statement not covered" >      _index = esLog._index;</span>
    }
&nbsp;
    let sfContactDetails, sfCustomerDetails, cardCustomerId;
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Check if the user already exists (DB call, safe outside transaction)
      const { userExist } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.EMAIL_ALREADY_REGISTERED);</span></span>
&nbsp;
      // Retrieve Salesforce contact details (external call)
<span class="cstat-no" title="statement not covered" >      sfContactDetails = await salesforceServiceClient.getContactDetails(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!sfContactDetails?.Id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
<span class="cstat-no" title="statement not covered" >      cardCustomerId = sfContactDetails.Account_Chargebee_Customer_ID__c;</span>
&nbsp;
      // Retrieve customer details from Salesforce (external call)
<span class="cstat-no" title="statement not covered" >      sfCustomerDetails = await salesforceServiceClient.getCustomerDetails(sfContactDetails.Id);</span>
&nbsp;
      // Start transaction for DB operations only
      const transaction = <span class="cstat-no" title="statement not covered" >await db.sequelize.transaction();</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
        // Insert customer details into the database
        const contactInsertionData = <span class="cstat-no" title="statement not covered" >await this.insertContactInDB(sfContactDetails, transaction);</span>
<span class="cstat-no" title="statement not covered" >        if (!contactInsertionData?.id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.SOMETHING_WENT_WRONG);</span></span>
&nbsp;
        // Retrieve card details from Chargebee (external call, but DB update inside transaction)
<span class="cstat-no" title="statement not covered" >        if (cardCustomerId) {</span>
          const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
<span class="cstat-no" title="statement not covered" >          await chargebeeServiceClient.getAllCards(contactInsertionData.id, cardCustomerId, transaction);</span>
        }
&nbsp;
        // Insert Salesforce customer details into the database
<span class="cstat-no" title="statement not covered" >        if (sfCustomerDetails.length) <span class="cstat-no" title="statement not covered" >await this.insertCustomerDetailsinDB(contactInsertionData.id, sfCustomerDetails, transaction);</span></span>
&nbsp;
        // Insert referrals into the database
<span class="cstat-no" title="statement not covered" >        await this.getAllReferrals(contactInsertionData, transaction);</span>
&nbsp;
        // Create Cognito user
<span class="cstat-no" title="statement not covered" >        if (isCognito) <span class="cstat-no" title="statement not covered" >await this.createUserInAwsCognito(email, transaction, "send", sfContactDetails.Id, _id, _index);</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        await transaction.commit();</span>
        const response = <span class="cstat-no" title="statement not covered" >{ status: true, data: payload, message: "Registration successful." };</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response });</span></span>
&nbsp;
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >        return response;</span>
      } catch (error) {
<span class="cstat-no" title="statement not covered" >        await transaction.rollback();</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >        console.error(`AuthService signup -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >        throw error;</span>
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService signup catch level 2 -&gt;`, error);</span>
      // For errors before transaction starts
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Referral Module
<span class="fstat-no" title="function not covered" >  as</span>ync getAllReferrals(contactData, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { id: contact_id, sf_record_id: contactSfId } = <span class="cstat-no" title="statement not covered" >contactData</span>
      const referralDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getReferralDetails(contactSfId);</span>
<span class="cstat-no" title="statement not covered" >      if (!referralDetails?.length) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >      for (const referralDetail of referralDetails) {</span>
&nbsp;
        const { Id: sf_record_id, Referrals_Live_Date__c: installed_on, Credit_Added_to_Account__c: credit_added_on, Credit_Amount__c: referred_amount, CreatedDate: createdAt, LastModifiedDate: sf_updatedAt, Referrals_Name__c: referral_name, Name: sf_name } = <span class="cstat-no" title="statement not covered" >referralDetail;</span>
&nbsp;
        const referralObj = <span class="cstat-no" title="statement not covered" >{</span>
          contact_id,
          sf_name,
          referred_amount,
          referral_name,
          installed_on,
          credit_added_on,
          createdAt: getCurrentAtlanticTime(createdAt, "sfUpdate"),
          sf_updatedAt: getCurrentAtlanticTime(sf_updatedAt, "sfUpdate")
        };
        const checkReferralExist = <span class="cstat-no" title="statement not covered" >await db.ContactsReferrals.count({ where: { sf_record_id }, transaction });</span>
<span class="cstat-no" title="statement not covered" >        if (!checkReferralExist) {</span>
<span class="cstat-no" title="statement not covered" >          referralObj.sf_record_id = sf_record_id;</span>
<span class="cstat-no" title="statement not covered" >          await db.ContactsReferrals.create(referralObj, { transaction });</span>
        } else <span class="cstat-no" title="statement not covered" >await db.ContactsReferrals.update(referralObj, { where: { sf_record_id }, transaction });</span>
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`Auth service get referral details -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to insert customer details into the database
<span class="fstat-no" title="function not covered" >  as</span>ync insertContactInDB(sfContactDetails, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Extract relevant fields from Salesforce contact details
      const {
        Id: sf_record_id,
        FirstName: first_name,
        LastName: last_name,
        Email: email,
        Company_Name__c: company_name,
        Primary_Phone__c: cell_phone,
        Alternate_Phone_Number__c: secondary_phone,
        Contact_Name_if_different_than_end_user__c: additional_name,
        Referral_Link_Full__c: referral_link,
        Account_Chargebee_Customer_ID__c: cb_customer_id,
        Total_Referrals__c: total_referral,
        Total_Paid_Referrals__c: total_paid_referral,
        Total_Earned_Referrals__c: total_earned_referral,
        LastModifiedDate: sf_updatedAt,
        CreatedDate: createdAt,
        Sticky_Sender__c: sticky_sender,
        Name: sf_name,
        CP_Cognito_ID__c: aws_cognito_id
      } = <span class="cstat-no" title="statement not covered" >sfContactDetails;</span>
&nbsp;
      const userDetails = <span class="cstat-no" title="statement not covered" >{ sf_record_id, email };</span>
<span class="cstat-no" title="statement not covered" >      if (first_name) <span class="cstat-no" title="statement not covered" >userDetails.first_name = first_name;</span></span>
<span class="cstat-no" title="statement not covered" >      if (last_name) <span class="cstat-no" title="statement not covered" >userDetails.last_name = last_name;</span></span>
<span class="cstat-no" title="statement not covered" >      if (sf_name) <span class="cstat-no" title="statement not covered" >userDetails.sf_name = sf_name;</span></span>
<span class="cstat-no" title="statement not covered" >      if (company_name) <span class="cstat-no" title="statement not covered" >userDetails.company_name = company_name;</span></span>
<span class="cstat-no" title="statement not covered" >      if (cell_phone) <span class="cstat-no" title="statement not covered" >userDetails.cell_phone = cell_phone.replace(/\D/g, '');</span></span>
<span class="cstat-no" title="statement not covered" >      if (secondary_phone) <span class="cstat-no" title="statement not covered" >userDetails.secondary_phone = secondary_phone.replace(/\D/g, '');</span></span>
<span class="cstat-no" title="statement not covered" >      if (additional_name) <span class="cstat-no" title="statement not covered" >userDetails.additional_name = additional_name;</span></span>
<span class="cstat-no" title="statement not covered" >      if (referral_link) <span class="cstat-no" title="statement not covered" >userDetails.referral_link = referral_link;</span></span>
<span class="cstat-no" title="statement not covered" >      if (cb_customer_id) <span class="cstat-no" title="statement not covered" >userDetails.cb_customer_id = cb_customer_id;</span></span>
<span class="cstat-no" title="statement not covered" >      if (total_referral) <span class="cstat-no" title="statement not covered" >userDetails.total_referral = total_referral;</span></span>
<span class="cstat-no" title="statement not covered" >      if (total_paid_referral) <span class="cstat-no" title="statement not covered" >userDetails.total_paid_referral = total_paid_referral;</span></span>
<span class="cstat-no" title="statement not covered" >      if (total_earned_referral) <span class="cstat-no" title="statement not covered" >userDetails.total_earned_referral = total_earned_referral;</span></span>
<span class="cstat-no" title="statement not covered" >      if (sticky_sender) <span class="cstat-no" title="statement not covered" >userDetails.sticky_sender = sticky_sender;</span></span>
<span class="cstat-no" title="statement not covered" >      if (aws_cognito_id) <span class="cstat-no" title="statement not covered" >userDetails.aws_cognito_id = aws_cognito_id;</span></span>
<span class="cstat-no" title="statement not covered" >      if (sf_updatedAt) <span class="cstat-no" title="statement not covered" >userDetails.sf_updatedAt = getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");</span></span>
<span class="cstat-no" title="statement not covered" >      if (createdAt) <span class="cstat-no" title="statement not covered" >userDetails.createdAt = getCurrentAtlanticTime(createdAt, "sfUpdate");</span></span>
&nbsp;
      // Only create, no need to check for existing contact
<span class="cstat-no" title="statement not covered" >      return await db.Contacts.create(userDetails, { transaction });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService insert Customer in DB -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Helper to resolve address and service IDs
<span class="fstat-no" title="function not covered" >  as</span>ync resolveCustomerDetailIds(sfCustomerDetail, transaction) {
    const {
      Service_Address__c,
      Mailing_Address__c,
      Latest_TV__c,
      Latest_Internet__c,
      Latest_Phone_VOIP__c
    } = <span class="cstat-no" title="statement not covered" >sfCustomerDetail;</span>
&nbsp;
    const addressPromises = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >    addressPromises.push(Service_Address__c ? await this.getServiceAddressId(Service_Address__c, transaction) : Promise.resolve(null));</span>
<span class="cstat-no" title="statement not covered" >    addressPromises.push(Mailing_Address__c ? await this.getMailingAddressId(Mailing_Address__c, transaction) : Promise.resolve(null));</span>
&nbsp;
    const servicePromises = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >    servicePromises.push(Latest_Internet__c ? this.getServiceDetailId(Latest_Internet__c, "Internet__c", "CustomerInternet", transaction) : Promise.resolve(null));</span>
<span class="cstat-no" title="statement not covered" >    servicePromises.push(Latest_TV__c ? this.getServiceDetailId(Latest_TV__c, "TV__c", "CustomerTv", transaction) : Promise.resolve(null));</span>
<span class="cstat-no" title="statement not covered" >    servicePromises.push(Latest_Phone_VOIP__c ? this.getServiceDetailId(Latest_Phone_VOIP__c, "Phone__c", "CustomerPhone", transaction) : Promise.resolve(null));</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return Promise.allSettled([...addressPromises, ...servicePromises]);</span>
  }
&nbsp;
  // Helper to upsert customer details
<span class="fstat-no" title="function not covered" >  as</span>ync upsertCustomerDetails(customerDetailData, transaction) {
    const checkCustomerExist = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findOne({ where: { sf_record_id: customerDetailData.sf_record_id }, transaction });</span>
<span class="cstat-no" title="statement not covered" >    if (!checkCustomerExist) {</span>
<span class="cstat-no" title="statement not covered" >      return await db.CustomerDetails.create(customerDetailData, { transaction });</span>
    } else {
<span class="cstat-no" title="statement not covered" >      await db.CustomerDetails.update(customerDetailData, { where: { sf_record_id: customerDetailData.sf_record_id }, transaction });</span>
<span class="cstat-no" title="statement not covered" >      return checkCustomerExist;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync insertCustomerDetailsinDB(contact_id, sfCustomerDetails, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const detailPromises = <span class="cstat-no" title="statement not covered" >sfCustomerDetails.map(<span class="fstat-no" title="function not covered" >as</span>ync sfCustomerDetail =&gt; {</span>
        const {
          Id: sf_record_id,
          CP_Stage__c,
          LastModifiedDate: sf_updatedAt,
          CreatedDate: createdAt,
          Name: sf_name,
          Latest_CB_Subscription__c,
          CB_Subscription_Id__c
        } = <span class="cstat-no" title="statement not covered" >sfCustomerDetail;</span>
&nbsp;
        const checkStage = <span class="cstat-no" title="statement not covered" >CP_Stage__c ? CP_Stage__c.toLowerCase() : null;</span>
<span class="cstat-no" title="statement not covered" >        if (checkStage &amp;&amp; checkStage !== "hidden") {</span>
          const [serviceAddress, mailingAddress, internet, tv, phone] = <span class="cstat-no" title="statement not covered" >await this.resolveCustomerDetailIds(sfCustomerDetail, transaction);</span>
&nbsp;
          const customerDetailData = <span class="cstat-no" title="statement not covered" >{</span>
            contact_id,
            sf_record_id,
            service_address_id: serviceAddress.status === 'fulfilled' ? serviceAddress.value : null,
            mailing_address_id: mailingAddress.status === 'fulfilled' ? mailingAddress.value : null,
            internet_id: internet.status === 'fulfilled' ? internet.value : null,
            tv_id: tv.status === 'fulfilled' ? tv.value : null,
            phone_id: phone.status === 'fulfilled' ? phone.value : null,
            stage: CP_Stage__c || "Hidden",
            sf_name: sf_name || null,
            sf_updatedAt: getCurrentAtlanticTime(sf_updatedAt, "sfUpdate"),
            createdAt: getCurrentAtlanticTime(createdAt, "sfUpdate")
          };
&nbsp;
          const insertCustomerDetails = <span class="cstat-no" title="statement not covered" >await this.upsertCustomerDetails(customerDetailData, transaction);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (insertCustomerDetails?.id) {</span>
<span class="cstat-no" title="statement not covered" >            if (Latest_CB_Subscription__c) {</span>
<span class="cstat-no" title="statement not covered" >              await this.getSubscriptionDetailsfromSf(Latest_CB_Subscription__c, insertCustomerDetails.id, transaction, "salesforce", customerDetailData);</span>
            } else <span class="cstat-no" title="statement not covered" >if (CB_Subscription_Id__c) {</span>
<span class="cstat-no" title="statement not covered" >              await this.getSubscriptionDetailsfromSf(CB_Subscription_Id__c, insertCustomerDetails.id, transaction, "chargebee", customerDetailData);</span>
            }
          }
        }
      });
<span class="cstat-no" title="statement not covered" >      await Promise.all(detailPromises);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService insert Customer Details in DB -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getServiceAddressId(serviceAddressId, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (serviceAddressId) {</span>
        const { returnStatus, addresstData: sfServiceAddressDetails } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getAddressDetails(serviceAddressId, "Service_Address__c");</span>
<span class="cstat-no" title="statement not covered" >        if (returnStatus) {</span>
&nbsp;
          const { Id: sf_record_id, Name: name, Service_Suite_Unit__c: suit_unit, Service_Street_Number__c: street_number, Service_Street_Name__c: street_name, Service_City_Town__c: town, Service_Province__c: province, Service_Postal_Code__c: postal_code, Full_Service_Address__c: full_address, Service_Country__c: country, Status__c: status, LastModifiedDate: sf_updatedAt, CreatedDate } = <span class="cstat-no" title="statement not covered" >sfServiceAddressDetails;</span>
&nbsp;
          const sfUpdatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");</span>
          const createdAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(CreatedDate, "sfUpdate");</span>
&nbsp;
          let [adrsDetails, created] = <span class="cstat-no" title="statement not covered" >await db.CustomerAddresses.findOrCreate({</span>
            where: {
              sf_record_id
            },
            defaults: { address_type: "service", sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt, createdAt }, transaction
          });
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (!created) <span class="cstat-no" title="statement not covered" >await adrsDetails.update({ name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt }, { transaction });</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          return adrsDetails?.id;</span>
        }
      }
<span class="cstat-no" title="statement not covered" >      return null;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService getServiceAddressId -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getMailingAddressId(mailingAddressId, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (mailingAddressId) {</span>
        const { returnStatus, addresstData: sfmailingAddressDetails } = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getAddressDetails(mailingAddressId, "Mailing_Address__c");</span>
<span class="cstat-no" title="statement not covered" >        if (returnStatus) {</span>
&nbsp;
          const { Id: sf_record_id, Name: name, Mailing_Suite_Unit__c: suit_unit, Mailing_Street_Number__c: street_number, Mailing_Street_Name__c: street_name, Mailing_City_Town__c: town, Mailing_Province__c: province, Mailing_Postal_Code__c: postal_code, Full_Mailing_Address__c: full_address, Mailing_Country__c: country, Status__c: status, LastModifiedDate: sf_updatedAt, CreatedDate } = <span class="cstat-no" title="statement not covered" >sfmailingAddressDetails;</span>
&nbsp;
          const sfUpdatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");</span>
          const createdAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(CreatedDate, "sfUpdate");</span>
&nbsp;
          let [adrsDetails, created] = <span class="cstat-no" title="statement not covered" >await db.CustomerAddresses.findOrCreate({</span>
            where: {
              sf_record_id
            },
            defaults: { address_type: "mailing", sf_record_id, name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt, createdAt }, transaction
          });
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (!created) <span class="cstat-no" title="statement not covered" >await adrsDetails.update({ name, suit_unit, street_number, street_name, province, town, country, postal_code, full_address, status, sf_updatedAt: sfUpdatedAt }, { transaction });</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          return adrsDetails?.id;</span>
        }
      }
<span class="cstat-no" title="statement not covered" >      return null;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService get Mailing AddressId -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getServiceDetailId(serviceId, serviceType, dbModel, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (!serviceId) <span class="cstat-no" title="statement not covered" >return null;</span></span>
      const serviceDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getInternetTvPhoneDetails(serviceId, serviceType);</span>
<span class="cstat-no" title="statement not covered" >      if (!serviceDetails) <span class="cstat-no" title="statement not covered" >return null;</span></span>
&nbsp;
      const sfUpdatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(serviceDetails.LastModifiedDate, "sfUpdate");</span>
      const createdAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(serviceDetails.CreatedDate, "sfUpdate");</span>
&nbsp;
      const insertData = <span class="cstat-no" title="statement not covered" >{ sf_record_id: serviceDetails.Id, sf_response_status: "success", sf_updatedAt: sfUpdatedAt, createdAt, sf_name: serviceDetails.Name };</span>
<span class="cstat-no" title="statement not covered" >      if (serviceType === "Internet__c") {</span>
<span class="cstat-no" title="statement not covered" >        if (!serviceDetails.CP_Speed__c) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >        insertData.plan_speed = serviceDetails.CP_Speed__c;</span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails?.Live_Date__c) <span class="cstat-no" title="statement not covered" >insertData.live_date = serviceDetails.Live_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails?.Disconnected_Date__c) <span class="cstat-no" title="statement not covered" >insertData.disconnected_date = serviceDetails.Disconnected_Date__c;</span></span>
&nbsp;
        // Status
<span class="cstat-no" title="statement not covered" >        insertData.internal_processing_status = serviceDetails.CP_Status_Internal_Processing__c;</span>
<span class="cstat-no" title="statement not covered" >        insertData.ship_package_status = serviceDetails.CP_Status_Ship_Package__c;</span>
<span class="cstat-no" title="statement not covered" >        insertData.modem_installation_status = serviceDetails.CP_Status_Modem_Installation__c;</span>
<span class="cstat-no" title="statement not covered" >        insertData.modem_activation_status = serviceDetails.CP_Status_Modem_Activation__c;</span>
&nbsp;
        const { creation_order, tech_appointment, disconnect_order, speed_change_order, move_order, swap_order } = <span class="cstat-no" title="statement not covered" >await this.getAllInternetOrderDetails(serviceDetails, transaction);</span>
<span class="cstat-no" title="statement not covered" >        if (creation_order) <span class="cstat-no" title="statement not covered" >insertData.creation_order = creation_order;</span></span>
<span class="cstat-no" title="statement not covered" >        if (tech_appointment) <span class="cstat-no" title="statement not covered" >insertData.tech_appointment = tech_appointment;</span></span>
<span class="cstat-no" title="statement not covered" >        if (disconnect_order) <span class="cstat-no" title="statement not covered" >insertData.disconnect_order = disconnect_order;</span></span>
<span class="cstat-no" title="statement not covered" >        if (speed_change_order) <span class="cstat-no" title="statement not covered" >insertData.speed_change_order = speed_change_order;</span></span>
<span class="cstat-no" title="statement not covered" >        if (move_order) <span class="cstat-no" title="statement not covered" >insertData.move_order = move_order;</span></span>
<span class="cstat-no" title="statement not covered" >        if (swap_order) <span class="cstat-no" title="statement not covered" >insertData.swap_order = swap_order;</span></span>
      }
<span class="cstat-no" title="statement not covered" >      if (serviceType === "TV__c") {</span>
        const plan_name = <span class="cstat-no" title="statement not covered" >serviceDetails.Current_Base_Package__c;</span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails.current_account_status__c == "REMOVED" &amp;&amp; serviceDetails?.State_Text__c == "Complete") <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >        if (plan_name) {</span>
<span class="cstat-no" title="statement not covered" >          insertData.plan_name = plan_name;</span>
<span class="cstat-no" title="statement not covered" >          insertData.state_text = serviceDetails?.State_Text__c;</span>
<span class="cstat-no" title="statement not covered" >          insertData.extra_packages = serviceDetails?.Current_Extra_Packages__c ? JSON.stringify(serviceDetails.Current_Extra_Packages__c.split(";")) : "[]";</span>
<span class="cstat-no" title="statement not covered" >          insertData.single_channels = serviceDetails?.Current_Single_Channels__c ? JSON.stringify(serviceDetails.Current_Single_Channels__c.split(";")) : "[]";</span>
<span class="cstat-no" title="statement not covered" >          insertData.iptv_products = serviceDetails?.current_iptv_products__c ? JSON.stringify(serviceDetails.current_iptv_products__c.split(";")) : "[]";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (serviceDetails?.current_account_status__c) <span class="cstat-no" title="statement not covered" >insertData.account_status = serviceDetails.current_account_status__c;</span></span>
<span class="cstat-no" title="statement not covered" >          if (serviceDetails?.Login_Details_Last_Sent__c) <span class="cstat-no" title="statement not covered" >insertData.login_details_last_sent = getCurrentAtlanticTime(serviceDetails.Login_Details_Last_Sent__c, "sfUpdate");</span></span>
<span class="cstat-no" title="statement not covered" >          if (serviceDetails?.Requested_Cancellation_Date__c) <span class="cstat-no" title="statement not covered" >insertData.requested_cancellation_date = serviceDetails.Requested_Cancellation_Date__c;</span></span>
        }
      }
<span class="cstat-no" title="statement not covered" >      if (serviceType === "Phone__c") {</span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails.Account_Status__c == "Deleted") <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >        insertData.api_name = serviceDetails.Calling_Plan__c;</span>
        const phoneRes = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getPhoneApiValue(serviceDetails.Id);</span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails?.Requested_Cancellation_Date__c) {</span>
<span class="cstat-no" title="statement not covered" >          insertData.requested_cancellation_date = getCurrentAtlanticTime(serviceDetails.Requested_Cancellation_Date__c);</span>
        }
        const { status: phStatus, data: pfData } = <span class="cstat-no" title="statement not covered" >phoneRes;</span>
<span class="cstat-no" title="statement not covered" >        if (phStatus) {</span>
<span class="cstat-no" title="statement not covered" >          insertData.plan_name = pfData?.Calling_Plan__c;</span>
        }
<span class="cstat-no" title="statement not covered" >        if (serviceDetails?.Service_Start_Date__c) <span class="cstat-no" title="statement not covered" >insertData.service_start_date = getCurrentAtlanticTime(serviceDetails.Service_Start_Date__c);</span></span>
<span class="cstat-no" title="statement not covered" >        insertData.account_status = serviceDetails.Account_Status__c;</span>
&nbsp;
        // Check the port type of home phone
        let sf_phone_type = <span class="cstat-no" title="statement not covered" >{ sf_phone_type: "new", phone_number: "" };</span>
<span class="cstat-no" title="statement not covered" >        if (serviceDetails?.Phone_Number_To_Port__c) {</span>
<span class="cstat-no" title="statement not covered" >          sf_phone_type.phone_number = serviceDetails.Phone_Number_To_Port__c;</span>
<span class="cstat-no" title="statement not covered" >          sf_phone_type.sf_phone_type = "existing";</span>
        }
<span class="cstat-no" title="statement not covered" >        insertData.sf_phone_type = JSON.stringify(sf_phone_type);</span>
      }
&nbsp;
      let [serviceInsert, created] = <span class="cstat-no" title="statement not covered" >await db[dbModel].findOrCreate({</span>
        where: {
          sf_record_id: serviceDetails?.Id
        },
        defaults: insertData, transaction
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!created) <span class="cstat-no" title="statement not covered" >await serviceInsert.update(insertData, { transaction });</span></span>
<span class="cstat-no" title="statement not covered" >      return serviceInsert?.id;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService getServiceDetailId -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getSubscriptionDetailsfromSf(subscriptionId, customer_details_id, transaction, type, customerDetailData) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      let cbSubId;
<span class="cstat-no" title="statement not covered" >      if (type == "salesforce") {</span>
        const subscriptionData = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getSubscriptionDetails(subscriptionId);</span>
<span class="cstat-no" title="statement not covered" >        if (!subscriptionData) <span class="cstat-no" title="statement not covered" >return null;</span></span>
        const { chargebeeapps__CB_Subscription_Id__c } = <span class="cstat-no" title="statement not covered" >subscriptionData;</span>
<span class="cstat-no" title="statement not covered" >        cbSubId = chargebeeapps__CB_Subscription_Id__c;</span>
      } else <span class="cstat-no" title="statement not covered" >if (type == "chargebee") {</span>
<span class="cstat-no" title="statement not covered" >        cbSubId = subscriptionId;</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!cbSubId) <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
      const chargebeeServiceClient = <span class="cstat-no" title="statement not covered" >new ChargebeeService();</span>
      const { status, subscriptionDetail } = <span class="cstat-no" title="statement not covered" >await chargebeeServiceClient.getSubscriptions(cbSubId);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!status) <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
      const insertCustomerSubsDetails = <span class="cstat-no" title="statement not covered" >await this.insertCustomerSubscriptionFromChargebee(subscriptionDetail, customer_details_id, transaction);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (insertCustomerSubsDetails?.id) {</span>
        const sfSubscriptionId = <span class="cstat-no" title="statement not covered" >await this.updateCustomerSubscriptionFromSf(insertCustomerSubsDetails, transaction);</span>
        const { internet_id, tv_id, phone_id } = <span class="cstat-no" title="statement not covered" >customerDetailData;</span>
        const subscription_type = <span class="cstat-no" title="statement not covered" >insertCustomerSubsDetails?.subscription_type;</span>
        const planServices = <span class="cstat-no" title="statement not covered" >new PlanServices();</span>
<span class="cstat-no" title="statement not covered" >        if (internet_id) {</span>
          const internetDetails = <span class="cstat-no" title="statement not covered" >await db.CustomerInternet.findOne({</span>
            where: { id: internet_id }, transaction
          });
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (internetDetails?.plan_speed) {</span>
            const internetPlanDetails = <span class="cstat-no" title="statement not covered" >await planServices.getPlanDetailsFromJSON(CONFIG.internet.plans);</span>
<span class="cstat-no" title="statement not covered" >            if (internetPlanDetails?.length) {</span>
              let updateObj = <span class="cstat-no" title="statement not covered" >{};</span>
              const getPrice = <span class="cstat-no" title="statement not covered" >internetPlanDetails.find(<span class="fstat-no" title="function not covered" >in</span>ternet =&gt; <span class="cstat-no" title="statement not covered" >internet.speed === internetDetails.plan_speed)</span>;</span>
<span class="cstat-no" title="statement not covered" >              if (getPrice?.api_name) <span class="cstat-no" title="statement not covered" >updateObj.plan_name = getPrice.api_name;</span></span>
<span class="cstat-no" title="statement not covered" >              if (getPrice?.billing?.[0]?.[subscription_type]?.price) {</span>
<span class="cstat-no" title="statement not covered" >                updateObj.plan_price = getPrice.billing[0][subscription_type].price;</span>
              }
<span class="cstat-no" title="statement not covered" >              await db.CustomerInternet.update(updateObj, { where: { id: internetDetails?.id }, transaction });</span>
            }
          }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (tv_id) {</span>
          const tvDetails = <span class="cstat-no" title="statement not covered" >await db.CustomerTv.findOne({</span>
            where: { id: tv_id }, transaction
          });
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (tvDetails) {</span>
            const { id, plan_name, single_channels, iptv_products, extra_packages } = <span class="cstat-no" title="statement not covered" >tvDetails;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (plan_name) {</span>
&nbsp;
              const payload = <span class="cstat-no" title="statement not covered" >{</span>
                plan_name: plan_name,
                extra_packages: JSON.parse(extra_packages),
                single_channels: JSON.parse(single_channels),
                iptv_products: JSON.parse(iptv_products)
              }
&nbsp;
              const subscriptionServices = <span class="cstat-no" title="statement not covered" >new SubscriptionServices();</span>
&nbsp;
              const { totalAmount } = <span class="cstat-no" title="statement not covered" >await subscriptionServices.getAddonsTelevisionPlanDetails(payload, subscription_type);</span>
<span class="cstat-no" title="statement not covered" >              await db.CustomerTv.update({ total_service_cost: totalAmount }, { where: { id }, transaction });</span>
            }
          }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (phone_id) {</span>
          const phoneDetails = <span class="cstat-no" title="statement not covered" >await db.CustomerPhone.findOne({</span>
            where: { id: phone_id }, transaction
          });
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (phoneDetails) {</span>
            const { id, api_name } = <span class="cstat-no" title="statement not covered" >phoneDetails;</span>
<span class="cstat-no" title="statement not covered" >            if (api_name) {</span>
              const phonePlanDetails = <span class="cstat-no" title="statement not covered" >await planServices.getPlanDetailsFromJSON(CONFIG.phone.plans);</span>
<span class="cstat-no" title="statement not covered" >              if (phonePlanDetails?.length) {</span>
                let updateObj = <span class="cstat-no" title="statement not covered" >{};</span>
                const getPrice = <span class="cstat-no" title="statement not covered" >phonePlanDetails.find(<span class="fstat-no" title="function not covered" >ph</span>one =&gt; <span class="cstat-no" title="statement not covered" >phone.api_name === api_name)</span>;</span>
<span class="cstat-no" title="statement not covered" >                if (getPrice?.billing_period?.[0]?.[subscription_type]?.price) {</span>
<span class="cstat-no" title="statement not covered" >                  updateObj.plan_price = getPrice.billing_period[0][subscription_type].price;</span>
                }
<span class="cstat-no" title="statement not covered" >                await db.CustomerPhone.update(updateObj, { where: { id }, transaction });</span>
              }
            }
          }
        }
<span class="cstat-no" title="statement not covered" >        await chargebeeServiceClient.getInvoicesList(insertCustomerSubsDetails, transaction);</span>
<span class="cstat-no" title="statement not covered" >        if (sfSubscriptionId) <span class="cstat-no" title="statement not covered" >await this.updateCustomerInvoice(sfSubscriptionId, transaction);</span></span>
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService getSubscriptionDetailsfromSf -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync updateCustomerSubscriptionFromSf(customerSubsDetails, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
&nbsp;
      const { cb_subscription_id, id } = <span class="cstat-no" title="statement not covered" >customerSubsDetails;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!cb_subscription_id) <span class="cstat-no" title="statement not covered" >return;</span></span>
&nbsp;
      const subscriptionData = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getSubscriptionDetailsUsingCbSubId(cb_subscription_id);</span>
<span class="cstat-no" title="statement not covered" >      if (!subscriptionData) <span class="cstat-no" title="statement not covered" >return null;</span></span>
      const { Id: sf_record_id, LastModifiedDate } = <span class="cstat-no" title="statement not covered" >subscriptionData;</span>
      const sf_updatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(LastModifiedDate, "sfUpdate");</span>
&nbsp;
      const updateObj = <span class="cstat-no" title="statement not covered" >{ sf_record_id, sf_updatedAt };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      await db.CustomerSubscriptions.update(updateObj, { where: { id }, transaction });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      return sf_record_id || null;</span>
&nbsp;
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService updateCustomerSubscription -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync updateCustomerInvoice(sfSubscriptionId, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
&nbsp;
      const invoiceDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getInvoiceDetails(sfSubscriptionId);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      for (const invoiceData of invoiceDetails) {</span>
        const { Id: sf_record_id, Expected_Payment_Date_Time__c: expected_payment_date, LastModifiedDate, chargebeeapps__Subscription_CB_Id__c: cb_subscription_id, chargebeeapps__CB_Invoice_Id__c: cb_invoice_id } = <span class="cstat-no" title="statement not covered" >invoiceData;</span>
&nbsp;
        const sf_updatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(LastModifiedDate, "sfUpdate");</span>
&nbsp;
        const updateObj = <span class="cstat-no" title="statement not covered" >{ sf_record_id, sf_updatedAt, expected_payment_date };</span>
<span class="cstat-no" title="statement not covered" >        await db.SubscriptionInvoice.update(updateObj, { where: { cb_invoice_id, cb_subscription_id }, transaction });</span>
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService updateCustomerInvoice -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to create user in AWS Cognito
<span class="fstat-no" title="function not covered" >  as</span>ync createUserInAwsCognito(email, transaction, type, sf_record_id, esId, esIndices) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const password = <span class="cstat-no" title="statement not covered" >passwordGenerator.generate({</span>
        length: 10, numbers: true, symbols: true, uppercase: true, lowercase: true, strict: true, exclude: '#`&amp;+?()_={}[]:;"\'&lt;&gt;,./|\\'
      });
      const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.register(email, password, type);</span>
<span class="cstat-no" title="statement not covered" >      if (esId &amp;&amp; esIndices) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(esIndices, esId, { response });</span></span>
<span class="cstat-no" title="statement not covered" >      if (type == "resend") <span class="cstat-no" title="statement not covered" >return response;</span></span>
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
        const { User } = <span class="cstat-no" title="statement not covered" >response;</span>
<span class="cstat-no" title="statement not covered" >        if (User &amp;&amp; User?.Username) {</span>
<span class="cstat-no" title="statement not covered" >          await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: User.Username, sf_record_id, cp_created_dateTime: User?.UserCreateDate, cp_status: "inactive" }, "cognitoUpdate");</span>
<span class="cstat-no" title="statement not covered" >          if (type == "send") {</span>
<span class="cstat-no" title="statement not covered" >            return await db.Contacts.update({ aws_cognito_id: User.Username }, { where: { email }, transaction });</span>
          } else {
<span class="cstat-no" title="statement not covered" >            return await db.Contacts.update({ aws_cognito_id: User.Username }, { where: { email } });</span>
          }
        }
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService create User In Aws Cognito -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method for user login
<span class="fstat-no" title="function not covered" >  as</span>ync login(payload, elasticLogObj) {
    const { email, password } = <span class="cstat-no" title="statement not covered" >payload;</span>
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("login_logs", { ...elasticLogObj, request: { email } });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { userExist, userData } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, `Incorrect username or password.`);</span></span>
<span class="cstat-no" title="statement not covered" >      if (!userData.aws_cognito_id) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, `Login Failed. Please contact support for assistance.`);</span></span>
&nbsp;
      const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.login(email, password);</span>
      let data = <span class="cstat-no" title="statement not covered" >{ email };</span>
      let message;
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
        const { ChallengeName, Session } = <span class="cstat-no" title="statement not covered" >response;</span>
<span class="cstat-no" title="statement not covered" >        if (ChallengeName === "NEW_PASSWORD_REQUIRED") {</span>
<span class="cstat-no" title="statement not covered" >          data.redirectUrl = "/login#reset-password";</span>
<span class="cstat-no" title="statement not covered" >          data.accessToken = Session;</span>
<span class="cstat-no" title="statement not covered" >          message = "Please reset your password.";</span>
        } else {
          const { AuthenticationResult } = <span class="cstat-no" title="statement not covered" >response;</span>
          const { AccessToken, ExpiresIn } = <span class="cstat-no" title="statement not covered" >AuthenticationResult;</span>
<span class="cstat-no" title="statement not covered" >          if (AccessToken) {</span>
<span class="cstat-no" title="statement not covered" >            data.redirectUrl = "/";</span>
<span class="cstat-no" title="statement not covered" >            data.accessToken = generateAccessToken(CONFIG.RESGISTRATION_TOKEN, AccessToken);</span>
<span class="cstat-no" title="statement not covered" >            data.expiresIn = ExpiresIn;</span>
          }
<span class="cstat-no" title="statement not covered" >          message = "Login successful";</span>
        }
      }
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, {</span></span>
        "log.level": 'INFO',
        response: {
          success: {
            expiresIn: data.expiresIn, message
          }
        }
      });
<span class="cstat-no" title="statement not covered" >      return { status: true, data, message };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService Login In Aws Cognito -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      if (_id) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'WARN', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      if (error?.message == "Temporary password has expired and must be reset by an administrator.") <span class="cstat-no" title="statement not covered" >return await this.resendInvitation(email);</span></span>
      else <span class="cstat-no" title="statement not covered" >throw error;</span>
    }
  }
&nbsp;
  // Method to resend invitation for password reset
<span class="fstat-no" title="function not covered" >  as</span>ync resendInvitation(email) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false, data: {} }</span>
      const response = <span class="cstat-no" title="statement not covered" >await this.createUserInAwsCognito(email, null, "resend");</span>
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.message = "Your temporary password has expired. A new temporary password has been sent to your email address. Please log in using your new temporary password.";</span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService Resend Invitation In Aws Cognito-&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to reset user password
<span class="fstat-no" title="function not covered" >  as</span>ync resetPassword(payload, elasticLogObj) {
    const { email, password, access_token, type, confirmation_code } = <span class="cstat-no" title="statement not covered" >payload;</span>
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_password_logs", { ...elasticLogObj, request: { email, access_token, type }, type: "RESET" });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { userExist, userData } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);</span></span>
&nbsp;
      // Call the new method to handle password reset and get the result
      const res = <span class="cstat-no" title="statement not covered" >await this.handlePasswordReset(type, { email, password, access_token, confirmation_code, userData });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: res });</span></span>
<span class="cstat-no" title="statement not covered" >      return res;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService Reset Password For First Time In Aws Cognito -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // New function to handle the password reset process in AWS Cognito
<span class="fstat-no" title="function not covered" >  as</span>ync handlePasswordReset(type, { email, password, access_token, confirmation_code, userData }, loginType) {
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Determine which AWS Cognito method to use based on the type
      const response = <span class="cstat-no" title="statement not covered" >type === "new"</span>
        ? await awsCognitoConnection.newPasswordChallenge({ email, password, session: access_token })
        : await awsCognitoConnection.forgotPasswordConfirmation({ email, password, confirmation_code });
&nbsp;
      // Prepare the default data object
      let data = <span class="cstat-no" title="statement not covered" >{ email, emailVerified: true };</span>
&nbsp;
      // If the response is successful, process further based on the type
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (type === "new") {</span>
          // If it's a new password, verify the email address
          const { status } = <span class="cstat-no" title="statement not covered" >await this.verifyEmailAddress(email, loginType);</span>
<span class="cstat-no" title="statement not covered" >          if (!status) <span class="cstat-no" title="statement not covered" >data.emailVerified = false; </span></span>// Update if email verification failed
          const { AuthenticationResult } = <span class="cstat-no" title="statement not covered" >response;</span>
          const { AccessToken, ExpiresIn } = <span class="cstat-no" title="statement not covered" >AuthenticationResult;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (userData.sf_record_id) <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: userData.aws_cognito_id, sf_record_id: userData.sf_record_id, cp_status: "active", first_login: true }, "cognitoUpdate");</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          if (AccessToken) {</span>
<span class="cstat-no" title="statement not covered" >            data.redirectUrl = "/";</span>
<span class="cstat-no" title="statement not covered" >            data.accessToken = generateAccessToken(CONFIG.RESGISTRATION_TOKEN, AccessToken);</span>
<span class="cstat-no" title="statement not covered" >            data.expiresIn = ExpiresIn;</span>
          }
        }
      }
&nbsp;
      // Return the final result object
<span class="cstat-no" title="statement not covered" >      return { status: true, data, message: "Password reset successfully" };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to verify user email address
<span class="fstat-no" title="function not covered" >  as</span>ync verifyEmailAddress(email, loginType) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false }</span>
      const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.verifyEmail(email);</span>
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
<span class="cstat-no" title="statement not covered" >        if (!loginType) <span class="cstat-no" title="statement not covered" >await db.Contacts.update({ status: "active" }, { where: { email } });</span></span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService Email Verification In Aws Cognito -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method for forgot password functionality
<span class="fstat-no" title="function not covered" >  as</span>ync forgotPassword(payload, elasticLogObj) {
    const { email } = <span class="cstat-no" title="statement not covered" >payload;</span>
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("update_password_logs", { ...elasticLogObj, request: { email }, type: "FORGOT" });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false };</span>
      const { userExist, userData } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);</span></span>
<span class="cstat-no" title="statement not covered" >      if (userData?.status === "inactive") <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Your account is currently inactive. Please activate it using the temporary password provided.");</span></span>
      const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.forgotPassword(email);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.data = payload;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.message = "Verification code sent to the registered email";</span>
      }
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'INFO', response: returnStatus });</span></span>
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService Forgot Password In Aws Cognito-&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method to check if user exists
<span class="fstat-no" title="function not covered" >  as</span>ync checkUserExist(email) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const returnStatus = <span class="cstat-no" title="statement not covered" >{ userExist: false };</span>
      const isUserExist = <span class="cstat-no" title="statement not covered" >await db.Contacts.findOne({ where: { email } });</span>
<span class="cstat-no" title="statement not covered" >      if (isUserExist) {</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.userExist = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.userData = isUserExist.toJSON();</span>
      }
<span class="cstat-no" title="statement not covered" >      return returnStatus;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`Check User Exist-&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getAllInternetOrderDetails(internetDetails, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { Creation_Order__c, Latest_Tech_Appointment__c, Latest_Disconnect_Order__c, Latest_Speed_Change_Order__c, Latest_Move_Order__c, Latest_Modem_Swap_Order__c } = <span class="cstat-no" title="statement not covered" >internetDetails;</span>
&nbsp;
      const orderPromises = <span class="cstat-no" title="statement not covered" >[</span>
        Creation_Order__c ? this.getOrderDetailsId(Creation_Order__c, "InternetElCreationOrder", transaction) : Promise.resolve(null),
        Latest_Tech_Appointment__c ? this.getTechDetailsId(Latest_Tech_Appointment__c, "InternetElTechAppointment", transaction) : Promise.resolve(null),
        Latest_Disconnect_Order__c ? this.getOrderDetailsId(Latest_Disconnect_Order__c, "InternetElDisconnectOrder", transaction) : Promise.resolve(null),
        Latest_Speed_Change_Order__c ? this.getOrderDetailsId(Latest_Speed_Change_Order__c, "InternetElSpeedChangeOrder", transaction) : Promise.resolve(null),
        Latest_Move_Order__c ? this.getOrderDetailsId(Latest_Move_Order__c, "InternetElMoveOrder", transaction) : Promise.resolve(null),
        Latest_Modem_Swap_Order__c ? this.getOrderDetailsId(Latest_Modem_Swap_Order__c, "InternetElSwapOrder", transaction) : Promise.resolve(null)
      ];
&nbsp;
      const [creationOrderId, techAppointmentId, disconnectOrderId, speedChangeOrderId, moveOrderId, swapOrderId] = <span class="cstat-no" title="statement not covered" >await Promise.allSettled(orderPromises);</span>
&nbsp;
      const customerDetailData = <span class="cstat-no" title="statement not covered" >{</span>
        creation_order: creationOrderId.status === 'fulfilled' ? creationOrderId.value : null,
        tech_appointment: techAppointmentId.status === 'fulfilled' ? techAppointmentId.value : null,
        disconnect_order: disconnectOrderId.status === 'fulfilled' ? disconnectOrderId.value : null,
        speed_change_order: speedChangeOrderId.status === 'fulfilled' ? speedChangeOrderId.value : null,
        move_order: moveOrderId.status === 'fulfilled' ? moveOrderId.value : null,
        swap_order: swapOrderId.status === 'fulfilled' ? swapOrderId.value : null
      };
&nbsp;
<span class="cstat-no" title="statement not covered" >      return customerDetailData;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("AuthService getAllInternetOrderDetails -&gt; ", error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync getOrderDetailsId(orderId, dbModel, transaction) {
&nbsp;
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (!orderId) <span class="cstat-no" title="statement not covered" >return null;</span></span>
      const orderDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getOrderDetails(orderId, dbModel);</span>
<span class="cstat-no" title="statement not covered" >      if (!orderDetails) <span class="cstat-no" title="statement not covered" >return null;</span></span>
&nbsp;
      const sfUpdatedAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(orderDetails?.LastModifiedDate, "sfUpdate");</span>
      const createdAt = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(orderDetails.CreatedDate, "sfUpdate");</span>
&nbsp;
      let insertOrderDetails = <span class="cstat-no" title="statement not covered" >{ sf_record_id: orderDetails?.Id, sf_updatedAt: sfUpdatedAt, createdAt, sf_name: orderDetails?.Name };</span>
<span class="cstat-no" title="statement not covered" >      if (dbModel === "InternetElCreationOrder") {</span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Related_Shipping_Order__c) {</span>
          const shippingData = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getShippingDetails(orderDetails.Related_Shipping_Order__c);</span>
          let shipping_id = <span class="cstat-no" title="statement not covered" >null;</span>
<span class="cstat-no" title="statement not covered" >          if (shippingData) {</span>
            const { Id: sf_record_id, Full_Mailing_Address__c: full_mailing_address, Ship_Date__c: ship_date, Ship_Drop_Off_Date__c: ship_drop_off_date, Tracking_URL__c: tracking_url, Courier__c: courier, LastModifiedDate: sf_updatedAt, Package_Delivered__c: package_deliverted_at, CreatedDate, Name: sf_name } = <span class="cstat-no" title="statement not covered" >shippingData;</span>
&nbsp;
            const modified_date = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");</span>
            const formatedDate = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(CreatedDate, "sfUpdate");</span>
&nbsp;
            const shipDetails = <span class="cstat-no" title="statement not covered" >{ sf_record_id, full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt: modified_date, package_deliverted_at, createdAt: formatedDate, sf_name };</span>
&nbsp;
            const [creationResponse, created] = <span class="cstat-no" title="statement not covered" >await db.CreationOrderShipping.findOrCreate({</span>
              where: {
                sf_record_id
              },
              defaults: shipDetails,
              transaction
            });
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (!created) <span class="cstat-no" title="statement not covered" >await creationResponse.update({ full_mailing_address, ship_date, ship_drop_off_date, tracking_url, courier, sf_updatedAt: modified_date, package_deliverted_at, createdAt: formatedDate }, { transaction });</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (creationResponse) <span class="cstat-no" title="statement not covered" >shipping_id = creationResponse?.id || null;</span></span>
          }
<span class="cstat-no" title="statement not covered" >          if (shipping_id) <span class="cstat-no" title="statement not covered" >insertOrderDetails.shipping_id = shipping_id;</span></span>
        }
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Record_Type_Name__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.record_type = orderDetails.Record_Type_Name__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Install_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.install_date = orderDetails?.Install_Date__c;</span></span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (dbModel === "InternetElDisconnectOrder" &amp;&amp; orderDetails?.Requested_Disconnect_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.requested_disconnect_date = orderDetails?.Requested_Disconnect_Date__c;</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (dbModel === "InternetElSwapOrder" &amp;&amp; orderDetails?.Stage__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.stage = orderDetails?.Stage__c;</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (dbModel === "InternetElSpeedChangeOrder") {</span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Expected_Completion_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.expected_completion_date = orderDetails?.Expected_Completion_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Speed__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.speed = orderDetails?.Speed__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Stage__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.stage = orderDetails?.Stage__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Response_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.response_date = getCurrentAtlanticTime(orderDetails?.Response_Date__c);</span></span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (dbModel === "InternetElMoveOrder") {</span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Requested_Move_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.requested_move_date = orderDetails?.Requested_Move_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Requested_Install_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.requested_install_date = orderDetails?.Requested_Install_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Install_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.install_date = orderDetails?.Install_Date__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Stage__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.stage = orderDetails?.Stage__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Install_Time__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.install_time = orderDetails?.Install_Time__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Order_Reject_Reason__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.reject_reason = orderDetails?.Order_Reject_Reason__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Order_Reject_Reason_Solved__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.reject_reason_solved = orderDetails?.Order_Reject_Reason_Solved__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_Suite_Unit__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.unit = orderDetails?.Service_Suite_Unit__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_Street_Number__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.street_number = orderDetails?.Service_Street_Number__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_Street_Name__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.street_name = orderDetails?.Service_Street_Name__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_City_Town__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.city = orderDetails?.Service_City_Town__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_Province__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.province = orderDetails?.Service_Province__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Service_Postal_Code__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.postal_code = orderDetails?.Service_Postal_Code__c;</span></span>
<span class="cstat-no" title="statement not covered" >        if (orderDetails?.Submit_Date__c) <span class="cstat-no" title="statement not covered" >insertOrderDetails.submit_date = getCurrentAtlanticTime(orderDetails.Submit_Date__c);</span></span>
      }
&nbsp;
      let [orderInsert, created] = <span class="cstat-no" title="statement not covered" >await db[dbModel].findOrCreate({</span>
        where: {
          sf_record_id: orderDetails?.Id
        },
        defaults: insertOrderDetails, transaction
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!created) <span class="cstat-no" title="statement not covered" >await orderInsert.update(insertOrderDetails, { transaction });</span></span>
<span class="cstat-no" title="statement not covered" >      return orderInsert?.id;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("AuthService getOrderDetailsId -&gt; ", error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
<span class="fstat-no" title="function not covered" >  as</span>ync getTechDetailsId(techId, dbModel, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      if (!techId) <span class="cstat-no" title="statement not covered" >return null;</span></span>
      const techDetails = <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.getTechAppDetails(techId);</span>
<span class="cstat-no" title="statement not covered" >      if (!techDetails) <span class="cstat-no" title="statement not covered" >return null;</span></span>
      const { Id: sf_record_id, Install_Date__c: install_date, Install_Time__c: install_time, LastModifiedDate: sf_updatedAt, CreatedDate, Name: sf_name } = <span class="cstat-no" title="statement not covered" >techDetails;</span>
&nbsp;
      const formatedDate = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(CreatedDate, "sfUpdate");</span>
      const modifiedDate = <span class="cstat-no" title="statement not covered" >getCurrentAtlanticTime(sf_updatedAt, "sfUpdate");</span>
&nbsp;
      const techAppDetails = <span class="cstat-no" title="statement not covered" >{ sf_record_id, install_date, install_time, sf_updatedAt: formatedDate, createdAt: modifiedDate, sf_name };</span>
&nbsp;
      let [techInsert, created] = <span class="cstat-no" title="statement not covered" >await db[dbModel].findOrCreate({</span>
        where: {
          sf_record_id
        },
        defaults: techAppDetails, transaction
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!created) <span class="cstat-no" title="statement not covered" >await techInsert.update(techAppDetails, { transaction });</span></span>
<span class="cstat-no" title="statement not covered" >      return techInsert?.id;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("AuthService getTechDetailsId -&gt; ", error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync insertCustomerSubscriptionFromChargebee(subscriptionData, customer_details_id, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
&nbsp;
      const { subscription: { id: cb_subscription_id, billing_period_unit, plan_amount: amount, status, next_billing_at, created_at, activated_at: activated_on, total_dues, payment_source_id } } = <span class="cstat-no" title="statement not covered" >subscriptionData;</span>
&nbsp;
      const subscription_type = <span class="cstat-no" title="statement not covered" >billindTypeCbSubs(billing_period_unit);</span>
&nbsp;
      const customerSubsDetails = <span class="cstat-no" title="statement not covered" >{</span>
        customer_details_id,
        cb_subscription_id,
        amount: amount ? amount / 100 : null,
        balance_due: total_dues ? total_dues / 100 : 0.00,
        activated_on: activated_on ? moment.unix(activated_on).format('YYYY-MM-DD HH:mm:ss') : null,
        next_billing_at: next_billing_at ? moment.unix(next_billing_at).format('YYYY-MM-DD HH:mm:ss') : null,
        createdAt: created_at ? moment.unix(created_at).format('YYYY-MM-DD HH:mm:ss') : null,
        status: status,
        subscription_type
      };
&nbsp;
      let [subscriptionInsert, created] = <span class="cstat-no" title="statement not covered" >await db.CustomerSubscriptions.findOrCreate({</span>
        where: {
          cb_subscription_id
        },
        defaults: customerSubsDetails, transaction
      });
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!created) <span class="cstat-no" title="statement not covered" >await subscriptionInsert.update(customerSubsDetails, { transaction });</span></span>
<span class="cstat-no" title="statement not covered" >      if (payment_source_id &amp;&amp; subscriptionInsert?.id) <span class="cstat-no" title="statement not covered" >await this.mapSubscriptionCard(payment_source_id, subscriptionInsert?.id, transaction);</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      return subscriptionInsert;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService insertCustomerSubscriptionFromChargebee -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync mapSubscriptionCard(cb_card_id, customer_subscription_id, transaction) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const checkCardExist = <span class="cstat-no" title="statement not covered" >await db.ContactsCardDetails.findOne({ where: { cb_card_id }, transaction });</span>
<span class="cstat-no" title="statement not covered" >      if (checkCardExist) {</span>
        const { id: contact_card_id, contact_id } = <span class="cstat-no" title="statement not covered" >checkCardExist;</span>
        const cardMappingData = <span class="cstat-no" title="statement not covered" >{ contact_id, contact_card_id, customer_subscription_id, is_primary: "1" };</span>
        let [cardMappingDetails, created] = <span class="cstat-no" title="statement not covered" >await db.SubscriptionCardMapping.findOrCreate({</span>
          where: { contact_id, contact_card_id, customer_subscription_id },
          defaults: cardMappingData,
          transaction, // Passing the transaction object
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (!created) <span class="cstat-no" title="statement not covered" >await cardMappingDetails.update(cardMappingData, { transaction });</span></span>
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService mapSubscriptionCard -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Method for user cognito account creation
<span class="fstat-no" title="function not covered" >  as</span>ync createCognitoAccount(payload, elasticLogObj) {
    const { email } = <span class="cstat-no" title="statement not covered" >payload;</span>
    const { _id, _index } = <span class="cstat-no" title="statement not covered" >await elasticSearch.insertDocument("cognito_user_creation_logs", { ...elasticLogObj, request: payload });</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Check if the user already exists
      const { userExist, userData } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.NOT_FOUND, `User ${RESPONSE_MESSAGES.NOT_FOUND.toLowerCase()}`);</span></span>
      const cGResponse = <span class="cstat-no" title="statement not covered" >await this.checkUserInCognito(email);</span>
      const { message, cognitoId, status } = <span class="cstat-no" title="statement not covered" >cGResponse;</span>
      let data;
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (status &amp;&amp; message === "Previously user exist in cognito. User updated.") {</span>
<span class="cstat-no" title="statement not covered" >        data = { cognitoId };</span>
<span class="cstat-no" title="statement not covered" >        if (userData.sf_record_id) <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: cognitoId, sf_record_id: userData.sf_record_id }, "cognitoUpdate");</span></span>
<span class="cstat-no" title="statement not covered" >        await this.updateUserCognitoId(cognitoId, userData.id);</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { response: cGResponse });</span></span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (status &amp;&amp; message === "User not exist in cognito. New user created.") {</span>
        const password = <span class="cstat-no" title="statement not covered" >passwordGenerator.generate({</span>
          length: 10, numbers: true, symbols: true, uppercase: true, lowercase: true, strict: true, exclude: '#`&amp;+?()_={}[]:;"\'&lt;&gt;,./|\\'
        });
        const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.register(email, password, "send");</span>
<span class="cstat-no" title="statement not covered" >        if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >await elasticSearch.updateDocument(_index, _id, { response });</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
          const { User } = <span class="cstat-no" title="statement not covered" >response;</span>
<span class="cstat-no" title="statement not covered" >          if (User &amp;&amp; User?.Username) {</span>
<span class="cstat-no" title="statement not covered" >            data = { cognitoId: User?.Username };</span>
<span class="cstat-no" title="statement not covered" >            if (userData.sf_record_id) <span class="cstat-no" title="statement not covered" >await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: User.Username, sf_record_id: userData.sf_record_id, cp_created_dateTime: User?.UserCreateDate, cp_status: "inactive" }, "cognitoUpdate");</span></span>
<span class="cstat-no" title="statement not covered" >            await this.updateUserCognitoId(User.Username, userData.id);</span>
          }
        }
      }
<span class="cstat-no" title="statement not covered" >      return { status: true, data, message };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (_id &amp;&amp; _index) <span class="cstat-no" title="statement not covered" >elasticSearch.updateDocument(_index, _id, { "log.level": 'ERROR', response: { error: error.message } });</span></span>
<span class="cstat-no" title="statement not covered" >      console.error(`AuthService createCognitoAccount -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
<span class="fstat-no" title="function not covered" >  as</span>ync checkUserInCognito(email) {
    const returnStatus = <span class="cstat-no" title="statement not covered" >{ status: false, cognitoId: null, message: null }</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.getUserDetail(email);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
        const { Username } = <span class="cstat-no" title="statement not covered" >response;</span>
<span class="cstat-no" title="statement not covered" >        if (Username) {</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.cognitoId = Username;</span>
<span class="cstat-no" title="statement not covered" >          returnStatus.message = "Previously user exist in cognito. User updated."</span>
        }
      }
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      if (error?.__type === "UserNotFoundException") {</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.status = true;</span>
<span class="cstat-no" title="statement not covered" >        returnStatus.message = "User not exist in cognito. New user created.";</span>
      } else {
<span class="cstat-no" title="statement not covered" >        console.error(`AuthService checkUserInCognito -&gt;`, error);</span>
<span class="cstat-no" title="statement not covered" >        throw error;</span>
      }
    }
<span class="cstat-no" title="statement not covered" >    return returnStatus;</span>
  }
<span class="fstat-no" title="function not covered" >  as</span>ync updateUserCognitoId(aws_cognito_id, id) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await db.Contacts.update({ aws_cognito_id }, { where: { id } });</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Delete a contact and all related data from the system
<span class="fstat-no" title="function not covered" >  as</span>ync deleteContact(payload) {
    const { email } = <span class="cstat-no" title="statement not covered" >payload;</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Check if user exists
      const { userExist, userData } = <span class="cstat-no" title="statement not covered" >await this.checkUserExist(email);</span>
<span class="cstat-no" title="statement not covered" >      if (!userExist) <span class="cstat-no" title="statement not covered" >throw new CustomError(RESPONSE_CODES.CONFLICT, RESPONSE_MESSAGES.NOT_FOUND);</span></span>
&nbsp;
      let cognitoDeleted = <span class="cstat-no" title="statement not covered" >false;</span>
&nbsp;
      // If user has aws_cognito_id, try to delete from Cognito
<span class="cstat-no" title="statement not covered" >      if (userData.aws_cognito_id) {</span>
        const response = <span class="cstat-no" title="statement not covered" >await awsCognitoConnection.deleteUser(email);</span>
<span class="cstat-no" title="statement not covered" >        if (response &amp;&amp; response?.$metadata?.httpStatusCode == 200) {</span>
<span class="cstat-no" title="statement not covered" >          cognitoDeleted = true;</span>
        }
      } else {
<span class="cstat-no" title="statement not covered" >        cognitoDeleted = true; </span>// If no aws_cognito_id, consider it deleted
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (!cognitoDeleted) {</span>
<span class="cstat-no" title="statement not covered" >        return { status: false, message: "Delete Failed in aws cognito" };</span>
      }
&nbsp;
      // Always attempt to delete local/contact data
      const { id: contactId } = <span class="cstat-no" title="statement not covered" >userData;</span>
&nbsp;
      // Update Salesforce profile if needed
<span class="cstat-no" title="statement not covered" >      if (userData.sf_record_id) {</span>
<span class="cstat-no" title="statement not covered" >        await salesforceServiceClient.updateProfileDetails({ aws_cognito_id: null, sf_record_id: userData.sf_record_id, cp_status: "inactive" }, "cognitoUpdate");</span>
      }
&nbsp;
      // Delete all related customer details and associated records
      const result = <span class="cstat-no" title="statement not covered" >await this.deleteOtherContactDetails(contactId);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (result.status) {</span>
<span class="cstat-no" title="statement not covered" >        return { status: true, message: "Delete successful." };</span>
      }
<span class="cstat-no" title="statement not covered" >      return { status: false, message: "Delete failed. Please try again." };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("deleteContact error:", error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Delete all customer details and related records for a contact
<span class="fstat-no" title="function not covered" >  as</span>ync deleteOtherContactDetails(contact_id) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      // Find all customer details for the contact
      let customerDetails = <span class="cstat-no" title="statement not covered" >await db.CustomerDetails.findAll({</span>
        where: { contact_id }
      });
&nbsp;
      // Delete each customer detail and its dependencies
<span class="cstat-no" title="statement not covered" >      if (customerDetails.length) {</span>
<span class="cstat-no" title="statement not covered" >        for (const customerDetail of customerDetails) {</span>
<span class="cstat-no" title="statement not covered" >          await this.deleteCustomerDetailsFromDb(customerDetail);</span>
        }
      }
&nbsp;
      // If no more customer details, delete the contact itself
      const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "contact_id", contact_id);</span>
<span class="cstat-no" title="statement not covered" >      if (checkCount === 0) {</span>
<span class="cstat-no" title="statement not covered" >        await this.deleteDataFromDb("Contacts", contact_id);</span>
<span class="cstat-no" title="statement not covered" >        return { status: true };</span>
      }
<span class="cstat-no" title="statement not covered" >      return { status: false };</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Delete a single customer detail and all associated records (addresses, services, etc.)
<span class="fstat-no" title="function not covered" >  as</span>ync deleteCustomerDetailsFromDb(customerDetails) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const { service_address_id, mailing_address_id, internet_id, tv_id, phone_id, id } = <span class="cstat-no" title="statement not covered" >customerDetails;</span>
&nbsp;
      // Handle service address deletion if only referenced by this customer detail
<span class="cstat-no" title="statement not covered" >      if (service_address_id) {</span>
        const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "service_address_id", service_address_id)</span>
<span class="cstat-no" title="statement not covered" >        if (checkCount === 1) {</span>
          // Set service_address_id to null before deleting address
<span class="cstat-no" title="statement not covered" >          await this.updateNullinDb("CustomerDetails", "service_address_id", id);</span>
<span class="cstat-no" title="statement not covered" >          await this.deleteDataFromDb("CustomerAddresses", service_address_id);</span>
        }
      }
&nbsp;
      // Handle mailing address deletion if only referenced by this customer detail
<span class="cstat-no" title="statement not covered" >      if (mailing_address_id) {</span>
        const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "mailing_address_id", mailing_address_id)</span>
<span class="cstat-no" title="statement not covered" >        if (checkCount === 1) {</span>
          // Set mailing_address_id to null before deleting address
<span class="cstat-no" title="statement not covered" >          await this.updateNullinDb("CustomerDetails", "mailing_address_id", id);</span>
<span class="cstat-no" title="statement not covered" >          await this.deleteDataFromDb("CustomerAddresses", mailing_address_id);</span>
        }
      }
&nbsp;
      // Handle internet service and its related orders
<span class="cstat-no" title="statement not covered" >      if (internet_id) {</span>
        const internetDetail = <span class="cstat-no" title="statement not covered" >await db.CustomerInternet.findOne({ where: { id: internet_id } });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (internetDetail) {</span>
          const { creation_order, tech_appointment, disconnect_order, speed_change_order } = <span class="cstat-no" title="statement not covered" >internetDetail;</span>
&nbsp;
          // Handle creation order and its shipping
<span class="cstat-no" title="statement not covered" >          if (creation_order) {</span>
            const shipping_details = <span class="cstat-no" title="statement not covered" >await db.InternetElCreationOrder.findOne({ where: { id: creation_order } });</span>
            const shipping_id = <span class="cstat-no" title="statement not covered" >shipping_details?.shipping_id;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (shipping_id) {</span>
              const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("InternetElCreationOrder", "shipping_id", shipping_id)</span>
<span class="cstat-no" title="statement not covered" >              if (checkCount === 1) {</span>
                // Set shipping_id to null before deleting shipping
<span class="cstat-no" title="statement not covered" >                await this.updateNullinDb("InternetElCreationOrder", "shipping_id", creation_order);</span>
<span class="cstat-no" title="statement not covered" >                await this.deleteDataFromDb("CreationOrderShipping", shipping_id);</span>
              }
            }
            const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerInternet", "creation_order", creation_order)</span>
<span class="cstat-no" title="statement not covered" >            if (checkCount === 1) {</span>
              // Set creation_order to null before deleting order
<span class="cstat-no" title="statement not covered" >              await this.updateNullinDb("CustomerInternet", "creation_order", internet_id);</span>
<span class="cstat-no" title="statement not covered" >              await this.deleteDataFromDb("InternetElCreationOrder", creation_order);</span>
            }
          }
&nbsp;
          // Handle tech appointment
<span class="cstat-no" title="statement not covered" >          if (tech_appointment) {</span>
            const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerInternet", "tech_appointment", tech_appointment)</span>
<span class="cstat-no" title="statement not covered" >            if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >              await this.updateNullinDb("CustomerInternet", "tech_appointment", internet_id); </span>//first "tech_appointment" set null in "CustomerInternet" table before deleting the appointment
<span class="cstat-no" title="statement not covered" >              await this.deleteDataFromDb("InternetElTechAppointment", tech_appointment);</span>
            }
          }
&nbsp;
          // Handle disconnect order
<span class="cstat-no" title="statement not covered" >          if (disconnect_order) {</span>
            const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerInternet", "disconnect_order", disconnect_order)</span>
<span class="cstat-no" title="statement not covered" >            if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >              await this.updateNullinDb("CustomerInternet", "disconnect_order", internet_id); </span>//first "disconnect_order" set null in "CustomerInternet" table before deleting the order
<span class="cstat-no" title="statement not covered" >              await this.deleteDataFromDb("InternetElDisconnectOrder", disconnect_order);</span>
            }
          }
&nbsp;
          // Handle speed change order
<span class="cstat-no" title="statement not covered" >          if (speed_change_order) {</span>
            const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerInternet", "speed_change_order", speed_change_order)</span>
<span class="cstat-no" title="statement not covered" >            if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >              await this.updateNullinDb("CustomerInternet", "speed_change_order", internet_id); </span>//first "speed_change_order" set null in "CustomerInternet" table before deleting the order
<span class="cstat-no" title="statement not covered" >              await this.deleteDataFromDb("InternetElSpeedChangeOrder", speed_change_order);</span>
            }
          }
        }
&nbsp;
        // Delete internet service if only referenced by this customer detail
        const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "internet_id", internet_id)</span>
<span class="cstat-no" title="statement not covered" >        if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >          await this.updateNullinDb("CustomerDetails", "internet_id", id); </span>//first "internet_id" set null in "CustomerDetails" table before deleting the internet
<span class="cstat-no" title="statement not covered" >          await this.deleteDataFromDb("CustomerInternet", internet_id);</span>
        }
      }
&nbsp;
      // Handle phone service deletion
<span class="cstat-no" title="statement not covered" >      if (phone_id) {</span>
        const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "phone_id", phone_id)</span>
<span class="cstat-no" title="statement not covered" >        if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >          await this.updateNullinDb("CustomerDetails", "phone_id", id); </span>//first "phone_id" set null in "CustomerDetails" table before deleting the phone
<span class="cstat-no" title="statement not covered" >          await this.deleteDataFromDb("CustomerPhone", phone_id);</span>
        }
      }
&nbsp;
      // Handle TV service deletion
<span class="cstat-no" title="statement not covered" >      if (tv_id) {</span>
        const checkCount = <span class="cstat-no" title="statement not covered" >await this.getCountFromDb("CustomerDetails", "tv_id", tv_id)</span>
<span class="cstat-no" title="statement not covered" >        if (checkCount === 1) {</span>
<span class="cstat-no" title="statement not covered" >          await this.updateNullinDb("CustomerDetails", "tv_id", id); </span>//first "tv_id" set null in "CustomerDetails" table before deleting the tv
<span class="cstat-no" title="statement not covered" >          await this.deleteDataFromDb("CustomerTv", tv_id);</span>
        }
      }
&nbsp;
      // Finally, delete the customer detail itself
<span class="cstat-no" title="statement not covered" >      await this.deleteDataFromDb("CustomerDetails", id);</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error("Error deleteCustomerDetailsFromDb -&gt; ", error);</span>
    }
  }
&nbsp;
  // Helper: Delete a row from a table by id
<span class="fstat-no" title="function not covered" >  as</span>ync deleteDataFromDb(tableName, id) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await db[tableName].destroy({</span>
        where: { id }
      });
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Helper: Count rows in a table where column = value
<span class="fstat-no" title="function not covered" >  as</span>ync getCountFromDb(tableName, columnName, value) {
<span class="cstat-no" title="statement not covered" >    try {</span>
      const count = <span class="cstat-no" title="statement not covered" >await db[tableName].count({</span>
        where: { [columnName]: value }
      });
<span class="cstat-no" title="statement not covered" >      return count;</span>
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`getCountFromDb error:`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
&nbsp;
  // Helper: Set a column to null for a given row (by id)
<span class="fstat-no" title="function not covered" >  as</span>ync updateNullinDb(tableName, columnName, id) {
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      await db[tableName].update(</span>
        { [columnName]: null },
        { where: { id } }
      );
    } catch (error) {
<span class="cstat-no" title="statement not covered" >      console.error(`updateNullinDb error:`, error);</span>
<span class="cstat-no" title="statement not covered" >      throw error;</span>
    }
  }
}
&nbsp;
<span class="cstat-no" title="statement not covered" >module.exports = AuthServices;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-18T12:27:00.911Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    