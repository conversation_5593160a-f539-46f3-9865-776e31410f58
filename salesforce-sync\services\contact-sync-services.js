const pool = require("../db");
const SalesforceClient = require('../clients/salesforce-client');
const CONFIG = require('../config');
const CustomerServices = require("../services/customer-sync-services");
const { getCurrentAtlanticTime } = require("../helper/custom-helper");
const ElasticSearch = require("../clients/elastic-search");
const elasticSearch = new ElasticSearch();

const salesforceConnection = new SalesforceClient(
    CONFIG.salesforce.baseurl,
    CONFIG.salesforce.username,
    CONFIG.salesforce.password,
    CONFIG.salesforce.token
);

const CHUNKSIZE = 100;

class ContactSyncServices {

    // Check contact Exist
    async checkUserExist(whereQuery) {
        try {
            const query = `SELECT id FROM contacts WHERE ${whereQuery}`;

            const res = await this.executeQuery(query);
            return {
                contactExist: res.length > 0,
                contactId: res.length ? res[0].id : null
            };
        } catch (error) {
            console.error("Check User Exist -> ", error);
        }
    }

    // Get contacts status whether exist in database or not  
    async checkAndManageContacts(sfContactDetails) {
        try {
            const existingContacts = [];
            for (const sfContactDetail of sfContactDetails) {
                existingContacts.push({ sfContactDetail });
                if (existingContacts.length >= CHUNKSIZE) {
                    await this.bulkUpdateContacts(existingContacts);
                    existingContacts.length = 0;
                }
            }
            if (existingContacts.length) await this.bulkUpdateContacts(existingContacts);
        } catch (error) {
            console.error("SalesforceService getAllContactDetails -> ", error);
        }
    }

    async bulkUpdateContacts(updateQueries) {
        try {
            if (!updateQueries.length) return;
            const updatedTime = getCurrentAtlanticTime();
            const updateStatement = `
                UPDATE contacts
                SET 
                    updatedAt = '${updatedTime}',
                    first_name = CASE ${this.buildCaseStatement(updateQueries, 'FirstName', "cleanName")} END,
                    last_name = CASE ${this.buildCaseStatement(updateQueries, 'LastName', "cleanName")} END,
                    sf_name = CASE ${this.buildCaseStatement(updateQueries, 'Name', "cleanName")} END,
                    company_name = CASE ${this.buildCaseStatement(updateQueries, 'Company_Name__c', "cleanName")} END,
                    cell_phone = CASE ${this.buildCaseStatement(updateQueries, 'Primary_Phone__c', "cleanPhone")} END,
                    secondary_phone = CASE ${this.buildCaseStatement(updateQueries, 'Alternate_Phone_Number__c', "cleanPhone")} END,
                    additional_name = CASE ${this.buildCaseStatement(updateQueries, 'Contact_Name_if_different_than_end_user__c')} END,
                    sticky_sender = CASE ${this.buildCaseStatement(updateQueries, 'Sticky_Sender__c')} END,
                    referral_link = CASE ${this.buildCaseStatement(updateQueries, 'Referral_Link_Full__c', "cleanName")} END,
                    cb_customer_id = CASE ${this.buildCaseStatement(updateQueries, 'Account_Chargebee_Customer_ID__c')} END,
                    total_referral = CASE ${this.buildCaseStatement(updateQueries, 'Total_Referrals__c', "cleanPrice")} END,
                    total_paid_referral = CASE ${this.buildCaseStatement(updateQueries, 'Total_Paid_Referrals__c', "cleanPrice")} END,
                    total_earned_referral = CASE ${this.buildCaseStatement(updateQueries, 'Total_Earned_Referrals__c', "cleanPrice")} END,
                    sf_updatedAt = CASE ${this.buildCaseStatement(updateQueries, 'LastModifiedDate')} END
                    WHERE sf_record_id IN (${updateQueries.map(query => `'${query.sfContactDetail.Id}'`).join(', ')})
            `;
            await this.executeQuery(updateStatement);

        } catch (error) {
            console.error("SalesforceService bulkUpdateContacts -> ", error);
        }
    }

    buildCaseStatement(queries, field, type) {
        return queries.map(query => {
            let value = query.sfContactDetail[field];

            // Handle NULL and empty values early
            if (value === undefined || value === null || value === '') {
                return `WHEN sf_record_id = '${query.sfContactDetail.Id}' THEN NULL`;
            }

            if (field === 'Company_Name__c' && typeof value === 'string') {
                value = value.substring(0, 49);
            }

            // Type-specific cleaning
            if (type === "cleanPhone" && typeof value === 'string') {
                value = value.replace(/\D/g, ''); // keep only digits
            }

            if (type === "cleanPrice") {
                value = parseFloat(value);
                return isNaN(value)
                    ? `WHEN sf_record_id = '${query.sfContactDetail.Id}' THEN 0`
                    : `WHEN sf_record_id = '${query.sfContactDetail.Id}' THEN ${value}`;
            }

            if (field === "LastModifiedDate") {
                value = getCurrentAtlanticTime(value);
            }

            // Sanitize for SQL
            if (typeof value === 'string') {
                value = value
                    .replace(/'/g, "''")     // Escape single quotes
                    .replace(/["`;]/g, '')   // Remove dangerous characters
                    .replace(/--/g, '')      // Remove SQL comment start
                    .replace(/[\n\r]/g, ' '); // Normalize newlines to space
            }

            return `WHEN sf_record_id = '${query.sfContactDetail.Id}' THEN '${value}'`;
        }).join(' ');
    }

    async executeQuery(query, queryValues = []) {
        return new Promise((resolve, reject) => {
            pool.query(query, queryValues, (error, results) => {
                if (error) reject(error);
                else resolve(results);
            });
        });
    }

    async checkAndDeleteDetails(deletedData) {
        try {
            for (const sltData of deletedData) {
                const { id } = sltData;
                const whereQuery = `sf_record_id = '${id}'`;
                const { contactExist, contactId } = await this.checkUserExist(whereQuery);
                if (contactExist) await this.deleteOtherContactDetails(contactId);
            }
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
        }
    }

    async deleteOtherContactDetails(contactId) {
        try {
            const query = `SELECT * FROM contacts_customer_details WHERE contact_id = '${contactId}'`;
            const customerDetails = await this.executeQuery(query);
            const customerServices = new CustomerServices();
            if (customerDetails.length) {
                for (const customerDetail of customerDetails) {
                    await customerServices.deleteCustomerDetailsFromDb(customerDetail);
                }
            }
            await customerServices.deleteQuery("contacts", contactId);
            return { execute: true, status: true, contactId }
        } catch (error) {
            console.error("SalesforceService deletedData -> ", error);
            return { execute: true, status: false, contactId, error: error?.message || null }
        }
    }
}

module.exports = ContactSyncServices;