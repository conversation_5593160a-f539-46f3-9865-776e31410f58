import React, { useEffect, useRef, useState } from "react";
import { LineIcon } from "../../assets/Icons";
import "../../assets/scss/pages/viewStatements.scss";
import StatementList from "./StatementList";
import useMediaQuery from "../../hooks/MediaQueryHook";

interface StatementProps {
  planData: any;
}

const Statements: React.FC<StatementProps> = ({ planData }) => {
  const [swipeStatement, setSwipeStatement] = useState<boolean>(false);
  const viewStatementRef = useRef<HTMLDivElement>(null);
  const [startY, setStartY] = useState<number>(0);
  const [endY, setEndY] = useState<number>(0);

  const isDesktop = useMediaQuery("(min-width:1025px)");

  useEffect(() => {
    let load = true;
    setTimeout(() => {
      load = false;
    }, 500);

    // Function to handle click outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        viewStatementRef.current &&
        !viewStatementRef.current.contains(event.target as Node) &&
        swipeStatement
      ) {
        !load && setSwipeStatement(!swipeStatement);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [swipeStatement]);

  // Handle touch start
  const handleTouchStart = (event: React.TouchEvent<HTMLDivElement>) => {
    setStartY(event.touches[0].clientY);
  };

  // Handle touch move
  const handleTouchMove = (event: React.TouchEvent<HTMLDivElement>) => {
    setEndY(event.touches[0].clientY);
  };

  // Handle touch end
  const handleTouchEnd = () => {
    const swipeDistance = startY - endY;
    if (swipeDistance > 10 && !swipeStatement) {
      setSwipeStatement(true); // Swipe up
    } else if (swipeDistance < -10 || swipeStatement) {
      setSwipeStatement(false); // Swipe down
    }
  };

  return (
    <div>
      <div className="max-lg:hidden">
        <h4 className="text-base font-medium text-primary uppercase">Statements</h4>
      </div>
      <div
        ref={viewStatementRef}
        className={`view-statement 2xl:mt-7 mt-5 lg:relative max-lg:bg-white max-lg:shadow-cardShadow07 max-lg:rounded-20 ${
          swipeStatement && !isDesktop && "active popup-open"
        }`}
      >
        {!isDesktop && (
          <div
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            className="flex flex-col text-center justify-center items-center gap-5 pb-5 pt-30 cursor-pointer shadow-cardShadow07"
          >
            <div>
              <LineIcon />
            </div>
            <div className="font-medium uppercase text-base">
              View Statements
            </div>
          </div>
        )}

        <div className="flex flex-col 2xl:gap-10 xl:gap-30 lg:gap-5 max-lg:overflow-y-auto max-lg:h-[calc(80%-100px)]">
          <div className="bg-[#FBF8FF] border border-[#EBDBFF] 2xl:py-7 2xl:px-5 px-4 py-5 rounded-20 mx-3 lg:mx-0">
            <StatementList id={planData?.id} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Statements;
