import React from "react";
import { useSelector } from "react-redux";
import { customerSubscriptionType } from "../../../store/selectors/customerSubscriptionSelectors";
import { formatCurrency } from "../../../utils/helper";
import Button from "../../common/Button";

type BaseTVPackageProps = {
  list: any;
  currentPackage: string;
  basePackage: string;
  handlePackageSelect: (value: any) => void;
  handleViewAll: (event: React.MouseEvent<HTMLElement>, listData: any) => void;
};
const BaseTVPackage: React.FC<BaseTVPackageProps> = ({
  list,
  basePackage,
  handlePackageSelect,
  handleViewAll,
}) => {
  const subscriptionType = useSelector(customerSubscriptionType);
  return (
    <div
      className={`w-[250px] xl:w-[280px] border-[3px] border-[#F5EFFF] p-3 rounded-20  cursor-pointer duration-300`}
    >
      <div className={`flex gap-3 rounded-10 flex-col justify-between h-full`}>
        <h1 className="font-anton uppercase text-2xl mt-3 lg:text-[26px] leading-tight">
          {list?.name}
        </h1>

        {/* Price */}
        <h1 className="font-anton uppercase text-2xl lg:text-[26px] flex items-center">
          {formatCurrency(list?.billing_period?.[0]?.[subscriptionType]?.price)}
          <span className="text-sm lowercase ml-2">
            /{subscriptionType === "monthly" ? "month" : "year"}
          </span>
        </h1>

        {/* View channels */}
        <div
          className="text-[#AA7DE6]"
          onClick={(event) => handleViewAll(event, list)}
        >
          View channels
        </div>

        {/* featured channels  images */}
        <div className="grid grid-cols-3 gap-2">
          {list?.featured_channels?.map((channel: any, index: number) => (
            <div key={index}>
              <img
                className="w-[50px] h-[50px] object-contain"
                src={channel?.image_url}
                alt={channel?.name}
              />
            </div>
          ))}
        </div>

        {/* Description */}
        <p>{list?.description}</p>

        <Button
          clickEvent={() => handlePackageSelect(list)}
          className={`!rounded-30 !transition-all !duration-150 ${
            basePackage === list?.api_name ? "!font-bold" : ""
          }`}
          title={basePackage === list?.api_name ? "Added" : "Add"}
        />
      </div>
    </div>
  );
};

export default BaseTVPackage;
