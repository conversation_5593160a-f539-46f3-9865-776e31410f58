import {
  AddressChangeDateDescription,
  convertDateStringToReadableDate,
  formatDateToMonthDayYear,
} from "../../../utils/helper";
import { CancelIcon, EditIcon } from "../../../assets/Icons";
import React, { useState } from "react";
import {
  currentAddress,
  moveDate,
  newAddress,
  serviceStatus,
  showServiceAddressConfirmationPopup,
  showServiceAddressDateSelectorPopup,
  showServiceAddressPopup,
  showFibreAddressPopup,
} from "../../../store/selectors/serviceAddressSelectors";
import {
  setCurrentServiceAddress,
  setMoveDate,
  setNewServiceAddress,
  setServiceStatus,
  toggleShowFibreAddressPopup,
  toggleShowServiceAddressConfirmationPopup,
  toggleShowServiceAddressDateSelectorPopup,
  toggleShowServiceAddressPopup,
} from "../../../store/reducers/serviceAddressReducer";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import AddLocationPopup from "../../dashboard/AddLocationPopup";
import ConfirmationMessagePopup from "../../common/ConfirmationMessagePopup";
import Popup from "../../common/Popup";
import ServiceBox from "../ServiceBox";
import ServiceDateSelector from "./ServiceDateSelector";
import { addNotification } from "../../../store/reducers/toasterReducer";
import { logout } from "../../../store/reducers/authenticationReducer";
import { useUpdateAddressMutation } from "../../../services/api";

// Interface for ServiceAddressProps
interface ServiceAddressProps {
  refetch: () => void;
  isMoveOrder: any;
  planData: any;
}

const ServiceAddressBox: React.FC<ServiceAddressProps> = ({
  refetch,
  isMoveOrder,
  planData,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const currAddress = useSelector(currentAddress);
  const changedAddress = useSelector(newAddress);
  const requestedMoveDate = useSelector(moveDate);
  const status = useSelector(serviceStatus);
  const showPopup = useSelector(showServiceAddressPopup);
  const showDatePopup = useSelector(showServiceAddressDateSelectorPopup);
  const showFibrePopup = useSelector(showFibreAddressPopup);
  const [updateAddress, updateAddressLoading] = useUpdateAddressMutation();
  const showConfirmationPopup = useSelector(
    showServiceAddressConfirmationPopup
  );

  // logic for creating move order as per notion document
  const enableCreateMoveorder =
    planData?.cp_stage === "Online" &&
    (planData?.customerInternet?.internetElSpeedChangeOrder === null ||
      (planData?.customerInternet?.internetElSpeedChangeOrder !== null &&
        planData?.customerInternet?.internetElSpeedChangeOrder?.stage ===
          "Complete")) &&
    (planData?.customerInternet?.internetElSwapOrder === null ||
      (planData?.customerInternet?.internetElSwapOrder !== null &&
        planData?.customerInternet?.internetElSwapOrder?.stage ===
          "Complete")) &&
    (planData?.customerInternet?.internetElMoveOrder === null ||
      (planData?.customerInternet?.internetElMoveOrder !== null &&
        planData?.customerInternet?.internetElMoveOrder?.stage === "Complete"));

  const enableEditMoveOrder =
    planData?.customerInternet?.internetElMoveOrder !== null &&
    planData?.customerInternet?.internetElMoveOrder?.submit_date === null;

  const anyPendingOrderExists =
    (planData?.customerInternet?.internetElSpeedChangeOrder !== null &&
      planData?.customerInternet?.internetElSpeedChangeOrder?.stage !==
        "Complete") ||
    (planData?.customerInternet?.internetElSwapOrder !== null &&
      planData?.customerInternet?.internetElSwapOrder?.stage !== "Complete") ||
    (planData?.customerInternet?.internetElMoveOrder !== null &&
      planData?.customerInternet?.internetElMoveOrder?.stage !== "Complete" &&
      planData?.customerInternet?.internetElMoveOrder?.submit_date !== null);
  const initialAddressState = {
    address: "",
    apartment: "",
    city: "",
    province: "",
    pinCode: "",
    service_change_date: "",
  };

  const [serviceAddressData, setServiceAddressDate] =
    useState<any>(initialAddressState);
  const [selectedDate, setSelectedDate] = useState<Date | null | string>(null);
  const [showPopupForCancelMoveOrder, setShowPopupForCancelMoveOrder] =
    useState(false);
  // const [updateShippingAddressDropDown, setUpdateShippingAddressDropDown] =
  //   useState<string>("");

  // Function to handle confirmation submission
  const handleConfirmationSubmit = async (type?: string) => {
    try {
      const selectedDateWithTime = new Date(selectedDate);
      const currentDateTime = new Date();
      selectedDateWithTime.setHours(currentDateTime.getHours());
      selectedDateWithTime.setMinutes(currentDateTime.getMinutes());
      selectedDateWithTime.setSeconds(currentDateTime.getSeconds());

      const dateObj = new Date(selectedDate);
      const convertToString = dateObj.toLocaleDateString("en-GB"); // dd/mm/yyyy format
      const [day, month, year] = convertToString.split("/");
      const formattedDate = `${year}-${month}-${day}`;

      const data = {
        customer_details_id: parseInt(id as string),
        address_id: currAddress?.id,
        street_number: serviceAddressData?.streetNumber,
        street_name: serviceAddressData?.streetName,
        suit_unit: serviceAddressData?.apartment,
        town: serviceAddressData?.city,
        province: serviceAddressData?.province,
        postal_code: serviceAddressData?.pinCode,
        service_change_date: formattedDate,
        // update_shipping_address: updateShippingAddressDropDown,
        country: "Canada",
      };

      const dataForCancelMoveOrder = {
        customer_details_id: parseInt(id as string),
        address_id: currAddress?.id,
        street_number:
          planData?.customerInternet?.internetElMoveOrder?.street_number,
        street_name:
          planData?.customerInternet?.internetElMoveOrder?.street_name,
        suit_unit: planData?.customerInternet?.internetElMoveOrder?.unit,
        town: planData?.customerInternet?.internetElMoveOrder?.city,
        province: planData?.customerInternet?.internetElMoveOrder?.province,
        postal_code:
          planData?.customerInternet?.internetElMoveOrder?.postal_code,
        service_change_date: formattedDate,
        // update_shipping_address: updateShippingAddressDropDown,
        country: "Canada",
        type,
      };

      const response = await updateAddress(
        type === "cancel" ? dataForCancelMoveOrder : data
      ).unwrap();

      if (response.status === 200) {
        dispatch(
          addNotification({ type: "success", message: response.message })
        );

        if (type !== "cancel") {
          dispatch(toggleShowServiceAddressConfirmationPopup());
        } else {
          setShowPopupForCancelMoveOrder(false);
        }

        if (!Object.keys(currAddress).length) {
          dispatch(setCurrentServiceAddress(serviceAddressData));
        } else {
          dispatch(setNewServiceAddress(serviceAddressData));
        }
        dispatch(setMoveDate(selectedDateWithTime));
        dispatch(setServiceStatus("Processing"));
        refetch();
      }
    } catch (error: any) {
      dispatch(
        addNotification({ type: "error", message: error?.data?.message })
      );
      if (
        error?.data?.status === 403 ||
        error?.data?.status === 401 ||
        error?.data?.message === "User not found."
      ) {
        localStorage.clear();
        dispatch(logout());
        navigate("/login");
      }
    }
  };

  // Function to handle form submission
  const handleSubmit = () => {
    dispatch(toggleShowServiceAddressPopup());
    dispatch(toggleShowServiceAddressDateSelectorPopup());
  };

  // Function to handle date Selection
  const handleDataSelection = () => {
    dispatch(toggleShowServiceAddressDateSelectorPopup());
    dispatch(toggleShowServiceAddressConfirmationPopup());
  };

  const handleNextFibreAddress = () => {
    dispatch(toggleShowServiceAddressPopup());
    dispatch(toggleShowFibreAddressPopup());
  };

  const {
    unit,
    street_number,
    street_name,
    city,
    province,
    postal_code,
    requested_install_date,
  } = planData?.customerInternet?.internetElMoveOrder || {};

  const formattedAddress = unit
    ? `${unit}-${street_number}, ${street_name}, ${city}, ${province}, ${postal_code}`
    : `${street_number}, ${street_name}, ${city}, ${province}, ${postal_code}`;

  const resetAddressClick = () => {
    setServiceAddressDate(initialAddressState);
  };

  return (
    <ServiceBox
      isEdit={currAddress?.full_address}
      attributes={{
        onClick: () => {
          dispatch(toggleShowServiceAddressPopup());
        },
      }}
    >
      {currAddress?.full_address ? (
        <div className="flex 2xl:gap-5 gap-2.5 flex-col">
          <div className="flex gap-5 justify-between h-[40px] items-center">
            <div className="flex">
              <p className="text-xl 2xl:text-[26px] font-medium">
                Service address
              </p>
            </div>

            {/* create and edit move order , keep this code */}
            {planData?.cp_stage === "Online" && (
              <div className="flex gap-1">
                <div
                  className={`border h-max bg-white border-[#D7B9FF] 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey ${
                    !enableCreateMoveorder && !enableEditMoveOrder
                      ? "custom-cursor-not-allowed opacity-50"
                      : ""
                  }`}
                  title={
                    anyPendingOrderExists
                      ? "Cannot modify service address, because there are pending orders on the account."
                      : ""
                  }
                  onClick={() => {
                    // show nofication if any pending order exists
                    if (anyPendingOrderExists && enableCreateMoveorder) {
                      dispatch(
                        addNotification({
                          type: "error",
                          message:
                            "Cannot modify service address, because there are pending orders on the account.",
                        })
                      );
                    }
                    (enableCreateMoveorder || enableEditMoveOrder) &&
                      dispatch(toggleShowServiceAddressPopup());
                  }}
                >
                  <EditIcon width={20} height={20} />
                </div>
              </div>
            )}
          </div>
          <div>
            <p className="text-base uppercase">{currAddress?.full_address}</p>
            {isMoveOrder &&
              (planData?.customerInternet?.internetElMoveOrder?.stage ===
                "Eligible For Submission" ||
                planData?.customerInternet?.internetElMoveOrder?.stage ===
                  "Pre Response") && (
                <div className="bg-white border border-[#FBC400] mt-3 flex flex-col justify-center 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
                  <div className="flex gap-2.5  items-center">
                    {planData?.customerInternet?.internetElMoveOrder !== null &&
                      planData?.customerInternet?.internetElMoveOrder
                        ?.submit_date === null && (
                        <div
                          title="Cancel move request"
                          className={`cursor-pointer text-red-500`}
                          onClick={() => {
                            setShowPopupForCancelMoveOrder(true);
                          }}
                        >
                          <CancelIcon />
                        </div>
                      )}
                    <div className="bg-warning-light rounded-[12px] py-2 px-3 gap-2.5">
                      {planData?.customerInternet?.internetElMoveOrder
                        ?.stage === "Eligible For Submission" ||
                      planData?.customerInternet?.internetElMoveOrder?.stage ===
                        "Pre Response" ? (
                        <p className=" text-warning">Processing move request</p>
                      ) : (
                        <p className=" text-warning">Moving</p>
                      )}
                    </div>
                  </div>
                  <p className="text-base">
                    {`Your move for ${formatDateToMonthDayYear(
                      requested_install_date
                    )} to ${formattedAddress} is
                    currently being processed.`}
                  </p>
                </div>
              )}
          </div>
        </div>
      ) : (
        <div className="flex rounded-20 items-center justify-between">
          <div className="items-center">
            <p className="text-xl 2xl:text-[26px] font-medium">
              Service address
            </p>
          </div>
          {/* <div
            className="border border-black 2xl:w-[44px] 2xl:h-[44px] 2xl:py-[6px] 2xl:px-[8px] p-2 rounded-[12px] flex justify-center items-center 2xl:gap-2.5 cursor-pointer duration-300 hover:bg-light-grey"
            onClick={() => dispatch(toggleShowServiceAddressPopup())}
          >
            <AddIcon width={20} height={20} />
          </div> */}
        </div>
      )}
      {requestedMoveDate && changedAddress?.address && (
        <div className="bg-white border border-[#FBC400] flex flex-col basis-full 2xl:gap-5 gap-2.5 p-2.5 rounded-10">
          <div className="flex p-[2px] gap-2.5 basis-full items-center">
            <div className="bg-warning-light w-fit rounded-[6px] p-2 gap-2.5">
              <p className=" text-warning font-bold">{status}</p>
            </div>
          </div>
          <div className="text-base font-medium">
            <p>
              <b>New address:</b> {changedAddress?.address},{" "}
              {changedAddress?.city}
            </p>
            <p>
              <b>Requested move date:</b>{" "}
              {requestedMoveDate?.toLocaleDateString("en-US")}
            </p>
          </div>
        </div>
      )}

      {showPopup && (
        <AddLocationPopup
          formData={serviceAddressData}
          setFormData={setServiceAddressDate}
          handleNextFibreAddress={handleNextFibreAddress}
          title={
            serviceAddressData ? "Update service address" : "Check new address"
          }
          description={
            "Please enter your new address below and we will check to make sure we have service at your new location."
          }
          currAddress={currAddress}
          handleSubmit={handleSubmit}
          closeHandler={() => {
            dispatch(toggleShowServiceAddressPopup());
            setServiceAddressDate(initialAddressState);
            setSelectedDate(null);
          }}
          disableManualEntryForaddress={true}
          resetAddressClick={resetAddressClick}
        />
      )}

      {showFibrePopup && (
        <Popup
          title="Great news! Looks like we offer Purple Fibre at this location, please contact us directly to move your services!"
          closeHandler={() => {
            dispatch(toggleShowFibreAddressPopup());
            dispatch(toggleShowServiceAddressPopup());
          }}
          height="780px"
          width="588px"
        >
          <div className="flex justify-center items-center p-10 bg-black rounded-30">
            <img
              className="shadow drop-shadow-[0_10px_20px_rgba(168,85,247,0.8)]"
              src={
                "https://purplecow-customer-portal.s3.ca-central-1.amazonaws.com/branding_assets/purple_fibre_logo.png"
              }
              alt="Purple Fibre"
            />
          </div>
        </Popup>
      )}

      {showDatePopup && (
        <Popup
          title="Address is serviceable"
          closeHandler={() => {
            dispatch(toggleShowServiceAddressDateSelectorPopup());
            dispatch(toggleShowServiceAddressPopup());
          }}
          height="780px"
          width="588px"
        >
          <ServiceDateSelector
            serviceChangeMessage={"select service change date"}
            description={AddressChangeDateDescription}
            closeHandler={() => {
              dispatch(toggleShowServiceAddressDateSelectorPopup());
              dispatch(toggleShowServiceAddressPopup());
            }}
            // showDropDown={true} // show select dropdown for service address
            // updateShippingAddressDropDown={updateShippingAddressDropDown}
            // setUpdateShippingAddressDropDown={setUpdateShippingAddressDropDown}
            selectedDate={selectedDate}
            setServiceDateSelectorDate={setSelectedDate}
            handleSubmit={handleDataSelection}
            disableWeekEnds={true} // pass flag true for disable weekends in calendar
            disableHolidays={true} // pass flag true for disable holidays in calendar
            disableNext3WorkingDays={true} // pass flag true for disable next 3 working days in calendar
          />
        </Popup>
      )}

      {showConfirmationPopup && (
        <ConfirmationMessagePopup
          title={
            !currAddress
              ? "Service address confirmation"
              : "Service address change confirmation"
          }
          message={
            serviceAddressData?.apartment
              ? `You have requested for your address to be changed to <b>${
                  serviceAddressData?.apartment
                }-${serviceAddressData?.streetNumber}, ${
                  serviceAddressData?.streetName
                }, ${serviceAddressData?.city}, ${
                  serviceAddressData?.province
                }, ${
                  serviceAddressData?.pinCode
                }</b> on <b>${convertDateStringToReadableDate(
                  selectedDate
                )}</b>. You should be able to take your modem to this new location and plug everything in.`
              : `You have requested for your address to be changed to <b>${
                  serviceAddressData?.streetNumber
                }, ${serviceAddressData?.streetName}, ${
                  serviceAddressData?.city
                }, ${serviceAddressData?.province}, ${
                  serviceAddressData?.pinCode
                }</b> on <b>${convertDateStringToReadableDate(
                  selectedDate
                )}</b>. You should be able to take your modem to this new location and plug everything in.`
          }
          handleSubmit={() => handleConfirmationSubmit("")}
          closeHandler={() => {
            dispatch(toggleShowServiceAddressConfirmationPopup());
            dispatch(toggleShowServiceAddressDateSelectorPopup());
          }}
          isLoading={updateAddressLoading?.isLoading}
        />
      )}

      {showPopupForCancelMoveOrder && (
        <ConfirmationMessagePopup
          title={"Cancel Move Order"}
          message={`Are you sure you want to cancel move order request ?`}
          handleSubmit={() => handleConfirmationSubmit("cancel")}
          closeHandler={() => {
            setShowPopupForCancelMoveOrder(false);
          }}
          isLoading={updateAddressLoading?.isLoading}
        />
      )}
    </ServiceBox>
  );
};

export default ServiceAddressBox;
