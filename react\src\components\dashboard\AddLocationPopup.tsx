import React, { ChangeEvent, useEffect, useRef, useState } from "react";
import { LocationDataProps, LocationDataState } from "../../typings/typing";
import Button from "../common/Button";
import CustomDropdown from "../common/CustomDropdown";
import Popup from "../common/Popup";
import AddressFields from "../forms/AddressFields";
import InputFields from "../forms/InputFields";
import { regEx } from "../../utils/helper";
import { CancelIcon } from "../../assets/Icons";

const AddLocationPopup: React.FC<LocationDataProps> = ({
  description,
  formData,
  title,
  setFormData,
  handleSubmit,
  closeHandler,
  isLoading,
  disableManualEntryForaddress,
  resetAddressClick,
  handleNextFibreAddress,
}) => {
  const [locationDataError, setLocationDataError] = useState<
    Partial<LocationDataState>
  >({});
  const inputRef = useRef<HTMLInputElement | null>(null);

  const [excludedAddress, setExcludedAddress] = useState<any>([]);
  const [fibreAddresses, setFibreAddresses] = useState<any>([]);
  const [showErrorMsgForExcludedAddress, setShowErrorMsgForExcludedAddress] =
    useState<boolean>(false);
  const provinceData = [
    {
      id: 1,
      label: "NS",
      value: "NS",
    },
    {
      id: 2,
      label: "NL",
      value: "NL",
    },
    {
      id: 3,
      label: "PE",
      value: "PE",
    },
  ];
  // Validate the form data
  const validateFormData = (formData: LocationDataState) => {
    const errors: Partial<LocationDataState> = {};
    let isValid = true;

    if (!formData?.streetAddress) {
      isValid = false;
      errors.streetAddress = "Enter street address";
    }
    if (!formData?.streetNumber) {
      isValid = false;
      errors.streetNumber = "Enter street number";
    }
    if (!formData?.streetName) {
      isValid = false;
      errors.streetName = "Enter street name";
    }
    // if (!formData.apartment) {
    //   isValid = false;
    //   errors.apartment = "Enter apartment";
    // }
    if (!formData?.city) {
      isValid = false;
      errors.city = "Enter city name";
    }
    if (!formData?.province) {
      isValid = false;
      errors.province = "Enter valid province";
    }
    if (!formData?.pinCode) {
      isValid = false;
      errors.pinCode = "Enter valid pin code";
    }
    if (!/^[A-Z]\d[A-Z] \d[A-Z]\d$/.test(formData?.pinCode)) {
      isValid = false;
      errors.pinCode = "Enter valid postal code in A1A 1A1 format";
    }
    return { isValid, errors };
  };

  // Handle input change
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    let updatedValue = value;

    if (name === "pinCode") {
      updatedValue = updatedValue.toUpperCase().replace(/[^A-Z0-9]/g, "");
      if (updatedValue.length > 6) {
        return; // Prevent entering more than 6 characters
      }
      if (updatedValue.length > 3) {
        updatedValue = updatedValue.slice(0, 3) + " " + updatedValue.slice(3);
      }
    }
    // if (name === "streetAddress") {
    //   setFormData((state) => ({ ...state, streetAddress: updatedValue,  }));
    // }

    setFormData((state) => ({ ...state, [name]: updatedValue }));
    setLocationDataError((state) => ({ ...state, [name]: "" }));
  };

  // Handle form submission
  const handlePopupSubmit = (e: ChangeEvent<HTMLFormElement>): void => {
    e.preventDefault();

    const { isValid, errors } = validateFormData(formData as LocationDataState);
    setLocationDataError(errors);

    // check this for Service address only
    if (!title?.includes("mail")) {
      isAddressExcluded(formData, excludedAddress)
        ? setShowErrorMsgForExcludedAddress(true)
        : setShowErrorMsgForExcludedAddress(false);
    }

    //  for Service address
    if (!title?.includes("mail")) {
      // user inputdata is valid and address is not excluded
      if (isValid && !isAddressExcluded(formData, excludedAddress)) {
        // if address is fibre address
        if (isFibreAddress(formData, fibreAddresses)) {
          handleNextFibreAddress && handleNextFibreAddress();
        } else {
          setLocationDataError({});
          handleSubmit();
        }
      }
    } else {
      //  for mailing address
      if (isValid) {
        setLocationDataError({});
        handleSubmit();
      }
    }
  };

  // reset error-messages on address change
  useEffect(() => {
    setLocationDataError({});
  }, [formData]);

  // get data of excluded address
  const handleGetExcludedAddress = async () => {
    try {
      const response = await fetch(
        "https://product-catalog-storage.s3.ca-central-1.amazonaws.com/excludedAddresses.json",
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );
      const data = await response.json();
      setExcludedAddress(data);
    } catch (error) {
      console.error("Failed to fetch excluded addresses:", error);
    }
  };

  const handleGetFibreAddresses = async () => {
    try {
      const response = await fetch(
        "http://product-catalog-storage.s3.ca-central-1.amazonaws.com/purpleFibreAddresses.json",
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );
      const data = await response.json();
      setFibreAddresses(data);
    } catch (error) {
      console.error("Failed to fetch fibre addresses:", error);
    }
  };

  useEffect(() => {
    // fetch data  for Service address only
    if (!title?.includes("mail")) {
      handleGetExcludedAddress();
      handleGetFibreAddresses();
    }
  }, []);

  // Function to check if the object matches any excludeAddress entry
  const isAddressExcluded = (address: any, excludeArray: any) => {
    for (const exclude of excludeArray) {
      let isMatch = true;
      // Check each field in the exclude object
      for (const key in exclude) {
        const mappedKey = mapKey(key);

        // Skip comparison if  value is an empty string
        if (typeof exclude[key] === "string" && exclude[key]?.trim() === "") {
          continue;
        }

        // while comparing PostalCode if its not in proper format then just ignore that particular field validation
        if (key === "PostalCode" && !regEx.POSTAL_CODE?.test(exclude[key])) {
          continue;
        }

        // If the key is Street and its value ends with "Road" or "Street", replace them
        if (key === "Street" && typeof exclude[key] === "string") {
          if (exclude[key]?.endsWith("Road")) {
            exclude[key] = exclude[key]?.replace(/Road$/, "Rd"); // Replace "Road" with "Rd"
          } else if (exclude[key]?.endsWith("Street")) {
            exclude[key] = exclude[key]?.replace(/Street$/, "St"); // Replace "Street" with "St"
          } else if (exclude[key]?.endsWith("Avenue")) {
            exclude[key] = exclude[key]?.replace(/Avenue$/, "Ave"); // Replace "Avenue" with "Ave"
          }
        }

        // convert matchining to lowercase and then match
        if (
          mappedKey &&
          address[mappedKey]?.toLowerCase() !== exclude[key]?.toLowerCase()
        ) {
          isMatch = false;
          break;
        }
      }

      // Return true if a match is found
      if (isMatch) {
        return true;
      }
    }
    return false;
  };

  const isFibreAddress = (address: any, fibreAddresses: any[]): boolean => {
    // Mapping between address keys and fibre address keys
    const keyMapping: { [key: string]: string } = {
      streetNumber: "BuildingNumber",
      streetName: "Street",
      city: "City",
      province: "Province",
      pinCode: "PostalCode",
    };

    for (const fibre of fibreAddresses) {
      let allMatch = true;

      for (const [fibreKey, fibreValue] of Object.entries(fibre)) {
        if (fibreKey === "SA_SFRecordID") continue; // Ignore this key

        const addressKey = Object.keys(keyMapping).find(
          (k) => keyMapping[k] === fibreKey
        );

        if (!addressKey) {
          allMatch = false;
          break;
        }

        // Normalize both values to lowercase
        const addressValue = (address[addressKey] || "")
          .toString()
          .trim()
          .toLowerCase();
        const fibreValueNorm = (fibreValue || "")
          .toString()
          .trim()
          .toLowerCase();

        if (addressValue !== fibreValueNorm) {
          allMatch = false;
          break;
        }
      }

      if (allMatch) return true;
    }

    return false;
  };

  // for logic (json file key : portal formData key)
  const mapping = {
    BuildingNumber: "streetNumber",
    Street: "streetName",
    City: "city",
    Province: "province",
    PostalCode: "pinCode",
    SubBuilding: "apartment",
  };

  const mapKey = (key: keyof typeof mapping) => {
    return mapping[key];
  };

  return (
    <Popup
      title={title ?? "Check new address"}
      closeHandler={closeHandler}
      width="700px"
    >
      <form onSubmit={handlePopupSubmit}>
        <div className="flex flex-col gap-2.5 lg:gap-5">
          {description && (
            <div>
              <p>{description}</p>
            </div>
          )}
          <div className="flex gap-2.5 lg:gap-5 flex-col">
            <div className="flex gap-2.5 lg:gap-5 lg:flex-row">
              <div className="basis-full">
                <AddressFields
                  placeHolder={"Street Address"}
                  isErrorMsg={locationDataError.streetAddress}
                  attributes={{
                    name: "streetAddress",
                    value: formData?.streetAddress || "",
                  }}
                  changeEvent={handleInputChange}
                  setLocationData={setFormData}
                  title={title}
                  isDisabled={
                    disableManualEntryForaddress && formData?.province !== ""
                  }
                  formData={formData}
                  inputRef={inputRef}
                />
              </div>
              {disableManualEntryForaddress && formData?.province !== "" && (
                <div className="flex items-end">
                  <div
                    title="Clear address"
                    className={`text-red-500 mb-3 cursor-pointer`}
                    onClick={() => {
                      resetAddressClick();
                      if (inputRef?.current) {
                        inputRef.current.value = ""; // clear input
                      }
                    }}
                  >
                    <CancelIcon />
                  </div>
                </div>
              )}
              {/* <div className="basis-full">
                <InputFields
                  placeHolder={"Street Name"}
                  isErrorMsg={locationDataError.streetName}
                  attributes={{
                    name: "streetName",
                    value: formData?.streetName,
                  }}
                  changeEvent={handleInputChange}
                />
              </div> */}
            </div>
            <div className="basis-full">
              <InputFields
                placeHolder={"Apartment, suite, unit, building, floor, etc..."}
                attributes={{
                  name: "apartment",
                  value: formData?.apartment,
                }}
                changeEvent={handleInputChange}
                isDisabled={disableManualEntryForaddress}
              />
            </div>
          </div>
          <div className="flex gap-2.5 lg:gap-5 lg:flex-row flex-col">
            <div className="basis-full">
              <InputFields
                changeEvent={handleInputChange}
                placeHolder={"City"}
                isErrorMsg={locationDataError.city}
                attributes={{ name: "city", value: formData?.city }}
                isDisabled={disableManualEntryForaddress}
              />
            </div>
            <div className="basis-full">
              <CustomDropdown
                placeholder="Province"
                name="province"
                value={formData?.province}
                onChange={handleInputChange}
                error={locationDataError?.province}
                options={provinceData}
                defaultValue={formData?.province}
                isDisabled={disableManualEntryForaddress}
              />
            </div>
            <div className="basis-full">
              <InputFields
                placeHolder={"Postal code"}
                isErrorMsg={locationDataError?.pinCode}
                changeEvent={handleInputChange}
                attributes={{ name: "pinCode", value: formData?.pinCode }}
                isDisabled={disableManualEntryForaddress}
              />
            </div>
          </div>
          {showErrorMsgForExcludedAddress && (
            <div className=" text-red-400">
              Oops, we are currently unable to provide service for this address.
            </div>
          )}{" "}
          <div className="flex gap-2.5 lg:gap-5 lg:flex-row flex-col ">
            <div className="basis-full">
              <Button
                title="Go back"
                btnType="transparent"
                type="button"
                attributes={{
                  disabled: isLoading,
                }}
                className="disabled:opacity-50"
                clickEvent={closeHandler}
              />
            </div>
            <div className="basis-full">
              <Button
                title={
                  title?.includes("mail") ? "Update" : "Check Availability"
                }
                type="submit"
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </form>
    </Popup>
  );
};

export default AddLocationPopup;
