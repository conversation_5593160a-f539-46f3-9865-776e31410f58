class SalesforceClientMock {
    getDeletedData = jest.fn().mockResolvedValue({
        deleteStatus: true,
        deletedData: [{ id: 1 }]
    });
    
    getAddressDetailsById = jest.fn().mockResolvedValue({
        returnStatus: true,
        addresstData: { id: 1 }
    });
    
    getDetailsByFieldAndApi = jest.fn().mockResolvedValue({
        returnStatus: true,
        salesforceData: [{ id: 1 }]
    });
    
    getBulkDetails = jest.fn().mockResolvedValue([
        { Id: 'SF1', FirstName: 'John' },
        { Id: 'SF2', FirstName: 'Jane' }
    ]);
}

module.exports = SalesforceClientMock;
