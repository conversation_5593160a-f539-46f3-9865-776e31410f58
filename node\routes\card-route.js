const Validator = require('../helpers/validators')
const { validator } = require("../middleware/validationMid");
const { fetchCard, addCard, updateCard, deleteCard } = require("../controllers/card-controller");
const router = require("express").Router();

module.exports = (app, basePath) => {
	// Endpoint for adding a card
	router.post("/", validator(Validator.cardValidation), addCard);
	// Endpoint for updating a card
	router.put("/", updateCard);
	// Endpoint for deleting a card
	router.delete("/:cardId", deleteCard);
	// Endpoint for fetching a card
	router.get("/", fetchCard);

	// Use the base path passed from index.js
	app.use(`${basePath}/card`, router); // Mount the router at the base path
};
