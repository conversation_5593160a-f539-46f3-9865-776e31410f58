const CustomerServices = require("./customer-sync-services");
const customerServices = new CustomerServices();

class DeleteDataServices {
    async getDeleteDetails() {
        try {
            this.getDetailsFromDB();
        } catch (error) {
            console.error("Sync service detele Details -> ", error);
        }
    }

    async getDetailsFromDB() {
        try {
            await this.handleUnwantedDetails(
                `SELECT cos.id FROM creation_order_shipping cos
                 LEFT JOIN internets_eastlink_creation_order ieco 
                 ON cos.id = ieco.shipping_id
                 WHERE ieco.id IS NULL`,
                "creation_order_shipping"
            );
            
            await this.handleUnwantedDetails(
                `SELECT ieco.id FROM internets_eastlink_creation_order ieco
                 LEFT JOIN customer_details_internets_eastlink cdie
                 ON cdie.creation_order = ieco.id
                 WHERE cdie.id IS NULL`,
                "internets_eastlink_creation_order"
            );

            await this.handleUnwantedDetails(
                `SELECT ieta.id FROM internets_eastlink_tech_appointment ieta
                 LEFT JOIN customer_details_internets_eastlink cdie
                 ON cdie.tech_appointment = ieta.id
                 WHERE cdie.id IS NULL`,
                "internets_eastlink_tech_appointment"
            );
            
            await this.handleUnwantedDetails(
                `SELECT iedo.id FROM internets_eastlink_disconnect_order iedo
                LEFT JOIN customer_details_internets_eastlink cdie
                ON cdie.disconnect_order = iedo.id
                WHERE cdie.id IS NULL`,
                "internets_eastlink_disconnect_order"
            );   
            
            await this.handleUnwantedDetails(
                `SELECT iesco.id FROM internets_eastlink_speed_change_order iesco
                LEFT JOIN customer_details_internets_eastlink cdie
                ON cdie.speed_change_order = iesco.id
                WHERE cdie.id IS NULL`,
                "internets_eastlink_speed_change_order"
            ); 
            
            await this.handleUnwantedDetails(
                `SELECT iemo.id FROM internets_eastlink_move_order iemo
                LEFT JOIN customer_details_internets_eastlink cdie
                ON cdie.move_order = iemo.id
                WHERE cdie.id IS NULL`,
                "internets_eastlink_move_order"
            ); 
            
            await this.handleUnwantedDetails(
                `SELECT cdie.id FROM customer_details_internets_eastlink cdie
                LEFT JOIN contacts_customer_details ccd 
                ON ccd.internet_id = cdie.id
                WHERE ccd.id IS NULL`,
                "customer_details_internets_eastlink"
            );
            
            await this.handleUnwantedDetails(
                `SELECT cdt.id FROM customer_details_tvs cdt
                LEFT JOIN contacts_customer_details ccd 
                ON ccd.tv_id = cdt.id
                WHERE ccd.id IS NULL`,
                "customer_details_tvs"
            );
            
            await this.handleUnwantedDetails(
                `SELECT cdp.id FROM customer_details_phones cdp
                LEFT JOIN contacts_customer_details ccd 
                ON ccd.phone_id = cdp.id
                WHERE ccd.id IS NULL`,
                "customer_details_phones"
            );
            
            await this.handleUnwantedDetails(
                `SELECT distinct cda.id 
                FROM customer_details_addresses cda
                LEFT JOIN contacts_customer_details ccd_service 
                    ON ccd_service.service_address_id = cda.id
                LEFT JOIN contacts_customer_details ccd_mailing 
                    ON ccd_mailing.mailing_address_id = cda.id
                WHERE (ccd_service.id IS NULL AND cda.address_type = 'service')
                OR (ccd_mailing.id IS NULL AND cda.address_type = 'mailing');`,
                "customer_details_addresses"
            );

        } catch (error) {
            console.error("Sync service getDetailsFromDB -> ", error);
        }
    }

    async handleUnwantedDetails(query, tableName) {
        try {
            const result = await customerServices.executeQuery(query);
            if (result && result.length) {
                const ids = result.map(item => item.id);
                await this.deleteFromDatabase(tableName, ids);
            }
        } catch (error) {
            console.error(`Sync service handleUnwantedDetails (${tableName}) -> `, error.message);
        }
    }

    async deleteFromDatabase(tableName, idArray) {
        try {
            const placeholders = idArray.map(() => '?').join(',');
            const deleteQuery = `DELETE FROM ${tableName} WHERE id IN (${placeholders})`;
            await customerServices.executeQuery(deleteQuery, idArray);
        } catch (error) {
            console.error("Sync service deleteFromDatabase -> ", error);
        }
    }
}

module.exports = DeleteDataServices;
